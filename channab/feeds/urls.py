from django.urls import path
from . import views

app_name = 'feeds'

urlpatterns = [
    # Dashboard
    path('', views.feed_dashboard, name='dashboard'),
    
    # Feed management
    path('feeds/', views.feed_list, name='feed_list'),
    path('feeds/create/', views.feed_create, name='feed_create'),
    path('feeds/<int:pk>/', views.feed_detail, name='feed_detail'),
    path('feeds/<int:pk>/edit/', views.feed_edit, name='feed_edit'),
    path('feeds/<int:pk>/delete/', views.feed_delete, name='feed_delete'),
    
    # Feed rule management
    path('feeds/<int:feed_pk>/rules/create/', views.feed_rule_create, name='feed_rule_create'),
    path('rules/<int:pk>/', views.feed_rule_detail, name='feed_rule_detail'),
    path('rules/<int:pk>/edit/', views.feed_rule_edit, name='feed_rule_edit'),
    path('rules/<int:pk>/toggle-status/', views.feed_rule_toggle_status, name='feed_rule_toggle_status'),
    
    # Feed activity management
    path('activities/<int:pk>/accept/', views.feed_activity_accept, name='feed_activity_accept'),
    path('activities/<int:pk>/skip/', views.feed_activity_skip, name='feed_activity_skip'),
    path('rules/<int:rule_pk>/create-activities/<str:date>/', views.create_feed_activities, name='create_feed_activities'),
    path('rules/<int:rule_pk>/complete-feeding/<str:date>/', views.complete_feeding, name='complete_feeding'),
    path('rules/<int:rule_pk>/skip-feeding/<str:date>/', views.skip_feeding, name='skip_feeding'),
    path('rules/<int:rule_pk>/complete-all/<str:date>/', views.complete_all_for_date, name='complete_all_for_date'),
    path('rules/<int:rule_pk>/skip-all/<str:date>/', views.skip_all_for_date, name='skip_all_for_date'),
    
    # Animal-specific feeds
    path('animals/<int:animal_pk>/feeds/', views.animal_feeds, name='animal_feeds'),
    
    # AJAX endpoints
    path('ajax/animal-count-preview/', views.get_animal_count_preview, name='animal_count_preview'),
]