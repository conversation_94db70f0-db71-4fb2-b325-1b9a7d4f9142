from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import json
import logging
from .models import Feed, FeedRule, FeedActivity
from dairy.models import Animal

logger = logging.getLogger(__name__)


class MultipleChoiceJSONWidget(forms.Widget):
    """Custom widget for handling multiple choice JSON fields"""
    
    def __init__(self, choices=None, attrs=None):
        self.choices = choices or []
        super().__init__(attrs)
    
    def value_from_datadict(self, data, files, name):
        """Extract value from form data"""
        return data.get(name, [])
    
    def prepare_value(self, value):
        """Prepare value for widget rendering - convert to JSON string"""
        if not value:
            return '[]'
        
        if isinstance(value, list):
            return json.dumps(value)
        
        if isinstance(value, str):
            if not value.strip():
                return '[]'
            
            try:
                parsed = json.loads(value)
                return json.dumps(parsed) if isinstance(parsed, list) else json.dumps([parsed])
            except (json.JSONDecodeError, ValueError):
                values = [v.strip() for v in value.split(',') if v.strip()]
                return json.dumps(values) if values else '[]'
        
        return '[]'
    
    def render(self, name, value, attrs=None, renderer=None):
        """Render as a hidden input with proper JSON value"""
        from django.utils.html import format_html
        from django.forms.utils import flatatt
        
        if attrs is None:
            attrs = {}
        
        # Use prepare_value to ensure consistent value handling
        json_value = self.prepare_value(value)
        
        final_attrs = {
            'type': 'hidden',
            'name': name,
            'value': json_value,
            'data-multiple': 'true',
            **attrs
        }
        
        if 'id' not in final_attrs and name:
            final_attrs['id'] = f'id_{name}'
        
        return format_html('<input{}>', flatatt(final_attrs))


class MultipleChoiceJSONField(forms.Field):
    """Custom field to handle multiple choice values for JSON fields"""
    
    def __init__(self, choices=None, **kwargs):
        self.choices = choices or []
        kwargs.setdefault('required', False)  # Default to not required
        # Use our custom widget
        kwargs.setdefault('widget', MultipleChoiceJSONWidget(choices=choices))
        super().__init__(**kwargs)
    
    def bound_data(self, data, initial):
        """Return the value bound to this field when processing form data"""
        if data is None:
            return initial
        return data
    
    def to_python(self, value):
        logger.info(f"MultipleChoiceJSONField.to_python called with value: {value} (type: {type(value)})")
        
        if not value:
            logger.info("Value is empty, returning []")
            return []
        
        if isinstance(value, list):
            logger.info(f"Value is already a list: {value}")
            return value
        
        if isinstance(value, str):
            if not value.strip():
                logger.info("Value is empty string, returning []")
                return []
            
            # Try JSON parsing first
            try:
                parsed = json.loads(value)
                logger.info(f"Successfully parsed JSON: {parsed}")
                if isinstance(parsed, list):
                    return parsed
                else:
                    result = [parsed] if parsed else []
                    logger.info(f"Converted single value to list: {result}")
                    return result
            except (json.JSONDecodeError, ValueError) as e:
                logger.info(f"JSON parsing failed: {e}, trying comma split")
                # If JSON parsing fails, try splitting by comma
                result = [item.strip() for item in value.split(',') if item.strip()]
                logger.info(f"Split by comma result: {result}")
                return result
        
        logger.info("Unknown value type, returning []")
        return []
    
    def validate(self, value):
        super().validate(value)
        if value and self.choices:
            valid_choices = [choice[0] for choice in self.choices]
            for item in value:
                if item not in valid_choices:
                    raise ValidationError(f"'{item}' is not a valid choice.")
    
    def prepare_value(self, value):
        """Prepare value for form rendering - convert to JSON string for hidden field"""
        if not value:
            return '[]'  # Return empty array instead of empty string
        
        if isinstance(value, list):
            # Convert list to JSON string for the hidden field
            return json.dumps(value)
        
        if isinstance(value, str):
            # If it's already a string, ensure it's proper JSON
            if not value.strip():
                return '[]'  # Return empty array instead of empty string
            
            try:
                # Try to parse as JSON to validate
                parsed = json.loads(value)
                return json.dumps(parsed) if isinstance(parsed, list) else json.dumps([parsed])
            except (json.JSONDecodeError, ValueError):
                # If not JSON, treat as comma-separated and convert to JSON
                values = [v.strip() for v in value.split(',') if v.strip()]
                return json.dumps(values) if values else '[]'  # Return empty array
        
        return '[]'  # Return empty array instead of empty string
    
    def clean(self, value):
        value = self.to_python(value)
        self.validate(value)
        return value


class FeedForm(forms.ModelForm):
    class Meta:
        model = Feed
        fields = ['name', 'description', 'unit_cost', 'unit']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Wanda25'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Feed description...'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'unit': forms.Select(attrs={
                'class': 'form-control'
            })
        }

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super().__init__(*args, **kwargs)

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if self.farm:
            # Check for existing feed with same name in the farm
            existing = Feed.objects.filter(
                farm=self.farm, 
                name=name,
                is_active=True
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError("A feed with this name already exists in your farm.")
        return name

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.farm:
            instance.farm = self.farm
        if commit:
            instance.save()
        return instance


class FeedRuleForm(forms.ModelForm):
    # Override the JSON fields with our custom field
    target_animal_categories = MultipleChoiceJSONField(
        required=False,
        choices=Animal.CATEGORY_CHOICES
    )
    target_animal_types = MultipleChoiceJSONField(
        required=False,
        choices=Animal.TYPE_CHOICES
    )
    
    class Meta:
        model = FeedRule
        fields = [
            'name', 
            # Enhanced target animal fields
            'target_all_animals', 'target_animal_categories', 'target_animal_types',
            'use_age_filter', 'min_age_months', 'max_age_months',
            # Legacy fields for backward compatibility
            'target_animal_category', 'target_animal_type',
            # Enhanced formula fields
            'formula_type', 'formula_application', 'dosage_qty', 'dosage_unit',
            'formula_value', 'min_dosage', 'max_dosage', 'custom_formula',
            # Scheduling fields
            'schedule_type', 'daily_type', 'interval_days', 'frequency_days',
            'weekly_type', 'monday', 'tuesday', 'wednesday', 'thursday', 
            'friday', 'saturday', 'sunday', 'interval_weeks',
            'monthly_type', 'day_of_month', 'week_of_month', 'weekday_of_month', 'interval_months',
            'interval_years', 'custom_interval_value', 'custom_interval_unit',
            'delivery_time', 'end_time', 'end_type', 'max_occurrences',
            'start_date', 'end_date', 'exception_dates'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'e.g., Daily Wanda25 for Milking Cows'
            }),
            
            # Enhanced target animal widgets
            'target_all_animals': forms.CheckboxInput(attrs={
                'class': 'form-check-input form-check-input-lg',
                'id': 'target_all_animals'
            }),
            'use_age_filter': forms.CheckboxInput(attrs={
                'class': 'form-check-input form-check-input-lg',
                'id': 'use_age_filter'
            }),
            
            # Legacy widgets (hidden)
            'target_animal_category': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px; display: none;'
            }),
            'target_animal_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px; display: none;'
            }),
            'min_age_months': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '0',
                'max': '300'
            }),
            'max_age_months': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '0',
                'max': '300'
            }),
            # Enhanced formula widgets
            'formula_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;',
                'id': 'formula_type'
            }),
            'formula_application': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            'dosage_qty': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.01',
                'min': '0'
            }),
            'dosage_unit': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            'formula_value': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.0001',
                'min': '0'
            }),
            'min_dosage': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.01',
                'min': '0'
            }),
            'max_dosage': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.01',
                'min': '0'
            }),
            'custom_formula': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'e.g., weight * 0.05 + 2'
            }),
            
            # Enhanced scheduling widgets
            'schedule_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;',
                'id': 'schedule_type'
            }),
            'daily_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            'weekly_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            'monthly_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            'end_type': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            'custom_interval_unit': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }),
            
            # Number inputs
            'interval_days': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '365'
            }),
            'frequency_days': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '365'
            }),
            'interval_weeks': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '52'
            }),
            'day_of_month': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '31'
            }),
            'week_of_month': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '5'
            }),
            'weekday_of_month': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'style': 'height: 50px; font-size: 16px;'
            }, choices=[
                (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
                (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
            ]),
            'interval_months': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '12'
            }),
            'interval_years': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1',
                'max': '10'
            }),
            'custom_interval_value': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1'
            }),
            'max_occurrences': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'min': '1'
            }),
            
            # Time inputs
            'delivery_time': forms.TimeInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'time'
            }),
            'end_time': forms.TimeInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'time'
            }),
            
            # Date inputs
            'start_date': forms.DateInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control form-control-lg',
                'type': 'date'
            }),
            
            # Checkboxes for weekdays
            'monday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            'tuesday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            'wednesday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            'thursday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            'friday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            'saturday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            'sunday': forms.CheckboxInput(attrs={'class': 'form-check-input form-check-input-lg'}),
            
            # Exception dates (JSON field)
            'exception_dates': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter dates to skip in YYYY-MM-DD format, one per line'
            })
        }

    def __init__(self, *args, **kwargs):
        self.feed = kwargs.pop('feed', None)
        super().__init__(*args, **kwargs)
        
        # Ensure the fields get the proper initial values for editing
        if self.instance.pk:
            # Force refresh from database to ensure we have latest data
            self.instance.refresh_from_db()
            
            logger.info(f"FeedRuleForm init - Instance PK: {self.instance.pk}")
            logger.info(f"FeedRuleForm init - Raw categories: {self.instance.target_animal_categories}")
            logger.info(f"FeedRuleForm init - Raw types: {self.instance.target_animal_types}")
            
            # Ensure the fields get the proper initial values
            if hasattr(self.instance, 'target_animal_categories') and self.instance.target_animal_categories:
                # Ensure it's a list
                if isinstance(self.instance.target_animal_categories, str):
                    try:
                        self.instance.target_animal_categories = json.loads(self.instance.target_animal_categories)
                    except (json.JSONDecodeError, ValueError):
                        self.instance.target_animal_categories = []
                self.fields['target_animal_categories'].initial = self.instance.target_animal_categories
                logger.info(f"FeedRuleForm init - Set categories initial: {self.fields['target_animal_categories'].initial}")
            
            if hasattr(self.instance, 'target_animal_types') and self.instance.target_animal_types:
                # Ensure it's a list
                if isinstance(self.instance.target_animal_types, str):
                    try:
                        self.instance.target_animal_types = json.loads(self.instance.target_animal_types)
                    except (json.JSONDecodeError, ValueError):
                        self.instance.target_animal_types = []
                self.fields['target_animal_types'].initial = self.instance.target_animal_types
                logger.info(f"FeedRuleForm init - Set types initial: {self.fields['target_animal_types'].initial}")
        # Custom fields already have choices set up
        
        # Set default values for new instances
        if not self.instance.pk:
            self.fields['start_date'].initial = timezone.now().date()
            self.fields['end_date'].initial = timezone.now().date() + timedelta(days=365)  # 1 year default
            self.fields['delivery_time'].initial = '08:00'
            self.fields['frequency_days'].initial = 1
            self.fields['formula_type'].initial = 'fixed'
            self.fields['formula_application'].initial = 'per_animal'
            self.fields['formula_value'].initial = 1.0
            self.fields['target_all_animals'].initial = False
            self.fields['use_age_filter'].initial = False
            self.fields['schedule_type'].initial = 'daily'
            self.fields['daily_type'].initial = 'every_day'
            self.fields['interval_days'].initial = 1
            self.fields['weekly_type'].initial = 'specific_day'
            self.fields['interval_weeks'].initial = 1
            self.fields['monthly_type'].initial = 'specific_date'
            self.fields['day_of_month'].initial = 1
            self.fields['week_of_month'].initial = 1
            self.fields['weekday_of_month'].initial = 0
            self.fields['interval_months'].initial = 1
            self.fields['interval_years'].initial = 1
            self.fields['custom_interval_value'].initial = 1
            self.fields['custom_interval_unit'].initial = 'hours'
            self.fields['end_type'].initial = 'date'
            
            if self.feed:
                self.fields['dosage_unit'].initial = self.feed.unit

    def clean(self):
        cleaned_data = super().clean()
        
        # Log the raw POST data for our custom fields
        logger.info(f"FeedRuleForm clean - Raw data categories: {self.data.get('target_animal_categories')}")
        logger.info(f"FeedRuleForm clean - Raw data types: {self.data.get('target_animal_types')}")
        logger.info(f"FeedRuleForm clean - Cleaned categories: {cleaned_data.get('target_animal_categories')}")
        logger.info(f"FeedRuleForm clean - Cleaned types: {cleaned_data.get('target_animal_types')}")
        
        min_age = cleaned_data.get('min_age_months')
        max_age = cleaned_data.get('max_age_months')
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        schedule_type = cleaned_data.get('schedule_type')
        end_type = cleaned_data.get('end_type')
        
        # Age validation and auto-enable age filter
        if min_age is not None or max_age is not None:
            # Automatically enable age filter if any age values are provided
            cleaned_data['use_age_filter'] = True
            logger.info(f"Auto-enabling age filter due to age values: min={min_age}, max={max_age}")
            
            if min_age and max_age and min_age > max_age:
                raise ValidationError("Minimum age cannot be greater than maximum age.")
        else:
            # Disable age filter if no age values are provided
            cleaned_data['use_age_filter'] = False

        # Date validation
        if start_date and end_date and end_date <= start_date:
            raise ValidationError("End date must be after start date.")
        
        # End condition validation
        if end_type == 'occurrences':
            max_occurrences = cleaned_data.get('max_occurrences')
            if not max_occurrences or max_occurrences < 1:
                raise ValidationError("Maximum occurrences must be specified and greater than 0 when using 'After N occurrences' end condition.")
        
        # Schedule-specific validation
        if schedule_type == 'weekly':
            weekly_type = cleaned_data.get('weekly_type')
            if weekly_type == 'multiple_days':
                weekdays = [
                    cleaned_data.get('monday', False),
                    cleaned_data.get('tuesday', False),
                    cleaned_data.get('wednesday', False),
                    cleaned_data.get('thursday', False),
                    cleaned_data.get('friday', False),
                    cleaned_data.get('saturday', False),
                    cleaned_data.get('sunday', False)
                ]
                if not any(weekdays):
                    raise ValidationError("At least one weekday must be selected for weekly scheduling.")
        
        elif schedule_type == 'monthly':
            monthly_type = cleaned_data.get('monthly_type')
            if monthly_type == 'specific_date':
                day_of_month = cleaned_data.get('day_of_month')
                if not day_of_month or day_of_month < 1 or day_of_month > 31:
                    raise ValidationError("Day of month must be between 1 and 31.")
            elif monthly_type == 'specific_weekday':
                week_of_month = cleaned_data.get('week_of_month')
                weekday_of_month = cleaned_data.get('weekday_of_month')
                if not week_of_month or week_of_month < 1 or week_of_month > 5:
                    raise ValidationError("Week of month must be between 1 and 5.")
                if weekday_of_month is None or weekday_of_month < 0 or weekday_of_month > 6:
                    raise ValidationError("Weekday must be selected.")
        
        elif schedule_type == 'custom':
            custom_interval_value = cleaned_data.get('custom_interval_value')
            if not custom_interval_value or custom_interval_value < 1:
                raise ValidationError("Custom interval value must be greater than 0.")
        
        # Exception dates validation
        exception_dates = cleaned_data.get('exception_dates', '')
        if exception_dates:
            try:
                dates = []
                for line in exception_dates.strip().split('\n'):
                    line = line.strip()
                    if line:
                        from datetime import datetime
                        datetime.strptime(line, '%Y-%m-%d')
                        dates.append(line)
                cleaned_data['exception_dates'] = dates
            except ValueError:
                raise ValidationError("Exception dates must be in YYYY-MM-DD format, one per line.")

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.feed:
            instance.feed = self.feed
        
        # Get the cleaned data for our custom fields
        categories_data = self.cleaned_data.get('target_animal_categories', [])
        types_data = self.cleaned_data.get('target_animal_types', [])
        
        # Set the instance fields with the cleaned data
        instance.target_animal_categories = categories_data if categories_data else []
        instance.target_animal_types = types_data if types_data else []
        
        logger.info(f"FeedRuleForm save - Categories: {instance.target_animal_categories}")
        logger.info(f"FeedRuleForm save - Types: {instance.target_animal_types}")
        
        # Sync with legacy fields
        if instance.target_animal_categories and len(instance.target_animal_categories) == 1:
            instance.target_animal_category = instance.target_animal_categories[0]
        else:
            instance.target_animal_category = ''
        
        if instance.target_animal_types and len(instance.target_animal_types) == 1:
            instance.target_animal_type = instance.target_animal_types[0]
        else:
            instance.target_animal_type = ''
        
        # Handle exception dates
        if hasattr(instance, 'exception_dates') and instance.exception_dates:
            if isinstance(instance.exception_dates, str):
                # Convert from textarea format to JSON list
                dates = [line.strip() for line in instance.exception_dates.split('\n') if line.strip()]
                instance.exception_dates = dates
        
        if commit:
            instance.save()
            # Force invalidation of cached calculations
            instance._invalidate_cache()
        return instance


class FeedActivityFilterForm(forms.Form):
    STATUS_CHOICES = [('', 'All Status')] + FeedActivity.STATUS_CHOICES
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    animal_tag = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by animal tag...'
        })
    )

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super().__init__(*args, **kwargs)


class FeedActivityAcceptForm(forms.Form):
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Optional notes...'
        })
    )


class FeedActivitySkipForm(forms.Form):
    reason = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Reason for skipping this feed...'
        })
    )


class FeedRuleFilterForm(forms.Form):
    STATUS_CHOICES = [('', 'All Status')] + FeedRule.STATUS_CHOICES
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    target_category = forms.ChoiceField(
        choices=[('', 'All Categories')] + Animal.CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    target_type = forms.ChoiceField(
        choices=[('', 'All Types')] + Animal.TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search feed rules...'
        })
    )