from django.core.management.base import BaseCommand
from django.utils import timezone
from feeds.tasks import run_feed_scheduler, mark_missed_activities, cleanup_expired_rules


class Command(BaseCommand):
    help = 'Run the feed scheduler to create planned feed activities'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Also run cleanup tasks (mark missed activities and expired rules)',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        start_time = timezone.now()
        
        if options['verbose']:
            self.stdout.write(
                self.style.SUCCESS(f'Starting feed scheduler at {start_time}')
            )

        try:
            # Run main scheduler
            result = run_feed_scheduler()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Processed {result['rules_processed']} rules, "
                    f"created {result['activities_created']} activities"
                )
            )

            # Run cleanup tasks if requested
            if options['cleanup']:
                if options['verbose']:
                    self.stdout.write('Running cleanup tasks...')
                
                missed_count = mark_missed_activities()
                expired_count = cleanup_expired_rules()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Marked {missed_count} activities as missed, "
                        f"{expired_count} rules as expired"
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Feed scheduler failed: {str(e)}')
            )
            raise

        end_time = timezone.now()
        duration = end_time - start_time
        
        if options['verbose']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Feed scheduler completed in {duration.total_seconds():.2f} seconds'
                )
            )