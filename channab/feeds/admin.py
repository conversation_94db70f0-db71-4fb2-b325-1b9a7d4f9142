from django.contrib import admin
from .models import (
    Feed, FeedRule, FeedActivity, FeedCostSnapshot, 
    FeedRuleHistory, FeedActivityHistory
)


@admin.register(Feed)
class FeedAdmin(admin.ModelAdmin):
    list_display = ['name', 'farm', 'unit_cost', 'unit', 'is_active', 'created_at']
    list_filter = ['farm', 'unit', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(FeedRule)
class FeedRuleAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'feed', 'target_animal_category', 'target_animal_type',
        'dosage_qty', 'dosage_unit', 'frequency_days', 'status', 'start_date', 'end_date'
    ]
    list_filter = [
        'status', 'target_animal_category', 'target_animal_type',
        'feed__farm', 'start_date', 'end_date'
    ]
    search_fields = ['name', 'feed__name']
    readonly_fields = ['created_at', 'updated_at', 'last_run']
    date_hierarchy = 'start_date'


@admin.register(FeedActivity)
class FeedActivityAdmin(admin.ModelAdmin):
    list_display = [
        'animal', 'feed_rule', 'scheduled_for', 'status',
        'dosage_qty', 'dosage_unit', 'total_cost', 'accepted_by'
    ]
    list_filter = [
        'status', 'scheduled_for', 'feed_rule__feed__farm',
        'dosage_unit', 'accepted_by'
    ]
    search_fields = ['animal__tag', 'feed_rule__name', 'notes']
    readonly_fields = ['created_at', 'updated_at', 'total_cost']
    date_hierarchy = 'scheduled_for'


@admin.register(FeedCostSnapshot)
class FeedCostSnapshotAdmin(admin.ModelAdmin):
    list_display = ['animal', 'feed', 'date', 'cost', 'quantity', 'unit']
    list_filter = ['date', 'feed__farm', 'unit', 'feed']
    search_fields = ['animal__tag', 'feed__name']
    readonly_fields = ['created_at']
    date_hierarchy = 'date'


@admin.register(FeedRuleHistory)
class FeedRuleHistoryAdmin(admin.ModelAdmin):
    list_display = ['feed_rule', 'old_status', 'new_status', 'changed_by', 'changed_at']
    list_filter = ['old_status', 'new_status', 'changed_at', 'feed_rule__feed__farm']
    search_fields = ['feed_rule__name', 'reason']
    readonly_fields = ['changed_at']
    date_hierarchy = 'changed_at'


@admin.register(FeedActivityHistory)
class FeedActivityHistoryAdmin(admin.ModelAdmin):
    list_display = ['feed_activity', 'old_status', 'new_status', 'changed_by', 'changed_at']
    list_filter = ['old_status', 'new_status', 'changed_at']
    search_fields = ['feed_activity__animal__tag', 'reason']
    readonly_fields = ['changed_at']
    date_hierarchy = 'changed_at'