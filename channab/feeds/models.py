from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from decimal import Decimal
from dairy.models import Animal
from accounts.models import Farm, CustomUser


class Feed(models.Model):
    """Base feed type (e.g., Wanda25)"""
    
    UNIT_CHOICES = [
        ('kg', 'Kilograms'),
        ('liter', 'Liters'),
        ('lot', 'Lots'),
        ('gram', 'Grams'),
        ('ml', 'Milliliters'),
    ]
    
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, related_name='feeds')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, help_text="Cost per unit")
    unit = models.CharField(max_length=20, choices=UNIT_CHOICES, default='kg')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        unique_together = ['farm', 'name']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.farm.name})"
    
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('feeds:feed_detail', kwargs={'pk': self.pk})


class FeedRule(models.Model):
    """Feed scheduling rules for animals"""
    
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('paused', 'Paused'),
        ('expired', 'Expired'),
    ]
    
    SCHEDULE_TYPE_CHOICES = [
        ('single', 'Single (One-time)'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
        ('custom', 'Custom Interval'),
        ('weekdays', 'Weekdays Only'),
    ]
    
    DAILY_TYPE_CHOICES = [
        ('every_day', 'Every day'),
        ('every_n_days', 'Every N days'),
        ('weekdays_only', 'Weekdays only (Mon-Fri)'),
    ]
    
    WEEKLY_TYPE_CHOICES = [
        ('specific_day', 'Specific day of week'),
        ('multiple_days', 'Multiple days per week'),
        ('every_n_weeks', 'Every N weeks'),
    ]
    
    MONTHLY_TYPE_CHOICES = [
        ('specific_date', 'Specific date each month'),
        ('specific_weekday', 'Specific weekday (e.g., 2nd Monday)'),
        ('every_n_months', 'Every N months'),
    ]
    
    END_TYPE_CHOICES = [
        ('never', 'Never'),
        ('date', 'On specific date'),
        ('occurrences', 'After N occurrences'),
    ]
    
    feed = models.ForeignKey(Feed, on_delete=models.CASCADE, related_name='rules')
    name = models.CharField(max_length=200, help_text="Rule description")
    
    # Target animal criteria
    target_all_animals = models.BooleanField(
        default=False,
        help_text="Apply to all animals in the farm"
    )
    target_animal_categories = models.JSONField(
        default=list,
        blank=True,
        help_text="Target animal categories (multiple selection)"
    )
    target_animal_types = models.JSONField(
        default=list,
        blank=True,
        help_text="Target animal types (multiple selection)"
    )
    
    # Legacy fields for backward compatibility
    target_animal_category = models.CharField(
        max_length=50, 
        choices=Animal.CATEGORY_CHOICES,
        blank=True,
        help_text="Target animal category (legacy)"
    )
    target_animal_type = models.CharField(
        max_length=50,
        choices=Animal.TYPE_CHOICES,
        blank=True,
        help_text="Target animal type (legacy)"
    )
    
    # Optional age criteria
    use_age_filter = models.BooleanField(
        default=False,
        help_text="Enable age-based filtering"
    )
    min_age_months = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Minimum age in months (optional)"
    )
    max_age_months = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum age in months (optional)"
    )
    
    # Formula-based dosage
    FORMULA_TYPE_CHOICES = [
        ('fixed', 'Fixed Amount'),
        ('per_animal', 'Per Animal'),
        ('per_kg_weight', 'Per Kg Body Weight'),
        ('percentage_weight', 'Percentage of Body Weight'),
        ('custom_formula', 'Custom Formula'),
    ]
    
    FORMULA_APPLICATION_CHOICES = [
        ('per_animal', 'Apply formula per individual animal'),
        ('total_batch', 'Calculate total for all selected animals'),
    ]
    
    formula_type = models.CharField(
        max_length=20,
        choices=FORMULA_TYPE_CHOICES,
        default='fixed',
        help_text="Type of dosage formula"
    )
    formula_application = models.CharField(
        max_length=20,
        choices=FORMULA_APPLICATION_CHOICES,
        default='per_animal',
        help_text="How to apply the formula"
    )
    
    # Legacy dosage fields
    dosage_qty = models.DecimalField(
        max_digits=8, 
        decimal_places=2, 
        help_text="Base quantity for formula calculation"
    )
    dosage_unit = models.CharField(max_length=20, choices=Feed.UNIT_CHOICES, default='kg')
    
    # Formula-specific fields
    formula_value = models.DecimalField(
        max_digits=8,
        decimal_places=4,
        default=1.0,
        help_text="Formula multiplier value"
    )
    min_dosage = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Minimum dosage per animal"
    )
    max_dosage = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Maximum dosage per animal"
    )
    custom_formula = models.TextField(
        blank=True,
        help_text="Custom formula (e.g., 'weight * 0.05 + 2')"
    )
    
    # Enhanced Scheduling Options
    schedule_type = models.CharField(
        max_length=20, 
        choices=SCHEDULE_TYPE_CHOICES, 
        default='daily',
        help_text="Type of schedule"
    )
    
    # Legacy field for backward compatibility
    frequency_days = models.PositiveIntegerField(
        default=1,
        help_text="Frequency in days (legacy field - use schedule_type instead)"
    )
    
    # Daily scheduling options
    daily_type = models.CharField(
        max_length=20,
        choices=DAILY_TYPE_CHOICES,
        default='every_day',
        blank=True
    )
    interval_days = models.PositiveIntegerField(default=1, help_text="Interval for every N days")
    
    # Weekly scheduling options
    weekly_type = models.CharField(
        max_length=20,
        choices=WEEKLY_TYPE_CHOICES,
        default='specific_day',
        blank=True
    )
    monday = models.BooleanField(default=False)
    tuesday = models.BooleanField(default=False)
    wednesday = models.BooleanField(default=False)
    thursday = models.BooleanField(default=False)
    friday = models.BooleanField(default=False)
    saturday = models.BooleanField(default=False)
    sunday = models.BooleanField(default=False)
    interval_weeks = models.PositiveIntegerField(default=1, help_text="Interval for every N weeks")
    
    # Monthly scheduling options
    monthly_type = models.CharField(
        max_length=20,
        choices=MONTHLY_TYPE_CHOICES,
        default='specific_date',
        blank=True
    )
    day_of_month = models.PositiveIntegerField(default=1, help_text="Day of month (1-31)")
    week_of_month = models.PositiveIntegerField(default=1, help_text="Week of month (1-5, 5=last)")
    weekday_of_month = models.PositiveIntegerField(default=0, help_text="Day of week (0=Mon, 6=Sun)")
    interval_months = models.PositiveIntegerField(default=1, help_text="Interval for every N months")
    
    # Yearly scheduling options
    interval_years = models.PositiveIntegerField(default=1, help_text="Interval for every N years")
    
    # Custom interval options
    custom_interval_value = models.PositiveIntegerField(default=1, help_text="Custom interval value")
    custom_interval_unit = models.CharField(
        max_length=10,
        choices=[
            ('minutes', 'Minutes'),
            ('hours', 'Hours'),
            ('days', 'Days'),
            ('weeks', 'Weeks'),
            ('months', 'Months'),
        ],
        default='hours',
        blank=True
    )
    
    # Time settings
    delivery_time = models.TimeField(help_text="Scheduled delivery time")
    end_time = models.TimeField(null=True, blank=True, help_text="End time (optional)")
    
    # End conditions
    end_type = models.CharField(
        max_length=20,
        choices=END_TYPE_CHOICES,
        default='date',
        help_text="How should this schedule end"
    )
    max_occurrences = models.PositiveIntegerField(
        null=True, 
        blank=True, 
        help_text="Maximum number of occurrences"
    )
    
    # Validity period
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField()
    
    # Exception dates (JSON field to store dates to skip)
    exception_dates = models.JSONField(default=list, blank=True, help_text="Dates to skip (holidays, etc.)")
    
    # Status and control
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    last_run = models.DateTimeField(null=True, blank=True, help_text="Last scheduler run")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']

    def clean(self):
        # Age validation
        if self.use_age_filter:
            if self.min_age_months is None or self.max_age_months is None:
                raise ValidationError("Both minimum and maximum age must be specified when using age filter")
            if self.min_age_months < 0 or self.max_age_months < 0:
                raise ValidationError("Age values cannot be negative")
            if self.min_age_months > self.max_age_months:
                raise ValidationError(
                    f"Minimum age ({self.min_age_months} months) cannot be greater than "
                    f"maximum age ({self.max_age_months} months)"
                )
        
        # Date validation
        if self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date")
        
        # Target validation
        if not self.target_all_animals:
            has_category_filter = bool(self.target_animal_categories or self.target_animal_category)
            has_type_filter = bool(self.target_animal_types or self.target_animal_type)
            if not has_category_filter and not has_type_filter:
                raise ValidationError(
                    "When not targeting all animals, you must specify at least one category or type filter"
                )

    def __str__(self):
        if self.feed_id:
            try:
                return f"{self.name} - {self.feed.name}"
            except:
                return f"{self.name} - Feed ID {self.feed_id}"
        return f"{self.name}"
    
    def save(self, *args, **kwargs):
        """Override save to ensure proper data handling"""
        # Ensure JSON fields are properly formatted
        if self.target_animal_categories and isinstance(self.target_animal_categories, str):
            try:
                import json
                self.target_animal_categories = json.loads(self.target_animal_categories)
            except (json.JSONDecodeError, ValueError):
                # If it's comma-separated, convert to list
                self.target_animal_categories = [x.strip() for x in self.target_animal_categories.split(',') if x.strip()]
        
        if self.target_animal_types and isinstance(self.target_animal_types, str):
            try:
                import json
                self.target_animal_types = json.loads(self.target_animal_types)
            except (json.JSONDecodeError, ValueError):
                # If it's comma-separated, convert to list
                self.target_animal_types = [x.strip() for x in self.target_animal_types.split(',') if x.strip()]
        
        # Always call clean() to validate before saving
        self.clean()
        
        super().save(*args, **kwargs)
        
        # Clear any cached data related to this rule
        self._invalidate_cache()
    
    def _invalidate_cache(self):
        """Invalidate any cached calculations"""
        # Clear any model-level cache by setting to None
        self._cached_matching_animals = None
        self._cached_dosage_calculation = None
    
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('feeds:feed_rule_detail', kwargs={'pk': self.pk})
    
    def get_matching_animals_debug_info(self):
        """Get detailed debug information about why animals match or don't match"""
        from dateutil.relativedelta import relativedelta
        today = timezone.now().date()
        
        debug_info = {
            'rule_config': {
                'target_all_animals': self.target_all_animals,
                'target_animal_categories': self.target_animal_categories,
                'target_animal_types': self.target_animal_types,
                'use_age_filter': self.use_age_filter,
                'min_age_months': self.min_age_months,
                'max_age_months': self.max_age_months,
            },
            'validation_errors': [],
            'filter_results': [],
            'final_count': 0
        }
        
        # Check for configuration issues
        if self.use_age_filter and self.min_age_months is not None and self.max_age_months is not None:
            if self.min_age_months > self.max_age_months:
                debug_info['validation_errors'].append(
                    f"Invalid age range: min_age ({self.min_age_months}) > max_age ({self.max_age_months})"
                )
        
        if not self.target_all_animals:
            has_category_filter = bool(self.target_animal_categories or self.target_animal_category)
            has_type_filter = bool(self.target_animal_types or self.target_animal_type)
            if not has_category_filter and not has_type_filter:
                debug_info['validation_errors'].append("No target criteria specified (not targeting all animals)")
        
        # Step-by-step filtering
        animals = Animal.objects.filter(farm=self.feed.farm, status='active')
        debug_info['filter_results'].append(f"Initial active animals: {animals.count()}")
        
        if not self.target_all_animals:
            # Category filter
            if self.target_animal_categories:
                before_count = animals.count()
                animals = animals.filter(category__in=self.target_animal_categories)
                debug_info['filter_results'].append(
                    f"After category filter {self.target_animal_categories}: {animals.count()} (was {before_count})"
                )
            elif self.target_animal_category:
                before_count = animals.count()
                animals = animals.filter(category=self.target_animal_category)
                debug_info['filter_results'].append(
                    f"After legacy category filter '{self.target_animal_category}': {animals.count()} (was {before_count})"
                )
            
            # Type filter
            if self.target_animal_types:
                before_count = animals.count()
                animals = animals.filter(animal_type__in=self.target_animal_types)
                debug_info['filter_results'].append(
                    f"After type filter {self.target_animal_types}: {animals.count()} (was {before_count})"
                )
            elif self.target_animal_type:
                before_count = animals.count()
                animals = animals.filter(animal_type=self.target_animal_type)
                debug_info['filter_results'].append(
                    f"After legacy type filter '{self.target_animal_type}': {animals.count()} (was {before_count})"
                )
        
        # Age filter
        if self.use_age_filter and self.min_age_months is not None and self.max_age_months is not None:
            if self.min_age_months <= self.max_age_months:  # Only apply if valid range
                before_count = animals.count()
                max_birth_date = today - relativedelta(months=self.min_age_months)
                min_birth_date = today - relativedelta(months=self.max_age_months)
                animals = animals.filter(dob__gte=min_birth_date, dob__lte=max_birth_date)
                debug_info['filter_results'].append(
                    f"After age filter ({self.min_age_months}-{self.max_age_months} months): {animals.count()} (was {before_count})"
                )
        
        debug_info['final_count'] = animals.count()
        return debug_info

    def get_matching_animals(self, force_refresh=False):
        """Get animals that match this rule's criteria"""
        # Use cached result if available and not forcing refresh
        if hasattr(self, '_cached_matching_animals') and self._cached_matching_animals is not None and not force_refresh:
            return self._cached_matching_animals
            
        from dateutil.relativedelta import relativedelta
        today = timezone.now().date()
        
        # Start with all active animals in the farm
        animals = Animal.objects.filter(
            farm=self.feed.farm,
            status='active'
        )
        
        # If targeting all animals, return all
        if self.target_all_animals:
            if self.use_age_filter and self.min_age_months is not None and self.max_age_months is not None:
                max_birth_date = today - relativedelta(months=self.min_age_months)
                min_birth_date = today - relativedelta(months=self.max_age_months)
                animals = animals.filter(
                    dob__gte=min_birth_date,
                    dob__lte=max_birth_date
                )
            return animals
        
        # Filter by categories (new multi-select)
        if self.target_animal_categories:
            animals = animals.filter(category__in=self.target_animal_categories)
        # Fallback to legacy single category
        elif self.target_animal_category:
            animals = animals.filter(category=self.target_animal_category)
        
        # Filter by types (new multi-select)
        if self.target_animal_types:
            animals = animals.filter(animal_type__in=self.target_animal_types)
        # Fallback to legacy single type
        elif self.target_animal_type:
            animals = animals.filter(animal_type=self.target_animal_type)
        
        # Apply age filter if enabled - but include animals without DOB
        if self.use_age_filter and self.min_age_months is not None and self.max_age_months is not None:
            max_birth_date = today - relativedelta(months=self.min_age_months)
            min_birth_date = today - relativedelta(months=self.max_age_months)
            
            # Include animals that either:
            # 1. Have DOB and fall within age range, OR
            # 2. Don't have DOB (treat as eligible)
            from django.db.models import Q
            animals = animals.filter(
                Q(dob__gte=min_birth_date, dob__lte=max_birth_date) |  # Within age range
                Q(dob__isnull=True)  # OR no birth date
            )
        
        # Cache the result
        self._cached_matching_animals = animals
        return animals
    
    def get_matching_animals_debug_info(self):
        """Debug method to understand why animals might not be matching"""
        from dateutil.relativedelta import relativedelta
        today = timezone.now().date()
        
        debug_info = {
            'rule_id': self.pk,
            'rule_name': self.name,
            'target_all_animals': self.target_all_animals,
            'target_animal_categories': self.target_animal_categories,
            'target_animal_types': self.target_animal_types,
            'use_age_filter': self.use_age_filter,
            'min_age_months': self.min_age_months,
            'max_age_months': self.max_age_months,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'status': self.status,
        }
        
        # Check total animals in farm
        all_animals = Animal.objects.filter(farm=self.feed.farm, status='active')
        debug_info['total_active_animals'] = all_animals.count()
        
        # Step by step filtering
        animals = all_animals
        debug_info['after_farm_filter'] = animals.count()
        
        if not self.target_all_animals:
            # Filter by categories
            if self.target_animal_categories:
                animals = animals.filter(category__in=self.target_animal_categories)
                debug_info['after_category_filter'] = animals.count()
                debug_info['available_categories'] = list(all_animals.values_list('category', flat=True).distinct())
            elif self.target_animal_category:
                animals = animals.filter(category=self.target_animal_category)
                debug_info['after_legacy_category_filter'] = animals.count()
            
            # Filter by types
            if self.target_animal_types:
                animals = animals.filter(animal_type__in=self.target_animal_types)
                debug_info['after_type_filter'] = animals.count()
                debug_info['available_types'] = list(all_animals.values_list('animal_type', flat=True).distinct())
            elif self.target_animal_type:
                animals = animals.filter(animal_type=self.target_animal_type)
                debug_info['after_legacy_type_filter'] = animals.count()
        
        # Age filter
        if self.use_age_filter and self.min_age_months is not None and self.max_age_months is not None:
            max_birth_date = today - relativedelta(months=self.min_age_months)
            min_birth_date = today - relativedelta(months=self.max_age_months)
            
            # Include animals with DOB in range OR without DOB
            from django.db.models import Q
            animals = animals.filter(
                Q(dob__gte=min_birth_date, dob__lte=max_birth_date) |  # Within age range
                Q(dob__isnull=True)  # OR no birth date
            )
            debug_info['age_filter_min_birth'] = min_birth_date
            debug_info['age_filter_max_birth'] = max_birth_date
            debug_info['after_age_filter'] = animals.count()
            debug_info['animals_with_dob'] = all_animals.filter(dob__isnull=False).count()
            debug_info['animals_without_dob'] = all_animals.filter(dob__isnull=True).count()
        
        debug_info['final_matching_count'] = animals.count()
        
        return debug_info

    def calculate_dosage_for_animal(self, animal):
        """Calculate dosage for a specific animal based on formula"""
        if self.formula_type == 'fixed':
            return float(self.dosage_qty)
        
        elif self.formula_type == 'per_animal':
            return float(self.formula_value)
        
        elif self.formula_type == 'per_kg_weight':
            if animal.last_weight:
                dosage = float(animal.last_weight) * float(self.formula_value)
            else:
                dosage = float(self.dosage_qty)  # Fallback to base amount
        
        elif self.formula_type == 'percentage_weight':
            if animal.last_weight:
                dosage = float(animal.last_weight) * (float(self.formula_value) / 100)
            else:
                dosage = float(self.dosage_qty)  # Fallback to base amount
        
        elif self.formula_type == 'custom_formula':
            try:
                # Simple formula evaluation (weight-based)
                if animal.last_weight and self.custom_formula:
                    # Replace 'weight' with actual weight in formula
                    formula = self.custom_formula.replace('weight', str(animal.last_weight))
                    # Basic eval for simple mathematical expressions
                    dosage = eval(formula)
                else:
                    dosage = float(self.dosage_qty)
            except:
                dosage = float(self.dosage_qty)  # Fallback on error
        
        else:
            dosage = float(self.dosage_qty)
        
        # Apply min/max limits
        if self.min_dosage and dosage < float(self.min_dosage):
            dosage = float(self.min_dosage)
        if self.max_dosage and dosage > float(self.max_dosage):
            dosage = float(self.max_dosage)
        
        return dosage

    def get_total_dosage_and_cost(self, force_refresh=False):
        """Calculate total dosage and cost for all matching animals"""
        # Use cached result if available and not forcing refresh
        if hasattr(self, '_cached_dosage_calculation') and self._cached_dosage_calculation is not None and not force_refresh:
            return self._cached_dosage_calculation
            
        animals = self.get_matching_animals(force_refresh=force_refresh)
        total_dosage = 0
        animal_count = animals.count()
        
        if self.formula_application == 'per_animal':
            for animal in animals:
                total_dosage += self.calculate_dosage_for_animal(animal)
        else:  # total_batch
            # Calculate total for all animals as a batch
            if self.formula_type == 'per_kg_weight':
                total_weight = sum(animal.last_weight or 0 for animal in animals)
                total_dosage = total_weight * float(self.formula_value)
            elif self.formula_type == 'percentage_weight':
                total_weight = sum(animal.last_weight or 0 for animal in animals)
                total_dosage = total_weight * (float(self.formula_value) / 100)
            else:
                total_dosage = float(self.dosage_qty) * animal_count
        
        total_cost = total_dosage * float(self.feed.unit_cost)
        
        result = {
            'animal_count': animal_count,
            'total_dosage': round(total_dosage, 2),
            'total_cost': round(total_cost, 2),
            'cost_per_animal': round(total_cost / animal_count, 2) if animal_count > 0 else 0
        }
        
        # Cache the result
        self._cached_dosage_calculation = result
        return result

    def is_due_today(self):
        """Check if this rule should run today based on frequency"""
        if self.status != 'scheduled':
            return False
            
        today = timezone.now().date()
        if today < self.start_date or today > self.end_date:
            return False
            
        # Calculate days since start
        days_since_start = (today - self.start_date).days
        return days_since_start % self.frequency_days == 0
    
    def is_due_on_date(self, check_date):
        """Check if this rule should run on a specific date based on frequency"""
        if self.status != 'scheduled':
            return False
            
        if check_date < self.start_date or check_date > self.end_date:
            return False
            
        # Calculate days since start
        days_since_start = (check_date - self.start_date).days
        return days_since_start % self.frequency_days == 0

    def pause(self, user=None):
        """Pause this feed rule"""
        self.status = 'paused'
        self.save()
        
        # Log the action
        FeedRuleHistory.objects.create(
            feed_rule=self,
            old_status='scheduled',
            new_status='paused',
            changed_by=user,
            reason='Manual pause'
        )

    def resume(self, user=None):
        """Resume this feed rule"""
        if timezone.now().date() > self.end_date:
            raise ValidationError("Cannot resume expired rule. Please extend the end date first.")
            
        self.status = 'scheduled'
        self.save()
        
        # Log the action
        FeedRuleHistory.objects.create(
            feed_rule=self,
            old_status='paused',
            new_status='scheduled',
            changed_by=user,
            reason='Manual resume'
        )


class FeedActivity(models.Model):
    """Individual feed activities for specific animals"""
    
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('accepted', 'Accepted'),
        ('skipped', 'Skipped'),
        ('missed', 'Missed'),
    ]
    
    feed_rule = models.ForeignKey(FeedRule, on_delete=models.CASCADE, related_name='activities')
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, related_name='feed_activities')
    
    # Schedule details
    scheduled_for = models.DateTimeField()
    dosage_qty = models.DecimalField(max_digits=8, decimal_places=2)
    dosage_unit = models.CharField(max_length=20, choices=Feed.UNIT_CHOICES)
    
    # Status tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    accepted_at = models.DateTimeField(null=True, blank=True)
    accepted_by = models.ForeignKey(
        CustomUser, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='accepted_feed_activities'
    )
    
    # Cost calculation
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2)
    total_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Notes and metadata
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['feed_rule', 'animal', 'scheduled_for']
        ordering = ['-scheduled_for']
        indexes = [
            models.Index(fields=['animal', 'scheduled_for']),
            models.Index(fields=['status', 'scheduled_for']),
            models.Index(fields=['feed_rule', 'status']),
        ]

    def save(self, *args, **kwargs):
        # Calculate total cost (handle type conversion)
        if self.dosage_qty is not None and self.unit_cost is not None:
            from decimal import Decimal
            self.total_cost = Decimal(str(self.dosage_qty)) * self.unit_cost
        else:
            self.total_cost = Decimal('0.00')
        super().save(*args, **kwargs)

    def accept(self, user, notes=""):
        """Accept this feed activity"""
        self.status = 'accepted'
        self.accepted_at = timezone.now()
        self.accepted_by = user
        if notes:
            self.notes = notes
        self.save()
        
        # Create cost snapshot
        FeedCostSnapshot.objects.create(
            animal=self.animal,
            feed=self.feed_rule.feed,
            feed_activity=self,
            date=self.accepted_at.date(),
            cost=self.total_cost,
            quantity=self.dosage_qty,
            unit=self.dosage_unit
        )

    def skip(self, user, reason=""):
        """Skip this feed activity"""
        self.status = 'skipped'
        self.notes = reason
        self.save()

    def __str__(self):
        return f"{self.animal.tag} - {self.feed_rule.feed.name} - {self.scheduled_for.date()}"


class FeedCostSnapshot(models.Model):
    """Daily cost snapshots for feed consumption"""
    
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, related_name='feed_costs')
    feed = models.ForeignKey(Feed, on_delete=models.CASCADE, related_name='cost_snapshots')
    feed_activity = models.ForeignKey(
        FeedActivity, 
        on_delete=models.CASCADE, 
        related_name='cost_snapshot',
        null=True,
        blank=True
    )
    
    date = models.DateField()
    cost = models.DecimalField(max_digits=10, decimal_places=2)
    quantity = models.DecimalField(max_digits=8, decimal_places=2)
    unit = models.CharField(max_length=20, choices=Feed.UNIT_CHOICES)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['animal', 'feed', 'date', 'feed_activity']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['animal', 'date']),
            models.Index(fields=['feed', 'date']),
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return f"{self.animal.tag} - {self.feed.name} - {self.date} - ${self.cost}"


class FeedRuleHistory(models.Model):
    """Audit trail for feed rule changes"""
    
    feed_rule = models.ForeignKey(FeedRule, on_delete=models.CASCADE, related_name='history')
    old_status = models.CharField(max_length=20, blank=True)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    changed_at = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True)

    class Meta:
        ordering = ['-changed_at']

    def __str__(self):
        return f"{self.feed_rule.name} - {self.old_status} → {self.new_status}"


class FeedActivityHistory(models.Model):
    """Audit trail for feed activity changes"""
    
    feed_activity = models.ForeignKey(FeedActivity, on_delete=models.CASCADE, related_name='history')
    old_status = models.CharField(max_length=20, blank=True)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True)
    changed_at = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True)

    class Meta:
        ordering = ['-changed_at']

    def __str__(self):
        return f"{self.feed_activity} - {self.old_status} → {self.new_status}"