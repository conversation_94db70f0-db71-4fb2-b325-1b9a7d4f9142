from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods, require_POST
from django.utils import timezone
from datetime import timedelta, date
from decimal import Decimal

from .models import Feed, FeedRule, FeedActivity, FeedCostSnapshot, FeedRuleHistory
from .forms import (
    FeedForm, FeedRuleForm, FeedActivityFilterForm, 
    FeedActivityAcceptForm, FeedActivitySkipForm, FeedRuleFilterForm
)
from dairy.models import Animal


@login_required
def feed_dashboard(request):
    """Main feed dashboard with filtering and overview"""
    farm = request.user.farm
    today = timezone.now().date()
    
    # Get filter parameters
    date_filter = request.GET.get('date_filter', 'today')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # Calculate date range based on filter
    if date_filter == 'today':
        filter_start = today
        filter_end = today
    elif date_filter == 'last_7_days':
        filter_start = today - timedelta(days=7)
        filter_end = today
    elif date_filter == 'next_7_days':
        filter_start = today
        filter_end = today + timedelta(days=7)
    elif date_filter == 'last_month':
        filter_start = today - timedelta(days=30)
        filter_end = today
    elif date_filter == 'next_month':
        filter_start = today
        filter_end = today + timedelta(days=30)
    elif date_filter == 'custom' and start_date and end_date:
        from datetime import datetime
        filter_start = datetime.strptime(start_date, '%Y-%m-%d').date()
        filter_end = datetime.strptime(end_date, '%Y-%m-%d').date()
    else:
        filter_start = today
        filter_end = today
    
    # Get all active rules
    active_rules = FeedRule.objects.filter(
        feed__farm=farm,
        status='scheduled',
        is_active=True
    ).select_related('feed')
    
    # Calculate unique animals receiving feed
    unique_animals = set()
    for rule in active_rules:
        animals = rule.get_matching_animals()
        unique_animals.update(animals.values_list('id', flat=True))
    
    # First, get all scheduled activities based on rules
    all_scheduled_dates = []
    for rule in active_rules:
        if rule.frequency_days and rule.frequency_days > 0:
            # Calculate all scheduled dates for this rule within the filter range
            current_date = max(rule.start_date, filter_start)
            while current_date <= min(rule.end_date, filter_end):
                if rule.is_due_on_date(current_date):
                    all_scheduled_dates.append((current_date, rule))
                current_date += timedelta(days=1)
    
    # Get all existing feed activities in date range
    existing_activities = FeedActivity.objects.filter(
        feed_rule__feed__farm=farm,
        scheduled_for__date__gte=filter_start,
        scheduled_for__date__lte=filter_end
    ).select_related('animal', 'feed_rule__feed', 'accepted_by').order_by('scheduled_for', 'animal__tag')
    
    # Create a comprehensive view including both existing and potential activities
    activities_by_date = {}
    
    # First, add all existing activities
    for activity in existing_activities:
        date_key = activity.scheduled_for.date()
        rule_key = activity.feed_rule.pk
        
        if date_key not in activities_by_date:
            activities_by_date[date_key] = {}
        
        if rule_key not in activities_by_date[date_key]:
            activities_by_date[date_key][rule_key] = {
                'rule': activity.feed_rule,
                'date': date_key,
                'activities': [],
                'total_cost': Decimal('0.00'),
                'expected_animals': 0,
                'status_counts': {
                    'planned': 0,
                    'accepted': 0,
                    'skipped': 0,
                    'missed': 0
                }
            }
        
        activities_by_date[date_key][rule_key]['activities'].append(activity)
        activities_by_date[date_key][rule_key]['total_cost'] += activity.total_cost
        activities_by_date[date_key][rule_key]['status_counts'][activity.status] += 1
    
    # Then, add scheduled but not created activities
    for scheduled_date, rule in all_scheduled_dates:
        date_key = scheduled_date
        rule_key = rule.pk
        
        if date_key not in activities_by_date:
            activities_by_date[date_key] = {}
        
        if rule_key not in activities_by_date[date_key]:
            # This rule should have activities but doesn't
            matching_animals = rule.get_matching_animals()
            expected_cost = rule.get_total_dosage_and_cost()
            
            activities_by_date[date_key][rule_key] = {
                'rule': rule,
                'date': date_key,
                'activities': [],
                'total_cost': Decimal(str(expected_cost['total_cost'])),
                'expected_animals': matching_animals.count(),
                'status_counts': {
                    'planned': 0,
                    'accepted': 0,
                    'skipped': 0,
                    'missed': 0,
                    'not_created': matching_animals.count()
                },
                'not_created': True  # Flag to indicate activities need to be created
            }
    
    # Flatten the nested structure for display
    flattened_activities = []
    for date_key in sorted(activities_by_date.keys()):
        for rule_key in activities_by_date[date_key]:
            flattened_activities.append(activities_by_date[date_key][rule_key])
    
    # Sort by date
    flattened_activities.sort(key=lambda x: x['date'])
    
    # Calculate feed costs for the period (based on actual activities)
    feed_costs = existing_activities.filter(status='accepted').aggregate(
        total_cost=Sum('total_cost')
    )['total_cost'] or Decimal('0.00')
    
    # Projected costs (for planned activities)
    projected_costs = existing_activities.filter(status='planned').aggregate(
        total_cost=Sum('total_cost')
    )['total_cost'] or Decimal('0.00')
    
    # Add expected costs for not-yet-created activities
    for date_data in flattened_activities:
        if date_data.get('not_created'):
            projected_costs += Decimal(str(date_data['total_cost']))
    
    # Paginate activities
    paginator = Paginator(flattened_activities, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Dashboard statistics
    stats = {
        'total_active_rules': active_rules.count(),
        'total_animals_feeding': len(unique_animals),
        'actual_feed_cost': feed_costs,
        'projected_feed_cost': projected_costs,
        'total_feed_cost': feed_costs + projected_costs,
        'pending_today': existing_activities.filter(status='planned', scheduled_for__date=today).count(),
        'completed_today': existing_activities.filter(status='accepted', scheduled_for__date=today).count(),
        'total_activities': existing_activities.count(),
        'completed_activities': existing_activities.filter(status='accepted').count(),
        'pending_activities': existing_activities.filter(status='planned').count(),
        'skipped_activities': existing_activities.filter(status='skipped').count(),
    }
    
    # Add feed consumption by type
    feed_consumption = {}
    for activity in existing_activities.filter(status='accepted'):
        feed_name = activity.feed_rule.feed.name
        if feed_name not in feed_consumption:
            feed_consumption[feed_name] = {
                'quantity': Decimal('0.00'),
                'cost': Decimal('0.00'),
                'unit': activity.dosage_unit
            }
        feed_consumption[feed_name]['quantity'] += activity.dosage_qty
        feed_consumption[feed_name]['cost'] += activity.total_cost
    
    context = {
        'stats': stats,
        'page_obj': page_obj,
        'date_filter': date_filter,
        'filter_start': filter_start,
        'filter_end': filter_end,
        'start_date': start_date,
        'end_date': end_date,
        'feed_consumption': feed_consumption,
        'active_rules': active_rules[:5],  # Show top 5 rules in sidebar
    }
    
    return render(request, 'feeds/dashboard.html', context)


@login_required
def feed_list(request):
    """List all feeds for the farm"""
    farm = request.user.farm
    feeds = Feed.objects.filter(farm=farm, is_active=True).order_by('-created_at')
    
    # Search functionality
    search = request.GET.get('search')
    if search:
        feeds = feeds.filter(
            Q(name__icontains=search) | 
            Q(description__icontains=search)
        )
    
    paginator = Paginator(feeds, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search': search,
    }
    
    return render(request, 'feeds/feed_list.html', context)


@login_required
def feed_create(request):
    """Create a new feed"""
    if request.method == 'POST':
        form = FeedForm(request.POST, farm=request.user.farm)
        if form.is_valid():
            feed = form.save(commit=False)
            feed.farm = request.user.farm
            feed.created_by = request.user
            feed.save()
            messages.success(request, f'Feed "{feed.name}" created successfully!')
            return redirect('feeds:feed_detail', pk=feed.pk)
    else:
        form = FeedForm(farm=request.user.farm)
    
    context = {
        'form': form,
        'title': 'Create New Feed'
    }
    
    return render(request, 'feeds/feed_form.html', context)


@login_required
def feed_detail(request, pk):
    """Feed detail with tabs: Details, Cost, Rules"""
    feed = get_object_or_404(Feed, pk=pk, farm=request.user.farm, is_active=True)
    
    # Get active tab
    active_tab = request.GET.get('tab', 'details')
    
    # Feed rules
    feed_rules = feed.rules.filter(is_active=True).order_by('-created_at')
    
    # Cost analysis
    today = timezone.now().date()
    last_30_days = today - timedelta(days=30)
    
    cost_data = {
        'total_cost': FeedCostSnapshot.objects.filter(
            feed=feed
        ).aggregate(total=Sum('cost'))['total'] or Decimal('0.00'),
        
        'last_30_days_cost': FeedCostSnapshot.objects.filter(
            feed=feed,
            date__gte=last_30_days
        ).aggregate(total=Sum('cost'))['total'] or Decimal('0.00'),
        
        'this_month_cost': FeedCostSnapshot.objects.filter(
            feed=feed,
            date__year=today.year,
            date__month=today.month
        ).aggregate(total=Sum('cost'))['total'] or Decimal('0.00'),
    }
    
    # Recent activities
    recent_activities = FeedActivity.objects.filter(
        feed_rule__feed=feed
    ).select_related('animal', 'accepted_by').order_by('-created_at')[:10]
    
    context = {
        'feed': feed,
        'feed_rules': feed_rules,
        'cost_data': cost_data,
        'recent_activities': recent_activities,
        'active_tab': active_tab,
    }
    
    return render(request, 'feeds/feed_detail.html', context)


@login_required
def feed_edit(request, pk):
    """Edit a feed"""
    feed = get_object_or_404(Feed, pk=pk, farm=request.user.farm, is_active=True)
    
    if request.method == 'POST':
        form = FeedForm(request.POST, instance=feed, farm=request.user.farm)
        if form.is_valid():
            form.save()
            messages.success(request, f'Feed "{feed.name}" updated successfully!')
            return redirect('feeds:feed_detail', pk=feed.pk)
    else:
        form = FeedForm(instance=feed, farm=request.user.farm)
    
    context = {
        'form': form,
        'feed': feed,
        'title': f'Edit {feed.name}'
    }
    
    return render(request, 'feeds/feed_form.html', context)


@login_required
def feed_delete(request, pk):
    """Delete (soft delete) a feed"""
    feed = get_object_or_404(Feed, pk=pk, farm=request.user.farm, is_active=True)
    
    if request.method == 'POST':
        # Check if there are active rules
        active_rules = feed.rules.filter(is_active=True, status='scheduled').count()
        if active_rules > 0:
            messages.error(request, 'Cannot delete feed with active rules. Please pause or delete the rules first.')
            return redirect('feeds:feed_detail', pk=feed.pk)
        
        feed.is_active = False
        feed.save()
        messages.success(request, f'Feed "{feed.name}" deleted successfully!')
        return redirect('feeds:feed_list')
    
    # Count dependencies
    total_rules = feed.rules.filter(is_active=True).count()
    total_activities = FeedActivity.objects.filter(feed_rule__feed=feed).count()
    
    context = {
        'feed': feed,
        'total_rules': total_rules,
        'total_activities': total_activities,
    }
    
    return render(request, 'feeds/feed_delete.html', context)


@login_required
def feed_rule_create(request, feed_pk):
    """Create a new feed rule"""
    feed = get_object_or_404(Feed, pk=feed_pk, farm=request.user.farm, is_active=True)
    
    if request.method == 'POST':
        form = FeedRuleForm(request.POST, feed=feed)
        if form.is_valid():
            rule = form.save(commit=False)
            rule.feed = feed
            rule.created_by = request.user
            rule.save()
            
            # Log the creation
            FeedRuleHistory.objects.create(
                feed_rule=rule,
                old_status='',
                new_status=rule.status,
                changed_by=request.user,
                reason='Rule created'
            )
            
            messages.success(request, f'Feed rule "{rule.name}" created successfully!')
            return redirect('feeds:feed_rule_detail', pk=rule.pk)
        else:
            # Add detailed error messages
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = FeedRuleForm(feed=feed)
    
    context = {
        'form': form,
        'feed': feed,
        'title': f'Create Rule for {feed.name}'
    }
    
    return render(request, 'feeds/feed_rule_form_advanced.html', context)


@login_required
def feed_rule_detail(request, pk):
    """Feed rule detail with activities"""
    rule = get_object_or_404(
        FeedRule, 
        pk=pk, 
        feed__farm=request.user.farm, 
        is_active=True
    )
    
    # Force refresh from database to ensure latest data
    rule.refresh_from_db()
    
    # Get active tab
    active_tab = request.GET.get('tab', 'details')
    
    # Apply activity filters - refresh activities from database
    filter_form = FeedActivityFilterForm(request.GET, farm=request.user.farm)
    # Force refresh activities from database
    activities = FeedActivity.objects.filter(
        feed_rule=rule
    ).select_related('animal', 'accepted_by').order_by('-scheduled_for')
    
    if filter_form.is_valid():
        if filter_form.cleaned_data.get('status'):
            activities = activities.filter(status=filter_form.cleaned_data['status'])
        
        if filter_form.cleaned_data.get('date_from'):
            activities = activities.filter(scheduled_for__date__gte=filter_form.cleaned_data['date_from'])
        
        if filter_form.cleaned_data.get('date_to'):
            activities = activities.filter(scheduled_for__date__lte=filter_form.cleaned_data['date_to'])
        
        if filter_form.cleaned_data.get('animal_tag'):
            activities = activities.filter(animal__tag__icontains=filter_form.cleaned_data['animal_tag'])
    
    # Pagination for activities - 10 per page
    paginator = Paginator(activities, 10)
    page_number = request.GET.get('page')
    activities_page = paginator.get_page(page_number)
    
    # Get matching animals and calculate total dosage/cost - force refresh to ensure latest data
    matching_animals = rule.get_matching_animals(force_refresh=True)
    dosage_calculation = rule.get_total_dosage_and_cost(force_refresh=True)
    
    # Debug: log the current rule state
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Feed Rule {rule.pk} - Categories: {rule.target_animal_categories}, Types: {rule.target_animal_types}")
    logger.info(f"Feed Rule {rule.pk} - Matching animals count: {matching_animals.count()}")
    logger.info(f"Feed Rule {rule.pk} - Dosage calculation: {dosage_calculation}")
    
    # Get debug information for troubleshooting
    debug_info = rule.get_matching_animals_debug_info()
    logger.info(f"Feed Rule {rule.pk} - Debug info: {debug_info}")
    
    # Enhanced Activity statistics
    activity_stats = {
        'total': activities.count(),
        'planned': activities.filter(status='planned').count(),
        'accepted': activities.filter(status='accepted').count(),
        'skipped': activities.filter(status='skipped').count(),
        'missed': activities.filter(status='missed').count(),
    }
    
    # Calculate quantity statistics
    accepted_activities = activities.filter(status='accepted')
    planned_activities = activities.filter(status='planned')
    
    # Total quantities given (from completed activities)
    total_given_quantity = accepted_activities.aggregate(
        total=Sum('dosage_qty')
    )['total'] or Decimal('0.00')
    
    # Total quantities planned (from planned activities) 
    total_planned_quantity = planned_activities.aggregate(
        total=Sum('dosage_qty')
    )['total'] or Decimal('0.00')
    
    # Proper date calculations
    from datetime import date
    today = date.today()
    
    # Ensure we have valid dates
    start_date = rule.start_date if rule.start_date else today
    end_date = rule.end_date if rule.end_date else today
    
    # Calculate total days in the rule period
    total_rule_days = (end_date - start_date).days + 1  # +1 to include both start and end day
    
    # Days since rule started (0 if rule hasn't started yet)
    if start_date <= today:
        days_since_start = (today - start_date).days + 1  # +1 to include today
    else:
        days_since_start = 0
    
    # Days remaining until rule ends
    if end_date > today:
        days_until_end = (end_date - today).days
    else:
        days_until_end = 0
    
    # Calculate total feeds in entire rule period
    total_feeds_in_period = 0
    expected_feeds_so_far = 0
    remaining_feeds = 0
    upcoming_feeds_30_days = 0
    
    if rule.frequency_days and rule.frequency_days > 0:
        # Total feeds from start to end date
        total_feeds_in_period = (total_rule_days // rule.frequency_days)
        
        # Expected feeds from start date until today
        if days_since_start > 0:
            expected_feeds_so_far = (days_since_start // rule.frequency_days)
        
        # Remaining feeds from today until end date
        if days_until_end > 0:
            remaining_feeds = (days_until_end // rule.frequency_days)
        
        # Upcoming feeds in next 30 days (or until end date, whichever is shorter)
        days_to_check = min(30, days_until_end)
        if days_to_check > 0:
            upcoming_feeds_30_days = (days_to_check // rule.frequency_days)
    
    # Calculate quantities and costs based on proper feed counts
    total_quantity_for_period = total_feeds_in_period * dosage_calculation['total_dosage']
    expected_quantity_so_far = expected_feeds_so_far * dosage_calculation['total_dosage']
    remaining_quantity = remaining_feeds * dosage_calculation['total_dosage']
    upcoming_quantity_30_days = upcoming_feeds_30_days * dosage_calculation['total_dosage']
    
    # Cost calculations
    total_cost_for_period = total_feeds_in_period * dosage_calculation['total_cost']
    expected_cost_so_far = expected_feeds_so_far * dosage_calculation['total_cost']
    remaining_cost = remaining_feeds * dosage_calculation['total_cost']
    projected_cost_30_days = upcoming_feeds_30_days * dosage_calculation['total_cost']
    
    # Cost statistics with corrected calculations
    cost_stats = {
        # Actual costs from activities
        'total_cost_spent': accepted_activities.aggregate(
            total=Sum('total_cost')
        )['total'] or Decimal('0.00'),
        
        # Per feed calculations
        'per_feed_cost': dosage_calculation['total_cost'],
        'per_feed_dosage': dosage_calculation['total_dosage'],
        'cost_per_animal': dosage_calculation['cost_per_animal'],
        
        # Quantity tracking (actual from activities)
        'total_given_quantity': total_given_quantity,
        'total_planned_quantity': total_planned_quantity,
        
        # Schedule-based calculations (entire period)
        'total_feeds_in_period': total_feeds_in_period,
        'total_quantity_for_period': total_quantity_for_period,
        'total_cost_for_period': total_cost_for_period,
        
        # Progress tracking
        'expected_feeds_so_far': expected_feeds_so_far,
        'expected_quantity_so_far': expected_quantity_so_far,
        'expected_cost_so_far': expected_cost_so_far,
        
        # Remaining/future tracking
        'remaining_feeds': remaining_feeds,
        'remaining_quantity': remaining_quantity,
        'remaining_cost': remaining_cost,
        
        # Next 30 days projections
        'upcoming_feeds_30_days': upcoming_feeds_30_days,
        'upcoming_quantity_30_days': upcoming_quantity_30_days,
        'projected_cost_30_days': projected_cost_30_days,
        
        # Date tracking
        'total_rule_days': total_rule_days,
        'days_since_start': days_since_start,
        'days_until_end': days_until_end,
        'frequency_days': rule.frequency_days,
        'start_date': start_date,
        'end_date': end_date,
    }
    
    # Get comprehensive feed schedule including past and future
    from datetime import datetime, timedelta
    
    # Get next 30 scheduled feed dates based on rule frequency
    all_feed_schedule = []
    if rule.status in ['scheduled', 'paused'] and rule.frequency_days:
        # Start from rule start date or today, whichever is later
        start_calc_date = max(rule.start_date, today) if rule.start_date else today
        
        # Calculate dates going backwards to include recent feeds
        for i in range(-5, 25):  # 5 past dates + 25 future dates = 30 total
            scheduled_date = start_calc_date + timedelta(days=i * rule.frequency_days)
            
            # Skip if outside rule period
            if scheduled_date < rule.start_date or scheduled_date > rule.end_date:
                continue
                
            # Check if activity already exists for this date
            existing_activities = activities.filter(
                scheduled_for__date=scheduled_date
            ).order_by('-scheduled_for')
            
            # Determine status
            if existing_activities.exists():
                # Get status counts for this date
                status_counts = {}
                for act in existing_activities:
                    status_counts[act.status] = status_counts.get(act.status, 0) + 1
                
                # Determine overall status for this date
                if status_counts.get('accepted', 0) == existing_activities.count():
                    status = 'accepted'  # All completed
                elif status_counts.get('skipped', 0) == existing_activities.count():
                    status = 'skipped'   # All skipped
                elif status_counts.get('planned', 0) > 0:
                    status = 'planned'   # Some still planned
                else:
                    status = 'mixed'     # Mixed statuses
                
                # For template compatibility, use first activity but add activity count
                activity = existing_activities.first()
                activity.total_activities = existing_activities.count()
                activity.status_counts = status_counts
            elif scheduled_date < today:
                status = 'missed'
                activity = None
            elif scheduled_date == today:
                status = 'due_today'
                activity = None
            else:
                status = 'upcoming'
                activity = None
            
            all_feed_schedule.append({
                'date': scheduled_date,
                'status': status,
                'activity': activity,
                'is_past': scheduled_date < today,
                'is_today': scheduled_date == today,
                'is_future': scheduled_date > today,
                'matching_animals_count': dosage_calculation['animal_count'],
                'expected_dosage': dosage_calculation['total_dosage'],
                'expected_cost': dosage_calculation['total_cost'],
            })
    
    # Sort by date and limit to reasonable number for display
    all_feed_schedule.sort(key=lambda x: x['date'])
    
    # Paginate the schedule
    schedule_paginator = Paginator(all_feed_schedule, 10)
    schedule_page_number = request.GET.get('schedule_page', 1)
    schedule_page = schedule_paginator.get_page(schedule_page_number)
    
    context = {
        'rule': rule,
        'activities_page': activities_page,
        'filter_form': filter_form,
        'cost_stats': cost_stats,
        'activity_stats': activity_stats,
        'matching_animals': matching_animals,
        'active_tab': active_tab,
        'dosage_calculation': dosage_calculation,
        'schedule_page': schedule_page,
        'all_feed_schedule': all_feed_schedule,  # For debugging
        'debug_info': debug_info,  # Debugging information
    }
    
    return render(request, 'feeds/feed_rule_detail.html', context)


@login_required
def feed_rule_edit(request, pk):
    """Edit a feed rule"""
    rule = get_object_or_404(
        FeedRule, 
        pk=pk, 
        feed__farm=request.user.farm, 
        is_active=True
    )
    
    # Force refresh from database to ensure we have latest data
    rule.refresh_from_db()
    
    # Log current state for debugging
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Editing Feed Rule {rule.pk} - Categories: {rule.target_animal_categories}, Types: {rule.target_animal_types}")
    
    if request.method == 'POST':
        # Log the POST data for debugging
        logger.info(f"POST data - categories: {request.POST.get('target_animal_categories')}, types: {request.POST.get('target_animal_types')}")
        
        form = FeedRuleForm(request.POST, instance=rule, feed=rule.feed)
        if form.is_valid():
            updated_rule = form.save()
            
            # Force refresh of the rule to ensure latest data
            updated_rule.refresh_from_db()
            
            
            # Log the saved data
            logger.info(f"Saved Feed Rule {updated_rule.pk} - Categories: {updated_rule.target_animal_categories}, Types: {updated_rule.target_animal_types}")
            
            # Log the update
            FeedRuleHistory.objects.create(
                feed_rule=updated_rule,
                old_status=rule.status,
                new_status=updated_rule.status,
                changed_by=request.user,
                reason=f'Rule updated via edit form'
            )
            
            # Cache invalidation is handled by the model's save() method
            
            messages.success(request, f'Feed rule "{updated_rule.name}" updated successfully!')
            return redirect('feeds:feed_rule_detail', pk=updated_rule.pk)
        else:
            # Add detailed error messages
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = FeedRuleForm(instance=rule, feed=rule.feed)
    
    context = {
        'form': form,
        'feed': rule.feed,
        'rule': rule,
        'title': f'Edit {rule.name}'
    }
    
    return render(request, 'feeds/feed_rule_form_advanced.html', context)


@login_required
def feed_rule_toggle_status(request, pk):
    """Pause or resume a feed rule"""
    rule = get_object_or_404(
        FeedRule, 
        pk=pk, 
        feed__farm=request.user.farm, 
        is_active=True
    )
    
    if request.method == 'POST':
        try:
            if rule.status == 'scheduled':
                rule.pause(request.user)
                messages.success(request, f'Feed rule "{rule.name}" paused successfully!')
            elif rule.status == 'paused':
                rule.resume(request.user)
                messages.success(request, f'Feed rule "{rule.name}" resumed successfully!')
            else:
                messages.error(request, f'Cannot toggle status for {rule.status} rule.')
        except Exception as e:
            messages.error(request, str(e))
    
    return redirect('feeds:feed_rule_detail', pk=rule.pk)


@login_required
def feed_activity_accept(request, pk):
    """Accept a feed activity"""
    activity = get_object_or_404(
        FeedActivity, 
        pk=pk, 
        feed_rule__feed__farm=request.user.farm,
        status='planned'
    )
    
    if request.method == 'POST':
        form = FeedActivityAcceptForm(request.POST)
        if form.is_valid():
            notes = form.cleaned_data.get('notes', '')
            activity.accept(request.user, notes)
            messages.success(request, f'Feed activity for {activity.animal.tag} accepted!')
            
            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': True, 'message': 'Feed accepted successfully!'})
    
    # Redirect back to the referring page or rule detail
    next_url = request.GET.get('next') or request.META.get('HTTP_REFERER')
    if not next_url or 'feed_activity_accept' in next_url:
        next_url = f"{activity.feed_rule.get_absolute_url()}?tab=activities"
    
    return redirect(next_url)


@login_required
def feed_activity_skip(request, pk):
    """Skip a feed activity"""
    activity = get_object_or_404(
        FeedActivity, 
        pk=pk, 
        feed_rule__feed__farm=request.user.farm,
        status='planned'
    )
    
    if request.method == 'POST':
        form = FeedActivitySkipForm(request.POST)
        if form.is_valid():
            reason = form.cleaned_data.get('reason', '')
            activity.skip(request.user, reason)
            messages.success(request, f'Feed activity for {activity.animal.tag} skipped!')
            
            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': True, 'message': 'Feed skipped successfully!'})
    
    # Redirect back to the referring page or rule detail
    next_url = request.GET.get('next') or request.META.get('HTTP_REFERER')
    if not next_url or 'feed_activity_skip' in next_url:
        next_url = f"{activity.feed_rule.get_absolute_url()}?tab=activities"
    
    return redirect(next_url)


@login_required
def animal_feeds(request, animal_pk):
    """View feeds for a specific animal"""
    animal = get_object_or_404(Animal, pk=animal_pk, farm=request.user.farm)
    
    # Get all feed activities for this animal
    activities = FeedActivity.objects.filter(
        animal=animal
    ).select_related(
        'feed_rule__feed', 'accepted_by'
    ).order_by('-scheduled_for')
    
    # Apply filters
    filter_form = FeedActivityFilterForm(request.GET, farm=request.user.farm)
    if filter_form.is_valid():
        if filter_form.cleaned_data.get('status'):
            activities = activities.filter(status=filter_form.cleaned_data['status'])
        
        if filter_form.cleaned_data.get('date_from'):
            activities = activities.filter(scheduled_for__date__gte=filter_form.cleaned_data['date_from'])
        
        if filter_form.cleaned_data.get('date_to'):
            activities = activities.filter(scheduled_for__date__lte=filter_form.cleaned_data['date_to'])
    
    # Pagination
    paginator = Paginator(activities, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Cost summary
    total_cost = activities.filter(status='accepted').aggregate(
        total=Sum('total_cost')
    )['total'] or Decimal('0.00')
    
    # Monthly cost breakdown
    monthly_costs = FeedCostSnapshot.objects.filter(
        animal=animal
    ).extra(
        select={'month': "DATE_FORMAT(date, '%%Y-%%m')"}
    ).values('month').annotate(
        total_cost=Sum('cost')
    ).order_by('-month')[:6]
    
    context = {
        'animal': animal,
        'page_obj': page_obj,
        'filter_form': filter_form,
        'total_cost': total_cost,
        'monthly_costs': monthly_costs,
    }
    
    return render(request, 'feeds/animal_feeds.html', context)


@login_required
@require_POST
def create_feed_activities(request, rule_pk, date):
    """Create feed activities for a specific rule and date"""
    from datetime import datetime
    
    # Get the rule and ensure it belongs to the user's farm
    rule = get_object_or_404(FeedRule, pk=rule_pk, feed__farm=request.user.farm)
    
    # Parse the date
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        messages.error(request, 'Invalid date format')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Check if activities already exist for this date
    existing_activities = FeedActivity.objects.filter(
        feed_rule=rule,
        scheduled_for__date=target_date
    )
    
    if existing_activities.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Activities already exist for this date'})
        messages.warning(request, f'Feed activities already exist for {target_date}')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Get matching animals
    matching_animals = rule.get_matching_animals()
    
    if not matching_animals.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'No matching animals found'})
        messages.error(request, 'No animals match the rule criteria')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Create activities for each matching animal
    activities_created = 0
    delivery_datetime = timezone.make_aware(
        datetime.combine(target_date, rule.delivery_time)
    )
    
    for animal in matching_animals:
        # Calculate proper dosage for this animal
        dosage_qty = rule.calculate_dosage_for_animal(animal)
        
        activity = FeedActivity.objects.create(
            feed_rule=rule,
            animal=animal,
            scheduled_for=delivery_datetime,
            dosage_qty=dosage_qty,
            dosage_unit=rule.dosage_unit,
            unit_cost=rule.feed.unit_cost,
            status='planned'
        )
        activities_created += 1
    
    # Success response
    if request.headers.get('Content-Type') == 'application/json':
        return JsonResponse({
            'success': True, 
            'message': f'Created {activities_created} feed activities for {target_date}',
            'activities_created': activities_created
        })
    
    messages.success(request, f'Created {activities_created} feed activities for {target_date}')
    return redirect('feeds:feed_rule_detail', pk=rule_pk)


@login_required
@require_POST
def complete_feeding(request, rule_pk, date):
    """Create and immediately complete feed activities for a specific rule and date"""
    from datetime import datetime
    
    # Get the rule and ensure it belongs to the user's farm
    rule = get_object_or_404(FeedRule, pk=rule_pk, feed__farm=request.user.farm)
    
    # Parse the date
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        messages.error(request, 'Invalid date format')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Check if activities already exist for this date
    existing_activities = FeedActivity.objects.filter(
        feed_rule=rule,
        scheduled_for__date=target_date
    )
    
    if existing_activities.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Activities already exist for this date'})
        messages.warning(request, f'Feed activities already exist for {target_date}')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Get matching animals
    matching_animals = rule.get_matching_animals()
    
    if not matching_animals.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'No matching animals found'})
        messages.error(request, 'No animals match the rule criteria')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Create and complete activities for each matching animal
    activities_completed = 0
    delivery_datetime = timezone.make_aware(
        datetime.combine(target_date, rule.delivery_time)
    )
    
    for animal in matching_animals:
        # Calculate proper dosage for this animal
        dosage_qty = rule.calculate_dosage_for_animal(animal)
        
        # Create activity
        activity = FeedActivity.objects.create(
            feed_rule=rule,
            animal=animal,
            scheduled_for=delivery_datetime,
            dosage_qty=dosage_qty,
            dosage_unit=rule.dosage_unit,
            unit_cost=rule.feed.unit_cost,
            status='planned'
        )
        
        # Immediately complete it
        activity.accept(request.user, notes=f"Completed via direct action on {target_date}")
        activities_completed += 1
    
    # Success response
    if request.headers.get('Content-Type') == 'application/json':
        return JsonResponse({
            'success': True, 
            'message': f'Completed feeding for {activities_completed} animals on {target_date}',
            'activities_completed': activities_completed
        })
    
    messages.success(request, f'Completed feeding for {activities_completed} animals on {target_date}')
    return redirect('feeds:feed_rule_detail', pk=rule_pk)


@login_required
@require_POST
def skip_feeding(request, rule_pk, date):
    """Create and immediately skip feed activities for a specific rule and date"""
    from datetime import datetime
    
    # Get the rule and ensure it belongs to the user's farm
    rule = get_object_or_404(FeedRule, pk=rule_pk, feed__farm=request.user.farm)
    
    # Parse the date
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        messages.error(request, 'Invalid date format')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Check if activities already exist for this date
    existing_activities = FeedActivity.objects.filter(
        feed_rule=rule,
        scheduled_for__date=target_date
    )
    
    if existing_activities.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Activities already exist for this date'})
        messages.warning(request, f'Feed activities already exist for {target_date}')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Get matching animals
    matching_animals = rule.get_matching_animals()
    
    if not matching_animals.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'No matching animals found'})
        messages.error(request, 'No animals match the rule criteria')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Create and skip activities for each matching animal
    activities_skipped = 0
    delivery_datetime = timezone.make_aware(
        datetime.combine(target_date, rule.delivery_time)
    )
    
    for animal in matching_animals:
        # Calculate proper dosage for this animal
        dosage_qty = rule.calculate_dosage_for_animal(animal)
        
        # Create activity
        activity = FeedActivity.objects.create(
            feed_rule=rule,
            animal=animal,
            scheduled_for=delivery_datetime,
            dosage_qty=dosage_qty,
            dosage_unit=rule.dosage_unit,
            unit_cost=rule.feed.unit_cost,
            status='planned'
        )
        
        # Immediately skip it
        activity.skip(request.user, reason=f"Skipped via direct action on {target_date}")
        activities_skipped += 1
    
    # Success response
    if request.headers.get('Content-Type') == 'application/json':
        return JsonResponse({
            'success': True, 
            'message': f'Skipped feeding for {activities_skipped} animals on {target_date}',
            'activities_skipped': activities_skipped
        })
    
    messages.success(request, f'Skipped feeding for {activities_skipped} animals on {target_date}')
    return redirect('feeds:feed_rule_detail', pk=rule_pk)


@login_required
@require_POST
def complete_all_for_date(request, rule_pk, date):
    """Complete all planned activities for a specific rule and date"""
    from datetime import datetime
    
    # Get the rule and ensure it belongs to the user's farm
    rule = get_object_or_404(FeedRule, pk=rule_pk, feed__farm=request.user.farm)
    
    # Parse the date
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        messages.error(request, 'Invalid date format')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Get all planned activities for this date
    planned_activities = FeedActivity.objects.filter(
        feed_rule=rule,
        scheduled_for__date=target_date,
        status='planned'
    )
    
    if not planned_activities.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'No planned activities found for this date'})
        messages.warning(request, f'No planned activities found for {target_date}')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Complete all planned activities
    activities_completed = 0
    for activity in planned_activities:
        activity.accept(request.user, notes=f"Bulk completed on {target_date}")
        activities_completed += 1
    
    # Success response
    if request.headers.get('Content-Type') == 'application/json':
        return JsonResponse({
            'success': True, 
            'message': f'Completed {activities_completed} activities for {target_date}',
            'activities_completed': activities_completed
        })
    
    messages.success(request, f'Completed {activities_completed} activities for {target_date}')
    return redirect('feeds:feed_rule_detail', pk=rule_pk)


@login_required
@require_POST
def skip_all_for_date(request, rule_pk, date):
    """Skip all planned activities for a specific rule and date"""
    from datetime import datetime
    
    # Get the rule and ensure it belongs to the user's farm
    rule = get_object_or_404(FeedRule, pk=rule_pk, feed__farm=request.user.farm)
    
    # Parse the date
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        messages.error(request, 'Invalid date format')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Get all planned activities for this date
    planned_activities = FeedActivity.objects.filter(
        feed_rule=rule,
        scheduled_for__date=target_date,
        status='planned'
    )
    
    if not planned_activities.exists():
        if request.headers.get('Content-Type') == 'application/json':
            return JsonResponse({'success': False, 'error': 'No planned activities found for this date'})
        messages.warning(request, f'No planned activities found for {target_date}')
        return redirect('feeds:feed_rule_detail', pk=rule_pk)
    
    # Skip all planned activities
    activities_skipped = 0
    for activity in planned_activities:
        activity.skip(request.user, reason=f"Bulk skipped on {target_date}")
        activities_skipped += 1
    
    # Success response
    if request.headers.get('Content-Type') == 'application/json':
        return JsonResponse({
            'success': True, 
            'message': f'Skipped {activities_skipped} activities for {target_date}',
            'activities_skipped': activities_skipped
        })
    
    messages.success(request, f'Skipped {activities_skipped} activities for {target_date}')
    return redirect('feeds:feed_rule_detail', pk=rule_pk)


@login_required
@require_http_methods(["POST"])
def get_animal_count_preview(request):
    """AJAX endpoint to get animal count and cost preview"""
    import json
    
    try:
        data = json.loads(request.body)
        farm = request.user.farm
        
        # Get target criteria
        target_all_animals = data.get('target_all_animals', False)
        target_categories = data.get('target_animal_categories', [])
        target_types = data.get('target_animal_types', [])
        use_age_filter = data.get('use_age_filter', False)
        min_age_months = data.get('min_age_months')
        max_age_months = data.get('max_age_months')
        
        # Get formula data
        formula_type = data.get('formula_type', 'fixed')
        formula_application = data.get('formula_application', 'per_animal')
        dosage_qty = float(data.get('dosage_qty', 0))
        formula_value = float(data.get('formula_value', 1))
        unit_cost = float(data.get('unit_cost', 0))
        
        # Start with all active animals
        animals = Animal.objects.filter(farm=farm, status='active')
        
        # Apply filters
        if not target_all_animals:
            if target_categories:
                animals = animals.filter(category__in=target_categories)
            if target_types:
                animals = animals.filter(animal_type__in=target_types)
        
        # Apply age filter if enabled
        if use_age_filter and min_age_months is not None and max_age_months is not None:
            from dateutil.relativedelta import relativedelta
            today = timezone.now().date()
            max_birth_date = today - relativedelta(months=min_age_months)
            min_birth_date = today - relativedelta(months=max_age_months)
            animals = animals.filter(dob__gte=min_birth_date, dob__lte=max_birth_date)
        
        animal_count = animals.count()
        
        # Calculate dosage and cost
        total_dosage = 0
        if formula_application == 'per_animal':
            for animal in animals:
                if formula_type == 'fixed':
                    total_dosage += dosage_qty
                elif formula_type == 'per_animal':
                    total_dosage += formula_value
                elif formula_type == 'per_kg_weight':
                    weight = animal.last_weight or 400  # Default weight
                    total_dosage += weight * formula_value
                elif formula_type == 'percentage_weight':
                    weight = animal.last_weight or 400  # Default weight
                    total_dosage += weight * (formula_value / 100)
                else:
                    total_dosage += dosage_qty
        else:  # total_batch
            if formula_type == 'per_kg_weight':
                total_weight = sum(animal.last_weight or 400 for animal in animals)
                total_dosage = total_weight * formula_value
            elif formula_type == 'percentage_weight':
                total_weight = sum(animal.last_weight or 400 for animal in animals)
                total_dosage = total_weight * (formula_value / 100)
            else:
                total_dosage = dosage_qty * animal_count
        
        total_cost = total_dosage * unit_cost
        cost_per_animal = total_cost / animal_count if animal_count > 0 else 0
        
        return JsonResponse({
            'success': True,
            'animal_count': animal_count,
            'total_dosage': round(total_dosage, 2),
            'total_cost': round(total_cost, 2),
            'cost_per_animal': round(cost_per_animal, 2)
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })