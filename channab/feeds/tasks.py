"""
Feed scheduling tasks and utilities.
This module handles the automated creation of feed activities based on feed rules.
"""

from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta
from .models import FeedRule, FeedActivity, Feed
import logging

logger = logging.getLogger(__name__)


def run_feed_scheduler():
    """
    Main scheduler function that creates feed activities based on active rules.
    This should be run hourly via cron job or task queue.
    """
    logger.info("Starting feed scheduler run...")
    
    current_time = timezone.now()
    today = current_time.date()
    
    # Get all active feed rules that should be scheduled
    active_rules = FeedRule.objects.filter(
        status='scheduled',
        is_active=True,
        start_date__lte=today,
        end_date__gte=today
    ).select_related('feed')
    
    total_rules_processed = 0
    total_activities_created = 0
    
    for rule in active_rules:
        try:
            created_count = process_feed_rule(rule, current_time)
            total_activities_created += created_count
            total_rules_processed += 1
            
            # Update last run time
            rule.last_run = current_time
            rule.save(update_fields=['last_run'])
            
        except Exception as e:
            logger.error(f"Error processing feed rule {rule.id}: {str(e)}")
            continue
    
    logger.info(
        f"Feed scheduler completed. Processed {total_rules_processed} rules, "
        f"created {total_activities_created} activities."
    )
    
    return {
        'rules_processed': total_rules_processed,
        'activities_created': total_activities_created
    }


def process_feed_rule(rule, current_time):
    """
    Process a single feed rule and create activities if needed.
    
    Args:
        rule (FeedRule): The feed rule to process
        current_time (datetime): Current time for scheduling
    
    Returns:
        int: Number of activities created
    """
    today = current_time.date()
    
    # Check if this rule should run today based on frequency
    if not rule.is_due_today():
        return 0
    
    # Get matching animals for this rule
    matching_animals = rule.get_matching_animals()
    
    if not matching_animals.exists():
        logger.debug(f"No animals match criteria for rule {rule.id}")
        return 0
    
    # Create scheduled datetime for today
    scheduled_datetime = timezone.make_aware(
        datetime.combine(today, rule.delivery_time)
    )
    
    activities_created = 0
    
    with transaction.atomic():
        for animal in matching_animals:
            # Check if activity already exists for this animal and date
            existing_activity = FeedActivity.objects.filter(
                feed_rule=rule,
                animal=animal,
                scheduled_for__date=today
            ).exists()
            
            if existing_activity:
                continue
            
            # Create new feed activity
            try:
                activity = FeedActivity.objects.create(
                    feed_rule=rule,
                    animal=animal,
                    scheduled_for=scheduled_datetime,
                    dosage_qty=rule.dosage_qty,
                    dosage_unit=rule.dosage_unit,
                    unit_cost=rule.feed.unit_cost,
                    status='planned'
                )
                activities_created += 1
                
                logger.debug(
                    f"Created feed activity for animal {animal.tag} "
                    f"with rule {rule.name}"
                )
                
            except Exception as e:
                logger.error(
                    f"Error creating activity for animal {animal.id} "
                    f"and rule {rule.id}: {str(e)}"
                )
                continue
    
    if activities_created > 0:
        logger.info(
            f"Created {activities_created} activities for rule {rule.name}"
        )
    
    return activities_created


def mark_missed_activities():
    """
    Mark planned activities as missed if they're past their deadline.
    This should be run daily to clean up overdue activities.
    """
    logger.info("Checking for missed feed activities...")
    
    # Define deadline (e.g., 4 hours after scheduled time)
    deadline_hours = 4
    cutoff_time = timezone.now() - timedelta(hours=deadline_hours)
    
    # Find activities that are planned but past deadline
    overdue_activities = FeedActivity.objects.filter(
        status='planned',
        scheduled_for__lt=cutoff_time
    )
    
    missed_count = overdue_activities.count()
    
    if missed_count > 0:
        # Mark as missed
        overdue_activities.update(status='missed')
        logger.info(f"Marked {missed_count} activities as missed")
    else:
        logger.info("No activities to mark as missed")
    
    return missed_count


def cleanup_expired_rules():
    """
    Mark expired feed rules and handle cleanup.
    This should be run daily.
    """
    logger.info("Checking for expired feed rules...")
    
    today = timezone.now().date()
    
    # Find rules that have passed their end date
    expired_rules = FeedRule.objects.filter(
        status__in=['scheduled', 'paused'],
        end_date__lt=today,
        is_active=True
    )
    
    expired_count = expired_rules.count()
    
    if expired_count > 0:
        # Mark as expired
        expired_rules.update(status='expired')
        logger.info(f"Marked {expired_count} rules as expired")
        
        # Optionally, cancel planned activities for expired rules
        planned_activities = FeedActivity.objects.filter(
            feed_rule__in=expired_rules,
            status='planned'
        )
        
        cancelled_activities = planned_activities.count()
        if cancelled_activities > 0:
            planned_activities.update(status='missed')
            logger.info(f"Cancelled {cancelled_activities} planned activities from expired rules")
    else:
        logger.info("No rules to mark as expired")
    
    return expired_count


def generate_feed_schedule_preview(rule, days=7):
    """
    Generate a preview of when feeds would be scheduled for a rule.
    
    Args:
        rule (FeedRule): The feed rule to preview
        days (int): Number of days to preview
    
    Returns:
        list: List of dictionaries with scheduling information
    """
    preview = []
    today = timezone.now().date()
    
    # Get matching animals
    matching_animals = rule.get_matching_animals()
    animal_count = matching_animals.count()
    
    for day_offset in range(days):
        check_date = today + timedelta(days=day_offset)
        
        # Skip if date is outside rule validity
        if check_date < rule.start_date or check_date > rule.end_date:
            continue
        
        # Check if this date aligns with frequency
        days_since_start = (check_date - rule.start_date).days
        if days_since_start % rule.frequency_days == 0:
            # Calculate scheduled datetime
            scheduled_datetime = timezone.make_aware(
                datetime.combine(check_date, rule.delivery_time)
            )
            
            # Calculate cost for this date
            total_cost = animal_count * rule.dosage_qty * rule.feed.unit_cost
            
            preview.append({
                'date': check_date,
                'scheduled_time': scheduled_datetime,
                'animal_count': animal_count,
                'total_dosage': animal_count * rule.dosage_qty,
                'total_cost': total_cost,
                'status': 'future' if check_date > today else 'past'
            })
    
    return preview


def get_feed_dashboard_stats(farm):
    """
    Get dashboard statistics for the feed management system.
    
    Args:
        farm (Farm): The farm to get stats for
    
    Returns:
        dict: Dashboard statistics
    """
    today = timezone.now().date()
    
    # Get basic counts
    total_feeds = Feed.objects.filter(farm=farm, is_active=True).count()
    total_rules = FeedRule.objects.filter(
        feed__farm=farm, 
        is_active=True
    ).count()
    
    active_rules = FeedRule.objects.filter(
        feed__farm=farm,
        status='scheduled',
        is_active=True
    ).count()
    
    # Today's activities
    today_activities = FeedActivity.objects.filter(
        feed_rule__feed__farm=farm,
        scheduled_for__date=today
    )
    
    pending_today = today_activities.filter(status='planned').count()
    completed_today = today_activities.filter(status='accepted').count()
    missed_today = today_activities.filter(status='missed').count()
    
    # This week's stats
    week_start = today - timedelta(days=today.weekday())
    week_activities = FeedActivity.objects.filter(
        feed_rule__feed__farm=farm,
        scheduled_for__date__gte=week_start,
        scheduled_for__date__lte=today
    )
    
    week_completed = week_activities.filter(status='accepted').count()
    week_total = week_activities.count()
    completion_rate = (week_completed / week_total * 100) if week_total > 0 else 0
    
    return {
        'total_feeds': total_feeds,
        'total_rules': total_rules,
        'active_rules': active_rules,
        'pending_today': pending_today,
        'completed_today': completed_today,
        'missed_today': missed_today,
        'week_completion_rate': round(completion_rate, 1),
        'total_animals_fed_today': today_activities.filter(
            status='accepted'
        ).values('animal').distinct().count()
    }