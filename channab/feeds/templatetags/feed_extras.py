from django import template
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from feeds.models import FeedRule

register = template.Library()


@register.filter
def get_matching_feed_rules(animal):
    """Get feed rules that match this animal's criteria"""
    today = timezone.now().date()
    
    # Calculate animal's age in months
    if not animal.dob:
        return FeedRule.objects.none()
    
    age_months = relativedelta(today, animal.dob).months + (relativedelta(today, animal.dob).years * 12)
    
    # Find matching rules
    return FeedRule.objects.filter(
        feed__farm=animal.farm,
        target_animal_category=animal.category,
        target_animal_type=animal.animal_type,
        min_age_months__lte=age_months,
        max_age_months__gte=age_months,
        status__in=['scheduled', 'paused'],
        is_active=True,
        start_date__lte=today,
        end_date__gte=today
    ).select_related('feed')


@register.filter
def aggregate_sum(queryset, field_name):
    """Sum a field from a queryset"""
    try:
        from django.db.models import Sum
        result = queryset.aggregate(total=Sum(field_name))['total']
        return result if result is not None else 0
    except:
        return 0


@register.filter
def filter_status(queryset, status):
    """Filter queryset by status"""
    try:
        if ',' in status:
            # Multiple statuses
            statuses = [s.strip() for s in status.split(',')]
            return queryset.filter(status__in=statuses)
        else:
            return queryset.filter(status=status)
    except:
        return queryset


@register.simple_tag
def get_animal_feed_stats(animal):
    """Get comprehensive feed statistics for an animal"""
    from django.db import models
    activities = animal.feed_activities.all()
    
    return {
        'total_cost': activities.filter(status='accepted').aggregate(
            total=models.Sum('total_cost')
        )['total'] or 0,
        'feeds_given': activities.filter(status='accepted').count(),
        'pending_feeds': activities.filter(status='planned').count(),
        'missed_feeds': activities.filter(status__in=['missed', 'skipped']).count(),
    }


@register.filter
def div(value, arg):
    """Divide value by arg"""
    try:
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0


@register.filter
def mul(value, arg):
    """Multiply value by arg"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def percentage(value, total):
    """Calculate percentage of value from total"""
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0