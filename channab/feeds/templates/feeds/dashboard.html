{% extends 'main/base.html' %}
{% load static %}
{% load humanize %}

{% block content %}
<style>
  .stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
  }
  
  .stats-card:hover {
    transform: translateY(-5px);
  }
  
  .stats-card .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }
  
  .stats-card.primary .stats-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .stats-card.success .stats-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  .stats-card.warning .stats-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  .stats-card.info .stats-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
  
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
  }
  
  .activity-table {
    font-size: 14px;
  }
  
  .activity-table th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 12px;
    padding: 15px;
  }
  
  .activity-table td {
    padding: 12px 15px;
    vertical-align: middle;
  }
  
  .btn-action {
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 12px;
    margin: 2px;
  }
  
  .consumption-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
  }
  
  .filter-dropdown {
    min-width: 200px;
  }
  
  .filter-dropdown .dropdown-item {
    padding: 10px 20px;
  }
  
  .filter-dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
  }
  
  .filter-dropdown .dropdown-item.active {
    background-color: #007bff;
    color: white;
  }
  
  .rule-badge {
    background: #e9ecef;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    color: #495057;
    transition: all 0.3s ease;
  }
  
  .rule-badge:hover {
    background: #d1d5db;
    text-decoration: none;
  }
  
  a.text-decoration-none:hover strong {
    color: #007bff !important;
  }
  
  .right-sidebar-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .quick-actions-card {
    flex-shrink: 0;
  }
  
  .sidebar-scrollable {
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 400px);
  }
</style>

<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header with Filter -->
        <div class="page-title">
          <div class="row">
            <div class="col-md-6">
              <h3>Feed Management Dashboard</h3>
            </div>
            <div class="col-md-6">
              <div class="d-flex justify-content-end align-items-center">
                <ol class="breadcrumb me-3 mb-0">
                  <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                  <li class="breadcrumb-item active">Feed Management</li>
                </ol>
                
                <!-- Date Filter Dropdown -->
                <div class="dropdown">
                  <button class="btn btn-outline-primary dropdown-toggle filter-dropdown" type="button" id="dateFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-calendar-alt me-2"></i>
                    {% if date_filter == 'today' %}Today
                    {% elif date_filter == 'last_7_days' %}Last 7 Days
                    {% elif date_filter == 'next_7_days' %}Next 7 Days
                    {% elif date_filter == 'last_month' %}Last Month
                    {% elif date_filter == 'next_month' %}Next Month
                    {% elif date_filter == 'custom' %}Custom Dates
                    {% endif %}
                  </button>
                  <ul class="dropdown-menu filter-dropdown" aria-labelledby="dateFilterDropdown">
                    <li><a class="dropdown-item {% if date_filter == 'today' %}active{% endif %}" href="?date_filter=today">Today</a></li>
                    <li><a class="dropdown-item {% if date_filter == 'last_7_days' %}active{% endif %}" href="?date_filter=last_7_days">Last 7 Days</a></li>
                    <li><a class="dropdown-item {% if date_filter == 'next_7_days' %}active{% endif %}" href="?date_filter=next_7_days">Next 7 Days</a></li>
                    <li><a class="dropdown-item {% if date_filter == 'last_month' %}active{% endif %}" href="?date_filter=last_month">Last Month</a></li>
                    <li><a class="dropdown-item {% if date_filter == 'next_month' %}active{% endif %}" href="?date_filter=next_month">Next Month</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item {% if date_filter == 'custom' %}active{% endif %}" href="#" onclick="showCustomDateModal(); return false;">Custom Dates...</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12">
              <small class="text-muted">
                Showing data for: 
                {% if date_filter == 'today' %}
                  {{ filter_start|date:"M d, Y" }}
                {% else %}
                  {{ filter_start|date:"M d, Y" }} - {{ filter_end|date:"M d, Y" }}
                {% endif %}
              </small>
            </div>
          </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row">
          <div class="col-xl-3 col-md-6">
            <div class="stats-card primary">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-1">{{ stats.total_active_rules }}</h2>
                  <p class="mb-0 text-muted">Total Active Rules</p>
                </div>
                <div class="stats-icon">
                  <i class="fas fa-clipboard-list"></i>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-xl-3 col-md-6">
            <div class="stats-card success">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-1">{{ stats.total_animals_feeding }}</h2>
                  <p class="mb-0 text-muted">Animals Taking Feed</p>
                  <small class="text-success">Unique count</small>
                </div>
                <div class="stats-icon">
                  <i class="fas fa-cow"></i>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-xl-3 col-md-6">
            <div class="stats-card warning">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-1">₹{{ stats.total_feed_cost|floatformat:0|intcomma }}</h2>
                  <p class="mb-0 text-muted">Total Feed Cost</p>
                  <small class="text-warning">Actual: ₹{{ stats.actual_feed_cost|floatformat:0|intcomma }}</small>
                </div>
                <div class="stats-icon">
                  <i class="fas fa-rupee-sign"></i>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-xl-3 col-md-6">
            <div class="stats-card info">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h2 class="mb-1">{{ stats.pending_today }}</h2>
                  <p class="mb-0 text-muted">Pending Today</p>
                  <small class="text-info">Completed: {{ stats.completed_today }}</small>
                </div>
                <div class="stats-icon">
                  <i class="fas fa-clock"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Main Content -->
        <div class="row">
          <!-- Feed Schedule & Activities -->
          <div class="col-lg-8">
            <div class="card card-modern">
              <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0">Feed Schedule & Activities</h5>
                  <div>
                    <span class="badge bg-light text-dark">
                      Total: {{ stats.total_activities }} activities
                    </span>
                    <span class="badge bg-success ms-2">
                      Completed: {{ stats.completed_activities }}
                    </span>
                    <span class="badge bg-warning ms-2">
                      Pending: {{ stats.pending_activities }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="card-body">
                {% if page_obj %}
                  <div class="table-responsive">
                    <table class="table activity-table">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Rule</th>
                          <th>Animals</th>
                          <th>Status</th>
                          <th>Cost</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for day_data in page_obj %}
                        <tr class="{% if day_data.date == filter_start and day_data.date == filter_end %}table-info{% endif %}">
                          <td>
                            <strong>{{ day_data.date|date:"M d, Y" }}</strong>
                            <br>
                            <small class="text-muted">{{ day_data.date|date:"l" }}</small>
                            {% if day_data.date == filter_start and day_data.date == filter_end %}
                              <br><span class="badge bg-info">Today</span>
                            {% endif %}
                          </td>
                          <td>
                            <div>
                              <a href="{% url 'feeds:feed_detail' day_data.rule.feed.pk %}" class="text-decoration-none text-dark">
                                <strong>{{ day_data.rule.feed.name }}</strong>
                              </a>
                              <br>
                              <a href="{% url 'feeds:feed_rule_detail' day_data.rule.pk %}" class="text-decoration-none">
                                <span class="rule-badge">{{ day_data.rule.name|truncatechars:30 }}</span>
                              </a>
                              <br>
                              <small class="text-muted">
                                {{ day_data.rule.dosage_qty }} {{ day_data.rule.dosage_unit }}
                              </small>
                            </div>
                          </td>
                          <td>
                            {% if day_data.not_created %}
                              <span class="text-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                {{ day_data.expected_animals }} animals
                              </span>
                              <br>
                              <small class="text-muted">Activities not created</small>
                            {% else %}
                              <strong>{{ day_data.activities|length }}</strong> animals
                              <br>
                              <small class="text-muted">
                                {% for activity in day_data.activities|slice:":3" %}
                                  {{ activity.animal.tag }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                                {% if day_data.activities|length > 3 %}
                                  ... +{{ day_data.activities|length|add:"-3" }}
                                {% endif %}
                              </small>
                            {% endif %}
                          </td>
                          <td>
                            {% if day_data.not_created %}
                              <span class="badge bg-warning">Not Created</span>
                            {% else %}
                              {% if day_data.status_counts.accepted > 0 %}
                                <span class="badge bg-success">✓ {{ day_data.status_counts.accepted }}</span>
                              {% endif %}
                              {% if day_data.status_counts.planned > 0 %}
                                <span class="badge bg-warning">⏰ {{ day_data.status_counts.planned }}</span>
                              {% endif %}
                              {% if day_data.status_counts.skipped > 0 %}
                                <span class="badge bg-secondary">✗ {{ day_data.status_counts.skipped }}</span>
                              {% endif %}
                              {% if day_data.status_counts.missed > 0 %}
                                <span class="badge bg-danger">! {{ day_data.status_counts.missed }}</span>
                              {% endif %}
                            {% endif %}
                          </td>
                          <td>
                            <strong>₹{{ day_data.total_cost|floatformat:2 }}</strong>
                          </td>
                          <td>
                            {% if day_data.not_created %}
                              {% with today_date=day_data.date|date:"Y-m-d" %}
                              <button class="btn btn-sm btn-success btn-action" 
                                      onclick="completeFeeding('{{ day_data.rule.pk }}', '{{ today_date }}')"
                                      title="Create and complete all activities">
                                <i class="fas fa-check"></i> Complete
                              </button>
                              <button class="btn btn-sm btn-outline-danger btn-action" 
                                      onclick="skipFeeding('{{ day_data.rule.pk }}', '{{ today_date }}')"
                                      title="Create and skip all activities">
                                <i class="fas fa-times"></i> Skip
                              </button>
                              {% endwith %}
                            {% elif day_data.status_counts.planned > 0 %}
                              {% with today_date=day_data.date|date:"Y-m-d" %}
                              <button class="btn btn-sm btn-success btn-action" 
                                      onclick="completeAllForDate('{{ day_data.rule.pk }}', '{{ today_date }}')"
                                      title="Complete all {{ day_data.status_counts.planned }} activities">
                                <i class="fas fa-check"></i> Complete All
                              </button>
                              <button class="btn btn-sm btn-outline-danger btn-action" 
                                      onclick="skipAllForDate('{{ day_data.rule.pk }}', '{{ today_date }}')"
                                      title="Skip all {{ day_data.status_counts.planned }} activities">
                                <i class="fas fa-times"></i> Skip All
                              </button>
                              {% endwith %}
                            {% else %}
                              <a href="{% url 'feeds:feed_rule_detail' day_data.rule.pk %}" 
                                 class="btn btn-sm btn-outline-primary btn-action">
                                <i class="fas fa-eye"></i> View
                              </a>
                            {% endif %}
                          </td>
                        </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                  
                  <!-- Pagination -->
                  {% if page_obj.has_other_pages %}
                  <nav aria-label="Feed activities pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                      {% if page_obj.has_previous %}
                        <li class="page-item">
                          <a class="page-link" href="?page={{ page_obj.previous_page_number }}&date_filter={{ date_filter }}{% if date_filter == 'custom' %}&start_date={{ start_date }}&end_date={{ end_date }}{% endif %}">Previous</a>
                        </li>
                      {% endif %}
                      
                      <li class="page-item active">
                        <span class="page-link">
                          Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                      </li>
                      
                      {% if page_obj.has_next %}
                        <li class="page-item">
                          <a class="page-link" href="?page={{ page_obj.next_page_number }}&date_filter={{ date_filter }}{% if date_filter == 'custom' %}&start_date={{ start_date }}&end_date={{ end_date }}{% endif %}">Next</a>
                        </li>
                      {% endif %}
                    </ul>
                  </nav>
                  {% endif %}
                  
                {% else %}
                  <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Feed Activities Found</h5>
                    <p class="text-muted">No feed activities scheduled for the selected date range.</p>
                    <a href="{% url 'feeds:feed_list' %}" class="btn btn-primary btn-action">
                      <i class="fas fa-plus"></i> Create Feed Rules
                    </a>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
          
          <!-- Right Sidebar -->
          <div class="col-lg-4" style="height: 600px;">
            <!-- Quick Actions (1/3 height - Compact) -->
            <div class="card card-modern mb-3" style="height: 130px;">
              <div class="card-body p-2">
                <h6 class="mb-2 small">Quick Actions</h6>
                <div class="row g-1">
                  <div class="col-4">
                    <a href="{% url 'feeds:feed_create' %}" class="btn btn-primary btn-sm w-100 p-1" title="Create New Feed" style="font-size: 10px;">
                      <i class="fas fa-plus d-block mb-1"></i>
                      <span>New Feed</span>
                    </a>
                  </div>
                  <div class="col-4">
                    <a href="{% url 'feeds:feed_list' %}" class="btn btn-success btn-sm w-100 p-1" title="Manage Feeds" style="font-size: 10px;">
                      <i class="fas fa-list d-block mb-1"></i>
                      <span>Feeds</span>
                    </a>
                  </div>
                  <div class="col-4">
                    <a href="{% url 'feeds:feed_list' %}" class="btn btn-info btn-sm w-100 p-1" title="Manage Feed Rules" style="font-size: 10px;">
                      <i class="fas fa-tasks d-block mb-1"></i>
                      <span>Rules</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Feed Consumption Summary (fills remaining space) -->
            {% if feed_consumption %}
            <div class="card card-modern mb-3" style="height: 220px;">
              <div class="card-header py-2">
                <h6 class="mb-0 small">Feed Consumption Summary</h6>
              </div>
              <div class="card-body py-2" style="overflow-y: auto; max-height: 180px;">
                {% for feed_name, data in feed_consumption.items %}
                <div class="consumption-card mb-2 p-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <strong class="small">{{ feed_name }}</strong>
                    <span class="text-muted small">₹{{ data.cost|floatformat:0 }}</span>
                  </div>
                  <div class="text-muted" style="font-size: 11px;">
                    Qty: {{ data.quantity|floatformat:1 }} {{ data.unit }}
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>
            {% endif %}
            
            <!-- Active Feed Rules (fills remaining space) -->
            <div class="card card-modern" style="height: {% if feed_consumption %}240px{% else %}460px{% endif %};">
              <div class="card-header py-2">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0 small">Active Feed Rules</h6>
                  <a href="{% url 'feeds:feed_list' %}" class="text-primary small">View All</a>
                </div>
              </div>
              <div class="card-body py-2" style="overflow-y: auto; max-height: {% if feed_consumption %}200px{% else %}420px{% endif %};">
                {% if active_rules %}
                  {% for rule in active_rules %}
                  <div class="d-flex justify-content-between align-items-center mb-2 p-2" style="background: #f8f9fa; border-radius: 8px;">
                    <div>
                      <div class="fw-bold small">{{ rule.feed.name }}</div>
                      <small class="text-muted" style="font-size: 11px;">
                        {{ rule.dosage_qty }} {{ rule.dosage_unit }} • 
                        Every {{ rule.frequency_days }}d
                      </small>
                    </div>
                    <a href="{% url 'feeds:feed_rule_detail' rule.pk %}" class="btn btn-sm btn-outline-primary py-0 px-2" style="font-size: 11px;">
                      <i class="fas fa-eye"></i>
                    </a>
                  </div>
                  {% endfor %}
                {% else %}
                  <p class="text-muted text-center mb-0 small">No active feed rules</p>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

<!-- Custom Date Modal -->
<div class="modal fade" id="customDateModal" tabindex="-1" aria-labelledby="customDateModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="customDateModalLabel">Select Date Range</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="get">
        <div class="modal-body">
          <input type="hidden" name="date_filter" value="custom">
          <div class="mb-3">
            <label for="start_date" class="form-label">Start Date</label>
            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}" required>
          </div>
          <div class="mb-3">
            <label for="end_date" class="form-label">End Date</label>
            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Apply Filter</button>
        </div>
      </form>
    </div>
  </div>
</div>

{% csrf_token %}
<script>
function showCustomDateModal() {
  var myModal = new bootstrap.Modal(document.getElementById('customDateModal'));
  myModal.show();
}

function completeFeeding(rulePk, date) {
  if (!confirm(`Create and complete all feed activities for ${date}?`)) {
    return;
  }
  
  const url = `/feeds/rules/${rulePk}/complete-feeding/${date}/`;
  
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message);
      location.reload();
    } else {
      alert('Error: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Error completing feeding. Please try again.');
  });
}

function skipFeeding(rulePk, date) {
  if (!confirm(`Create and skip all feed activities for ${date}?`)) {
    return;
  }
  
  const url = `/feeds/rules/${rulePk}/skip-feeding/${date}/`;
  
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message);
      location.reload();
    } else {
      alert('Error: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Error skipping feeding. Please try again.');
  });
}

function completeAllForDate(rulePk, date) {
  if (!confirm(`Complete all planned activities for ${date}?`)) {
    return;
  }
  
  const url = `/feeds/rules/${rulePk}/complete-all/${date}/`;
  
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message);
      location.reload();
    } else {
      alert('Error: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Error completing activities. Please try again.');
  });
}

function skipAllForDate(rulePk, date) {
  if (!confirm(`Skip all planned activities for ${date}?`)) {
    return;
  }
  
  const url = `/feeds/rules/${rulePk}/skip-all/${date}/`;
  
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(data.message);
      location.reload();
    } else {
      alert('Error: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Error skipping activities. Please try again.');
  });
}

</script>

{% endblock %}