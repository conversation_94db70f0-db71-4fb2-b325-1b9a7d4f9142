{% extends 'main/base.html' %}
{% load static %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
  }
  
  .form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e0e6ed;
    padding: 15px 20px;
    font-size: 16px;
    height: auto;
    min-height: 50px;
    line-height: 1.4;
  }
  
  .form-select {
    background-position: right 15px center;
    background-size: 16px 12px;
    padding-right: 45px;
  }
  
  .form-control-lg, .form-select-lg {
    min-height: 55px;
    font-size: 17px;
    padding: 15px 20px;
  }
  
  .form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
  }
  
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    font-size: 15px;
  }
  
  .section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    margin-bottom: 20px;
  }
  
  .info-box {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .preview-box {
    background: #f8f9fa;
    border: 2px solid #28a745;
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
  }
  
  .preview-stat {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }
  
  .preview-stat h4 {
    color: #28a745;
    margin-bottom: 5px;
  }
  
  .formula-section {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .hidden {
    display: none !important;
  }
  
  #specific-animals-section {
    transition: all 0.3s ease;
  }
  
  #specific-animals-section.show {
    display: block !important;
    visibility: visible !important;
  }
  
  .form-check-input-lg {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .animal-count-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
    margin-top: 10px;
  }
  
  .dropdown-item-check {
    padding: 8px 16px;
    cursor: pointer;
  }
  
  .dropdown-item-check:hover {
    background-color: #f8f9fa;
  }
  
  .dropdown-item-check .form-check-input {
    margin-right: 8px;
  }
  
  .dropdown-menu {
    max-height: 250px;
    overflow-y: auto;
  }
  
  .dropdown-toggle {
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
</style>

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>{% if form.instance.pk %}Edit Feed Rule{% else %}Create Advanced Feed Rule{% endif %}</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:feed_detail' feed.pk %}">{{ feed.name }}</a></li>
                <li class="breadcrumb-item active">{% if form.instance.pk %}Edit Rule{% else %}Add Rule{% endif %}</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Feed Info Box -->
        <div class="info-box">
          <div class="d-flex align-items-center">
            <i class="fas fa-seedling fa-2x text-primary me-3"></i>
            <div>
              <h6 class="mb-1">Creating rule for: <strong>{{ feed.name }}</strong></h6>
              <small class="text-muted">Cost: ₹{{ feed.unit_cost|floatformat:2 }} per {{ feed.get_unit_display }}</small>
            </div>
          </div>
        </div>
        
        <!-- Form -->
        <div class="row justify-content-center">
          <div class="col-lg-12">
            <form method="post" id="feedRuleForm" novalidate>
              {% csrf_token %}
              
              {% if messages %}
                {% for message in messages %}
                  <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                  </div>
                {% endfor %}
              {% endif %}
              
              <!-- Display non-field errors -->
              {% if form.non_field_errors %}
              <div class="alert alert-danger alert-dismissible fade show" role="alert">
                  {% for error in form.non_field_errors %}
                      <p class="mb-0">{{ error }}</p>
                  {% endfor %}
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>
              {% endif %}
              
              <!-- Hidden legacy fields -->
              <div style="display: none;">
                {{ form.target_animal_category }}
                {{ form.target_animal_type }}
                {{ form.frequency_days }}
                {{ form.target_animal_categories }}
                {{ form.target_animal_types }}
              </div>
              
              
              <!-- Rule Basic Information -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Rule Information</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-12 mb-3">
                      <label for="{{ form.name.id_for_label }}" class="form-label">
                        Rule Name <span class="text-danger">*</span>
                      </label>
                      {{ form.name }}
                      {% if form.name.errors %}
                        <div class="text-danger small">
                          {% for error in form.name.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Descriptive name for this feeding rule</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Enhanced Target Animals -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-crosshairs me-2"></i>Target Animals</h5>
                </div>
                <div class="card-body">
                  
                  <!-- Target All Animals Option -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <div class="form-check">
                        {{ form.target_all_animals }}
                        <label class="form-check-label" for="{{ form.target_all_animals.id_for_label }}">
                          <strong>Apply to ALL animals in the farm</strong>
                        </label>
                      </div>
                      <small class="text-muted">If checked, this rule will apply to all active animals in your farm</small>
                      <div id="animal-count-display" class="animal-count-badge hidden">
                        Estimated animals: <span id="animal-count">0</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Specific Animal Selection -->
                  <div id="specific-animals-section">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label class="form-label">
                          Animal Categories <span class="text-danger">*</span>
                        </label>
                        <div class="custom-dropdown" id="categories-dropdown-container">
                          <button type="button" class="btn btn-outline-secondary w-100 text-start d-flex justify-content-between align-items-center" id="categoriesDropdown">
                            <span id="categories-selected">Select Categories</span>
                            <i class="fas fa-chevron-down"></i>
                          </button>
                          <div class="dropdown-menu w-100" style="display: none; max-height: 250px; overflow-y: auto;" id="categories-dropdown-menu">
                            {% for value, label in form.target_animal_categories.field.choices %}
                            <div class="dropdown-item-check" style="padding: 8px 16px; cursor: pointer;">
                              <div class="form-check">
                                <input class="form-check-input category-checkbox" type="checkbox" value="{{ value }}" id="category_{{ value }}">
                                <label class="form-check-label" for="category_{{ value }}">
                                  {{ label }}
                                </label>
                              </div>
                            </div>
                            {% endfor %}
                          </div>
                        </div>
                        {% if form.target_animal_categories.errors %}
                          <div class="text-danger small">
                            {% for error in form.target_animal_categories.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                        <div class="form-text">Select multiple categories as needed</div>
                      </div>
                      
                      <div class="col-md-6 mb-3">
                        <label class="form-label">
                          Animal Types <span class="text-danger">*</span>
                        </label>
                        <div class="custom-dropdown" id="types-dropdown-container">
                          <button type="button" class="btn btn-outline-secondary w-100 text-start d-flex justify-content-between align-items-center" id="typesDropdown">
                            <span id="types-selected">Select Types</span>
                            <i class="fas fa-chevron-down"></i>
                          </button>
                          <div class="dropdown-menu w-100" style="display: none; max-height: 250px; overflow-y: auto;" id="types-dropdown-menu">
                            {% for value, label in form.target_animal_types.field.choices %}
                            <div class="dropdown-item-check" style="padding: 8px 16px; cursor: pointer;">
                              <div class="form-check">
                                <input class="form-check-input type-checkbox" type="checkbox" value="{{ value }}" id="type_{{ value }}">
                                <label class="form-check-label" for="type_{{ value }}">
                                  {{ label }}
                                </label>
                              </div>
                            </div>
                            {% endfor %}
                          </div>
                        </div>
                        {% if form.target_animal_types.errors %}
                          <div class="text-danger small">
                            {% for error in form.target_animal_types.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                        <div class="form-text">Select multiple types as needed</div>
                      </div>
                    </div>
                  </div>
                  
                </div>
                
                <!-- Age Range (Optional - applies to both "All Animals" and specific selections) -->
                <div class="row mt-4" id="age-filter-section">
                  <div class="col-12 mb-3">
                    <h6 class="text-muted">
                      <i class="fas fa-filter me-2"></i>Age Filter <small>(Optional - works with both "All Animals" and specific selections)</small>
                    </h6>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.min_age_months.id_for_label }}" class="form-label">
                      Minimum Age (months) <small class="text-muted">(Optional)</small>
                    </label>
                    {{ form.min_age_months }}
                    {% if form.min_age_months.errors %}
                      <div class="text-danger small">{{ form.min_age_months.errors.0 }}</div>
                    {% endif %}
                    <div class="form-text">Leave empty for no minimum age limit</div>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="{{ form.max_age_months.id_for_label }}" class="form-label">
                      Maximum Age (months) <small class="text-muted">(Optional)</small>
                    </label>
                    {{ form.max_age_months }}
                    {% if form.max_age_months.errors %}
                      <div class="text-danger small">{{ form.max_age_months.errors.0 }}</div>
                    {% endif %}
                    <div class="form-text">Leave empty for no maximum age limit</div>
                  </div>
                </div>
                  
                  <!-- Hidden use_age_filter field -->
                  <div style="display: none;">
                    {{ form.use_age_filter }}
                  </div>
                  
                </div>
              </div>
              
              <!-- Enhanced Formula Section -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Formula Configuration</h5>
                </div>
                <div class="card-body">
                  
                  <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.formula_type.id_for_label }}" class="form-label">
                        Formula Type <span class="text-danger">*</span>
                      </label>
                      {{ form.formula_type }}
                      {% if form.formula_type.errors %}
                        <div class="text-danger small">
                          {% for error in form.formula_type.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Choose how to calculate dosage</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.formula_application.id_for_label }}" class="form-label">
                        Application Method <span class="text-danger">*</span>
                      </label>
                      {{ form.formula_application }}
                      {% if form.formula_application.errors %}
                        <div class="text-danger small">
                          {% for error in form.formula_application.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Per animal or total batch calculation</div>
                    </div>
                  </div>
                  
                  <!-- Formula-specific fields -->
                  <div class="formula-section">
                    
                    <!-- Fixed Amount -->
                    <div id="fixed-formula" class="row">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.dosage_qty.id_for_label }}" class="form-label">
                          Fixed Amount <span class="text-danger">*</span>
                        </label>
                        {{ form.dosage_qty }}
                        {% if form.dosage_qty.errors %}
                          <div class="text-danger small">
                            {% for error in form.dosage_qty.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                      </div>
                      
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.dosage_unit.id_for_label }}" class="form-label">
                          Unit <span class="text-danger">*</span>
                        </label>
                        {{ form.dosage_unit }}
                        {% if form.dosage_unit.errors %}
                          <div class="text-danger small">
                            {% for error in form.dosage_unit.errors %}{{ error }}{% endfor %}
                          </div>
                        {% endif %}
                      </div>
                    </div>
                    
                    <!-- Per Animal -->
                    <div id="per-animal-formula" class="row hidden">
                      <div class="col-md-12 mb-3">
                        <label for="{{ form.formula_value.id_for_label }}" class="form-label">
                          Amount per Animal
                        </label>
                        {{ form.formula_value }}
                        <div class="form-text">Fixed amount to give each animal</div>
                      </div>
                    </div>
                    
                    <!-- Weight-based formulas -->
                    <div id="weight-formula" class="row hidden">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.formula_value.id_for_label }}" class="form-label">
                          <span id="weight-label">Multiplier</span>
                        </label>
                        {{ form.formula_value }}
                        <div class="form-text" id="weight-help">Amount per kg of body weight</div>
                      </div>
                      <div class="col-md-3 mb-3">
                        <label for="{{ form.min_dosage.id_for_label }}" class="form-label">
                          Min Dosage
                        </label>
                        {{ form.min_dosage }}
                        <div class="form-text">Optional minimum</div>
                      </div>
                      <div class="col-md-3 mb-3">
                        <label for="{{ form.max_dosage.id_for_label }}" class="form-label">
                          Max Dosage
                        </label>
                        {{ form.max_dosage }}
                        <div class="form-text">Optional maximum</div>
                      </div>
                    </div>
                    
                    <!-- Custom Formula -->
                    <div id="custom-formula" class="row hidden">
                      <div class="col-md-12 mb-3">
                        <label for="{{ form.custom_formula.id_for_label }}" class="form-label">
                          Custom Formula
                        </label>
                        {{ form.custom_formula }}
                        <div class="form-text">Use 'weight' as variable. Example: weight * 0.05 + 2</div>
                      </div>
                    </div>
                    
                  </div>
                  
                </div>
              </div>
              
              
              <!-- Scheduling (condensed version) -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Schedule & Timing</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.schedule_type.id_for_label }}" class="form-label">
                        Schedule Type <span class="text-danger">*</span>
                      </label>
                      {{ form.schedule_type }}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.delivery_time.id_for_label }}" class="form-label">
                        Delivery Time <span class="text-danger">*</span>
                      </label>
                      {{ form.delivery_time }}
                    </div>
                  </div>
                  
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.start_date.id_for_label }}" class="form-label">
                        Start Date <span class="text-danger">*</span>
                      </label>
                      {{ form.start_date }}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.end_date.id_for_label }}" class="form-label">
                        End Date <span class="text-danger">*</span>
                      </label>
                      {{ form.end_date }}
                    </div>
                  </div>
                  
                  <!-- Include other scheduling fields as hidden for now -->
                  <div style="display: none;">
                    {{ form.daily_type }}
                    {{ form.interval_days }}
                    {{ form.weekly_type }}
                    {{ form.monday }}
                    {{ form.tuesday }}
                    {{ form.wednesday }}
                    {{ form.thursday }}
                    {{ form.friday }}
                    {{ form.saturday }}
                    {{ form.sunday }}
                    {{ form.interval_weeks }}
                    {{ form.monthly_type }}
                    {{ form.day_of_month }}
                    {{ form.week_of_month }}
                    {{ form.weekday_of_month }}
                    {{ form.interval_months }}
                    {{ form.interval_years }}
                    {{ form.custom_interval_value }}
                    {{ form.custom_interval_unit }}
                    {{ form.end_time }}
                    {{ form.end_type }}
                    {{ form.max_occurrences }}
                    {{ form.exception_dates }}
                  </div>
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="card card-modern">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <a href="{% url 'feeds:feed_detail' feed.pk %}" class="btn btn-secondary btn-modern">
                      <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary btn-modern">
                      <i class="fas fa-save me-2"></i>
                      {% if form.instance.pk %}Update Feed Rule{% else %}Create Advanced Feed Rule{% endif %}
                    </button>
                  </div>
                </div>
              </div>
              
            </form>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

<script>
// Ensure DOM is ready before initializing
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCheckboxesNow);
} else {
    // DOM is already loaded, initialize immediately
    setTimeout(initializeCheckboxesNow, 100);
}

function initializeCheckboxesNow() {
    console.log('=== INITIALIZING CHECKBOXES ===');
    
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    const typeCheckboxes = document.querySelectorAll('.type-checkbox');
    const categoriesSelected = document.getElementById('categories-selected');
    const typesSelected = document.getElementById('types-selected');
    const categoriesField = document.getElementById('id_target_animal_categories');
    const typesField = document.getElementById('id_target_animal_types');
    
    console.log('Found category checkboxes:', categoryCheckboxes.length);
    console.log('Found type checkboxes:', typeCheckboxes.length);
    console.log('Categories field:', categoriesField);
    console.log('Types field:', typesField);
    
    {% if form.instance.pk %}
    // Get initial values from the hidden fields or template
    let initialCategories = [];
    let initialTypes = [];
    
    // Try to get from hidden field first
    if (categoriesField && categoriesField.value) {
        try {
            initialCategories = JSON.parse(categoriesField.value);
            console.log('Got categories from field:', initialCategories);
        } catch (e) {
            console.log('Failed to parse categories field:', e);
        }
    }
    
    if (typesField && typesField.value) {
        try {
            initialTypes = JSON.parse(typesField.value);
            console.log('Got types from field:', initialTypes);
        } catch (e) {
            console.log('Failed to parse types field:', e);
        }
    }
    
    // Fallback to template data if field parsing failed
    if (initialCategories.length === 0) {
        initialCategories = {{ form.instance.target_animal_categories|default:"[]"|safe }};
        console.log('Using template categories:', initialCategories);
    }
    
    if (initialTypes.length === 0) {
        initialTypes = {{ form.instance.target_animal_types|default:"[]"|safe }};
        console.log('Using template types:', initialTypes);
    }
    
    // Set categories checkboxes
    if (Array.isArray(initialCategories) && initialCategories.length > 0) {
        categoryCheckboxes.forEach(cb => {
            cb.checked = initialCategories.includes(cb.value);
            if (cb.checked) {
                console.log(`Checked category: ${cb.value}`);
            }
        });
        
        // Update dropdown text
        updateDropdownText(categoryCheckboxes, categoriesSelected, 'Select Categories');
    }
    
    // Set types checkboxes
    if (Array.isArray(initialTypes) && initialTypes.length > 0) {
        typeCheckboxes.forEach(cb => {
            cb.checked = initialTypes.includes(cb.value);
            if (cb.checked) {
                console.log(`Checked type: ${cb.value}`);
            }
        });
        
        // Update dropdown text
        updateDropdownText(typeCheckboxes, typesSelected, 'Select Types');
    }
    {% else %}
    console.log('New rule - no initial values to set');
    {% endif %}
    
    // Set up event handlers for multiple selection
    setupMultipleSelectionHandlers();
    
    console.log('=== CHECKBOX INITIALIZATION COMPLETE ===');
}

// Helper function to update dropdown text
function updateDropdownText(checkboxes, selectedSpan, defaultText) {
    if (!selectedSpan) return;
    
    const checked = Array.from(checkboxes).filter(cb => cb.checked);
    if (checked.length === 0) {
        selectedSpan.textContent = defaultText;
    } else if (checked.length === 1) {
        const label = checked[0].parentNode.querySelector('label');
        selectedSpan.textContent = label ? label.textContent.trim() : checked[0].value;
    } else {
        selectedSpan.textContent = `${checked.length} items selected`;
    }
}

function setupMultipleSelectionHandlers() {
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    const typeCheckboxes = document.querySelectorAll('.type-checkbox');
    const categoriesSelected = document.getElementById('categories-selected');
    const typesSelected = document.getElementById('types-selected');
    
    // Custom dropdown elements
    const categoriesDropdown = document.getElementById('categoriesDropdown');
    const categoriesDropdownMenu = document.getElementById('categories-dropdown-menu');
    const typesDropdown = document.getElementById('typesDropdown');
    const typesDropdownMenu = document.getElementById('types-dropdown-menu');
    
    // Function to update dropdown text
    function updateDropdownText(checkboxes, selectedSpan, defaultText) {
        const checked = Array.from(checkboxes).filter(cb => cb.checked);
        if (checked.length === 0) {
            selectedSpan.textContent = defaultText;
        } else if (checked.length === 1) {
            selectedSpan.textContent = checked[0].parentNode.querySelector('label').textContent.trim();
        } else {
            selectedSpan.textContent = `${checked.length} items selected`;
        }
    }
    
    // Custom dropdown toggle handlers
    if (categoriesDropdown && categoriesDropdownMenu) {
        categoriesDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Toggle visibility
            if (categoriesDropdownMenu.style.display === 'none' || !categoriesDropdownMenu.style.display) {
                categoriesDropdownMenu.style.display = 'block';
                categoriesDropdownMenu.style.position = 'absolute';
                categoriesDropdownMenu.style.zIndex = '1000';
                categoriesDropdownMenu.style.backgroundColor = 'white';
                categoriesDropdownMenu.style.border = '1px solid #ccc';
                categoriesDropdownMenu.style.borderRadius = '4px';
                categoriesDropdownMenu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            } else {
                categoriesDropdownMenu.style.display = 'none';
            }
        });
    }
    
    if (typesDropdown && typesDropdownMenu) {
        typesDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Toggle visibility
            if (typesDropdownMenu.style.display === 'none' || !typesDropdownMenu.style.display) {
                typesDropdownMenu.style.display = 'block';
                typesDropdownMenu.style.position = 'absolute';
                typesDropdownMenu.style.zIndex = '1000';
                typesDropdownMenu.style.backgroundColor = 'white';
                typesDropdownMenu.style.border = '1px solid #ccc';
                typesDropdownMenu.style.borderRadius = '4px';
                typesDropdownMenu.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            } else {
                typesDropdownMenu.style.display = 'none';
            }
        });
    }
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (categoriesDropdownMenu && !categoriesDropdownMenu.contains(e.target) && !categoriesDropdown.contains(e.target)) {
            categoriesDropdownMenu.style.display = 'none';
        }
        if (typesDropdownMenu && !typesDropdownMenu.contains(e.target) && !typesDropdown.contains(e.target)) {
            typesDropdownMenu.style.display = 'none';
        }
    });
    
    // Category checkbox handling
    categoryCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function(e) {
            updateDropdownText(categoryCheckboxes, categoriesSelected, 'Select Categories');
            console.log('Category changed:', checkbox.value, 'checked:', checkbox.checked);
        });
    });
    
    // Type checkbox handling
    typeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function(e) {
            updateDropdownText(typeCheckboxes, typesSelected, 'Select Types');
            console.log('Type changed:', checkbox.value, 'checked:', checkbox.checked);
        });
    });
    
    console.log('Custom dropdown handlers set up');
}

document.addEventListener('DOMContentLoaded', function() {
    // Get elements for event listeners
    const targetAllAnimalsCheckbox = document.getElementById('id_target_all_animals');
    const formulaTypeSelect = document.getElementById('id_formula_type');
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    const typeCheckboxes = document.querySelectorAll('.type-checkbox');
    
    // updateDropdownText function is already defined above
    
    // Category dropdown handling
    categoryCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function(e) {
            e.stopPropagation(); // Prevent dropdown from closing
            const categoriesSelected = document.getElementById('categories-selected');
            if (categoriesSelected) {
                updateDropdownText(categoryCheckboxes, categoriesSelected, 'Select Categories');
            }
            updatePreview();
        });
        
        // Also prevent clicks on the checkbox from closing dropdown
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // Type dropdown handling
    typeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function(e) {
            e.stopPropagation(); // Prevent dropdown from closing
            const typesSelected = document.getElementById('types-selected');
            if (typesSelected) {
                updateDropdownText(typeCheckboxes, typesSelected, 'Select Types');
            }
            updatePreview();
        });
        
        // Also prevent clicks on the checkbox from closing dropdown
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // Prevent dropdown from closing when clicking anywhere in the dropdown items
    document.querySelectorAll('.dropdown-item-check').forEach(item => {
        item.addEventListener('click', function(e) {
            e.stopPropagation();
        });
        
        // Also handle mousedown to prevent dropdown closing
        item.addEventListener('mousedown', function(e) {
            e.stopPropagation();
        });
    });
    
    // Prevent dropdown toggles from closing when clicking inside
    const categoriesDropdownMenu = document.querySelector('#categoriesDropdown + .dropdown-menu');
    const typesDropdownMenu = document.querySelector('#typesDropdown + .dropdown-menu');
    
    if (categoriesDropdownMenu) {
        categoriesDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
    
    if (typesDropdownMenu) {
        typesDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
    
    // Target animals toggle
    function toggleTargetOptions() {
        // Find the target all animals checkbox
        let targetAllAnimalsCheckbox = document.getElementById('target_all_animals') ||
                                      document.getElementById('id_target_all_animals') ||
                                      document.querySelector('input[name="target_all_animals"]');
        const specificAnimalsSection = document.getElementById('specific-animals-section');
        const animalCountDisplay = document.getElementById('animal-count-display');
        const categoriesSelected = document.getElementById('categories-selected');
        const typesSelected = document.getElementById('types-selected');
        const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
        const typeCheckboxes = document.querySelectorAll('.type-checkbox');
        
        if (!targetAllAnimalsCheckbox || !specificAnimalsSection || !animalCountDisplay) {
            return;
        }
        
        if (targetAllAnimalsCheckbox.checked) {
            // Hide the specific animals section with both CSS class and inline style
            specificAnimalsSection.classList.add('hidden');
            specificAnimalsSection.style.display = 'none';
            
            // Show animal count display
            animalCountDisplay.classList.remove('hidden');
            animalCountDisplay.style.display = 'block';
            
            // Clear specific selections (but keep age filters since they work with "All Animals" too)
            categoryCheckboxes.forEach(cb => cb.checked = false);
            typeCheckboxes.forEach(cb => cb.checked = false);
            
            // Update dropdown text
            if (categoriesSelected && typesSelected) {
                updateDropdownText(categoryCheckboxes, categoriesSelected, 'Select Categories');
                updateDropdownText(typeCheckboxes, typesSelected, 'Select Types');
            }
            
            // Clear hidden fields to ensure no residual data
            const categoriesField = document.getElementById('id_target_animal_categories');
            const typesField = document.getElementById('id_target_animal_types');
            if (categoriesField) categoriesField.value = '[]';
            if (typesField) typesField.value = '[]';
            
            
            // Force update the dropdown display text immediately
            if (categoriesSelected) {
                categoriesSelected.textContent = 'Select Categories';
            }
            if (typesSelected) {
                typesSelected.textContent = 'Select Types';
            }
        } else {
            // Show the specific animals section with force
            specificAnimalsSection.classList.remove('hidden');
            specificAnimalsSection.classList.add('show');
            specificAnimalsSection.style.display = 'block';
            specificAnimalsSection.style.visibility = 'visible';
            specificAnimalsSection.style.opacity = '1';
            
            // Hide animal count display
            animalCountDisplay.classList.add('hidden');
            animalCountDisplay.style.display = 'none';
            
            
            // When switching back to specific selection, ensure hidden fields are cleared
            // so user can make new selections
            const categoriesField = document.getElementById('id_target_animal_categories');
            const typesField = document.getElementById('id_target_animal_types');
            
            // Only clear if they were previously set to empty arrays
            if (categoriesField && categoriesField.value === '[]') {
                categoriesField.value = '';
            }
            if (typesField && typesField.value === '[]') {
                typesField.value = '';
            }
        }
        updatePreview();
    }
    
    // Formula type toggle
    function toggleFormulaType() {
        const formulaTypeSelect = document.getElementById('id_formula_type');
        const fixedFormula = document.getElementById('fixed-formula');
        const perAnimalFormula = document.getElementById('per-animal-formula');
        const weightFormula = document.getElementById('weight-formula');
        const customFormula = document.getElementById('custom-formula');
        
        if (!formulaTypeSelect) {
            console.log('Formula type select not found');
            return;
        }
        
        const formulaType = formulaTypeSelect.value;
        
        // Hide all formula sections
        if (fixedFormula) fixedFormula.classList.add('hidden');
        if (perAnimalFormula) perAnimalFormula.classList.add('hidden');
        if (weightFormula) weightFormula.classList.add('hidden');
        if (customFormula) customFormula.classList.add('hidden');
        
        // Show relevant section
        switch(formulaType) {
            case 'fixed':
                if (fixedFormula) fixedFormula.classList.remove('hidden');
                break;
            case 'per_animal':
                if (perAnimalFormula) perAnimalFormula.classList.remove('hidden');
                break;
            case 'per_kg_weight':
                if (weightFormula) weightFormula.classList.remove('hidden');
                const weightLabel = document.getElementById('weight-label');
                const weightHelp = document.getElementById('weight-help');
                if (weightLabel) weightLabel.textContent = 'Amount per Kg';
                if (weightHelp) weightHelp.textContent = 'Amount per kg of body weight';
                break;
            case 'percentage_weight':
                if (weightFormula) weightFormula.classList.remove('hidden');
                const weightLabel2 = document.getElementById('weight-label');
                const weightHelp2 = document.getElementById('weight-help');
                if (weightLabel2) weightLabel2.textContent = 'Percentage (%)';
                if (weightHelp2) weightHelp2.textContent = 'Percentage of body weight';
                break;
            case 'custom_formula':
                if (customFormula) customFormula.classList.remove('hidden');
                break;
        }
        updatePreview();
    }
    
    // Real-time cost preview with AJAX
    function updatePreview() {
        // Get elements with null checks
        const targetAllAnimalsCheckbox = document.getElementById('id_target_all_animals');
        const formulaTypeSelect = document.getElementById('id_formula_type');
        const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
        const typeCheckboxes = document.querySelectorAll('.type-checkbox');
        
        // Ensure elements exist before proceeding
        if (!targetAllAnimalsCheckbox || !formulaTypeSelect) {
            console.log('Required elements not found for preview update');
            return;
        }
        
        // Get selected categories from checkboxes
        const selectedCategories = Array.from(categoryCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
        
        // Get selected types from checkboxes
        const selectedTypes = Array.from(typeCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
        
        const formData = {
            target_all_animals: targetAllAnimalsCheckbox.checked,
            target_animal_categories: selectedCategories,
            target_animal_types: selectedTypes,
            use_age_filter: document.getElementById('id_min_age_months').value || document.getElementById('id_max_age_months').value,
            min_age_months: parseInt(document.getElementById('id_min_age_months').value) || null,
            max_age_months: parseInt(document.getElementById('id_max_age_months').value) || null,
            formula_type: formulaTypeSelect.value,
            formula_application: document.getElementById('id_formula_application').value,
            dosage_qty: parseFloat(document.getElementById('id_dosage_qty').value) || 0,
            formula_value: parseFloat(document.getElementById('id_formula_value').value) || 1,
            unit_cost: {{ feed.unit_cost }}
        };
        
        // Make AJAX call to get real-time data
        fetch('{% url "feeds:animal_count_preview" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update preview display
                document.getElementById('preview-animals').textContent = data.animal_count;
                document.getElementById('preview-total-dosage').textContent = data.total_dosage;
                document.getElementById('preview-cost-per-animal').textContent = '₹' + data.cost_per_animal;
                document.getElementById('preview-total-cost').textContent = '₹' + data.total_cost;
                
                // Update animal count badge
                document.getElementById('animal-count').textContent = data.animal_count;
            } else {
                console.error('Error updating preview:', data.error);
                // Fallback to placeholder values
                document.getElementById('preview-animals').textContent = '0';
                document.getElementById('preview-total-dosage').textContent = '0.00';
                document.getElementById('preview-cost-per-animal').textContent = '₹0.00';
                document.getElementById('preview-total-cost').textContent = '₹0.00';
                document.getElementById('animal-count').textContent = '0';
            }
        })
        .catch(error => {
            console.error('AJAX error:', error);
            // Fallback to placeholder values
            document.getElementById('preview-animals').textContent = '0';
            document.getElementById('preview-total-dosage').textContent = '0.00';
            document.getElementById('preview-cost-per-animal').textContent = '₹0.00';
            document.getElementById('preview-total-cost').textContent = '₹0.00';
            document.getElementById('animal-count').textContent = '0';
        });
    }
    
    // Handle form submission - collect checkbox values
    const form = document.getElementById('feedRuleForm');
    console.log('Form element found:', form);
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('=== FORM SUBMISSION EVENT TRIGGERED ===');
            console.log('Form submitting, collecting checkbox values...');
            
            // Update hidden form fields with checkbox values
            const categoriesField = document.getElementById('id_target_animal_categories');
            const typesField = document.getElementById('id_target_animal_types');
            // Find the use_age_filter field (try multiple possible IDs)
            const useAgeField = document.getElementById('id_use_age_filter') || 
                               document.getElementById('use_age_filter') ||
                               document.querySelector('input[name="use_age_filter"]');
            
            // Get current checkbox selections
            const currentCategoryCheckboxes = document.querySelectorAll('.category-checkbox');
            const currentTypeCheckboxes = document.querySelectorAll('.type-checkbox');
            
            // Set categories - send as JSON array
            const selectedCategories = Array.from(currentCategoryCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);
            
            if (categoriesField) {
                categoriesField.value = JSON.stringify(selectedCategories);
                console.log('Setting categories field to:', categoriesField.value);
            }
            
            // Set types - send as JSON array
            const selectedTypes = Array.from(currentTypeCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);
            
            if (typesField) {
                typesField.value = JSON.stringify(selectedTypes);
                console.log('Setting types field to:', typesField.value);
            }
            
            // If "Apply to ALL animals" is checked, clear specific selections
            if (targetAllAnimalsCheckbox.checked) {
                // Clear hidden fields
                if (categoriesField) {
                    categoriesField.value = '[]';
                    console.log('Cleared categories field for target_all_animals');
                }
                if (typesField) {
                    typesField.value = '[]';
                    console.log('Cleared types field for target_all_animals');
                }
                // Note: Age filters are kept since they work with "Apply to ALL animals" too
            }
            
            // Set age filter usage - always check if any age values are provided
            const minAgeValue = document.getElementById('id_min_age_months').value;
            const maxAgeValue = document.getElementById('id_max_age_months').value;
            const hasAgeInput = minAgeValue || maxAgeValue;
            
            console.log('Looking for use_age_filter field...');
            console.log('useAgeField found:', useAgeField);
            console.log('useAgeField ID:', useAgeField ? useAgeField.id : 'none');
            console.log('useAgeField name:', useAgeField ? useAgeField.name : 'none');
            
            if (useAgeField) {
                useAgeField.checked = !!hasAgeInput;
                console.log('Setting use_age_filter to:', !!hasAgeInput, 'based on min:', minAgeValue, 'max:', maxAgeValue);
                console.log('use_age_filter field checked status:', useAgeField.checked);
            } else {
                console.error('use_age_filter field not found! Cannot set age filter usage.');
                
                // Debug: List all checkboxes to find the correct one
                const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
                console.log('All checkbox elements:');
                allCheckboxes.forEach(cb => {
                    console.log(`- ID: ${cb.id}, Name: ${cb.name}, Value: ${cb.value}`);
                });
            }
        
        // Validate that at least one target is selected if not targeting all
        if (!targetAllAnimalsCheckbox.checked && selectedCategories.length === 0 && selectedTypes.length === 0) {
            e.preventDefault();
            alert('Please select at least one animal category or type, or check "Apply to ALL animals"');
            
            // Show the specific animals section if it's hidden
            const specificAnimalsSection = document.getElementById('specific-animals-section');
            if (specificAnimalsSection) {
                specificAnimalsSection.classList.remove('hidden');
                specificAnimalsSection.style.display = 'block';
            }
            
            // Hide animal count display
            const animalCountDisplay = document.getElementById('animal-count-display');
            if (animalCountDisplay) {
                animalCountDisplay.classList.add('hidden');
                animalCountDisplay.style.display = 'none';
            }
            
            return false;
        }
        
        // Ensure frequency_days is set for backward compatibility
        const scheduleType = document.getElementById('id_schedule_type').value;
        const frequencyField = document.getElementById('id_frequency_days');
        if (!frequencyField.value || frequencyField.value === '0') {
            // Set default frequency based on schedule type
            switch(scheduleType) {
                case 'daily':
                    const dailyType = document.getElementById('id_daily_type').value;
                    if (dailyType === 'every_n_days') {
                        frequencyField.value = document.getElementById('id_interval_days').value || 1;
                    } else {
                        frequencyField.value = 1;
                    }
                    break;
                case 'weekly':
                    frequencyField.value = 7;
                    break;
                case 'monthly':
                    frequencyField.value = 30;
                    break;
                default:
                    frequencyField.value = 1;
            }
        }
        });
    }
    
    // Initialization is handled by initializeCheckboxesNow() above
    
    // Setup event listeners for target all animals checkbox
    const actualCheckbox = targetAllAnimalsCheckbox || 
                          document.getElementById('target_all_animals') ||
                          document.getElementById('id_target_all_animals') ||
                          document.querySelector('input[name="target_all_animals"]');
    
    if (actualCheckbox) {
        actualCheckbox.addEventListener('change', function(e) {
            console.log('Target all animals changed to:', e.target.checked);
            toggleTargetOptions();
        });
        
        // Also listen for clicks on the label
        const label = document.querySelector(`label[for="${actualCheckbox.id}"]`);
        if (label) {
            label.addEventListener('click', function() {
                setTimeout(toggleTargetOptions, 10);
            });
        }
    } else {
        console.warn('Target all animals checkbox not found');
    }
    if (formulaTypeSelect) {
        formulaTypeSelect.addEventListener('change', toggleFormulaType);
    }
    
    // Add event listeners for real-time preview updates
    const dosageQtyInput = document.getElementById('id_dosage_qty');
    const formulaValueInput = document.getElementById('id_formula_value');
    const minAgeInput = document.getElementById('id_min_age_months');
    const maxAgeInput = document.getElementById('id_max_age_months');
    
    if (dosageQtyInput) dosageQtyInput.addEventListener('input', updatePreview);
    if (formulaValueInput) formulaValueInput.addEventListener('input', updatePreview);
    if (minAgeInput) minAgeInput.addEventListener('input', updatePreview);
    if (maxAgeInput) maxAgeInput.addEventListener('input', updatePreview);
    
    // Initialize display
    toggleTargetOptions();
    toggleFormulaType();
    
    // Checkboxes are already initialized by initializeCheckboxesNow()
    updatePreview();
    
    // Set default values
    const startDateInput = document.querySelector('#id_start_date');
    const endDateInput = document.querySelector('#id_end_date');
    const deliveryTimeInput = document.querySelector('#id_delivery_time');
    
    if (startDateInput && !startDateInput.value) {
        const today = new Date();
        startDateInput.value = today.toISOString().split('T')[0];
    }
    
    if (endDateInput && !endDateInput.value) {
        const oneYearLater = new Date();
        oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
        endDateInput.value = oneYearLater.toISOString().split('T')[0];
    }
    
    if (deliveryTimeInput && !deliveryTimeInput.value) {
        deliveryTimeInput.value = '08:00';
    }
});
</script>

{% endblock %}

{% block extra_js %}
<script>
// Age filter validation
function validateAgeFilter() {
    const useAgeFilter = document.getElementById('id_use_age_filter');
    const minAge = document.getElementById('id_min_age_months');
    const maxAge = document.getElementById('id_max_age_months');
    
    if (useAgeFilter && useAgeFilter.checked && minAge && maxAge) {
        const minVal = parseInt(minAge.value);
        const maxVal = parseInt(maxAge.value);
        
        if (minVal && maxVal && minVal > maxVal) {
            alert('⚠️ Invalid age range: Minimum age cannot be greater than maximum age!');
            minAge.focus();
            return false;
        }
    }
    return true;
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const minAge = document.getElementById('id_min_age_months');
    const maxAge = document.getElementById('id_max_age_months');
    const form = document.querySelector('form');
    
    if (minAge) {
        minAge.addEventListener('blur', validateAgeFilter);
    }
    if (maxAge) {
        maxAge.addEventListener('blur', validateAgeFilter);
    }
    if (form) {
        form.addEventListener('submit', function(e) {
            // Validate age filter first
            if (!validateAgeFilter()) {
                e.preventDefault();
                return false;
            }
            
            // Collect and update checkbox values before submission
            console.log('=== FORM SUBMISSION: Collecting checkbox values ===');
            
            // Collect category values
            const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
            const checkedCategories = Array.from(categoryCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);
            
            // Collect type values
            const typeCheckboxes = document.querySelectorAll('.type-checkbox');
            const checkedTypes = Array.from(typeCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);
            
            console.log('Checked categories:', checkedCategories);
            console.log('Checked types:', checkedTypes);
            
            // Update hidden fields
            const categoriesField = document.getElementById('id_target_animal_categories');
            const typesField = document.getElementById('id_target_animal_types');
            
            if (categoriesField) {
                categoriesField.value = JSON.stringify(checkedCategories);
                console.log('Updated categories hidden field:', categoriesField.value);
            } else {
                console.error('Categories hidden field not found!');
            }
            
            if (typesField) {
                typesField.value = JSON.stringify(checkedTypes);
                console.log('Updated types hidden field:', typesField.value);
            } else {
                console.error('Types hidden field not found!');
            }
            
            console.log('=== FORM SUBMISSION: Ready to submit ===');
        });
    }
});
</script>
{% endblock %}