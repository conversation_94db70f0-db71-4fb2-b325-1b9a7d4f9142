{% extends 'main/base.html' %}
{% load static %}
{% load humanize %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
  }
  
  .info-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
  }
  
  .stat-box {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 15px;
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
  }
  
  .page-body {
    padding-top: 15px;
  }
  
  .container-fluid {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .feed-rules-section {
    margin-top: 30px;
    clear: both;
  }
  
  .quick-actions-sidebar {
    position: sticky;
    top: 20px;
    height: fit-content;
  }
  
  .quick-actions-sidebar .card-body {
    padding: 15px;
  }
  
  .quick-actions-sidebar .btn-modern {
    padding: 8px 15px;
    font-size: 14px;
  }
  
  @media (max-width: 991px) {
    .quick-actions-sidebar {
      position: relative;
      margin-top: 20px;
    }
    
    .feed-rules-section {
      margin-top: 20px;
    }
  }
  
  .row {
    margin-bottom: 15px;
  }
  
  .table-responsive {
    border-radius: 10px;
  }
</style>

<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>{{ feed.name }} Details</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:feed_list' %}">Feed Types</a></li>
                <li class="breadcrumb-item active">{{ feed.name }}</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Feed Information -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="info-card">
              <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                  <h4 class="mb-2">{{ feed.name }}</h4>
                  {% if feed.description %}
                    <p class="text-muted mb-0">{{ feed.description }}</p>
                  {% endif %}
                </div>
                <div>
                  {% if feed.is_active %}
                    <span class="badge bg-success">Active</span>
                  {% else %}
                    <span class="badge bg-secondary">Inactive</span>
                  {% endif %}
                </div>
              </div>
              
              <div class="row mt-4">
                <div class="col-md-3">
                  <div class="stat-box">
                    <h3 class="text-primary">₹{{ feed.unit_cost|floatformat:2 }}</h3>
                    <small class="text-muted">Cost per {{ feed.get_unit_display }}</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="stat-box">
                    <h3 class="text-success">{{ feed.rules.count }}</h3>
                    <small class="text-muted">Active Rules</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="stat-box">
                    <h3 class="text-info">{{ feed.activities_count|default:0 }}</h3>
                    <small class="text-muted">Total Activities</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="stat-box">
                    <h3 class="text-warning">₹{{ feed.total_cost|default:0|floatformat:0 }}</h3>
                    <small class="text-muted">Total Cost</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Second Row: Feed Rules + Quick Actions -->
        <div class="row">
          <!-- Feed Rules Section -->
          <div class="col-lg-8">
            {% if feed_rules %}
            <div class="card card-modern">
              <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0">Feeding Rules ({{ feed_rules.count }})</h5>
                  <a href="{% url 'feeds:feed_rule_create' feed.pk %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Add Rule
                  </a>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Rule Name</th>
                        <th>Target Animals</th>
                        <th>Formula</th>
                        <th>Frequency</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for rule in feed_rules %}
                      <tr>
                        <td>
                          <div class="fw-bold">{{ rule.name }}</div>
                          <small class="text-muted">{{ rule.delivery_time }}</small>
                        </td>
                        <td>
                          {% if rule.target_all_animals %}
                            <span class="badge bg-primary">All Animals</span>
                          {% else %}
                            {% if rule.target_animal_categories %}
                              {% for category in rule.target_animal_categories %}
                                <span class="badge bg-info">{{ category|title }}</span>
                              {% endfor %}
                            {% elif rule.target_animal_category %}
                              <span class="badge bg-info">{{ rule.get_target_animal_category_display }}</span>
                            {% endif %}
                            
                            {% if rule.target_animal_types %}
                              {% for type in rule.target_animal_types %}
                                <span class="badge bg-secondary">{{ type|title }}</span>
                              {% endfor %}
                            {% elif rule.target_animal_type %}
                              <span class="badge bg-secondary">{{ rule.get_target_animal_type_display }}</span>
                            {% endif %}
                            
                            {% if not rule.target_animal_categories and not rule.target_animal_category and not rule.target_animal_types and not rule.target_animal_type %}
                              <span class="badge bg-light text-dark">Any Animal</span>
                            {% endif %}
                          {% endif %}
                          {% if rule.use_age_filter %}
                            <div><small class="text-muted">Age: {{ rule.min_age_months|default:"0" }}-{{ rule.max_age_months|default:"∞" }} months</small></div>
                          {% endif %}
                        </td>
                        <td>
                          <div class="fw-bold">{{ rule.get_formula_type_display }}</div>
                          {% if rule.formula_type == 'fixed' %}
                            <small>{{ rule.dosage_qty }} {{ rule.get_dosage_unit_display }}</small>
                          {% elif rule.formula_type == 'per_animal' %}
                            <small>{{ rule.formula_value }} per animal</small>
                          {% elif rule.formula_type == 'per_kg_weight' %}
                            <small>{{ rule.formula_value }} per kg body weight</small>
                          {% elif rule.formula_type == 'percentage_weight' %}
                            <small>{{ rule.formula_value }}% of body weight</small>
                          {% else %}
                            <small>Custom formula</small>
                          {% endif %}
                        </td>
                        <td>
                          Every {{ rule.frequency_days }} day{{ rule.frequency_days|pluralize }}
                        </td>
                        <td>
                          {% if rule.status == 'scheduled' %}
                            <span class="badge bg-success">Active</span>
                          {% elif rule.status == 'paused' %}
                            <span class="badge bg-warning">Paused</span>
                          {% elif rule.status == 'expired' %}
                            <span class="badge bg-danger">Expired</span>
                          {% endif %}
                        </td>
                        <td>
                          <div class="btn-group btn-group-sm">
                            <a href="{% url 'feeds:feed_rule_detail' rule.pk %}" class="btn btn-outline-primary">
                              <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'feeds:feed_rule_edit' rule.pk %}" class="btn btn-outline-success">
                              <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'feeds:feed_rule_toggle_status' rule.pk %}" class="btn btn-outline-warning">
                              {% if rule.status == 'scheduled' %}
                                <i class="fas fa-pause"></i>
                              {% else %}
                                <i class="fas fa-play"></i>
                              {% endif %}
                            </a>
                          </div>
                        </td>
                      </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            {% else %}
            <div class="card card-modern">
              <div class="card-body text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Feeding Rules Created</h5>
                <p class="text-muted mb-4">Create feeding rules to automatically schedule this feed for your animals.</p>
                <a href="{% url 'feeds:feed_rule_create' feed.pk %}" class="btn btn-primary btn-modern">
                  <i class="fas fa-plus-circle me-2"></i>Create First Feeding Rule
                </a>
              </div>
            </div>
            {% endif %}
          </div>
          
          <!-- Quick Actions Sidebar -->
          <div class="col-lg-4">
            <div class="card card-modern quick-actions-sidebar">
              <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
              </div>
              <div class="card-body">
                <div class="d-grid gap-2">
                  <a href="{% url 'feeds:feed_edit' feed.pk %}" class="btn btn-primary btn-modern">
                    <i class="fas fa-edit me-2"></i>Edit Feed Type
                  </a>
                  <a href="{% url 'feeds:feed_rule_create' feed.pk %}" class="btn btn-success btn-modern">
                    <i class="fas fa-plus-circle me-2"></i>Add Feeding Rule
                  </a>
                  <a href="{% url 'feeds:feed_list' %}" class="btn btn-outline-secondary btn-modern">
                    <i class="fas fa-arrow-left me-2"></i>Back to Feed Types
                  </a>
                  <hr>
                  <a href="{% url 'feeds:feed_delete' feed.pk %}" class="btn btn-outline-danger btn-modern">
                    <i class="fas fa-trash me-2"></i>Delete Feed Type
                  </a>
                </div>
              </div>
            </div>
            
            <div class="card card-modern mt-3">
              <div class="card-header">
                <h6 class="mb-0">Feed Details</h6>
              </div>
              <div class="card-body">
                <table class="table table-borderless table-sm" style="font-size: 13px;">
                  <tr>
                    <td><strong>Unit:</strong></td>
                    <td>{{ feed.get_unit_display }}</td>
                  </tr>
                  <tr>
                    <td><strong>Cost per Unit:</strong></td>
                    <td>₹{{ feed.unit_cost|floatformat:2 }}</td>
                  </tr>
                  <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                      {% if feed.is_active %}
                        <span class="badge bg-success">Active</span>
                      {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                      {% endif %}
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Created:</strong></td>
                    <td>{{ feed.created_at|naturaltime }}</td>
                  </tr>
                  {% if feed.created_by %}
                  <tr>
                    <td><strong>Created By:</strong></td>
                    <td>{{ feed.created_by.get_full_name|default:feed.created_by.mobile }}</td>
                  </tr>
                  {% endif %}
                </table>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

{% endblock %}