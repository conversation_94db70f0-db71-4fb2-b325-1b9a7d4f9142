{% extends 'main/base.html' %}
{% load static %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
  }
  
  .form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e0e6ed;
    padding: 15px 20px;
    font-size: 16px;
    height: auto;
    min-height: 50px;
    line-height: 1.4;
  }
  
  .form-select {
    background-position: right 15px center;
    background-size: 16px 12px;
    padding-right: 45px;
  }
  
  .form-control-lg, .form-select-lg {
    min-height: 55px;
    font-size: 17px;
    padding: 15px 20px;
  }
  
  .form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
  }
  
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    font-size: 15px;
  }
  
  .schedule-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    border: 2px solid #e9ecef;
  }
  
  .schedule-section.active {
    background: #e3f2fd;
    border-color: #2196f3;
  }
  
  .weekday-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    margin-top: 10px;
  }
  
  .weekday-item {
    text-align: center;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .weekday-item.selected {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
  
  .info-box {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
  }
  
  .section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    margin-bottom: 20px;
  }
  
  .hidden {
    display: none !important;
  }
  
  .form-check-input-lg {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .form-text {
    font-size: 13px;
    color: #6c757d;
    margin-top: 5px;
  }
</style>

<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>{% if form.instance.pk %}Edit Feed Rule{% else %}Create Feed Rule{% endif %}</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:feed_detail' feed.pk %}">{{ feed.name }}</a></li>
                <li class="breadcrumb-item active">{% if form.instance.pk %}Edit Rule{% else %}Add Rule{% endif %}</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Feed Info Box -->
        <div class="info-box">
          <div class="d-flex align-items-center">
            <i class="fas fa-seedling fa-2x text-primary me-3"></i>
            <div>
              <h6 class="mb-1">Creating rule for: <strong>{{ feed.name }}</strong></h6>
              <small class="text-muted">Cost: ₹{{ feed.unit_cost|floatformat:2 }} per {{ feed.get_unit_display }}</small>
            </div>
          </div>
        </div>
        
        <!-- Form -->
        <div class="row justify-content-center">
          <div class="col-lg-12">
            <form method="post" id="feedRuleForm">
              {% csrf_token %}
              
              {% if messages %}
                {% for message in messages %}
                  <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                  </div>
                {% endfor %}
              {% endif %}
              
              <!-- Rule Basic Information -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Rule Information</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-12 mb-3">
                      <label for="{{ form.name.id_for_label }}" class="form-label">
                        Rule Name <span class="text-danger">*</span>
                      </label>
                      {{ form.name }}
                      {% if form.name.errors %}
                        <div class="text-danger small">
                          {% for error in form.name.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Descriptive name for this feeding rule</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Target Animals -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-crosshairs me-2"></i>Target Animals</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4 mb-3">
                      <label for="{{ form.target_animal_category.id_for_label }}" class="form-label">
                        Animal Category <span class="text-danger">*</span>
                      </label>
                      {{ form.target_animal_category }}
                      {% if form.target_animal_category.errors %}
                        <div class="text-danger small">
                          {% for error in form.target_animal_category.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                      <label for="{{ form.target_animal_type.id_for_label }}" class="form-label">
                        Animal Type <span class="text-danger">*</span>
                      </label>
                      {{ form.target_animal_type }}
                      {% if form.target_animal_type.errors %}
                        <div class="text-danger small">
                          {% for error in form.target_animal_type.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                      <label class="form-label">Age Range (months) <span class="text-danger">*</span></label>
                      <div class="row">
                        <div class="col-6">
                          {{ form.min_age_months }}
                          {% if form.min_age_months.errors %}
                            <div class="text-danger small">{{ form.min_age_months.errors.0 }}</div>
                          {% endif %}
                          <div class="form-text">Minimum age</div>
                        </div>
                        <div class="col-6">
                          {{ form.max_age_months }}
                          {% if form.max_age_months.errors %}
                            <div class="text-danger small">{{ form.max_age_months.errors.0 }}</div>
                          {% endif %}
                          <div class="form-text">Maximum age</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Dosage -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-prescription-bottle me-2"></i>Dosage</h5>
                </div>
                <div class="card-body">
                  <!-- Hidden frequency_days field for backward compatibility -->
                  <div style="display: none;">
                    {{ form.frequency_days }}
                  </div>
                  
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.dosage_qty.id_for_label }}" class="form-label">
                        Dosage Quantity <span class="text-danger">*</span>
                      </label>
                      {{ form.dosage_qty }}
                      {% if form.dosage_qty.errors %}
                        <div class="text-danger small">
                          {% for error in form.dosage_qty.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.dosage_unit.id_for_label }}" class="form-label">
                        Unit <span class="text-danger">*</span>
                      </label>
                      {{ form.dosage_unit }}
                      {% if form.dosage_unit.errors %}
                        <div class="text-danger small">
                          {% for error in form.dosage_unit.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <!-- Cost Preview -->
                  <div class="mt-3 p-3" style="background: #f8f9fa; border-radius: 10px;">
                    <h6 class="mb-2"><i class="fas fa-calculator me-2"></i>Cost Preview</h6>
                    <p class="mb-0 text-muted">
                      <span id="cost-preview">Enter dosage quantity to see estimated cost per feeding</span>
                    </p>
                  </div>
                </div>
              </div>
              
              <!-- Enhanced Scheduling -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Schedule Configuration</h5>
                </div>
                <div class="card-body">
                  
                  <!-- Schedule Type Selection -->
                  <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.schedule_type.id_for_label }}" class="form-label">
                        Schedule Type <span class="text-danger">*</span>
                      </label>
                      {{ form.schedule_type }}
                      {% if form.schedule_type.errors %}
                        <div class="text-danger small">
                          {% for error in form.schedule_type.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.delivery_time.id_for_label }}" class="form-label">
                        Delivery Time <span class="text-danger">*</span>
                      </label>
                      {{ form.delivery_time }}
                      {% if form.delivery_time.errors %}
                        <div class="text-danger small">
                          {% for error in form.delivery_time.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <!-- Daily Schedule Options -->
                  <div id="daily-options" class="schedule-section hidden">
                    <h6 class="text-primary mb-3"><i class="fas fa-sun me-2"></i>Daily Schedule Options</h6>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.daily_type.id_for_label }}" class="form-label">Daily Type</label>
                        {{ form.daily_type }}
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.interval_days.id_for_label }}" class="form-label">Every N Days</label>
                        {{ form.interval_days }}
                        <div class="form-text">For "Every N days" option</div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Weekly Schedule Options -->
                  <div id="weekly-options" class="schedule-section hidden">
                    <h6 class="text-success mb-3"><i class="fas fa-calendar-week me-2"></i>Weekly Schedule Options</h6>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.weekly_type.id_for_label }}" class="form-label">Weekly Type</label>
                        {{ form.weekly_type }}
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.interval_weeks.id_for_label }}" class="form-label">Every N Weeks</label>
                        {{ form.interval_weeks }}
                        <div class="form-text">For "Every N weeks" option</div>
                      </div>
                    </div>
                    
                    <!-- Weekday Selection -->
                    <div class="mt-3">
                      <label class="form-label">Select Days of Week</label>
                      <div class="weekday-grid">
                        <div class="weekday-item" data-day="monday">
                          {{ form.monday }}
                          <label for="{{ form.monday.id_for_label }}">Mon</label>
                        </div>
                        <div class="weekday-item" data-day="tuesday">
                          {{ form.tuesday }}
                          <label for="{{ form.tuesday.id_for_label }}">Tue</label>
                        </div>
                        <div class="weekday-item" data-day="wednesday">
                          {{ form.wednesday }}
                          <label for="{{ form.wednesday.id_for_label }}">Wed</label>
                        </div>
                        <div class="weekday-item" data-day="thursday">
                          {{ form.thursday }}
                          <label for="{{ form.thursday.id_for_label }}">Thu</label>
                        </div>
                        <div class="weekday-item" data-day="friday">
                          {{ form.friday }}
                          <label for="{{ form.friday.id_for_label }}">Fri</label>
                        </div>
                        <div class="weekday-item" data-day="saturday">
                          {{ form.saturday }}
                          <label for="{{ form.saturday.id_for_label }}">Sat</label>
                        </div>
                        <div class="weekday-item" data-day="sunday">
                          {{ form.sunday }}
                          <label for="{{ form.sunday.id_for_label }}">Sun</label>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Monthly Schedule Options -->
                  <div id="monthly-options" class="schedule-section hidden">
                    <h6 class="text-warning mb-3"><i class="fas fa-calendar me-2"></i>Monthly Schedule Options</h6>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.monthly_type.id_for_label }}" class="form-label">Monthly Type</label>
                        {{ form.monthly_type }}
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.interval_months.id_for_label }}" class="form-label">Every N Months</label>
                        {{ form.interval_months }}
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-md-4 mb-3">
                        <label for="{{ form.day_of_month.id_for_label }}" class="form-label">Day of Month</label>
                        {{ form.day_of_month }}
                        <div class="form-text">For "Specific date" option</div>
                      </div>
                      <div class="col-md-4 mb-3">
                        <label for="{{ form.week_of_month.id_for_label }}" class="form-label">Week of Month</label>
                        {{ form.week_of_month }}
                        <div class="form-text">1=First, 5=Last</div>
                      </div>
                      <div class="col-md-4 mb-3">
                        <label for="{{ form.weekday_of_month.id_for_label }}" class="form-label">Weekday</label>
                        {{ form.weekday_of_month }}
                        <div class="form-text">For "Specific weekday" option</div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Yearly Schedule Options -->
                  <div id="yearly-options" class="schedule-section hidden">
                    <h6 class="text-info mb-3"><i class="fas fa-calendar-alt me-2"></i>Yearly Schedule Options</h6>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.interval_years.id_for_label }}" class="form-label">Every N Years</label>
                        {{ form.interval_years }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- Custom Interval Options -->
                  <div id="custom-options" class="schedule-section hidden">
                    <h6 class="text-secondary mb-3"><i class="fas fa-cog me-2"></i>Custom Interval Options</h6>
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.custom_interval_value.id_for_label }}" class="form-label">Interval Value</label>
                        {{ form.custom_interval_value }}
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="{{ form.custom_interval_unit.id_for_label }}" class="form-label">Interval Unit</label>
                        {{ form.custom_interval_unit }}
                      </div>
                    </div>
                  </div>
                  
                </div>
              </div>
              
              <!-- End Conditions and Validity -->
              <div class="card card-modern mb-4">
                <div class="section-header">
                  <h5 class="mb-0"><i class="fas fa-stop-circle me-2"></i>End Conditions & Validity</h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4 mb-3">
                      <label for="{{ form.end_type.id_for_label }}" class="form-label">
                        End Condition <span class="text-danger">*</span>
                      </label>
                      {{ form.end_type }}
                      {% if form.end_type.errors %}
                        <div class="text-danger small">
                          {% for error in form.end_type.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                      <label for="{{ form.start_date.id_for_label }}" class="form-label">
                        Start Date <span class="text-danger">*</span>
                      </label>
                      {{ form.start_date }}
                      {% if form.start_date.errors %}
                        <div class="text-danger small">
                          {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3" id="end-date-field">
                      <label for="{{ form.end_date.id_for_label }}" class="form-label">
                        End Date <span class="text-danger">*</span>
                      </label>
                      {{ form.end_date }}
                      {% if form.end_date.errors %}
                        <div class="text-danger small">
                          {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3 hidden" id="max-occurrences-field">
                      <label for="{{ form.max_occurrences.id_for_label }}" class="form-label">
                        Maximum Occurrences
                      </label>
                      {{ form.max_occurrences }}
                      {% if form.max_occurrences.errors %}
                        <div class="text-danger small">
                          {% for error in form.max_occurrences.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.end_time.id_for_label }}" class="form-label">
                        End Time (Optional)
                      </label>
                      {{ form.end_time }}
                      <div class="form-text">Leave empty if feed doesn't have duration</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Exception Dates (Optional) -->
              <div class="card card-modern mb-4">
                <div class="card-header">
                  <h6 class="mb-0"><i class="fas fa-ban me-2"></i>Exception Dates (Optional)</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-12 mb-3">
                      <label for="{{ form.exception_dates.id_for_label }}" class="form-label">
                        Dates to Skip
                      </label>
                      {{ form.exception_dates }}
                      {% if form.exception_dates.errors %}
                        <div class="text-danger small">
                          {% for error in form.exception_dates.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Enter dates to skip (holidays, special occasions) in YYYY-MM-DD format, one per line</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="card card-modern">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <a href="{% url 'feeds:feed_detail' feed.pk %}" class="btn btn-secondary btn-modern">
                      <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary btn-modern">
                      <i class="fas fa-save me-2"></i>
                      {% if form.instance.pk %}Update Feed Rule{% else %}Create Feed Rule{% endif %}
                    </button>
                  </div>
                </div>
              </div>
              
            </form>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Schedule type change handler
    const scheduleTypeSelect = document.getElementById('id_schedule_type');
    const endTypeSelect = document.getElementById('id_end_type');
    
    function toggleScheduleOptions() {
        const scheduleType = scheduleTypeSelect.value;
        
        // Hide all schedule sections
        document.querySelectorAll('.schedule-section').forEach(section => {
            section.classList.add('hidden');
            section.classList.remove('active');
        });
        
        // Show relevant section
        const sectionMap = {
            'daily': 'daily-options',
            'weekly': 'weekly-options',
            'monthly': 'monthly-options',
            'yearly': 'yearly-options',
            'custom': 'custom-options',
            'weekdays': 'daily-options'  // Weekdays use daily options
        };
        
        if (sectionMap[scheduleType]) {
            const targetSection = document.getElementById(sectionMap[scheduleType]);
            if (targetSection) {
                targetSection.classList.remove('hidden');
                targetSection.classList.add('active');
            }
        }
    }
    
    function toggleEndConditionFields() {
        const endType = endTypeSelect.value;
        const endDateField = document.getElementById('end-date-field');
        const maxOccurrencesField = document.getElementById('max-occurrences-field');
        
        if (endType === 'occurrences') {
            endDateField.classList.add('hidden');
            maxOccurrencesField.classList.remove('hidden');
        } else if (endType === 'never') {
            endDateField.classList.add('hidden');
            maxOccurrencesField.classList.add('hidden');
        } else {
            endDateField.classList.remove('hidden');
            maxOccurrencesField.classList.add('hidden');
        }
    }
    
    // Event listeners
    scheduleTypeSelect.addEventListener('change', toggleScheduleOptions);
    endTypeSelect.addEventListener('change', toggleEndConditionFields);
    
    // Initial setup
    toggleScheduleOptions();
    toggleEndConditionFields();
    
    // Weekday selection styling
    document.querySelectorAll('.weekday-item').forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        
        function updateWeekdayStyle() {
            if (checkbox.checked) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        }
        
        item.addEventListener('click', function(e) {
            if (e.target.tagName !== 'INPUT') {
                checkbox.checked = !checkbox.checked;
                updateWeekdayStyle();
            }
        });
        
        checkbox.addEventListener('change', updateWeekdayStyle);
        updateWeekdayStyle(); // Initial setup
    });
    
    // Cost calculation
    const dosageQtyInput = document.querySelector('#id_dosage_qty');
    const costPreview = document.querySelector('#cost-preview');
    const unitCost = {{ feed.unit_cost }};
    
    function updateCostPreview() {
        const qty = parseFloat(dosageQtyInput.value) || 0;
        const cost = qty * unitCost;
        if (qty > 0) {
            costPreview.textContent = `Estimated cost per feeding: ₹${cost.toFixed(2)}`;
        } else {
            costPreview.textContent = 'Enter dosage quantity to see estimated cost per feeding';
        }
    }
    
    if (dosageQtyInput) {
        dosageQtyInput.addEventListener('input', updateCostPreview);
        updateCostPreview(); // Initial calculation
    }
    
    // Sync frequency_days with interval_days for backward compatibility
    const intervalDaysInput = document.querySelector('#id_interval_days');
    const frequencyDaysInput = document.querySelector('#id_frequency_days');
    
    if (intervalDaysInput && frequencyDaysInput) {
        intervalDaysInput.addEventListener('input', function() {
            frequencyDaysInput.value = intervalDaysInput.value || 1;
        });
        
        // Initial sync
        frequencyDaysInput.value = intervalDaysInput.value || 1;
    }
    
    // Set default values
    const startDateInput = document.querySelector('#id_start_date');
    const endDateInput = document.querySelector('#id_end_date');
    const deliveryTimeInput = document.querySelector('#id_delivery_time');
    
    if (startDateInput && !startDateInput.value) {
        const today = new Date();
        startDateInput.value = today.toISOString().split('T')[0];
    }
    
    if (endDateInput && !endDateInput.value) {
        const oneYearLater = new Date();
        oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
        endDateInput.value = oneYearLater.toISOString().split('T')[0];
    }
    
    if (deliveryTimeInput && !deliveryTimeInput.value) {
        deliveryTimeInput.value = '08:00';
    }
});
</script>

{% endblock %}