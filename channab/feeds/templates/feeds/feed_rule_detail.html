{% extends 'main/base.html' %}
{% load static %}
{% load humanize %}
{% load feed_extras %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
  }
  
  .info-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
  }
  
  .stat-box {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 15px;
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
  }
  
  .rule-detail-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 15px 20px;
    margin-bottom: 20px;
  }
  
  .formula-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-weight: 500;
    display: inline-block;
    margin: 2px 3px;
    font-size: 12px;
  }
  
  .activity-card {
    border-left: 4px solid #28a745;
    margin-bottom: 15px;
  }
  
  .activity-card.planned {
    border-left-color: #ffc107;
  }
  
  .activity-card.skipped {
    border-left-color: #dc3545;
  }
  
  .activity-card.missed {
    border-left-color: #6c757d;
  }
  
  .schedule-table {
    font-size: 0.9rem;
  }
  
  .schedule-table td {
    padding: 0.5rem;
  }
</style>

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>{{ rule.name }} - Rule Details</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:feed_detail' rule.feed.pk %}">{{ rule.feed.name }}</a></li>
                <li class="breadcrumb-item active">{{ rule.name }}</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Rule Information -->
        <div class="row mb-3">
          <div class="col-12">
            <div class="rule-detail-box">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                  <h5 class="mb-1">{{ rule.name }}</h5>
                  <small class="opacity-75">Feed: {{ rule.feed.name }}</small>
                </div>
                <div>
                  {% if rule.status == 'scheduled' %}
                    <span class="badge bg-success">Active</span>
                  {% elif rule.status == 'paused' %}
                    <span class="badge bg-warning">Paused</span>
                  {% elif rule.status == 'expired' %}
                    <span class="badge bg-danger">Expired</span>
                  {% endif %}
                </div>
              </div>
              
              <div class="row">
                <!-- Target Animals Column -->
                <div class="col-md-4">
                  <h6 class="mb-1" style="font-size: 13px;">Target:</h6>
                  {% if rule.target_all_animals %}
                    <span class="formula-badge">All Animals</span>
                  {% else %}
                    {% if rule.target_animal_categories %}
                      {% for category in rule.target_animal_categories %}
                        <span class="formula-badge">{{ category|title }}</span>
                      {% endfor %}
                    {% elif rule.target_animal_category %}
                      <span class="formula-badge">{{ rule.get_target_animal_category_display }}</span>
                    {% endif %}
                    
                    {% if rule.target_animal_types %}
                      {% for type in rule.target_animal_types %}
                        <span class="formula-badge">{{ type|title }}</span>
                      {% endfor %}
                    {% elif rule.target_animal_type %}
                      <span class="formula-badge">{{ rule.get_target_animal_type_display }}</span>
                    {% endif %}
                    
                    {% if not rule.target_animal_categories and not rule.target_animal_category and not rule.target_animal_types and not rule.target_animal_type %}
                      <span class="formula-badge">Any Animal</span>
                    {% endif %}
                  {% endif %}
                  
                  {% if rule.use_age_filter %}
                    <div class="mt-1">
                      <small class="opacity-75" style="font-size: 11px;">Age: {{ rule.min_age_months|default:"0" }}-{{ rule.max_age_months|default:"∞" }}m</small>
                    </div>
                  {% endif %}
                </div>
                
                <!-- Formula Column -->
                <div class="col-md-4">
                  <h6 class="mb-1" style="font-size: 13px;">Formula:</h6>
                  <span class="formula-badge">{{ rule.get_formula_type_display }}</span>
                  <span class="formula-badge">{{ rule.get_formula_application_display }}</span>
                  {% if rule.formula_type == 'fixed' %}
                    <div class="mt-1"><small class="opacity-75" style="font-size: 11px;">{{ rule.dosage_qty }} {{ rule.get_dosage_unit_display }}</small></div>
                  {% elif rule.formula_type == 'per_animal' %}
                    <div class="mt-1"><small class="opacity-75" style="font-size: 11px;">{{ rule.formula_value }} per animal</small></div>
                  {% elif rule.formula_type == 'per_kg_weight' %}
                    <div class="mt-1"><small class="opacity-75" style="font-size: 11px;">{{ rule.formula_value }} per kg weight</small></div>
                  {% elif rule.formula_type == 'percentage_weight' %}
                    <div class="mt-1"><small class="opacity-75" style="font-size: 11px;">{{ rule.formula_value }}% of weight</small></div>
                  {% elif rule.formula_type == 'custom_formula' %}
                    <div class="mt-1"><small class="opacity-75" style="font-size: 11px;">{{ rule.custom_formula }}</small></div>
                  {% endif %}
                </div>
                
                <!-- Schedule Column -->
                <div class="col-md-4">
                  <h6 class="mb-1" style="font-size: 13px;">Schedule:</h6>
                  <span class="formula-badge">{{ rule.get_schedule_type_display }}</span>
                  <span class="formula-badge">{{ rule.delivery_time }}</span>
                  <div class="mt-1">
                    <small class="opacity-75" style="font-size: 11px;">
                      {{ rule.start_date|date:"M d" }} to {{ rule.end_date|date:"M d, Y" }}
                      {% if rule.frequency_days %}
                        • Every {{ rule.frequency_days }}d
                      {% endif %}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Feed Calculation Summary -->
        <div class="row mb-3">
          <div class="col-12">
            <div class="card card-modern" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
              <div class="card-body text-white">
                <h5 class="mb-3">Feed Calculation Summary 
                  <small class="text-light opacity-75">(Updated: {{ rule.modified_at|date:"M d, H:i" }})</small>
                </h5>
                
                <!-- Basic Calculation Row -->
                <div class="row mb-3">
                  <div class="col-md-3">
                    <div class="text-center">
                      <h2 class="mb-1">{{ dosage_calculation.animal_count }}</h2>
                      <p class="mb-0">Matching Animals</p>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h2 class="mb-1">{{ cost_stats.total_feeds_in_period }}</h2>
                      <p class="mb-0">Total Feeds</p>
                      <small class="opacity-75">({{ cost_stats.start_date|date:"M d" }} - {{ cost_stats.end_date|date:"M d" }})</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h2 class="mb-1">{{ cost_stats.total_rule_days }}</h2>
                      <p class="mb-0">Total Days</p>
                      <small class="opacity-75">(Every {{ cost_stats.frequency_days }} days)</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h2 class="mb-1">₹{{ cost_stats.total_cost_for_period|floatformat:2 }}</h2>
                      <p class="mb-0">Total Cost</p>
                      <small class="opacity-75">(Entire period)</small>
                    </div>
                  </div>
                </div>
                
                <!-- Progress Tracking Row -->
                <div class="row mb-3">
                  <div class="col-md-3">
                    <div class="text-center">
                      <h3 class="mb-1 text-success">{{ activity_stats.accepted }}</h3>
                      <p class="mb-0">Feeds Completed</p>
                      <small class="opacity-75">{{ cost_stats.total_given_quantity|floatformat:1 }} {{ rule.get_dosage_unit_display }}</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h3 class="mb-1 text-info">{{ cost_stats.expected_feeds_so_far }}</h3>
                      <p class="mb-0">Expected So Far</p>
                      <small class="opacity-75">{{ cost_stats.expected_quantity_so_far|floatformat:1 }} {{ rule.get_dosage_unit_display }}</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h3 class="mb-1 text-warning">{{ cost_stats.remaining_feeds }}</h3>
                      <p class="mb-0">Feeds Remaining</p>
                      <small class="opacity-75">{{ cost_stats.remaining_quantity|floatformat:1 }} {{ rule.get_dosage_unit_display }}</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h3 class="mb-1 text-primary">{{ cost_stats.upcoming_feeds_30_days }}</h3>
                      <p class="mb-0">Next 30 Days</p>
                      <small class="opacity-75">{{ cost_stats.upcoming_quantity_30_days|floatformat:1 }} {{ rule.get_dosage_unit_display }}</small>
                    </div>
                  </div>
                </div>
                
                <!-- Cost Tracking Row -->
                <div class="row">
                  <div class="col-md-4">
                    <div class="text-center">
                      <h4 class="mb-1">₹{{ cost_stats.total_cost_spent|floatformat:2 }}</h4>
                      <p class="mb-0">Cost Spent</p>
                      <small class="opacity-75">(Actual)</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="text-center">
                      <h4 class="mb-1">₹{{ cost_stats.expected_cost_so_far|floatformat:2 }}</h4>
                      <p class="mb-0">Expected Cost</p>
                      <small class="opacity-75">(So far)</small>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="text-center">
                      <h4 class="mb-1">₹{{ cost_stats.remaining_cost|floatformat:2 }}</h4>
                      <p class="mb-0">Remaining Cost</p>
                      <small class="opacity-75">({{ cost_stats.days_until_end }} days left)</small>
                    </div>
                  </div>
                </div>
                
                <!-- Progress Bar -->
                {% if cost_stats.expected_feeds_so_far > 0 %}
                <div class="row mt-3">
                  <div class="col-12">
                    <div class="text-center mb-2">
                      <small class="opacity-75">
                        Progress: {{ activity_stats.accepted }} of {{ cost_stats.expected_feeds_so_far }} expected feeds completed
                        ({{ cost_stats.days_since_start }} of {{ cost_stats.total_rule_days }} days)
                      </small>
                    </div>
                    <div class="progress" style="height: 10px;">
                      {% with completion_percent=activity_stats.accepted|percentage:cost_stats.expected_feeds_so_far %}
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {% if completion_percent > 100 %}100{% else %}{{ completion_percent|floatformat:0 }}{% endif %}%"
                             aria-valuenow="{{ completion_percent|floatformat:0 }}" 
                             aria-valuemin="0" aria-valuemax="100">
                        </div>
                      {% endwith %}
                    </div>
                  </div>
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        
        
        <!-- Content Tabs -->
        <div class="row">
          <!-- Feed Schedule -->
          <div class="col-lg-8">
            <div class="card card-modern">
              <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0">Feed Schedule & Activities</h5>
                  {% if rule.status == 'scheduled' %}
                    <span class="badge bg-success">Rule is Active</span>
                  {% elif rule.status == 'paused' %}
                    <span class="badge bg-warning">Rule is Paused</span>
                  {% endif %}
                </div>
              </div>
              <div class="card-body">
                {% if schedule_page %}
                  <div class="table-responsive">
                    <table class="table table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>Date</th>
                          <th>Status</th>
                          <th>Expected</th>
                          <th>Actual</th>
                          <th>Cost</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for feed in schedule_page %}
                        <tr class="{% if feed.is_today %}table-info{% elif feed.is_past %}table-light{% endif %}">
                          <td>
                            <strong>{{ feed.date|date:"M d, Y" }}</strong>
                            {% if feed.is_today %}
                              <span class="badge bg-info ms-2">Today</span>
                            {% elif feed.is_past %}
                              <span class="badge bg-secondary ms-2">Past</span>
                            {% else %}
                              <span class="badge bg-primary ms-2">Future</span>
                            {% endif %}
                          </td>
                          <td>
                            {% if feed.status == 'accepted' %}
                              <span class="badge bg-success">Completed</span>
                            {% elif feed.status == 'planned' %}
                              <span class="badge bg-warning">Planned</span>
                            {% elif feed.status == 'skipped' %}
                              <span class="badge bg-danger">Skipped</span>
                            {% elif feed.status == 'missed' %}
                              <span class="badge bg-secondary">Missed</span>
                            {% elif feed.status == 'due_today' %}
                              <span class="badge bg-info">Due Today</span>
                            {% elif feed.status == 'upcoming' %}
                              <span class="badge bg-light text-dark">Upcoming</span>
                            {% endif %}
                          </td>
                          <td>
                            <small class="text-muted">
                              {{ feed.matching_animals_count }} animals<br>
                              {{ feed.expected_dosage|floatformat:1 }} {{ rule.get_dosage_unit_display }}
                            </small>
                          </td>
                          <td>
                            {% if feed.activity %}
                              <small class="text-success">
                                {{ feed.activity.animal.tag }}<br>
                                {{ feed.activity.dosage_qty|floatformat:1 }} {{ feed.activity.get_dosage_unit_display }}
                              </small>
                            {% else %}
                              <small class="text-muted">-</small>
                            {% endif %}
                          </td>
                          <td>
                            {% if feed.activity %}
                              <span class="text-success">₹{{ feed.activity.total_cost|floatformat:2 }}</span>
                            {% else %}
                              <span class="text-muted">₹{{ feed.expected_cost|floatformat:2 }}</span>
                            {% endif %}
                          </td>
                          <td>
                            {% if feed.status == 'planned' %}
                              <div class="btn-group-vertical" role="group">
                                <button class="btn btn-sm btn-success" 
                                        onclick="completeAllForDate('{{ feed.date|date:"Y-m-d" }}')"
                                        title="Mark all {{ feed.activity.total_activities|default:1 }} activities as completed">
                                  <i class="fas fa-check"></i> Complete All
                                  {% if feed.activity.total_activities > 1 %}
                                    ({{ feed.activity.total_activities }})
                                  {% endif %}
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="skipAllForDate('{{ feed.date|date:"Y-m-d" }}')"
                                        title="Skip all {{ feed.activity.total_activities|default:1 }} activities">
                                  <i class="fas fa-times"></i> Skip All
                                  {% if feed.activity.total_activities > 1 %}
                                    ({{ feed.activity.total_activities }})
                                  {% endif %}
                                </button>
                              </div>
                            {% elif feed.status == 'due_today' %}
                              <div class="btn-group-vertical" role="group">
                                <button class="btn btn-sm btn-success" 
                                        onclick="alert('Button clicked!'); completeFeeding('{{ feed.date|date:"Y-m-d" }}')"
                                        title="Mark feeding as completed for all animals">
                                  <i class="fas fa-check"></i> Complete
                                </button>
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="skipFeeding('{{ feed.date|date:"Y-m-d" }}')"
                                        title="Skip feeding for today">
                                  <i class="fas fa-times"></i> Skip
                                </button>
                              </div>
                            {% elif feed.status == 'upcoming' %}
                              <small class="text-muted">Scheduled</small>
                            {% elif feed.status == 'accepted' %}
                              <small class="text-success">
                                <i class="fas fa-check-circle"></i> Done
                                {% if feed.activity.accepted_at %}
                                  <br>{{ feed.activity.accepted_at|date:"M d, H:i" }}
                                {% endif %}
                              </small>
                            {% elif feed.status == 'skipped' %}
                              <small class="text-danger">
                                <i class="fas fa-times-circle"></i> Skipped
                              </small>
                            {% elif feed.status == 'missed' %}
                              <small class="text-secondary">
                                <i class="fas fa-exclamation-circle"></i> Missed
                              </small>
                            {% endif %}
                          </td>
                        </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                  
                  <!-- Schedule Pagination -->
                  {% if schedule_page.has_other_pages %}
                  <nav aria-label="Schedule pagination" class="mt-3">
                    <ul class="pagination justify-content-center">
                      {% if schedule_page.has_previous %}
                        <li class="page-item">
                          <a class="page-link" href="?schedule_page={{ schedule_page.previous_page_number }}">Previous</a>
                        </li>
                      {% endif %}
                      
                      <li class="page-item active">
                        <span class="page-link">
                          Page {{ schedule_page.number }} of {{ schedule_page.paginator.num_pages }}
                        </span>
                      </li>
                      
                      {% if schedule_page.has_next %}
                        <li class="page-item">
                          <a class="page-link" href="?schedule_page={{ schedule_page.next_page_number }}">Next</a>
                        </li>
                      {% endif %}
                    </ul>
                  </nav>
                  {% endif %}
                  
                {% else %}
                  <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No Schedule Found</h6>
                    <p class="text-muted">Feed schedule will appear here based on rule frequency.</p>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
          
          <!-- Quick Actions & Info -->
          <div class="col-lg-4">
            <div class="card card-modern">
              <div class="card-header">
                <h6 class="mb-0">Quick Actions</h6>
              </div>
              <div class="card-body">
                <div class="d-grid gap-2">
                  <a href="{% url 'feeds:feed_rule_edit' rule.pk %}" class="btn btn-primary btn-modern">
                    <i class="fas fa-edit me-2"></i>Edit Rule
                  </a>
                  
                  {% if rule.status == 'scheduled' %}
                    <form method="post" action="{% url 'feeds:feed_rule_toggle_status' rule.pk %}">
                      {% csrf_token %}
                      <button type="submit" class="btn btn-warning btn-modern w-100">
                        <i class="fas fa-pause me-2"></i>Pause Rule
                      </button>
                    </form>
                  {% elif rule.status == 'paused' %}
                    <form method="post" action="{% url 'feeds:feed_rule_toggle_status' rule.pk %}">
                      {% csrf_token %}
                      <button type="submit" class="btn btn-success btn-modern w-100">
                        <i class="fas fa-play me-2"></i>Resume Rule
                      </button>
                    </form>
                  {% endif %}
                  
                  <a href="{% url 'feeds:feed_detail' rule.feed.pk %}" class="btn btn-outline-secondary btn-modern">
                    <i class="fas fa-arrow-left me-2"></i>Back to Feed
                  </a>
                </div>
              </div>
            </div>
            
            
            <!-- Recent Activities Summary -->
            {% if activities_page %}
            <div class="card card-modern mt-3">
              <div class="card-header">
                <h6 class="mb-0">Recent Activities</h6>
              </div>
              <div class="card-body">
                {% for activity in activities_page|slice:":5" %}
                  <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div>
                      <small class="fw-bold">{{ activity.animal.tag }}</small><br>
                      <small class="text-muted">{{ activity.scheduled_for|date:"M d" }}</small>
                    </div>
                    <div class="text-end">
                      {% if activity.status == 'accepted' %}
                        <span class="badge bg-success">✓</span>
                      {% elif activity.status == 'planned' %}
                        <span class="badge bg-warning">⏰</span>
                      {% elif activity.status == 'skipped' %}
                        <span class="badge bg-danger">✗</span>
                      {% elif activity.status == 'missed' %}
                        <span class="badge bg-secondary">!</span>
                      {% endif %}
                    </div>
                  </div>
                {% endfor %}
                
                {% if activities_page.count > 5 %}
                <div class="text-center mt-2">
                  <small class="text-muted">See full schedule in main panel</small>
                </div>
                {% endif %}
              </div>
            </div>
            {% endif %}
            
            <!-- Rule Details -->
            <div class="card card-modern mt-3">
              <div class="card-header">
                <h6 class="mb-0">Rule Details</h6>
              </div>
              <div class="card-body">
                <table class="table table-borderless table-sm" style="font-size: 13px;">
                  <tr>
                    <td><strong>Created:</strong></td>
                    <td>{{ rule.created_at|naturaltime }}</td>
                  </tr>
                  <tr>
                    <td><strong>Last Modified:</strong></td>
                    <td>{{ rule.modified_at|naturaltime }}</td>
                  </tr>
                  <tr>
                    <td><strong>Created By:</strong></td>
                    <td>{{ rule.created_by.get_full_name|default:rule.created_by.mobile }}</td>
                  </tr>
                  <tr>
                    <td><strong>Status:</strong></td>
                    <td>
                      {% if rule.status == 'scheduled' %}
                        <span class="badge bg-success">Active</span>
                      {% elif rule.status == 'paused' %}
                        <span class="badge bg-warning">Paused</span>
                      {% elif rule.status == 'expired' %}
                        <span class="badge bg-danger">Expired</span>
                      {% endif %}
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Per Feed:</strong></td>
                    <td>₹{{ cost_stats.per_feed_cost|floatformat:2 }}</td>
                  </tr>
                  <tr>
                    <td><strong>Per Animal:</strong></td>
                    <td>₹{{ cost_stats.cost_per_animal|floatformat:2 }}</td>
                  </tr>
                  <tr>
                    <td><strong>Total Budget:</strong></td>
                    <td>₹{{ cost_stats.total_cost_for_period|floatformat:2 }}</td>
                  </tr>
                  {% if cost_stats.projected_cost_30_days %}
                  <tr>
                    <td><strong>Next 30 Days:</strong></td>
                    <td>₹{{ cost_stats.projected_cost_30_days|floatformat:2 }}</td>
                  </tr>
                  {% endif %}
                  <tr>
                    <td><strong>Schedule:</strong></td>
                    <td>Every {{ cost_stats.frequency_days }} days</td>
                  </tr>
                  <tr>
                    <td><strong>Period:</strong></td>
                    <td>{{ rule.start_date|date:"M d, Y" }} to {{ rule.end_date|date:"M d, Y" }}</td>
                  </tr>
                </table>
                
                <!-- Debug info (remove in production) -->
                {% if user.is_superuser %}
                <div class="mt-2 p-2 bg-light rounded">
                  <small class="text-muted">
                    <strong>Debug Info for Rule {{ rule.pk }}:</strong><br>
                    <strong>Targeting:</strong> 
                    {% if rule.target_all_animals %}
                      All Animals
                    {% else %}
                      Categories: {{ rule.target_animal_categories|default:"None" }}, 
                      Types: {{ rule.target_animal_types|default:"None" }}
                    {% endif %}<br>
                    <strong>Age Filter:</strong> {{ rule.use_age_filter }} 
                    {% if rule.use_age_filter %}
                      ({{ rule.min_age_months }}-{{ rule.max_age_months }} months)
                    {% endif %}<br>
                    <strong>Total Farm Animals:</strong> {{ debug_info.total_active_animals }}<br>
                    <strong>Final Matching:</strong> {{ debug_info.final_matching_count }}<br>
                    {% if debug_info.available_categories %}
                      <strong>Available Categories:</strong> {{ debug_info.available_categories|join:", " }}<br>
                    {% endif %}
                    {% if debug_info.available_types %}
                      <strong>Available Types:</strong> {{ debug_info.available_types|join:", " }}<br>
                    {% endif %}
                    
                    {% if dosage_calculation.animal_count == 0 %}
                    <br><span class="text-danger"><strong>⚠️ Zero animals matching! Possible issues:</strong></span>
                    {% if rule.use_age_filter and rule.min_age_months and rule.max_age_months %}
                      {% if rule.min_age_months > rule.max_age_months %}
                        <br><span class="text-danger">❌ Invalid age range: min_age ({{ rule.min_age_months }}) > max_age ({{ rule.max_age_months }})</span>
                      {% endif %}
                    {% endif %}
                    {% if not rule.target_all_animals and not rule.target_animal_categories and not rule.target_animal_category and not rule.target_animal_types and not rule.target_animal_type %}
                      <br><span class="text-danger">❌ No target criteria selected (not targeting all animals and no categories/types selected)</span>
                    {% endif %}
                    {% if rule.target_animal_categories %}
                      <br><span class="text-warning">🔍 Filtering by categories: {{ rule.target_animal_categories|join:", " }}</span>
                      {% if debug_info.available_categories %}
                        <br><span class="text-info">ℹ️ Available categories: {{ debug_info.available_categories|join:", " }}</span>
                      {% endif %}
                    {% endif %}
                    {% if rule.target_animal_types %}
                      <br><span class="text-warning">🔍 Filtering by types: {{ rule.target_animal_types|join:", " }}</span>
                      {% if debug_info.available_types %}
                        <br><span class="text-info">ℹ️ Available types: {{ debug_info.available_types|join:", " }}</span>
                      {% endif %}
                    {% endif %}
                    {% if rule.use_age_filter %}
                      <br><span class="text-warning">🔍 Age filter active: {{ rule.min_age_months }}-{{ rule.max_age_months }} months</span>
                      <br><span class="text-info">ℹ️ Animals with DOB: {{ debug_info.animals_with_dob }} of {{ debug_info.total_active_animals }}</span>
                      <br><span class="text-info">ℹ️ Animals without DOB: {{ debug_info.animals_without_dob }} (will be included)</span>
                    {% endif %}
                    {% endif %}
                  </small>
                </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

{% csrf_token %}
<script>
function completeFeeding(date) {
    console.log('completeFeeding called with date:', date);
    
    // Confirm with user before completing
    if (!confirm(`Mark feeding as completed for ${date}?\n\nThis will create and complete feed activities for all matching animals.`)) {
        console.log('User cancelled confirmation');
        return;
    }
    
    console.log('User confirmed, proceeding with completion');
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Completing...';
    button.disabled = true;
    
    console.log('Button state changed, making AJAX call');
    
    // Make AJAX call to complete feeding
    const url = `{% url 'feeds:complete_feeding' rule.pk '1900-01-01' %}`.replace('1900-01-01', date);
    console.log('Making request to URL:', url);
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        console.log('Response received:', response);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            // Show success message and reload page
            alert(`Success: ${data.message}`);
            location.reload();
        } else {
            // Show error message and restore button
            alert(`Error: ${data.error}`);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error completing feeding:', error);
        alert('Error completing feeding. Please try again.');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function skipFeeding(date) {
    // Confirm with user before skipping
    if (!confirm(`Skip feeding for ${date}?\n\nThis will mark the feeding as skipped for all matching animals.`)) {
        return;
    }
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Skipping...';
    button.disabled = true;
    
    // Make AJAX call to skip feeding
    fetch(`{% url 'feeds:skip_feeding' rule.pk '1900-01-01' %}`.replace('1900-01-01', date), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message and reload page
            alert(`Success: ${data.message}`);
            location.reload();
        } else {
            // Show error message and restore button
            alert(`Error: ${data.error}`);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error skipping feeding:', error);
        alert('Error skipping feeding. Please try again.');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function completeAllForDate(date) {
    console.log('completeAllForDate called with date:', date);
    
    // Confirm with user before completing
    if (!confirm(`Mark ALL feeding activities as completed for ${date}?\n\nThis will complete all planned activities for this date.`)) {
        console.log('User cancelled confirmation');
        return;
    }
    
    console.log('User confirmed, proceeding with bulk completion');
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Completing...';
    button.disabled = true;
    
    // Make AJAX call to complete all activities for date
    const url = `{% url 'feeds:complete_all_for_date' rule.pk '1900-01-01' %}`.replace('1900-01-01', date);
    console.log('Making request to URL:', url);
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        console.log('Response received:', response);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            alert(`Success: ${data.message}`);
            location.reload();
        } else {
            alert(`Error: ${data.error}`);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error completing activities:', error);
        alert('Error completing activities. Please try again.');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function skipAllForDate(date) {
    console.log('skipAllForDate called with date:', date);
    
    // Confirm with user before skipping
    if (!confirm(`Skip ALL feeding activities for ${date}?\n\nThis will skip all planned activities for this date.`)) {
        console.log('User cancelled confirmation');
        return;
    }
    
    console.log('User confirmed, proceeding with bulk skip');
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Skipping...';
    button.disabled = true;
    
    // Make AJAX call to skip all activities for date
    const url = `{% url 'feeds:skip_all_for_date' rule.pk '1900-01-01' %}`.replace('1900-01-01', date);
    console.log('Making request to URL:', url);
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        console.log('Response received:', response);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            alert(`Success: ${data.message}`);
            location.reload();
        } else {
            alert(`Error: ${data.error}`);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error skipping activities:', error);
        alert('Error skipping activities. Please try again.');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>

{% endblock %}