{% extends 'main/base.html' %}
{% load static %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
  }
  
  .form-control {
    border-radius: 10px;
    border: 1px solid #e0e6ed;
    padding: 12px 15px;
  }
  
  .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }
  
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
  }
  
  .form-text {
    font-size: 12px;
    color: #6c757d;
  }
</style>

<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>{% if form.instance.pk %}Edit Feed Type{% else %}Add New Feed Type{% endif %}</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:feed_list' %}">Feed Types</a></li>
                <li class="breadcrumb-item active">{% if form.instance.pk %}Edit Feed{% else %}Add Feed{% endif %}</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Form -->
        <div class="row justify-content-center">
          <div class="col-lg-8">
            <div class="card card-modern">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-seedling me-2"></i>
                  {% if form.instance.pk %}Edit Feed Type{% else %}Create New Feed Type{% endif %}
                </h5>
              </div>
              <div class="card-body">
                
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                      {{ message }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                  {% endfor %}
                {% endif %}
                
                <form method="post">
                  {% csrf_token %}
                  
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.name.id_for_label }}" class="form-label">
                        Feed Name <span class="text-danger">*</span>
                      </label>
                      {{ form.name }}
                      {% if form.name.errors %}
                        <div class="text-danger small">
                          {% for error in form.name.errors %}
                            {{ error }}
                          {% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Enter a descriptive name for this feed type (e.g., "Wanda25", "Protein Mix")</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.unit.id_for_label }}" class="form-label">
                        Unit of Measurement <span class="text-danger">*</span>
                      </label>
                      {{ form.unit }}
                      {% if form.unit.errors %}
                        <div class="text-danger small">
                          {% for error in form.unit.errors %}
                            {{ error }}
                          {% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Select how this feed is measured</div>
                    </div>
                  </div>
                  
                  <div class="row">
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.unit_cost.id_for_label }}" class="form-label">
                        Cost per Unit (₹) <span class="text-danger">*</span>
                      </label>
                      <div class="input-group">
                        <span class="input-group-text">₹</span>
                        {{ form.unit_cost }}
                      </div>
                      {% if form.unit_cost.errors %}
                        <div class="text-danger small">
                          {% for error in form.unit_cost.errors %}
                            {{ error }}
                          {% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Enter the cost per unit (e.g., per kg, per liter)</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label class="form-label">Status</label>
                      <div class="form-check form-switch">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                          Feed is active and available for use
                        </label>
                      </div>
                      {% if form.is_active.errors %}
                        <div class="text-danger small">
                          {% for error in form.is_active.errors %}
                            {{ error }}
                          {% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <div class="mb-4">
                    <label for="{{ form.description.id_for_label }}" class="form-label">
                      Description
                    </label>
                    {{ form.description }}
                    {% if form.description.errors %}
                      <div class="text-danger small">
                        {% for error in form.description.errors %}
                          {{ error }}
                        {% endfor %}
                      </div>
                    {% endif %}
                    <div class="form-text">Optional: Add details about this feed type, its benefits, or usage instructions</div>
                  </div>
                  
                  <!-- Action Buttons -->
                  <div class="d-flex justify-content-between">
                    <a href="{% url 'feeds:feed_list' %}" class="btn btn-secondary btn-modern">
                      <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary btn-modern">
                      <i class="fas fa-save me-2"></i>
                      {% if form.instance.pk %}Update Feed Type{% else %}Create Feed Type{% endif %}
                    </button>
                  </div>
                  
                </form>
                
                <!-- Help Section -->
                {% if not form.instance.pk %}
                <div class="mt-5 p-4" style="background: #f8f9fa; border-radius: 10px;">
                  <h6 class="mb-3"><i class="fas fa-info-circle me-2"></i>What's Next?</h6>
                  <p class="mb-2">After creating this feed type, you can:</p>
                  <ul class="mb-0">
                    <li>Create feeding rules that automatically schedule this feed for specific animals</li>
                    <li>Set dosage amounts and feeding frequency</li>
                    <li>Track feeding costs and consumption</li>
                    <li>Monitor animal feeding activities</li>
                  </ul>
                </div>
                {% endif %}
                
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        if (!control.classList.contains('btn') && !control.classList.contains('form-check-input')) {
            control.classList.add('form-control');
        }
    });
    
    // Handle form switches
    const switches = document.querySelectorAll('input[type="checkbox"]');
    switches.forEach(function(switchEl) {
        switchEl.classList.add('form-check-input');
    });
});
</script>

{% endblock %}