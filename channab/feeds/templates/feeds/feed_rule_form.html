{% extends 'main/base.html' %}
{% load static %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
  }
  
  .form-control {
    border-radius: 10px;
    border: 1px solid #e0e6ed;
    padding: 12px 15px;
  }
  
  .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }
  
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
  }
  
  .info-box {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
  }
</style>

<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>{% if form.instance.pk %}Edit Feed Rule{% else %}Create Feed Rule{% endif %}</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:feed_detail' feed.pk %}">{{ feed.name }}</a></li>
                <li class="breadcrumb-item active">{% if form.instance.pk %}Edit Rule{% else %}Add Rule{% endif %}</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Feed Info Box -->
        <div class="info-box">
          <div class="d-flex align-items-center">
            <i class="fas fa-seedling fa-2x text-primary me-3"></i>
            <div>
              <h6 class="mb-1">Creating rule for: <strong>{{ feed.name }}</strong></h6>
              <small class="text-muted">Cost: ₹{{ feed.unit_cost|floatformat:2 }} per {{ feed.get_unit_display }}</small>
            </div>
          </div>
        </div>
        
        <!-- Form -->
        <div class="row justify-content-center">
          <div class="col-lg-10">
            <div class="card card-modern">
              <div class="card-header">
                <h5 class="mb-0">
                  <i class="fas fa-calendar-plus me-2"></i>
                  {% if form.instance.pk %}Edit Feeding Rule{% else %}Create New Feeding Rule{% endif %}
                </h5>
              </div>
              <div class="card-body">
                
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                      {{ message }}
                      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                  {% endfor %}
                {% endif %}
                
                <form method="post">
                  {% csrf_token %}
                  
                  <!-- Rule Basic Info -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>Rule Information</h6>
                    </div>
                    <div class="col-md-12 mb-3">
                      <label for="{{ form.name.id_for_label }}" class="form-label">
                        Rule Name <span class="text-danger">*</span>
                      </label>
                      {{ form.name }}
                      {% if form.name.errors %}
                        <div class="text-danger small">
                          {% for error in form.name.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">Descriptive name for this feeding rule</div>
                    </div>
                  </div>
                  
                  <!-- Target Animals -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="text-success mb-3"><i class="fas fa-crosshairs me-2"></i>Target Animals</h6>
                    </div>
                    <div class="col-md-4 mb-3">
                      <label for="{{ form.target_animal_category.id_for_label }}" class="form-label">
                        Animal Category <span class="text-danger">*</span>
                      </label>
                      {{ form.target_animal_category }}
                      {% if form.target_animal_category.errors %}
                        <div class="text-danger small">
                          {% for error in form.target_animal_category.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                      <label for="{{ form.target_animal_type.id_for_label }}" class="form-label">
                        Animal Type <span class="text-danger">*</span>
                      </label>
                      {{ form.target_animal_type }}
                      {% if form.target_animal_type.errors %}
                        <div class="text-danger small">
                          {% for error in form.target_animal_type.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-4 mb-3">
                      <label class="form-label">Age Range (months) <span class="text-danger">*</span></label>
                      <div class="row">
                        <div class="col-6">
                          {{ form.min_age_months }}
                          {% if form.min_age_months.errors %}
                            <div class="text-danger small">{{ form.min_age_months.errors.0 }}</div>
                          {% endif %}
                          <div class="form-text">Min</div>
                        </div>
                        <div class="col-6">
                          {{ form.max_age_months }}
                          {% if form.max_age_months.errors %}
                            <div class="text-danger small">{{ form.max_age_months.errors.0 }}</div>
                          {% endif %}
                          <div class="form-text">Max</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Dosage and Schedule -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="text-warning mb-3"><i class="fas fa-prescription-bottle me-2"></i>Dosage & Schedule</h6>
                    </div>
                    <div class="col-md-3 mb-3">
                      <label for="{{ form.dosage_qty.id_for_label }}" class="form-label">
                        Dosage Quantity <span class="text-danger">*</span>
                      </label>
                      {{ form.dosage_qty }}
                      {% if form.dosage_qty.errors %}
                        <div class="text-danger small">
                          {% for error in form.dosage_qty.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-3 mb-3">
                      <label for="{{ form.dosage_unit.id_for_label }}" class="form-label">
                        Unit <span class="text-danger">*</span>
                      </label>
                      {{ form.dosage_unit }}
                      {% if form.dosage_unit.errors %}
                        <div class="text-danger small">
                          {% for error in form.dosage_unit.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-3 mb-3">
                      <label for="{{ form.frequency_days.id_for_label }}" class="form-label">
                        Frequency (days) <span class="text-danger">*</span>
                      </label>
                      {{ form.frequency_days }}
                      {% if form.frequency_days.errors %}
                        <div class="text-danger small">
                          {% for error in form.frequency_days.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                      <div class="form-text">1=Daily, 2=Every 2 days</div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                      <label for="{{ form.delivery_time.id_for_label }}" class="form-label">
                        Delivery Time <span class="text-danger">*</span>
                      </label>
                      {{ form.delivery_time }}
                      {% if form.delivery_time.errors %}
                        <div class="text-danger small">
                          {% for error in form.delivery_time.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <!-- Validity Period -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="text-info mb-3"><i class="fas fa-calendar-alt me-2"></i>Validity Period</h6>
                    </div>
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.start_date.id_for_label }}" class="form-label">
                        Start Date <span class="text-danger">*</span>
                      </label>
                      {{ form.start_date }}
                      {% if form.start_date.errors %}
                        <div class="text-danger small">
                          {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    
                    <div class="col-md-6 mb-3">
                      <label for="{{ form.end_date.id_for_label }}" class="form-label">
                        End Date <span class="text-danger">*</span>
                      </label>
                      {{ form.end_date }}
                      {% if form.end_date.errors %}
                        <div class="text-danger small">
                          {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <!-- Action Buttons -->
                  <div class="d-flex justify-content-between">
                    <a href="{% url 'feeds:feed_detail' feed.pk %}" class="btn btn-secondary btn-modern">
                      <i class="fas fa-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary btn-modern">
                      <i class="fas fa-save me-2"></i>
                      {% if form.instance.pk %}Update Rule{% else %}Create Rule{% endif %}
                    </button>
                  </div>
                  
                </form>
                
                <!-- Cost Preview -->
                <div class="mt-4 p-3" style="background: #f8f9fa; border-radius: 10px;">
                  <h6 class="mb-2"><i class="fas fa-calculator me-2"></i>Cost Preview</h6>
                  <p class="mb-0 text-muted">
                    <span id="cost-preview">Enter dosage quantity to see estimated cost per feeding</span>
                  </p>
                </div>
                
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        if (!control.classList.contains('btn')) {
            control.classList.add('form-control');
        }
    });
    
    // Cost calculation
    const dosageQtyInput = document.querySelector('#id_dosage_qty');
    const costPreview = document.querySelector('#cost-preview');
    const unitCost = {{ feed.unit_cost }};
    
    function updateCostPreview() {
        const qty = parseFloat(dosageQtyInput.value) || 0;
        const cost = qty * unitCost;
        if (qty > 0) {
            costPreview.textContent = `Estimated cost per feeding: ₹${cost.toFixed(2)}`;
        } else {
            costPreview.textContent = 'Enter dosage quantity to see estimated cost per feeding';
        }
    }
    
    if (dosageQtyInput) {
        dosageQtyInput.addEventListener('input', updateCostPreview);
        updateCostPreview(); // Initial calculation
    }
    
    // Set default date inputs to today and one year from now
    const startDateInput = document.querySelector('#id_start_date');
    const endDateInput = document.querySelector('#id_end_date');
    
    if (startDateInput && !startDateInput.value) {
        const today = new Date();
        startDateInput.value = today.toISOString().split('T')[0];
    }
    
    if (endDateInput && !endDateInput.value) {
        const oneYearLater = new Date();
        oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
        endDateInput.value = oneYearLater.toISOString().split('T')[0];
    }
    
    // Set default delivery time
    const deliveryTimeInput = document.querySelector('#id_delivery_time');
    if (deliveryTimeInput && !deliveryTimeInput.value) {
        deliveryTimeInput.value = '08:00';
    }
});
</script>

{% endblock %}