{% extends 'main/base.html' %}
{% load static %}
{% load humanize %}

{% block content %}
<style>
  .card-modern {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
  }
  
  .card-modern:hover {
    transform: translateY(-2px);
  }
  
  .btn-modern {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
  }
  
  .badge-modern {
    border-radius: 15px;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 500;
  }
  
  .feed-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
  }
  
  .cost-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
  }
</style>

<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->

<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {% include 'home/backend_header.html' %}
  <!-- Page Header Ends-->
  
  <!-- mobile fix menu start -->
  {% include 'home/mobile_menu.html' %}
  <!-- mobile fix menu end -->
  
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {% include 'home/backend_sidebar.html' %}
    <!-- Page Sidebar Ends-->
    
    <!-- Page Body Start -->
    <div class="page-body">
      <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="page-title">
          <div class="row">
            <div class="col-6">
              <h3>Feed Types Management</h3>
            </div>
            <div class="col-6">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home:dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'feeds:dashboard' %}">Feed Management</a></li>
                <li class="breadcrumb-item active">Feed Types</li>
              </ol>
            </div>
          </div>
        </div>
        
        <!-- Actions Header -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card card-modern">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <h5 class="mb-0">Feed Types ({{ page_obj.paginator.count }})</h5>
                    <small class="text-muted">Manage your farm's feed inventory</small>
                  </div>
                  <div>
                    <a href="{% url 'feeds:feed_create' %}" class="btn btn-primary btn-modern">
                      <i class="fas fa-plus-circle me-2"></i>Add New Feed Type
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Feed Types Grid -->
        {% if page_obj %}
          <div class="row">
            {% for feed in page_obj %}
            <div class="col-lg-6 col-xl-4 mb-4">
              <div class="feed-card">
                <div class="d-flex justify-content-between align-items-start mb-3">
                  <div>
                    <h5 class="mb-1">{{ feed.name }}</h5>
                    {% if feed.description %}
                      <p class="text-muted mb-2">{{ feed.description|truncatewords:10 }}</p>
                    {% endif %}
                  </div>
                  <div class="dropdown">
                    <button class="btn btn-sm btn-light" data-bs-toggle="dropdown">
                      <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                      <li><a class="dropdown-item" href="{% url 'feeds:feed_detail' feed.pk %}">
                        <i class="fas fa-eye me-2"></i>View Details
                      </a></li>
                      <li><a class="dropdown-item" href="{% url 'feeds:feed_edit' feed.pk %}">
                        <i class="fas fa-edit me-2"></i>Edit Feed
                      </a></li>
                      <li><a class="dropdown-item" href="{% url 'feeds:feed_rule_create' feed.pk %}">
                        <i class="fas fa-plus me-2"></i>Add Rule
                      </a></li>
                      <li><hr class="dropdown-divider"></li>
                      <li><a class="dropdown-item text-danger" href="{% url 'feeds:feed_delete' feed.pk %}">
                        <i class="fas fa-trash me-2"></i>Delete
                      </a></li>
                    </ul>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-6">
                    <div class="cost-badge text-center">
                      ₹{{ feed.unit_cost|floatformat:2 }}
                      <div style="font-size: 10px; opacity: 0.8;">per {{ feed.get_unit_display }}</div>
                    </div>
                  </div>
                  <div class="col-6 text-end">
                    {% if feed.is_active %}
                      <span class="badge badge-modern bg-success">Active</span>
                    {% else %}
                      <span class="badge badge-modern bg-secondary">Inactive</span>
                    {% endif %}
                  </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <small class="text-muted">
                      <i class="fas fa-calendar me-1"></i>
                      Created {{ feed.created_at|naturaltime }}
                    </small>
                  </div>
                  <div>
                    <span class="badge bg-info">{{ feed.rules.count }} rule{{ feed.rules.count|pluralize }}</span>
                  </div>
                </div>
                
                <div class="mt-3 d-flex gap-2">
                  <a href="{% url 'feeds:feed_detail' feed.pk %}" class="btn btn-outline-primary btn-sm flex-fill">
                    <i class="fas fa-eye me-1"></i>View
                  </a>
                  <a href="{% url 'feeds:feed_rule_create' feed.pk %}" class="btn btn-outline-success btn-sm flex-fill">
                    <i class="fas fa-plus me-1"></i>Add Rule
                  </a>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
          
          <!-- Pagination -->
          {% if page_obj.has_other_pages %}
          <div class="row">
            <div class="col-12">
              <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                  {% if page_obj.has_previous %}
                    <li class="page-item">
                      <a class="page-link" href="?page=1">First</a>
                    </li>
                    <li class="page-item">
                      <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                  {% endif %}
                  
                  <li class="page-item active">
                    <span class="page-link">
                      Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>
                  </li>
                  
                  {% if page_obj.has_next %}
                    <li class="page-item">
                      <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                      <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                    </li>
                  {% endif %}
                </ul>
              </nav>
            </div>
          </div>
          {% endif %}
          
        {% else %}
          <!-- Empty State -->
          <div class="row">
            <div class="col-12">
              <div class="card card-modern">
                <div class="card-body text-center py-5">
                  <i class="fas fa-seedling fa-4x text-muted mb-4"></i>
                  <h4 class="text-muted mb-3">No Feed Types Found</h4>
                  <p class="text-muted mb-4">Start by creating your first feed type to manage animal feeding schedules.</p>
                  <a href="{% url 'feeds:feed_create' %}" class="btn btn-primary btn-modern">
                    <i class="fas fa-plus-circle me-2"></i>Create Your First Feed Type
                  </a>
                </div>
              </div>
            </div>
          </div>
        {% endif %}
        
      </div>
    </div>
    <!-- Page Body End -->
  </div>
  <!-- Page Body End -->
</div>

{% endblock %}