# Generated by Django 3.2.16 on 2025-06-06 15:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0019_unpaidleave'),
        ('dairy', '0046_animaltypechange'),
    ]

    operations = [
        migrations.CreateModel(
            name='Feed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('unit_cost', models.DecimalField(decimal_places=2, help_text='Cost per unit', max_digits=10)),
                ('unit', models.CharField(choices=[('kg', 'Kilograms'), ('liter', 'Liters'), ('lot', 'Lots'), ('gram', 'Grams'), ('ml', 'Milliliters')], default='kg', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('farm', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feeds', to='accounts.farm')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FeedActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_for', models.DateTimeField()),
                ('dosage_qty', models.DecimalField(decimal_places=2, max_digits=8)),
                ('dosage_unit', models.CharField(choices=[('kg', 'Kilograms'), ('liter', 'Liters'), ('lot', 'Lots'), ('gram', 'Grams'), ('ml', 'Milliliters')], max_length=20)),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('accepted', 'Accepted'), ('skipped', 'Skipped'), ('missed', 'Missed')], default='planned', max_length=20)),
                ('accepted_at', models.DateTimeField(blank=True, null=True)),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('accepted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='accepted_feed_activities', to=settings.AUTH_USER_MODEL)),
                ('animal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feed_activities', to='dairy.animal')),
            ],
            options={
                'ordering': ['-scheduled_for'],
            },
        ),
        migrations.CreateModel(
            name='FeedRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Rule description', max_length=200)),
                ('target_animal_category', models.CharField(choices=[('dairy', 'Dairy'), ('buffalo', 'Buffalo'), ('cow', 'Cow'), ('goat', 'Goat'), ('sheep', 'Sheep'), ('beef', 'Beef'), ('other', 'Other')], help_text='Target animal category', max_length=50)),
                ('target_animal_type', models.CharField(choices=[('breeder', 'Breeder'), ('pregnant', 'Pregnant'), ('dry', 'Dry'), ('milking', 'Milking'), ('preg_milking', 'Pre-Milk'), ('calf', 'Calf'), ('other', 'Other')], help_text='Target animal type', max_length=50)),
                ('min_age_months', models.PositiveIntegerField(help_text='Minimum age in months')),
                ('max_age_months', models.PositiveIntegerField(help_text='Maximum age in months')),
                ('dosage_qty', models.DecimalField(decimal_places=2, help_text='Quantity per dose', max_digits=8)),
                ('dosage_unit', models.CharField(choices=[('kg', 'Kilograms'), ('liter', 'Liters'), ('lot', 'Lots'), ('gram', 'Grams'), ('ml', 'Milliliters')], default='kg', max_length=20)),
                ('frequency_days', models.PositiveIntegerField(help_text='Frequency in days (1=daily, 2=every other day)')),
                ('delivery_time', models.TimeField(help_text='Scheduled delivery time')),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('end_date', models.DateField()),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('paused', 'Paused'), ('expired', 'Expired')], default='scheduled', max_length=20)),
                ('last_run', models.DateTimeField(blank=True, help_text='Last scheduler run', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('feed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rules', to='feeds.feed')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FeedRuleHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(blank=True, max_length=20)),
                ('new_status', models.CharField(max_length=20)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('reason', models.TextField(blank=True)),
                ('changed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('feed_rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='feeds.feedrule')),
            ],
            options={
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='FeedCostSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=8)),
                ('unit', models.CharField(choices=[('kg', 'Kilograms'), ('liter', 'Liters'), ('lot', 'Lots'), ('gram', 'Grams'), ('ml', 'Milliliters')], max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('animal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feed_costs', to='dairy.animal')),
                ('feed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cost_snapshots', to='feeds.feed')),
                ('feed_activity', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cost_snapshot', to='feeds.feedactivity')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='FeedActivityHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(blank=True, max_length=20)),
                ('new_status', models.CharField(max_length=20)),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('reason', models.TextField(blank=True)),
                ('changed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('feed_activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='feeds.feedactivity')),
            ],
            options={
                'ordering': ['-changed_at'],
            },
        ),
        migrations.AddField(
            model_name='feedactivity',
            name='feed_rule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='feeds.feedrule'),
        ),
        migrations.AddIndex(
            model_name='feedcostsnapshot',
            index=models.Index(fields=['animal', 'date'], name='feeds_feedc_animal__28ab3d_idx'),
        ),
        migrations.AddIndex(
            model_name='feedcostsnapshot',
            index=models.Index(fields=['feed', 'date'], name='feeds_feedc_feed_id_cd0a0b_idx'),
        ),
        migrations.AddIndex(
            model_name='feedcostsnapshot',
            index=models.Index(fields=['date'], name='feeds_feedc_date_ca1fa0_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='feedcostsnapshot',
            unique_together={('animal', 'feed', 'date', 'feed_activity')},
        ),
        migrations.AddIndex(
            model_name='feedactivity',
            index=models.Index(fields=['animal', 'scheduled_for'], name='feeds_feeda_animal__b96de1_idx'),
        ),
        migrations.AddIndex(
            model_name='feedactivity',
            index=models.Index(fields=['status', 'scheduled_for'], name='feeds_feeda_status_f6f8fe_idx'),
        ),
        migrations.AddIndex(
            model_name='feedactivity',
            index=models.Index(fields=['feed_rule', 'status'], name='feeds_feeda_feed_ru_291436_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='feedactivity',
            unique_together={('feed_rule', 'animal', 'scheduled_for')},
        ),
        migrations.AlterUniqueTogether(
            name='feed',
            unique_together={('farm', 'name')},
        ),
    ]
