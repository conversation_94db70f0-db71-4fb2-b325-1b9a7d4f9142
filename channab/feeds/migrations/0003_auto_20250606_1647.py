# Generated by Django 3.2.16 on 2025-06-06 16:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0002_auto_20250606_1618'),
    ]

    operations = [
        migrations.AddField(
            model_name='feedrule',
            name='custom_formula',
            field=models.TextField(blank=True, help_text="Custom formula (e.g., 'weight * 0.05 + 2')"),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='formula_application',
            field=models.CharField(choices=[('per_animal', 'Apply formula per individual animal'), ('total_batch', 'Calculate total for all selected animals')], default='per_animal', help_text='How to apply the formula', max_length=20),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='formula_type',
            field=models.CharField(choices=[('fixed', 'Fixed Amount'), ('per_animal', 'Per Animal'), ('per_kg_weight', 'Per Kg Body Weight'), ('percentage_weight', 'Percentage of Body Weight'), ('custom_formula', 'Custom Formula')], default='fixed', help_text='Type of dosage formula', max_length=20),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='formula_value',
            field=models.DecimalField(decimal_places=4, default=1.0, help_text='Formula multiplier value', max_digits=8),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='max_dosage',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum dosage per animal', max_digits=8, null=True),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='min_dosage',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Minimum dosage per animal', max_digits=8, null=True),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='target_all_animals',
            field=models.BooleanField(default=False, help_text='Apply to all animals in the farm'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='target_animal_categories',
            field=models.JSONField(blank=True, default=list, help_text='Target animal categories (multiple selection)'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='target_animal_types',
            field=models.JSONField(blank=True, default=list, help_text='Target animal types (multiple selection)'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='use_age_filter',
            field=models.BooleanField(default=False, help_text='Enable age-based filtering'),
        ),
        migrations.AlterField(
            model_name='feedrule',
            name='dosage_qty',
            field=models.DecimalField(decimal_places=2, help_text='Base quantity for formula calculation', max_digits=8),
        ),
        migrations.AlterField(
            model_name='feedrule',
            name='max_age_months',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum age in months (optional)', null=True),
        ),
        migrations.AlterField(
            model_name='feedrule',
            name='min_age_months',
            field=models.PositiveIntegerField(blank=True, help_text='Minimum age in months (optional)', null=True),
        ),
        migrations.AlterField(
            model_name='feedrule',
            name='target_animal_category',
            field=models.CharField(blank=True, choices=[('dairy', 'Dairy'), ('buffalo', 'Buffalo'), ('cow', 'Cow'), ('goat', 'Goat'), ('sheep', 'Sheep'), ('beef', 'Beef'), ('other', 'Other')], help_text='Target animal category (legacy)', max_length=50),
        ),
        migrations.AlterField(
            model_name='feedrule',
            name='target_animal_type',
            field=models.CharField(blank=True, choices=[('breeder', 'Breeder'), ('pregnant', 'Pregnant'), ('dry', 'Dry'), ('milking', 'Milking'), ('preg_milking', 'Pre-Milk'), ('calf', 'Calf'), ('other', 'Other')], help_text='Target animal type (legacy)', max_length=50),
        ),
    ]
