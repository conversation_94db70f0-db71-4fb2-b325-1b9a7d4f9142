# Generated by Django 3.2.16 on 2025-06-06 16:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='feedrule',
            name='custom_interval_unit',
            field=models.CharField(blank=True, choices=[('minutes', 'Minutes'), ('hours', 'Hours'), ('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], default='hours', max_length=10),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='custom_interval_value',
            field=models.PositiveIntegerField(default=1, help_text='Custom interval value'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='daily_type',
            field=models.CharField(blank=True, choices=[('every_day', 'Every day'), ('every_n_days', 'Every N days'), ('weekdays_only', 'Weekdays only (Mon-Fri)')], default='every_day', max_length=20),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='day_of_month',
            field=models.PositiveIntegerField(default=1, help_text='Day of month (1-31)'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='end_time',
            field=models.TimeField(blank=True, help_text='End time (optional)', null=True),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='end_type',
            field=models.CharField(choices=[('never', 'Never'), ('date', 'On specific date'), ('occurrences', 'After N occurrences')], default='date', help_text='How should this schedule end', max_length=20),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='exception_dates',
            field=models.JSONField(blank=True, default=list, help_text='Dates to skip (holidays, etc.)'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='friday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='interval_days',
            field=models.PositiveIntegerField(default=1, help_text='Interval for every N days'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='interval_months',
            field=models.PositiveIntegerField(default=1, help_text='Interval for every N months'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='interval_weeks',
            field=models.PositiveIntegerField(default=1, help_text='Interval for every N weeks'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='interval_years',
            field=models.PositiveIntegerField(default=1, help_text='Interval for every N years'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='max_occurrences',
            field=models.PositiveIntegerField(blank=True, help_text='Maximum number of occurrences', null=True),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='monday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='monthly_type',
            field=models.CharField(blank=True, choices=[('specific_date', 'Specific date each month'), ('specific_weekday', 'Specific weekday (e.g., 2nd Monday)'), ('every_n_months', 'Every N months')], default='specific_date', max_length=20),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='saturday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='schedule_type',
            field=models.CharField(choices=[('single', 'Single (One-time)'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly'), ('custom', 'Custom Interval'), ('weekdays', 'Weekdays Only')], default='daily', help_text='Type of schedule', max_length=20),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='sunday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='thursday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='tuesday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='wednesday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='week_of_month',
            field=models.PositiveIntegerField(default=1, help_text='Week of month (1-5, 5=last)'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='weekday_of_month',
            field=models.PositiveIntegerField(default=0, help_text='Day of week (0=Mon, 6=Sun)'),
        ),
        migrations.AddField(
            model_name='feedrule',
            name='weekly_type',
            field=models.CharField(blank=True, choices=[('specific_day', 'Specific day of week'), ('multiple_days', 'Multiple days per week'), ('every_n_weeks', 'Every N weeks')], default='specific_day', max_length=20),
        ),
        migrations.AlterField(
            model_name='feedrule',
            name='frequency_days',
            field=models.PositiveIntegerField(default=1, help_text='Frequency in days (legacy field - use schedule_type instead)'),
        ),
    ]
