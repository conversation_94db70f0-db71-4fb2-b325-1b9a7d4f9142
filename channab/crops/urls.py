from django.urls import path
from . import views, views_api

app_name = 'crops'

urlpatterns = [
    path('', views.crops_dashboard, name='dashboard'),
    # Field CRUD
    path('fields/', views.field_list, name='field_list'),
    path('fields/create/', views.create_field, name='create_field'),
    path('fields/<int:pk>/', views.field_detail, name='field_detail'),
    path('fields/<int:pk>/edit/', views.edit_field, name='edit_field'),
    path('fields/<int:pk>/delete/', views.delete_field, name='delete_field'),
    
    # Crop CRUD
    path('crops/', views.crop_list, name='crop_list'),
    path('crops/create/', views.create_crop, name='create_crop'),
    path('crops/<int:pk>/', views.crop_detail, name='crop_detail'),
    path('crops/<int:pk>/edit/', views.edit_crop, name='edit_crop'),
    path('crops/<int:pk>/delete/', views.delete_crop, name='delete_crop'),
    
    # Crop Stage Management
    path('crops/<int:crop_id>/advance-stage/', views.advance_crop_stage, name='advance_crop_stage'),



    path('api/fields/', views_api.field_list_api, name='field_list_api'),
    path('api/fields/<int:field_id>/crops/', views_api.crop_list_by_field, name='crop_list_by_field'),
    path('api/fields/<int:field_id>/', views_api.field_detail_api, name='field_detail_api'),  # For fetching field details
        



    path('api/tasks/', views_api.create_task, name='create_task'),
    path('api/crops/<int:crop_id>/tasks/', views_api.list_tasks, name='list_tasks'),



    # Task Management
    path('crops/<int:crop_id>/tasks/', views.task_list, name='task_list'),
    path('crops/<int:crop_id>/task-management/', views.task_management, name='task_management'),
    path('tasks/<int:task_id>/', views.task_detail, name='task_detail'),
    path('crops/<int:crop_id>/tasks/create/', views.task_create, name='task_create'),
    path('tasks/<int:task_id>/edit/', views.task_update, name='task_update'),
    path('tasks/<int:task_id>/delete/', views.task_delete, name='task_delete'),
    path('tasks/<int:instance_id>/done/', views.mark_instance_done, name='mark_instance_done'),
    path('tasks/<int:instance_id>/cancel/', views.mark_instance_cancel, name='mark_instance_cancel'),
    path('tasks/<int:task_id>/edit_future_instances/', views.edit_future_instances, name='edit_future_instances'),
    
    # Task Instance AJAX actions
    path('tasks/<int:instance_id>/complete/', views.task_instance_complete, name='task_instance_complete'),
    path('tasks/<int:instance_id>/cancel/', views.task_instance_cancel, name='task_instance_cancel'),
    path('tasks/<int:instance_id>/reschedule/', views.task_instance_reschedule, name='task_instance_reschedule'),

    path('api/<int:crop_id>/tasks/', views_api.task_list_api, name='task_list_api'),
    path('api/fields/create/', views_api.create_field_api, name='create_field_api'),
    path('api/fields/<int:field_id>/edit/', views_api.edit_field_api, name='edit_field_api'),

    path('api/crops/create/', views_api.create_crop_api, name='create_crop_api'),

    path('api/crop-notes/', views_api.CropNoteListCreateView.as_view(), name='cropnote-list-create'),
    path('api/crop-notes/<int:pk>/', views_api.CropNoteDetailView.as_view(), name='cropnote-detail'),



]
