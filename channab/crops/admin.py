from django.contrib import admin
from .models import Field, Crop, CropGallery, Task, TaskInstance, CropNote

@admin.register(Field)
class FieldAdmin(admin.ModelAdmin):
    list_display = ('name', 'farm', 'area')
    search_fields = ('name', 'farm__name')

@admin.register(Crop)
class CropAdmin(admin.ModelAdmin):
    list_display = ('name', 'variety', 'planting_date', 'stage')
    search_fields = ('name', 'variety', 'fields__name')
    list_filter = ('stage',)

@admin.register(CropGallery)
class CropGalleryAdmin(admin.ModelAdmin):
    list_display = ('crop', 'uploaded_at')
    search_fields = ('crop__name',)

@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ('title', 'crop', 'recurrence', 'recurrence_count', 'start_date')
    search_fields = ('title', 'crop__name')
    list_filter = ('recurrence',)

@admin.register(TaskInstance)
class TaskInstanceAdmin(admin.ModelAdmin):
    list_display = ('task', 'scheduled_date', 'status')
    search_fields = ('task__title', 'task__crop__name')
    list_filter = ('status',)

@admin.register(CropNote)
class CropNoteAdmin(admin.ModelAdmin):
    list_display = ('crop', 'user', 'creation_date')
    search_fields = ('crop__name', 'user__username', 'description')
    list_filter = ('creation_date',)
