from django.db import models
from django.utils import timezone
from farm_finances.models import ExpenseCategory, Expense
from accounts.models import CustomUser, Farm
from django.utils.timezone import timedelta


class Field(models.Model):
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    area = models.DecimalField(max_digits=10, decimal_places=2, help_text="Area in acres")
    image = models.ImageField(upload_to='field_images/', blank=True, null=True)

    def __str__(self):
        return self.name

class Crop(models.Model):
    STAGES = [
        ('planning', 'Planning'),
        ('soil_prep', 'Soil Preparation'),
        ('planting', 'Planting'),
        ('growing', 'Growing'),
        ('flowering', 'Flowering'),
        ('harvesting', 'Harvesting'),
        ('completed', 'Completed'),
    ]
    
    name = models.CharField(max_length=100)
    variety = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    planting_date = models.DateField()
    stage = models.CharField(max_length=20, choices=STAGES, default='planning')
    fields = models.ManyToManyField(Field, related_name='crops')
    
    def __str__(self):
        return self.name
    
    def get_first_image_url(self):
        first_image = self.gallery.first()
        if first_image:
            return first_image.image.url
        return None
    
    def can_advance_stage(self):
        """Check if crop can advance to next stage"""
        stage_order = [s[0] for s in self.STAGES]
        current_index = stage_order.index(self.stage)
        return current_index < len(stage_order) - 1
    
    def get_next_stage(self):
        """Get the next stage in the workflow"""
        stage_order = [s[0] for s in self.STAGES]
        current_index = stage_order.index(self.stage)
        if current_index < len(stage_order) - 1:
            return stage_order[current_index + 1]
        return None
    
    def advance_stage(self):
        """Advance crop to next stage"""
        next_stage = self.get_next_stage()
        if next_stage:
            self.stage = next_stage
            self.save()
            return True
        return False
    
    def get_stage_display_color(self):
        """Get color class for stage display"""
        colors = {
            'planning': 'secondary',
            'soil_prep': 'info',
            'planting': 'primary',
            'growing': 'success',
            'flowering': 'warning',
            'harvesting': 'orange',
            'completed': 'dark',
        }
        return colors.get(self.stage, 'secondary')
    
    def get_stage_progress(self):
        """Get stage completion percentage"""
        stage_order = [s[0] for s in self.STAGES]
        current_index = stage_order.index(self.stage)
        return int((current_index / (len(stage_order) - 1)) * 100)
    
class CropGallery(models.Model):
    crop = models.ForeignKey(Crop, on_delete=models.CASCADE, related_name='gallery')
    image = models.ImageField(upload_to='crop_images/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Image for {self.crop.name}"
    
class Task(models.Model):
    RECURRENCE_CHOICES = [
        ('single', 'Single (Non-Recurring)'),
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
        ('interval', 'Custom Interval'),
        ('weekdays', 'Weekdays Only'),
        ('biweekly', 'Bi-weekly'),
        ('quarterly', 'Quarterly'),
    ]
    
    MONTHLY_PATTERN_CHOICES = [
        ('day_of_month', 'Same Day of Month'),
        ('relative_weekday', 'Relative Weekday (e.g., 2nd Tuesday)'),
    ]
    
    END_TYPE_CHOICES = [
        ('never', 'Never'),
        ('occurrences', 'After X Occurrences'),
        ('date', 'On Specific Date'),
    ]
    
    crop = models.ForeignKey(Crop, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    
    # Basic recurrence settings
    recurrence = models.CharField(max_length=20, choices=RECURRENCE_CHOICES, default='single')
    
    # Time settings
    start_date = models.DateField(default=timezone.now)
    start_time = models.TimeField(blank=True, null=True)
    end_time = models.TimeField(blank=True, null=True)
    
    # Interval settings (for daily/custom interval)
    interval_days = models.IntegerField(default=1, help_text="Number of days between occurrences")
    
    # Weekly settings
    monday = models.BooleanField(default=False)
    tuesday = models.BooleanField(default=False)
    wednesday = models.BooleanField(default=False)
    thursday = models.BooleanField(default=False)
    friday = models.BooleanField(default=False)
    saturday = models.BooleanField(default=False)
    sunday = models.BooleanField(default=False)
    
    # Monthly settings
    monthly_pattern = models.CharField(max_length=20, choices=MONTHLY_PATTERN_CHOICES, default='day_of_month')
    day_of_month = models.IntegerField(default=1, blank=True, null=True)
    week_of_month = models.IntegerField(default=1, blank=True, null=True)  # 1-5 (5 = last)
    weekday_of_month = models.IntegerField(default=1, blank=True, null=True)  # 0=Mon, 6=Sun
    
    # End conditions
    end_type = models.CharField(max_length=20, choices=END_TYPE_CHOICES, default='never')
    end_occurrences = models.IntegerField(default=1, blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    
    # Exception dates (JSON field to store dates to skip)
    exception_dates = models.JSONField(default=list, blank=True)
    
    # Legacy field for backward compatibility
    recurrence_count = models.IntegerField(default=1)  # How many times the task should recur

    def __str__(self):
        return self.title
    
    def get_weekdays_list(self):
        """Return list of weekdays selected"""
        weekdays = []
        if self.monday: weekdays.append(0)
        if self.tuesday: weekdays.append(1)
        if self.wednesday: weekdays.append(2)
        if self.thursday: weekdays.append(3)
        if self.friday: weekdays.append(4)
        if self.saturday: weekdays.append(5)
        if self.sunday: weekdays.append(6)
        return weekdays

class TaskInstance(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('unattended', 'Unattended'),
        ('canceled', 'Canceled'),
    ]

    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='instances')
    scheduled_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    completed_date = models.DateField(blank=True, null=True)

    def __str__(self):
        return f"{self.task.title} on {self.scheduled_date}"

    @staticmethod
    def create_instances(task):
        instances = []
        date = task.start_date
        for _ in range(task.recurrence_count):
            instances.append(TaskInstance(task=task, scheduled_date=date))
            if task.recurrence == 'daily':
                date += timezone.timedelta(days=1)
            elif task.recurrence == 'weekly':
                date += timezone.timedelta(weeks=1)
            elif task.recurrence == 'monthly':
                date += timezone.timedelta(days=30)  # Approximate month
        TaskInstance.objects.bulk_create(instances)


class CropNote(models.Model):
    crop = models.ForeignKey(Crop, on_delete=models.CASCADE, related_name='notes')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    description = models.TextField()
    creation_date = models.DateTimeField(auto_now_add=True)
    image = models.ImageField(upload_to='crop_notes_images/', blank=True, null=True)

    def __str__(self):
        return f"Note for {self.crop.name} by {self.user.username}"


