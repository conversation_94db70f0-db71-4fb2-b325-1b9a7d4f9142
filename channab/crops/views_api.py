from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import generics, permissions
from rest_framework import status
from django.utils import timezone
from django.shortcuts import get_object_or_404
from .models import Crop, CropNote, Field, Task
from .serializers import CropNoteSerializer, CropSerializer, FieldSerializer, TaskInstanceSerializer, TaskSerializer

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def field_list_api(request):
    farm = request.user.farm
    fields = Field.objects.filter(farm=farm)
    serializer = FieldSerializer(fields, many=True)
    return Response(serializer.data)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_field_api(request):
    print("Request data received by backend:", request.data)  # Debug print
    serializer = FieldSerializer(data=request.data)
    if serializer.is_valid():
        field = serializer.save(farm=request.user.farm)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    else:
        print("Serializer errors:", serializer.errors)  # Debug print
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def edit_field_api(request, field_id):
    field = get_object_or_404(Field, id=field_id)
    if request.method == 'PATCH':
        serializer = FieldSerializer(field, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def crop_list_by_field(request, field_id):
    try:
        field = Field.objects.get(id=field_id, farm=request.user.farm)
    except Field.DoesNotExist:
        return Response({'error': 'Field not found'}, status=404)

    crops = field.crops.all()
    serializer = CropSerializer(crops, many=True)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_crop_api(request):
    serializer = CropSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




@api_view(['GET'])
@permission_classes([IsAuthenticated])
def field_detail_api(request, field_id):
    field = get_object_or_404(Field, id=field_id)
    serializer = FieldSerializer(field)
    return Response(serializer.data)




@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_task(request):
    serializer = TaskSerializer(data=request.data)
    if serializer.is_valid():
        task = serializer.save()
        TaskInstance.create_instances(task)  # Create instances
        return Response({'status': 'Task created successfully'}, status=201)
    return Response(serializer.errors, status=400)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_tasks(request, crop_id):
    try:
        crops = Crop.objects.filter(id=crop_id, fields__farm=request.user.farm)
        if not crops.exists():
            return Response({'error': 'Crop not found'}, status=404)
        crop = crops.first()
    except Crop.DoesNotExist:
        return Response({'error': 'Crop not found'}, status=404)
    
    tasks = crop.tasks.all()
    serializer = TaskSerializer(tasks, many=True)
    print(serializer.data)
    return Response(serializer.data)






@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_task_status(request, instance_id):
    try:
        instance = TaskInstance.objects.get(id=instance_id, task__crop__fields__farm=request.user.farm)
    except TaskInstance.DoesNotExist:
        return Response({'error': 'Task instance not found'}, status=404)
    
    status = request.data.get('status')
    if status not in ['completed', 'canceled', 'unattended']:
        return Response({'error': 'Invalid status'}, status=400)
    
    if status == 'canceled' and instance.task.recurrence != 'single':
        recurrence_action = request.data.get('recurrence_action')
        if recurrence_action == 'single':
            instance.status = status
        elif recurrence_action == 'all':
            TaskInstance.objects.filter(task=instance.task, scheduled_date__gte=instance.scheduled_date).update(status=status)
        else:
            return Response({'error': 'Invalid recurrence action'}, status=400)
    else:
        instance.status = status
    
    instance.save()
    return Response({'status': 'Task instance status updated successfully'})



# Tasks Api Views


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def task_list_api(request, crop_id):
    crop = get_object_or_404(Crop, id=crop_id)
    tasks = crop.tasks.all()
    task_data = []

    for task in tasks:
        instances = task.instances.all()
        upcoming_instances = []
        previous_instances = []
        has_future_instances = False

        for instance in instances:
            if instance.scheduled_date < timezone.now().date() and instance.status == 'pending':
                instance.status = 'unattended'
                instance.save()

            if instance.scheduled_date >= timezone.now().date():
                upcoming_instances.append(instance)
                has_future_instances = True
            else:
                previous_instances.append(instance)

        task_data.append({
            'task': TaskSerializer(task).data,
            'upcoming_instances': TaskInstanceSerializer(upcoming_instances, many=True).data,
            'previous_instances': TaskInstanceSerializer(previous_instances, many=True).data,
            'has_future_instances': has_future_instances,
        })

    return Response({'task_data': task_data, 'crop': {'id': crop.id, 'name': crop.name}})




class CropNoteListCreateView(generics.ListCreateAPIView):
    queryset = CropNote.objects.all()
    serializer_class = CropNoteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)

class CropNoteDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = CropNote.objects.all()
    serializer_class = CropNoteSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)


