from django import forms
from .models import Field, Crop, Task, CropNote

class FieldForm(forms.ModelForm):
    class Meta:
        model = Field
        fields = ['name', 'area', 'image']

class CropForm(forms.ModelForm):
    class Meta:
        model = Crop
        fields = ['name', 'variety', 'planting_date', 'stage', 'fields']
        widgets = {
            'planting_date': forms.DateInput(attrs={'type': 'date'}),
            'fields': forms.CheckboxSelectMultiple(),
        }

class TaskForm(forms.ModelForm):
    class Meta:
        model = Task
        fields = ['title', 'description', 'recurrence', 'recurrence_count', 'start_date']

class EditFutureInstancesForm(forms.ModelForm):
    class Meta:
        model = Task
        fields = ['recurrence', 'recurrence_count', 'start_date']

class CropNoteForm(forms.ModelForm):
    class Meta:
        model = CropNote
        fields = ['description', 'image']