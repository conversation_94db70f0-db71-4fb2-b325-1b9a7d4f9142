# Generated by Django 3.2.16 on 2024-06-03 16:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('crops', '0010_taskinstance'),
    ]

    operations = [
        migrations.CreateModel(
            name='CropNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='crop_notes_images/')),
                ('crop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='crops.crop')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
