# Generated by Django 3.2.16 on 2024-05-26 09:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0012_alter_customuser_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='Crop',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('variety', models.CharField(blank=True, max_length=100, null=True)),
                ('planting_date', models.DateField()),
                ('harvest_date', models.DateField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Field',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('area', models.DecimalField(decimal_places=2, help_text='Area in acres', max_digits=10)),
                ('farm', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.farm')),
            ],
        ),
        migrations.CreateModel(
            name='CropActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('description', models.TextField()),
                ('expense', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('crop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='crops.crop')),
            ],
        ),
        migrations.AddField(
            model_name='crop',
            name='field',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='crops.field'),
        ),
    ]
