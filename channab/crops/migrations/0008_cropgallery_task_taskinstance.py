# Generated by Django 3.2.16 on 2024-05-28 05:57

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('crops', '0007_alter_cropactivity_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('recurrence', models.CharField(choices=[('single', 'Single'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], default='single', max_length=20)),
                ('recurrence_count', models.IntegerField(default=1)),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('crop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='crops.crop')),
            ],
        ),
        migrations.CreateModel(
            name='TaskInstance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('unattended', 'Unattended'), ('canceled', 'Canceled')], default='pending', max_length=20)),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instances', to='crops.task')),
            ],
        ),
        migrations.CreateModel(
            name='CropGallery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='crop_images/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('crop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gallery', to='crops.crop')),
            ],
        ),
    ]
