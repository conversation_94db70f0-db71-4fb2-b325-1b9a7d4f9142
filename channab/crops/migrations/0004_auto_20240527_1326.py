# Generated by Django 3.2.16 on 2024-05-27 10:26

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('crops', '0003_auto_20240526_1250'),
    ]

    operations = [
        migrations.RenameField(
            model_name='cropactivity',
            old_name='expense',
            new_name='cost',
        ),
        migrations.RemoveField(
            model_name='crop',
            name='harvest_date',
        ),
        migrations.AddField(
            model_name='crop',
            name='stage',
            field=models.CharField(choices=[('planning', 'Planning'), ('planting', 'Planting'), ('harvesting', 'Harvesting')], default='planning', max_length=20),
        ),
        migrations.AddField(
            model_name='cropactivity',
            name='recurrence',
            field=models.CharField(choices=[('single', 'Single'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], default='single', max_length=20),
        ),
        migrations.AddField(
            model_name='cropactivity',
            name='title',
            field=models.CharField(default=django.utils.timezone.now, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='cropactivity',
            name='date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.CreateModel(
            name='Harvest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('crop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='crops.crop')),
            ],
        ),
    ]
