# Generated by Django 3.2.16 on 2024-05-27 10:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('crops', '0004_auto_20240527_1326'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='crop',
            name='field',
        ),
        migrations.AddField(
            model_name='crop',
            name='fields',
            field=models.ManyToManyField(related_name='crops', to='crops.Field'),
        ),
        migrations.AddField(
            model_name='cropactivity',
            name='field',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='crops.field'),
        ),
        migrations.AlterField(
            model_name='cropactivity',
            name='crop',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='crops.crop'),
        ),
    ]
