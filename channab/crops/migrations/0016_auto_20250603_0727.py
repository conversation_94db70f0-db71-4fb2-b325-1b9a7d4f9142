# Generated by Django 3.2.16 on 2025-06-03 07:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('crops', '0015_alter_crop_stage'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='day_of_month',
            field=models.IntegerField(blank=True, default=1, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='end_occurrences',
            field=models.IntegerField(blank=True, default=1, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='end_time',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='end_type',
            field=models.CharField(choices=[('never', 'Never'), ('occurrences', 'After X Occurrences'), ('date', 'On Specific Date')], default='never', max_length=20),
        ),
        migrations.AddField(
            model_name='task',
            name='exception_dates',
            field=models.J<PERSON><PERSON>ield(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='task',
            name='friday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='interval_days',
            field=models.IntegerField(default=1, help_text='Number of days between occurrences'),
        ),
        migrations.AddField(
            model_name='task',
            name='monday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='monthly_pattern',
            field=models.CharField(choices=[('day_of_month', 'Same Day of Month'), ('relative_weekday', 'Relative Weekday (e.g., 2nd Tuesday)')], default='day_of_month', max_length=20),
        ),
        migrations.AddField(
            model_name='task',
            name='saturday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='start_time',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='sunday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='thursday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='tuesday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='wednesday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='task',
            name='week_of_month',
            field=models.IntegerField(blank=True, default=1, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='weekday_of_month',
            field=models.IntegerField(blank=True, default=1, null=True),
        ),
        migrations.AlterField(
            model_name='task',
            name='recurrence',
            field=models.CharField(choices=[('single', 'Single (Non-Recurring)'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly'), ('interval', 'Custom Interval'), ('weekdays', 'Weekdays Only'), ('biweekly', 'Bi-weekly'), ('quarterly', 'Quarterly')], default='single', max_length=20),
        ),
    ]
