# Generated by Django 3.2.16 on 2025-06-04 07:09

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('crops', '0016_auto_20250603_0727'),
    ]

    operations = [
        # Drop the CropActivity and Harvest tables
        migrations.RunSQL(
            "DROP TABLE IF EXISTS crops_cropactivity;",
            reverse_sql="-- No reverse operation for dropping tables"
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS crops_harvest;",
            reverse_sql="-- No reverse operation for dropping tables"
        ),
    ]
