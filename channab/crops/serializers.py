from rest_framework import serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import CropNote, Field, Crop, Task, TaskInstance

class CropSerializer(serializers.ModelSerializer):
    first_image_url = serializers.SerializerMethodField()

    class Meta:
        model = Crop
        fields = ['id', 'name', 'variety', 'planting_date', 'stage', 'fields', 'first_image_url']

    def get_first_image_url(self, obj):
        return obj.get_first_image_url()

class FieldSerializer(serializers.ModelSerializer):
    crops = CropSerializer(many=True, read_only=True)

    class Meta:
        model = Field
        fields = ['id', 'name', 'area', 'image', 'crops']

class CropNoteSerializer(serializers.ModelSerializer):
    class Meta:
        model = CropNote
        fields = ['id', 'crop', 'user', 'description', 'creation_date', 'image']
        read_only_fields = ['user', 'creation_date']

class TaskInstanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = TaskInstance
        fields = ['id', 'scheduled_date', 'status']

class TaskSerializer(serializers.ModelSerializer):
    instances = TaskInstanceSerializer(many=True, read_only=True)

    class Meta:
        model = Task
        fields = ['id', 'title', 'description', 'recurrence', 'recurrence_count', 'start_date', 'instances']
