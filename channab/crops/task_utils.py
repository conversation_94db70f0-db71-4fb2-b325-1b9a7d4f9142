from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
from dateutil.rrule import rrule, DAILY, WEEKLY, MONTHLY, YEARLY, MO, TU, WE, TH, FR, SA, SU
import calendar

def generate_task_instances(task):
    """
    Generate task instances based on the task's recurrence pattern and end conditions.
    Returns a list of scheduled dates.
    """
    start_date = task.start_date
    recurrence_type = task.recurrence
    dates = []
    
    # Single task - just return the start date
    if recurrence_type == 'single':
        return [start_date]
    
    # Determine end date based on end_type
    if task.end_type == 'date' and task.end_date:
        end_date = task.end_date
    elif task.end_type == 'occurrences' and task.end_occurrences:
        # We'll generate dates and limit by occurrences
        end_date = start_date + timedelta(days=365 * 10)  # 10 years max
    else:
        # Never ends - generate for 2 years by default
        end_date = start_date + timedelta(days=365 * 2)
    
    # Generate dates based on recurrence type
    if recurrence_type == 'daily':
        # Daily with interval
        current_date = start_date
        while current_date <= end_date:
            if is_valid_date(current_date, task):
                dates.append(current_date)
            current_date += timedelta(days=task.interval_days)
            
    elif recurrence_type == 'interval':
        # Custom interval (same as daily)
        current_date = start_date
        while current_date <= end_date:
            if is_valid_date(current_date, task):
                dates.append(current_date)
            current_date += timedelta(days=task.interval_days)
            
    elif recurrence_type == 'weekly':
        # Weekly on specific days
        weekdays = task.get_weekdays_list()
        if not weekdays:
            weekdays = [start_date.weekday()]  # Default to start date's weekday
        
        current_date = start_date
        while current_date <= end_date:
            if current_date.weekday() in weekdays and is_valid_date(current_date, task):
                dates.append(current_date)
            current_date += timedelta(days=1)
            
    elif recurrence_type == 'biweekly':
        # Bi-weekly on specific days
        weekdays = task.get_weekdays_list()
        if not weekdays:
            weekdays = [start_date.weekday()]
        
        # Find the first occurrence
        current_date = start_date
        week_count = 0
        
        while current_date <= end_date:
            if current_date.weekday() in weekdays:
                if week_count % 2 == 0 and is_valid_date(current_date, task):
                    dates.append(current_date)
            
            # Check if we've moved to a new week
            if current_date.weekday() == 6:  # Sunday
                week_count += 1
                
            current_date += timedelta(days=1)
            
    elif recurrence_type == 'monthly':
        # Monthly recurrence
        if task.monthly_pattern == 'day_of_month':
            # Same day of each month
            day_of_month = task.day_of_month or start_date.day
            current_date = start_date
            
            while current_date <= end_date:
                # Try to set the day, handle months with fewer days
                try:
                    scheduled_date = current_date.replace(day=day_of_month)
                except ValueError:
                    # Day doesn't exist in this month (e.g., Feb 31)
                    # Use the last day of the month instead
                    last_day = calendar.monthrange(current_date.year, current_date.month)[1]
                    scheduled_date = current_date.replace(day=last_day)
                
                if scheduled_date >= start_date and scheduled_date <= end_date and is_valid_date(scheduled_date, task):
                    dates.append(scheduled_date)
                
                # Move to next month
                current_date = (current_date.replace(day=1) + timedelta(days=32)).replace(day=1)
                
        else:
            # Relative weekday (e.g., 2nd Tuesday)
            week_of_month = task.week_of_month or 1
            weekday = task.weekday_of_month or start_date.weekday()
            
            current_date = start_date.replace(day=1)
            
            while current_date <= end_date:
                scheduled_date = get_nth_weekday_of_month(
                    current_date.year, 
                    current_date.month, 
                    weekday, 
                    week_of_month
                )
                
                if scheduled_date and scheduled_date >= start_date and scheduled_date <= end_date and is_valid_date(scheduled_date, task):
                    dates.append(scheduled_date)
                
                # Move to next month
                current_date = (current_date + timedelta(days=32)).replace(day=1)
                
    elif recurrence_type == 'quarterly':
        # Every 3 months
        current_date = start_date
        
        while current_date <= end_date:
            if is_valid_date(current_date, task):
                dates.append(current_date)
            # Add 3 months
            current_date = current_date + relativedelta(months=3)
            
    elif recurrence_type == 'yearly':
        # Yearly on the same date
        current_date = start_date
        
        while current_date <= end_date:
            if is_valid_date(current_date, task):
                dates.append(current_date)
            # Add 1 year
            try:
                current_date = current_date.replace(year=current_date.year + 1)
            except ValueError:
                # Handle Feb 29 on non-leap years
                current_date = current_date.replace(year=current_date.year + 1, day=28)
                
    elif recurrence_type == 'weekdays':
        # Monday through Friday only
        current_date = start_date
        
        while current_date <= end_date:
            if current_date.weekday() < 5 and is_valid_date(current_date, task):  # 0-4 are Mon-Fri
                dates.append(current_date)
            current_date += timedelta(days=1)
    
    # Apply occurrence limit if specified
    if task.end_type == 'occurrences' and task.end_occurrences:
        dates = dates[:task.end_occurrences]
    
    return dates


def get_nth_weekday_of_month(year, month, weekday, n):
    """
    Get the nth occurrence of a weekday in a month.
    weekday: 0=Monday, 6=Sunday
    n: 1-4 for first through fourth, 5 for last
    """
    # Get the first day of the month
    first_day = date(year, month, 1)
    first_weekday = first_day.weekday()
    
    # Calculate the first occurrence of the target weekday
    days_until_target = (weekday - first_weekday) % 7
    first_occurrence = first_day + timedelta(days=days_until_target)
    
    if n <= 4:
        # Get the nth occurrence
        target_date = first_occurrence + timedelta(weeks=n-1)
        # Check if it's still in the same month
        if target_date.month == month:
            return target_date
        else:
            return None
    else:
        # Get the last occurrence
        # Start from the first occurrence and keep adding weeks
        last_occurrence = first_occurrence
        while True:
            next_occurrence = last_occurrence + timedelta(weeks=1)
            if next_occurrence.month != month:
                break
            last_occurrence = next_occurrence
        return last_occurrence


def is_valid_date(date, task):
    """
    Check if a date is valid (not in exception list).
    """
    if task.exception_dates:
        # Convert date to string format for comparison
        date_str = date.isoformat()
        return date_str not in task.exception_dates
    return True