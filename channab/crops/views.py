from django.utils import timezone
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Q
from django import forms
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from datetime import datetime, timedelta
import calendar
from .models import Field, Crop, Task, TaskInstance, CropNote
from .forms import EditFutureInstancesForm, FieldForm, CropForm

@login_required
def create_field(request):
    if request.method == 'POST':
        form = FieldForm(request.POST, request.FILES)
        if form.is_valid():
            field = form.save(commit=False)
            field.farm = request.user.farm
            field.save()
            return redirect('crops:field_list')
    else:
        form = FieldForm()
    return render(request, 'crops/create_field.html', {'form': form})

@login_required
def edit_field(request, pk):
    field = get_object_or_404(Field, pk=pk, farm=request.user.farm)
    if request.method == 'POST':
        form = FieldForm(request.POST, request.FILES, instance=field)
        if form.is_valid():
            form.save()
            return redirect('crops:field_detail', pk=field.pk)
    else:
        form = FieldForm(instance=field)
    return render(request, 'crops/edit_field.html', {'form': form, 'field': field})

@login_required
def delete_field(request, pk):
    field = get_object_or_404(Field, pk=pk, farm=request.user.farm)
    if request.method == 'POST':
        field.delete()
        return redirect('crops:field_list')
    return render(request, 'crops/delete_field.html', {'field': field})

@login_required
def create_crop(request):
    farm = request.user.farm
    if request.method == 'POST':
        form = CropForm(request.POST)
        if form.is_valid():
            crop = form.save()
            return redirect('crops:crop_detail', pk=crop.pk)
    else:
        form = CropForm()
        # Filter fields to only show farm's fields
        form.fields['fields'].queryset = Field.objects.filter(farm=farm)
    return render(request, 'crops/create_crop.html', {'form': form})

@login_required
def edit_crop(request, pk):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), pk=pk)
    if request.method == 'POST':
        form = CropForm(request.POST, instance=crop)
        if form.is_valid():
            form.save()
            return redirect('crops:crop_detail', pk=crop.pk)
    else:
        form = CropForm(instance=crop)
        # Filter fields to only show farm's fields
        form.fields['fields'].queryset = Field.objects.filter(farm=request.user.farm)
    return render(request, 'crops/edit_crop.html', {'form': form, 'crop': crop})

@login_required
def delete_crop(request, pk):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), pk=pk)
    if request.method == 'POST':
        crop.delete()
        return redirect('crops:crop_list')
    return render(request, 'crops/delete_crop.html', {'crop': crop})

@login_required
def field_list(request):
    farm = request.user.farm
    fields = Field.objects.filter(farm=farm)
    
    # Calculate statistics
    total_area = sum(field.area for field in fields) if fields else 0
    active_crops_count = Crop.objects.filter(fields__farm=farm).distinct().count()
    pending_tasks = TaskInstance.objects.filter(task__crop__fields__farm=farm, status='pending').count()
    
    # Add additional field data
    for field in fields:
        # Add any additional field calculations here if needed
        pass
    
    context = {
        'fields': fields,
        'total_area': total_area,
        'active_crops_count': active_crops_count,
        'pending_tasks': pending_tasks,
    }
    
    return render(request, 'crops/field_list.html', context)


@login_required
def field_detail(request, pk):
    field = get_object_or_404(Field, pk=pk, farm=request.user.farm)
    
    # Add calculated fields for analytics
    field.pending_tasks = TaskInstance.objects.filter(task__crop__fields=field, status='pending').count()
    field.total_tasks = TaskInstance.objects.filter(task__crop__fields=field).count()
    field.completed_tasks = TaskInstance.objects.filter(task__crop__fields=field, status='completed').count()
    
    # Analytics data - simplified without harvest data
    field.total_production = 0  # Can be updated when harvest feature is added back
    field.avg_yield = 0
    field.productivity = 0
    field.total_cost = 0  # Can be calculated from task costs if needed
    
    # Utilization percentages (mock data for now)
    field.utilized_percentage = min(field.crops.count() * 25, 100)  # Assume each crop uses 25% of field
    field.available_percentage = 100 - field.utilized_percentage
    
    # Production timeline (mock data for charts)
    field.production_timeline_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
    field.production_timeline_data = [10, 15, 8, 12, 20, 18]  # Sample data
    
    return render(request, 'crops/field_detail.html', {'field': field})


@login_required
def crop_list(request):
    farm = request.user.farm
    crops = Crop.objects.filter(fields__farm=farm).distinct()
    
    # Stage statistics
    stage_counts = {
        'planning': crops.filter(stage='planning').count(),
        'soil_prep': crops.filter(stage='soil_prep').count(),
        'planting': crops.filter(stage='planting').count(),
        'growing': crops.filter(stage='growing').count(),
        'flowering': crops.filter(stage='flowering').count(),
        'harvesting': crops.filter(stage='harvesting').count(),
        'completed': crops.filter(stage='completed').count(),
    }
    
    # Additional statistics
    pending_tasks = TaskInstance.objects.filter(
        task__crop__fields__farm=farm,
        status='pending'
    ).count()
    
    # Check if fields are available for adding crops
    fields_available = Field.objects.filter(farm=farm).exists()

    # Determine the next upcoming task for each crop
    upcoming_tasks = {}
    for crop in crops:
        next_task = TaskInstance.objects.filter(
            task__crop=crop, 
            scheduled_date__gte=timezone.now().date(),
            status='pending'
        ).order_by('scheduled_date').first()
        upcoming_tasks[crop.id] = next_task

    return render(request, 'crops/crop_list.html', {
        'crops': crops,
        'upcoming_tasks': upcoming_tasks,
        'stage_counts': stage_counts,
        'pending_tasks': pending_tasks,
        'fields_available': fields_available,
    })

@login_required
def crop_detail(request, pk):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), pk=pk)
    
    # Basic related data
    tasks = Task.objects.filter(crop=crop)
    notes = CropNote.objects.filter(crop=crop).order_by('-creation_date')
    
    # Task statistics
    total_tasks = tasks.count()
    completed_task_instances = TaskInstance.objects.filter(
        task__crop=crop, 
        status='completed'
    ).count()
    pending_task_instances = TaskInstance.objects.filter(
        task__crop=crop, 
        status='pending'
    ).count()
    
    # Upcoming tasks
    upcoming_tasks = TaskInstance.objects.filter(
        task__crop=crop,
        scheduled_date__gte=timezone.now().date(),
        status='pending'
    ).order_by('scheduled_date')[:5]
    
    # Days since planting
    days_since_planting = (timezone.now().date() - crop.planting_date).days if crop.planting_date else 0
    
    # Expected harvest date (estimate based on crop type - this could be enhanced)
    expected_harvest_days = 120  # Default assumption, could be crop-specific
    expected_harvest_date = crop.planting_date + timedelta(days=expected_harvest_days) if crop.planting_date else None
    
    # Timeline data for charts (last 6 months of completed tasks)
    monthly_labels = []
    monthly_counts = []
    
    for i in range(6):
        month_start = (timezone.now() - timedelta(days=30*i)).replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        
        count = TaskInstance.objects.filter(
            task__crop=crop,
            completed_date__gte=month_start,
            completed_date__lte=month_end,
            status='completed'
        ).count()
        
        monthly_labels.insert(0, month_start.strftime('%b'))
        monthly_counts.insert(0, count)
    
    context = {
        'crop': crop,
        'tasks': tasks,
        'notes': notes,
        'total_tasks': total_tasks,
        'completed_task_instances': completed_task_instances,
        'pending_task_instances': pending_task_instances,
        'upcoming_tasks': upcoming_tasks,
        'days_since_planting': days_since_planting,
        'expected_harvest_date': expected_harvest_date,
        'monthly_labels': monthly_labels,
        'monthly_counts': monthly_counts,
    }
    
    return render(request, 'crops/crop_detail.html', context)



# Task Related Views


from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse
from django.contrib.auth.decorators import login_required
from .models import Task, TaskInstance, Crop
from .forms import TaskForm

from django.utils import timezone


@login_required
def mark_instance_done(request, instance_id):
    instance = get_object_or_404(TaskInstance.objects.filter(id=instance_id, task__crop__fields__farm=request.user.farm).distinct())
    instance.status = 'completed'
    if not instance.completed_date:
        instance.completed_date = timezone.now().date()
    instance.save()
    return redirect('crops:task_list', crop_id=instance.task.crop.id)

@login_required
def mark_instance_cancel(request, instance_id):
    instance = get_object_or_404(TaskInstance.objects.filter(id=instance_id, task__crop__fields__farm=request.user.farm).distinct())
    instance.status = 'canceled'
    instance.save()
    return redirect('crops:task_list', crop_id=instance.task.crop.id)


@login_required
def task_detail(request, task_id):
    task = get_object_or_404(Task.objects.filter(id=task_id, crop__fields__farm=request.user.farm).distinct())
    return render(request, 'crops/tasks/task_detail.html', {'task': task})

@login_required
def task_create(request, crop_id):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), id=crop_id)
    if request.method == 'POST':
        form = TaskForm(request.POST)
        if form.is_valid():
            task = form.save(commit=False)
            task.crop = crop
            task.save()
            TaskInstance.create_instances(task)
            return redirect('crops:task_list', crop_id=crop.id)
    else:
        form = TaskForm()
    return render(request, 'crops/tasks/task_form.html', {'form': form, 'crop': crop, 'crop_id': crop.id})


@login_required
def task_update(request, task_id):
    task = get_object_or_404(Task.objects.filter(id=task_id, crop__fields__farm=request.user.farm).distinct())
    if request.method == 'POST':
        form = TaskForm(request.POST, instance=task)
        if form.is_valid():
            task = form.save(commit=False)
            task.instances.all().delete()  # Delete existing instances
            task.save()
            TaskInstance.create_instances(task)  # Recreate instances
            return redirect('crops:task_detail', task_id=task.id)
    else:
        form = TaskForm(instance=task)
    return render(request, 'crops/tasks/task_form.html', {'form': form, 'task': task, 'crop_id': task.crop.id})




@login_required
def task_delete(request, task_id):
    task = get_object_or_404(Task.objects.filter(id=task_id, crop__fields__farm=request.user.farm).distinct())
    if request.method == 'POST':
        # Check if it's an AJAX request
        if request.headers.get('Content-Type') == 'application/json':
            task.delete()
            return JsonResponse({'success': True, 'message': 'Task deleted successfully'})
        else:
            # Traditional form submission
            task.delete()
            return redirect('crops:task_list', crop_id=task.crop.id)
    return render(request, 'crops/tasks/task_confirm_delete.html', {'task': task})

@login_required
def task_list(request, crop_id):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), id=crop_id)
    tasks = crop.tasks.all()
    task_data = []

    for task in tasks:
        instances = task.instances.all()
        upcoming_instances = []
        previous_instances = []
        has_future_instances = False

        for instance in instances:
            if instance.scheduled_date < timezone.now().date() and instance.status == 'pending':
                instance.status = 'unattended'
                instance.save()

            if instance.scheduled_date >= timezone.now().date():
                upcoming_instances.append(instance)
                has_future_instances = True
            else:
                previous_instances.append(instance)

        task_data.append({
            'task': task,
            'upcoming_instances': upcoming_instances,
            'previous_instances': previous_instances,
            'has_future_instances': has_future_instances,
        })

    return render(request, 'crops/tasks/task_list.html', {'task_data': task_data, 'crop': crop})

@login_required
def edit_future_instances(request, task_id):
    task = get_object_or_404(Task.objects.filter(id=task_id, crop__fields__farm=request.user.farm).distinct())
    if request.method == 'POST':
        form = EditFutureInstancesForm(request.POST, instance=task)
        if form.is_valid():
            task = form.save(commit=False)
            # Delete future instances
            TaskInstance.objects.filter(task=task, scheduled_date__gte=timezone.now()).delete()
            # Create new instances based on the updated task
            TaskInstance.create_instances(task)
            return redirect('crops:task_list', crop_id=task.crop.id)
    else:
        form = EditFutureInstancesForm(instance=task)
    return render(request, 'crops/tasks/edit_future_instances_form.html', {'form': form, 'task': task})

@login_required
def crops_dashboard(request):
    farm = request.user.farm
    
    # Basic statistics
    total_fields = Field.objects.filter(farm=farm).count()
    total_crops = Crop.objects.filter(fields__farm=farm).distinct().count()
    pending_tasks = TaskInstance.objects.filter(
        task__crop__fields__farm=farm, 
        status='pending'
    ).count()
    completed_tasks = TaskInstance.objects.filter(
        task__crop__fields__farm=farm, 
        status='completed'
    ).count()
    
    # Crop stages distribution
    stage_counts = {
        'planning': Crop.objects.filter(fields__farm=farm, stage='planning').distinct().count(),
        'soil_prep': Crop.objects.filter(fields__farm=farm, stage='soil_prep').distinct().count(),
        'planting': Crop.objects.filter(fields__farm=farm, stage='planting').distinct().count(),
        'growing': Crop.objects.filter(fields__farm=farm, stage='growing').distinct().count(),
        'flowering': Crop.objects.filter(fields__farm=farm, stage='flowering').distinct().count(),
        'harvesting': Crop.objects.filter(fields__farm=farm, stage='harvesting').distinct().count(),
        'completed': Crop.objects.filter(fields__farm=farm, stage='completed').distinct().count(),
    }
    
    # Monthly task completion for chart (last 6 months)
    six_months_ago = timezone.now() - timedelta(days=180)
    monthly_data = {}
    monthly_labels = []
    monthly_tasks = []
    
    for i in range(6):
        month_start = (timezone.now() - timedelta(days=30*i)).replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        
        count = TaskInstance.objects.filter(
            task__crop__fields__farm=farm,
            completed_date__gte=month_start,
            completed_date__lte=month_end,
            status='completed'
        ).count()
        
        monthly_labels.insert(0, month_start.strftime('%b'))
        monthly_tasks.insert(0, count)
    
    # Recent completed tasks
    recent_tasks = TaskInstance.objects.filter(
        task__crop__fields__farm=farm,
        status='completed'
    ).order_by('-completed_date')[:10]
    
    # Upcoming tasks
    upcoming_tasks = TaskInstance.objects.filter(
        task__crop__fields__farm=farm,
        scheduled_date__gte=timezone.now().date(),
        status='pending'
    ).order_by('scheduled_date')[:10]
    
    # Fields overview
    fields_overview = []
    for field in Field.objects.filter(farm=farm):
        field.pending_tasks = TaskInstance.objects.filter(
            task__crop__fields=field, 
            status='pending'
        ).count()
        field.completed_tasks = TaskInstance.objects.filter(
            task__crop__fields=field, 
            status='completed'
        ).count()
        fields_overview.append(field)
    
    context = {
        'total_fields': total_fields,
        'total_crops': total_crops,
        'pending_tasks': pending_tasks,
        'completed_tasks': completed_tasks,
        'stage_counts': stage_counts,
        'monthly_labels': monthly_labels,
        'monthly_tasks': monthly_tasks,
        'recent_tasks': recent_tasks,
        'upcoming_tasks': upcoming_tasks,
        'fields_overview': fields_overview,
    }
    
    return render(request, 'crops/dashboard.html', context)

@login_required
def advance_crop_stage(request, crop_id):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), id=crop_id)
    
    if request.method == 'POST':
        if crop.advance_stage():
            # Stage advancement successful
            messages.success(request, f'Crop stage advanced to {crop.get_stage_display()}')
            return redirect('crops:crop_detail', pk=crop.id)
    
    return redirect('crops:crop_detail', pk=crop.id)



@login_required
def task_management(request, crop_id):
    crop = get_object_or_404(Crop.objects.filter(fields__farm=request.user.farm).distinct(), id=crop_id)
    today = timezone.now().date()
    
    # Get all tasks for this crop
    tasks = Task.objects.filter(crop=crop)
    
    # Get all task instances
    all_instances = TaskInstance.objects.filter(task__crop=crop)
    
    # Filter instances by status and date
    upcoming_instances = all_instances.filter(
        scheduled_date__gte=today,
        status='pending'
    ).order_by('scheduled_date')[:20]
    
    completed_instances = all_instances.filter(
        status='completed'
    ).order_by('-completed_date', '-scheduled_date')[:20]
    
    overdue_instances = all_instances.filter(
        scheduled_date__lt=today,
        status='pending'
    ).order_by('-scheduled_date')
    
    # Update overdue instances to unattended
    for instance in overdue_instances:
        instance.status = 'unattended'
        instance.save()
    
    overdue_instances = all_instances.filter(
        status='unattended'
    ).order_by('-scheduled_date')
    
    # Calculate statistics
    total_tasks = tasks.count()
    pending_instances_count = upcoming_instances.count()
    overdue_instances_count = overdue_instances.count()
    completed_instances_count = completed_instances.count()
    
    # Get recurring tasks with completion data
    recurring_tasks = []
    for task in tasks.filter(recurrence__in=['daily', 'weekly', 'monthly']):
        total_instances = task.instances.count()
        completed_count = task.instances.filter(status='completed').count()
        completion_percentage = int((completed_count / total_instances * 100)) if total_instances > 0 else 0
        
        task.total_instances = total_instances
        task.completed_instances = completed_count
        task.completion_percentage = completion_percentage
        recurring_tasks.append(task)
    
    context = {
        'crop': crop,
        'today': today,
        'tasks': tasks,
        'upcoming_instances': upcoming_instances,
        'completed_instances': completed_instances,
        'overdue_instances': overdue_instances,
        'recurring_tasks': recurring_tasks,
        'total_tasks': total_tasks,
        'pending_instances': pending_instances_count,
        'overdue_instances_count': overdue_instances_count,
        'completed_instances_count': completed_instances_count,
    }
    
    return render(request, 'crops/task_management.html', context)

@login_required
def task_instance_complete(request, instance_id):
    """Mark a task instance as completed via AJAX"""
    if request.method == 'POST':
        instance = get_object_or_404(TaskInstance.objects.filter(id=instance_id, task__crop__fields__farm=request.user.farm).distinct())
        instance.status = 'completed'
        if not instance.completed_date:
            instance.completed_date = timezone.now().date()
        instance.save()
        return HttpResponse(status=200)
    return HttpResponse(status=405)

@login_required
def task_instance_cancel(request, instance_id):
    """Cancel a task instance via AJAX"""
    if request.method == 'POST':
        instance = get_object_or_404(TaskInstance.objects.filter(id=instance_id, task__crop__fields__farm=request.user.farm).distinct())
        instance.status = 'canceled'
        instance.save()
        return HttpResponse(status=200)
    return HttpResponse(status=405)

@login_required
def task_instance_reschedule(request, instance_id):
    """Reschedule a task instance via AJAX"""
    if request.method == 'POST':
        import json
        try:
            data = json.loads(request.body)
            new_date = data.get('new_date')
            if not new_date:
                return JsonResponse({'error': 'New date is required'}, status=400)
            
            instance = get_object_or_404(TaskInstance.objects.filter(id=instance_id, task__crop__fields__farm=request.user.farm).distinct())
            instance.scheduled_date = new_date
            instance.save()
            return JsonResponse({'success': True, 'message': 'Task rescheduled successfully'})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)
    return HttpResponse(status=405)

@login_required
def task_create(request, crop_id):
    """Create a new task for a crop"""
    from .task_utils import generate_task_instances
    
    crop = get_object_or_404(Crop.objects.filter(pk=crop_id, fields__farm=request.user.farm).distinct())
    
    if request.method == 'POST':
        # Create the task with all the new fields
        task = Task(
            crop=crop,
            title=request.POST.get('title'),
            description=request.POST.get('description', ''),
            recurrence=request.POST.get('recurrence', 'single'),
            start_date=request.POST.get('start_date'),
            start_time=request.POST.get('start_time') or None,
            end_time=request.POST.get('end_time') or None,
            
            # Interval settings
            interval_days=int(request.POST.get('interval_days', 1)),
            
            # Weekly settings
            monday=request.POST.get('monday') == 'true',
            tuesday=request.POST.get('tuesday') == 'true',
            wednesday=request.POST.get('wednesday') == 'true',
            thursday=request.POST.get('thursday') == 'true',
            friday=request.POST.get('friday') == 'true',
            saturday=request.POST.get('saturday') == 'true',
            sunday=request.POST.get('sunday') == 'true',
            
            # Monthly settings
            monthly_pattern=request.POST.get('monthly_pattern', 'day_of_month'),
            day_of_month=int(request.POST.get('day_of_month', 1)) if request.POST.get('day_of_month') else None,
            week_of_month=int(request.POST.get('week_of_month', 1)) if request.POST.get('week_of_month') else None,
            weekday_of_month=int(request.POST.get('weekday_of_month', 0)) if request.POST.get('weekday_of_month') else None,
            
            # End conditions
            end_type=request.POST.get('end_type', 'never'),
            end_occurrences=int(request.POST.get('end_occurrences', 1)) if request.POST.get('end_occurrences') else None,
            end_date=request.POST.get('end_date') or None,
        )
        
        # Convert string dates to date objects
        if task.start_date and isinstance(task.start_date, str):
            task.start_date = datetime.strptime(task.start_date, '%Y-%m-%d').date()
        if task.end_date and isinstance(task.end_date, str):
            task.end_date = datetime.strptime(task.end_date, '%Y-%m-%d').date()
            
        task.save()
        
        # Generate task instances based on the recurrence pattern
        scheduled_dates = generate_task_instances(task)
        
        # Create TaskInstance objects for each scheduled date
        instances = []
        for scheduled_date in scheduled_dates:
            instances.append(TaskInstance(
                task=task,
                scheduled_date=scheduled_date,
                status='pending'
            ))
        
        # Bulk create all instances
        if instances:
            TaskInstance.objects.bulk_create(instances)
        
        messages.success(request, f'Task "{task.title}" created successfully with {len(instances)} scheduled instance(s)!')
        return redirect('crops:task_management', crop_id=crop.pk)
    
    return redirect('crops:task_management', crop_id=crop.pk)

@login_required
def task_detail(request, task_id):
    """Display task details"""
    task = get_object_or_404(Task.objects.filter(pk=task_id, crop__fields__farm=request.user.farm).distinct())
    instances = task.instances.all().order_by('-scheduled_date')
    
    # Calculate counts for statistics
    completed_count = instances.filter(status='completed').count()
    pending_count = instances.filter(status='pending').count()
    canceled_count = instances.filter(status='canceled').count()
    unattended_count = instances.filter(status='unattended').count()
    
    # Add completed count to task object for template
    task.completed_count = completed_count
    
    context = {
        'task': task,
        'instances': instances,
        'crop': task.crop,
        'today': timezone.now().date(),
        'completed_count': completed_count,
        'pending_count': pending_count,
        'canceled_count': canceled_count,
        'unattended_count': unattended_count,
    }
    
    return render(request, 'crops/task_detail.html', context)
