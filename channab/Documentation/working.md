# Animal Scoring & Listing API - Comprehensive Filtering Guide

## Overview
This document explains the comprehensive animal listing and filtering API that allows mobile apps to retrieve animals with detailed scoring information and apply multiple filters.

## API Endpoint
```
GET /dairy/api/scores/list/
```

## 🔥 **IMPORTANT DEFAULT BEHAVIOR**

### **Default Response**
- **Returns ALL ACTIVE animals** from the farm (no pagination limit by default)
- Default `page_size` is **1000** to ensure all animals are returned
- Default `animal_status` is **'active'**
- **expired** and **sold** animals are only returned when explicitly requested

### **When to Use Filters**
- Use filters to narrow down specific animals you need
- For dashboard summaries, use appropriate filters for each category
- For management actions, combine multiple filters for targeted results

## 📋 **Complete Filter Parameters**

### 1. **Animal Status Filter** (🔴 **Most Important**)
**Parameter**: `animal_status`  
**Default**: `active`  
**Values**: `active`, `expired`, `sold`
**Purpose**: Controls which animals by status are returned

```python
# Backend Processing
animals = Animal.objects.filter(farm=farm, status=animal_status)
```

#### Examples:
```bash
# Get all active animals (default)
GET /dairy/api/scores/list/

# Get expired animals  
GET /dairy/api/scores/list/?animal_status=expired

# Get sold animals
GET /dairy/api/scores/list/?animal_status=sold
```

### 2. **Animal Category Filter**
**Parameter**: `animal_category`  
**Values**: `dairy`, `cow`, `buffalo`, `goat`, `sheep`, `beef`, `other`
**Purpose**: Filter by animal species/category

```python
# Backend Processing  
if animal_category:
    animals = animals.filter(category=animal_category)
```

### 3. **Animal Type Filter**
**Parameter**: `animal_type`  
**Values**: `breeder`, `pregnant`, `dry`, `milking`, `preg_milking`, `calf`, `other`
**Purpose**: Filter by animal's current type/lifecycle stage

```python
# Backend Processing
if animal_type:
    animals = animals.filter(animal_type=animal_type)
```

### 4. **Gender Filter**
**Parameter**: `animal_gender`  
**Values**: `male`, `female`
**Purpose**: Filter by animal gender

```python
# Backend Processing
if animal_gender:
    animals = animals.filter(sex=animal_gender)
```

### 5. **Age Filtering (in Months)**
**Parameters**: `min_age_months`, `max_age_months`  
**Purpose**: Filter by age range in months

```python
# Backend Processing
from dateutil.relativedelta import relativedelta

if min_age_months:
    max_birth_date = date.today() - relativedelta(months=int(min_age_months))
    animals = animals.filter(dob__lte=max_birth_date)

if max_age_months:
    min_birth_date = date.today() - relativedelta(months=int(max_age_months))
    animals = animals.filter(dob__gte=min_birth_date)
```

### 6. **Score Filtering (Two Options)**

#### Option A: Predefined Score Categories
**Parameter**: `score_filter`  
**Values**: `excellent`, `good`, `fair`, `poor`

- **`excellent`**: 80-100 points (Green)
- **`good`**: 60-79 points (Yellow)  
- **`fair`**: 40-59 points (Orange)
- **`poor`**: 0-39 points (Red)

#### Option B: Custom Score Range
**Parameters**: `min_score`, `max_score`  
**Values**: 0-100 (integers)

```python
# Backend Processing
if score_filter == 'excellent':
    animals = animals.filter(score__total_score__gte=80)
# ... other predefined filters

# OR custom range
if min_score:
    animals = animals.filter(score__total_score__gte=int(min_score))
if max_score:
    animals = animals.filter(score__total_score__lte=int(max_score))
```

### 7. **Search & Sorting**
**Parameter**: `search` - Search by animal tag  
**Parameter**: `order_by` - Sort results
- `score_desc`: Highest scores first (default)
- `score_asc`: Lowest scores first  
- `tag_asc`: Tag A-Z
- `tag_desc`: Tag Z-A

### 8. **Pagination**
**Parameter**: `page` - Page number (default: 1)  
**Parameter**: `page_size` - Items per page (default: 1000)

## 🎯 **Common API Usage Patterns**

### **1. Get All Active Animals (Default)**
```bash
GET /dairy/api/scores/list/
```
Returns all active animals with scores.

### **2. Get Animals by Category**
```bash
# All active dairy cows
GET /dairy/api/scores/list/?animal_category=dairy

# All active buffalo
GET /dairy/api/scores/list/?animal_category=buffalo
```

### **3. Get Animals by Type** 
```bash
# All active milking animals
GET /dairy/api/scores/list/?animal_type=milking

# All active calves
GET /dairy/api/scores/list/?animal_type=calf

# All active pregnant animals
GET /dairy/api/scores/list/?animal_type=pregnant
```

### **4. Get Animals by Gender**
```bash
# All active female animals
GET /dairy/api/scores/list/?animal_gender=female

# All active male animals  
GET /dairy/api/scores/list/?animal_gender=male
```

### **5. Age-Based Filtering**
```bash
# Animals older than 2 years (24 months)
GET /dairy/api/scores/list/?min_age_months=24

# Young animals under 1 year (12 months)
GET /dairy/api/scores/list/?max_age_months=12

# Animals between 6-18 months old
GET /dairy/api/scores/list/?min_age_months=6&max_age_months=18
```

### **6. Score-Based Filtering**
```bash
# Excellent performers
GET /dairy/api/scores/list/?score_filter=excellent

# Poor performers needing attention
GET /dairy/api/scores/list/?score_filter=poor

# Custom score range (70-90 points)
GET /dairy/api/scores/list/?min_score=70&max_score=90
```

### **7. Get Non-Active Animals**
```bash
# Get expired animals
GET /dairy/api/scores/list/?animal_status=expired

# Get sold animals
GET /dairy/api/scores/list/?animal_status=sold
```

### **8. Complex Multi-Filter Combinations**
```bash
# Young female milking cows with excellent scores
GET /dairy/api/scores/list/?animal_category=cow&animal_type=milking&animal_gender=female&max_age_months=36&score_filter=excellent

# Poor performing male animals that need attention
GET /dairy/api/scores/list/?animal_gender=male&score_filter=poor&order_by=score_asc

# All pregnant animals with age and score info
GET /dairy/api/scores/list/?animal_type=pregnant&min_age_months=18&order_by=score_desc
```

## 📱 **Mobile App Integration Examples**

### **Dashboard Summary Cards**
```javascript
// Get counts for different categories
const responses = await Promise.all([
    fetch('/dairy/api/scores/list/?page_size=1'), // Total active count
    fetch('/dairy/api/scores/list/?animal_type=milking&page_size=1'), // Milking count
    fetch('/dairy/api/scores/list/?animal_type=pregnant&page_size=1'), // Pregnant count
    fetch('/dairy/api/scores/list/?animal_type=dry&page_size=1'), // Dry count
    fetch('/dairy/api/scores/list/?score_filter=poor&page_size=1'), // Poor performers
]);

// Extract total_count from each response for dashboard cards
```

### **Performance Management Views**
```javascript
// Top performers list
const topPerformers = await fetch('/dairy/api/scores/list/?score_filter=excellent&order_by=score_desc&page_size=10');

// Animals needing attention
const needsAttention = await fetch('/dairy/api/scores/list/?score_filter=poor&order_by=score_asc&page_size=20');

// Young animals monitoring
const youngAnimals = await fetch('/dairy/api/scores/list/?max_age_months=12&order_by=score_desc');
```

### **Category Management**
```javascript
// Milking animals management
const milkingAnimals = await fetch('/dairy/api/scores/list/?animal_type=milking&order_by=score_desc');

// Breeding management  
const breedingAnimals = await fetch('/dairy/api/scores/list/?animal_type=breeder&animal_gender=female&min_age_months=18');

// Calf management
const calves = await fetch('/dairy/api/scores/list/?animal_type=calf&order_by=tag_asc');
```

## 📊 **API Response Format**

```json
{
    "success": true,
    "data": {
        "results": [
            {
                "id": 123,
                "tag": "COW001",
                "dob": "2020-01-15",
                "sex": "female",
                "category": "dairy",
                "animal_type": "milking",
                "status": "active",
                "image": "/media/animals/cow001.jpg",
                "score": {
                    "id": 456,
                    "animal_id": 123,
                    "animal_tag": "COW001",
                    "photo_score": 33,
                    "weight_score": 33,
                    "milk_score": 34,
                    "total_score": 100,
                    "grade": "A+",
                    "score_color": "success",
                    "last_calculated": "2024-01-15T10:30:00Z",
                    "last_photo_date": "2024-01-10",
                    "last_weight_date": "2024-01-14",
                    "days_since_photo": 5,
                    "days_since_weight": 1,
                    "milk_compliance_rate": 95.2
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 1,
            "total_count": 150,
            "page_size": 1000,
            "has_next": false,
            "has_previous": false
        }
    }
}
```

## 🔧 **Backend Processing Flow**

```python
# 1. Extract all filter parameters
animal_status = request.GET.get('animal_status', 'active')  # Default: active only
animal_category = request.GET.get('animal_category', None)
animal_type = request.GET.get('animal_type', None)
animal_gender = request.GET.get('animal_gender', None)
min_age_months = request.GET.get('min_age_months', None)
max_age_months = request.GET.get('max_age_months', None)
score_filter = request.GET.get('score_filter', None)
min_score = request.GET.get('min_score', None)
max_score = request.GET.get('max_score', None)

# 2. Build base query with status filter
animals = Animal.objects.filter(farm=farm, status=animal_status)

# 3. Apply all filters sequentially
if animal_category:
    animals = animals.filter(category=animal_category)
if animal_type:
    animals = animals.filter(animal_type=animal_type)
if animal_gender:
    animals = animals.filter(sex=animal_gender)
# ... age filters, score filters, etc.

# 4. Apply ordering and pagination
animals = animals.order_by('-score__total_score')  # default
paginator = Paginator(animals, page_size)
```

## 📝 **Terminal Debug Logging**

The API logs comprehensive filter information:

```
[SCORING API] Animal Scores List Response - Filters: [Category: dairy, Type: milking, Gender: female, Score: excellent], Page: 1/1, Results: 25, Total: 25
```

## ⚡ **Performance Notes**

1. **Default Large Page Size**: Returns all animals by default for comprehensive mobile app data
2. **Status-First Filtering**: Status filter is applied first for efficiency
3. **Automatic Score Updates**: Scores update automatically if older than 1 hour
4. **Database Optimization**: Uses `select_related('score')` to avoid N+1 queries

## 🎯 **Key Points for Mobile App Development**

1. **Default Behavior**: API returns ALL active animals unless filtered
2. **Status Control**: Use `animal_status` parameter to get expired/sold animals
3. **Comprehensive Filtering**: Combine multiple filters for specific use cases
4. **Real-time Scoring**: Scores are automatically updated and accurate
5. **Flexible Pagination**: Adjust `page_size` based on UI needs
6. **Rich Response Data**: Each animal includes complete score breakdown and metadata

This API design ensures mobile apps can efficiently retrieve exactly the animals they need for any view or functionality while maintaining high performance and comprehensive data.