# Health Module API Documentation

## Vaccine Management

### API Endpoints

#### 1. Get All Vaccines

- **Endpoint**: `/api/health/vaccines/all_vaccines/`
- **Method**: GET
- **Description**: Retrieves all vaccines for the authenticated user's farm, followed by relevant site-level vaccines
- **Headers**:
  ```
  Authorization: Token <token>
  ```
- **Query Parameters**:
  - `page`: Page number for pagination
  - `page_size`: Number of items per page (default: 10, max: 100)

- **Response Format**:
  ```json
  {
    "count": 10,
    "next": "api_url?page=2",
    "previous": null,
    "results": [
      {
        "id": "farm_1",
        "title": "Custom Farm Vaccine",
        "description": "Farm-specific vaccine",
        "animal_type": "cow",
        "dosage": "5ml",
        "months": ["June", "July"],
        "is_recommended": true,
        "is_required": true,
        "status": "Completed",
        "schedule": "June/July",
        "farm": 1,
        "created_at": "2024-03-20T10:00:00Z",
        "updated_at": "2024-03-20T10:00:00Z"
      },
      {
        "id": "site_1_1",
        "title": "FMD Vaccine",
        "description": "Foot and Mouth Disease vaccine",
        "animal_type": "cow",
        "dosage": "3MI",
        "months": ["March", "April"],
        "is_recommended": true,
        "is_required": true,
        "status": "Available",
        "schedule": "March/April",
        "farm": null,
        "created_at": null,
        "updated_at": null
      }
    ]
  }
  ```

#### 2. Get Vaccine Detail

- **Endpoint**: `/api/health/vaccines/{id}/`
- **Method**: GET
- **Description**: Retrieves detailed information for a specific vaccine
- **Headers**:
  ```
  Authorization: Token <token>
  ```
- **URL Parameters**:
  - `id`: The vaccine ID (format: "farm_1" or "site_1_1")

- **Response Format for Farm Vaccine**:
  ```json
  {
    "id": "farm_1",
    "title": "Custom Farm Vaccine",
    "description": "Farm-specific vaccine",
    "animal_type": "cow",
    "dosage": "5ml",
    "months": ["June", "July"],
    "is_recommended": true,
    "is_required": true,
    "status": "Completed",
    "schedule": "June/July",
    "farm": 1,
    "created_at": "2024-03-20T10:00:00Z",
    "updated_at": "2024-03-20T10:00:00Z",
    "type": "farm",
    "manufacturer": null,
    "code": null
  }
  ```

- **Response Format for Site Vaccine**:
  ```json
  {
    "id": "site_1_1",
    "title": "FMD Vaccine",
    "description": "Foot and Mouth Disease vaccine",
    "animal_type": "cow",
    "dosage": "3MI",
    "months": ["March", "April"],
    "is_recommended": true,
    "is_required": true,
    "status": "Available",
    "schedule": "March/April",
    "farm": null,
    "created_at": null,
    "updated_at": null,
    "type": "site",
    "manufacturer": "Vaccine Corp",
    "code": "FMD-01",
    "frequency": "Yearly"
  }
  ```

### Notes
- Authentication is required via token in the request header
- Farm-specific vaccines are listed first, followed by site-level vaccines
- Site-level vaccines are filtered to show only those relevant to the farm's animal types
- The status field indicates whether a vaccine is "Available" (for site-level) or "Completed" (for farm-level)
- IDs are prefixed with "site_" or "farm_" to distinguish between the two types
- The response is paginated with a default of 10 items per page
- The detail view includes additional fields like `type`, `manufacturer`, `code`, and `frequency` (for site vaccines)
