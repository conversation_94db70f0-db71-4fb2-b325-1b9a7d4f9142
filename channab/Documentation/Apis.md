# Accounts API Documentation

This document provides details for the API endpoints related to user accounts, profiles, employees, and salary management.

---

## 1. User Signup

-   **URL:** `/accounts/api/signup/`
-   **Method(s):** `POST`
-   **Purpose:** Allows new users to register an account.
-   **Permissions:** `AllowAny`
-   **Request Body:** 
    ```json
    {
        "mobile": "string (required)",
        "password": "string (required)",
        "first_name": "string (required)",
        "last_name": "string (optional)",
        "email": "string (optional, valid email format)",
        "farm_name": "string (required)",
        "city": "string (required)",
        "role": "string (optional, default: 'read_only', choices: 'admin', 'farm_owner', 'farm_manager', 'employee', 'read_only')",
        "profile_picture": "image file (optional)"
    }
    ```
-   **Response:**
    -   **Success (201 Created):**
        ```json
        {
            "user": {
                "id": "integer",
                "mobile": "string",
                "email": "string",
                "first_name": "string",
                "last_name": "string",
                "farm": "integer (farm_id)",
                "role": "string",
                "profile": {
                    "profile_picture": "string (url to image or null)",
                    "first_name": "string",
                    "last_name": "string",
                    "city": "string",
                    "email": "string",
                    "joining_date": "date (YYYY-MM-DD)",
                    "end_date": "date (YYYY-MM-DD or null)"
                },
                "is_active": "boolean",
                "is_active_until": "datetime (YYYY-MM-DDTHH:MM:SSZ or null)",
                "is_temporarily_active": "boolean"
            },
            "token": "string (auth token)",
            "message": "User registered successfully"
        }
        ```
    -   **Error (400 Bad Request):** If validation fails (e.g., missing fields, invalid data format).
        ```json
        {
            "field_name": ["Error message for the field"]
        }
        ```

---

## 2. User Login

-   **URL:** `/accounts/api/login/`
-   **Method(s):** `POST`
-   **Purpose:** Authenticates a user and returns an auth token.
-   **Permissions:** `AllowAny`
-   **Request Body:**
    ```json
    {
        "mobile": "string (required, user's registered mobile number)",
        "password": "string (required)"
    }
    ```
-   **Response:**
    -   **Success (200 OK):**
        ```json
        {
            "token": "string (auth token)",
            "user_id": "integer",
            "email": "string",
            "mobile": "string",
            "first_name": "string",
            "last_name": "string",
            "role": "string",
            "farm_id": "integer or null",
            "farm_name": "string or null",
            "profile_picture_url": "string (url or null)"
        }
        ```
    -   **Error (400 Bad Request):** If mobile or password is not provided.
    -   **Error (401 Unauthorized):** If authentication fails (invalid credentials).

---

## 3. User Logout

-   **URL:** `/accounts/api/logout/`
-   **Method(s):** `POST`
-   **Purpose:** Logs out the currently authenticated user by invalidating their token.
-   **Permissions:** `IsAuthenticated`
-   **Request Body:** None
-   **Response:**
    -   **Success (200 OK):**
        ```json
        {
            "message": "Successfully logged out."
        }
        ```
    -   **Error (401 Unauthorized):** If the user is not authenticated.

---

## 4. User Profile

-   **URL:** `/accounts/api/user-profile/`
-   **Method(s):** `GET`, `PUT`, `PATCH`, `DELETE`
-   **Purpose:** 
    - `GET`: Retrieves the profile of the authenticated user.
    - `PUT`/`PATCH`: Updates the profile of the authenticated user.
    - `DELETE`: Deletes the account of the authenticated user.
-   **Permissions:** `IsAuthenticated`
-   **Request Body (for `PUT`/`PATCH`):** (Based on `ProfileSerializer`)
    ```json
    {
        "profile_picture": "image file (optional)",
        "first_name": "string (required)",
        "last_name": "string (optional)",
        "city": "string (required)",
        "email": "string (optional, valid email format)",
        "joining_date": "date (YYYY-MM-DD, required)",
        "end_date": "date (YYYY-MM-DD, optional)"
    }
    ```
-   **Response:**
    -   **Success (`GET` - 200 OK, `PUT`/`PATCH` - 200 OK):** Profile data (based on `ProfileSerializer`)
        ```json
        {
            "profile_picture": "string (url to image or null)",
            "first_name": "string",
            "last_name": "string",
            "city": "string",
            "email": "string",
            "joining_date": "date (YYYY-MM-DD)",
            "end_date": "date (YYYY-MM-DD or null)"
        }
        ```
    -   **Success (`DELETE` - 204 No Content):** No content in response body.
    -   **Error (400 Bad Request):** If validation fails for `PUT`/`PATCH`.
    -   **Error (401 Unauthorized):** If the user is not authenticated.
    -   **Error (404 Not Found):** If the profile does not exist (should not happen for authenticated user).

---

## 5. Create Employee

-   **URL:** `/accounts/api/create_employee/`
-   **Method(s):** `POST`
-   **Purpose:** Allows a farm owner or admin to create a new employee/farm member for their farm.
-   **Permissions:** `IsAuthenticated` (and user must be farm owner/admin - checked in view logic)
-   **Request Body:** (Multipart/form-data, based on `CustomUserSerializer` and `ProfileSerializer`)
    -   `mobile`: string (required)
    -   `password`: string (required)
    -   `first_name`: string (required)
    -   `last_name`: string (optional)
    -   `email`: string (optional)
    -   `city`: string (required)
    -   `role`: string (required, e.g., 'employee', 'farm_manager')
    -   `joining_date`: date (YYYY-MM-DD, required)
    -   `profile_picture`: image file (optional)
-   **Response:**
    -   **Success (201 Created):** 
        ```json
        {
            "user": { // Based on CustomUserSerializer
                "id": "integer",
                "mobile": "string",
                "email": "string",
                "first_name": "string",
                "last_name": "string",
                "farm": "integer (farm_id)",
                "role": "string",
                "profile": { // Based on ProfileSerializer
                    "profile_picture": "string (url or null)",
                    "first_name": "string",
                    "last_name": "string",
                    "city": "string",
                    "email": "string",
                    "joining_date": "date",
                    "end_date": "date or null"
                },
                "is_active": "boolean",
                "is_active_until": "datetime or null",
                "is_temporarily_active": "boolean"
            },
            "message": "Employee created successfully."
        }
        ```
    -   **Error (400 Bad Request):** If validation fails or required fields are missing.
    -   **Error (403 Forbidden):** If the authenticated user is not authorized to create employees.

---

## 6. Edit Employee

-   **URL:** `/accounts/api/edit_employee/<int:pk>/`
-   **Method(s):** `PUT`, `PATCH`
-   **Purpose:** Allows a farm owner or admin to edit an existing employee/farm member's details.
-   **Permissions:** `IsAuthenticated` (and user must be farm owner/admin of the employee's farm)
-   **Path Parameters:**
    -   `pk`: integer (ID of the `CustomUser` to edit)
-   **Request Body:** (Multipart/form-data, based on `CustomUserSerializer` and `ProfileSerializer` - fields are optional for `PATCH`)
    -   `mobile`: string
    -   `first_name`: string
    -   `last_name`: string
    -   `email`: string
    -   `city`: string
    -   `role`: string
    -   `joining_date`: date (YYYY-MM-DD)
    -   `end_date`: date (YYYY-MM-DD or null) - Setting this will deactivate the user.
    -   `profile_picture`: image file
    -   `is_active`: boolean (optional, typically managed by `end_date`)
-   **Response:**
    -   **Success (200 OK):** 
        ```json
        {
            "user": { // Updated user data, based on CustomUserSerializer
                "id": "integer",
                "mobile": "string",
                "email": "string",
                "first_name": "string",
                "last_name": "string",
                // ... other fields as in CustomUserSerializer
            },
            "message": "Employee updated successfully."
        }
        ```
    -   **Error (400 Bad Request):** If validation fails.
    -   **Error (403 Forbidden):** If the authenticated user is not authorized to edit this employee.
    -   **Error (404 Not Found):** If the employee with the given `pk` does not exist.

---

## 7. Get Employee Details

-   **URL:** `/accounts/api/employee/<int:pk>/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves details of a specific employee/farm member.
-   **Permissions:** `IsAuthenticated` (user must be associated with the same farm or be a superuser)
-   **Path Parameters:**
    -   `pk`: integer (ID of the `CustomUser`)
-   **Response:**
    -   **Success (200 OK):** Employee data (based on `CustomUserSerializer`)
        ```json
        { // Based on CustomUserSerializer
            "id": "integer",
            "mobile": "string",
            "email": "string",
            "first_name": "string",
            "last_name": "string",
            "farm": "integer (farm_id)",
            "role": "string",
            "profile": { // Based on ProfileSerializer
                "profile_picture": "string (url or null)",
                "first_name": "string",
                "last_name": "string",
                "city": "string",
                "email": "string",
                "joining_date": "date",
                "end_date": "date or null"
            },
            "is_active": "boolean",
            "is_active_until": "datetime or null",
            "is_temporarily_active": "boolean"
        }
        ```
    -   **Error (403 Forbidden):** If the user is not authorized to view this employee.
    -   **Error (404 Not Found):** If the employee with the given `pk` does not exist.

---

## 8. List Active Employees

-   **URL:** `/accounts/api/active-employees/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves a list of active employees for the authenticated user's farm, including basic salary status.
-   **Permissions:** `IsAuthenticated`
-   **Response:**
    -   **Success (200 OK):** A list of active employees.
        ```json
        [
            {
                "employee_id": "integer (user.id)",
                "first_name": "string",
                "last_name": "string",
                "mobile": "string",
                "email": "string",
                "user_id": "integer (user.id)",
                "role": "string",
                "joining_date": "date (YYYY-MM-DD)",
                "end_date": "date (YYYY-MM-DD or null)",
                "status": "string ('Active' or 'Inactive')",
                "profile_picture": "string (url to image or null)",
                "city": "string",
                "salary_info": {
                    "total_monthly_salary": "decimal",
                    "expected_salary_till_now": "decimal",
                    "total_salary_received": "decimal",
                    "remaining_salary": "decimal"
                }
            }
            // ... more employees
        ]
        ```
    -   **Error (401 Unauthorized):** If the user is not authenticated.

---

## 9. Add Salary Transaction

-   **URL:** `/accounts/api/add-salary-transaction/`
-   **Method(s):** `POST`, `GET`
-   **Purpose:** 
    - `POST`: Adds a new salary transaction for an employee. Also creates/updates a corresponding expense record.
    - `GET`: Retrieves salary components for a given farm member (employee).
-   **Permissions:** `IsAuthenticated`
-   **Request (`POST`):** (Multipart/form-data or application/json)
    -   `farm_member`: integer (ID of the `CustomUser` - employee, required)
    -   `component`: integer (ID of the `SalaryComponent`, required)
    -   `amount_paid`: decimal (required)
    -   `transaction_date`: date (YYYY-MM-DD, required)
    -   `description`: string (optional)
    -   `receipt_image`: image file (optional)
-   **Request (`GET`):**
    -   **Query Parameters:**
        -   `farm_member`: integer (ID of the `CustomUser` - employee, required)
-   **Response (`POST`):**
    -   **Success (201 Created):** Salary transaction data (based on `SalaryTransactionSerializer`)
        ```json
        {
            "id": "integer",
            "farm_member": "integer (user_id)",
            "component": "integer (component_id)",
            "component_name": "string",
            "amount_paid": "decimal string",
            "transaction_date": "date (YYYY-MM-DD)",
            "description": "string or null",
            "receipt_image": "string (url to image or null)"
        }
        ```
    -   **Error (400 Bad Request):** If validation fails (e.g., missing fields, invalid IDs).
    -   **Error (404 Not Found):** If `farm_member` or `component` does not exist.
-   **Response (`GET`):**
    -   **Success (200 OK):**
        ```json
        {
            "components": [
                { // Based on SalaryComponentSerializer
                    "id": "integer",
                    "name": "string",
                    "amount": "decimal string",
                    "duration": "string (e.g., 'Monthly', 'One-Time')",
                    "employee_name": "string",
                    "employee_id": "integer",
                    "profile_id": "integer"
                }
                // ... more components
            ]
        }
        ```
    -   **Error (400 Bad Request):** If `farm_member` query parameter is missing.
    -   **Error (404 Not Found):** If `farm_member` does not exist.

---

## 10. Edit Salary Transaction

-   **URL:** `/accounts/api/edit-salary-transaction/<int:pk>/`
-   **Method(s):** `PUT`, `PATCH`
-   **Purpose:** Updates an existing salary transaction. Also updates the corresponding expense record.
-   **Permissions:** `IsAuthenticated` (user must own the transaction or be an admin of the farm)
-   **Path Parameters:**
    -   `pk`: integer (ID of the `SalaryTransaction` to edit)
-   **Request Body:** (Multipart/form-data or application/json - fields are optional for `PATCH`)
    -   `farm_member`: integer (ID of the `CustomUser` - employee)
    -   `component`: integer (ID of the `SalaryComponent`)
    -   `amount_paid`: decimal
    -   `transaction_date`: date (YYYY-MM-DD)
    -   `description`: string
    -   `receipt_image`: image file (pass `null` or empty to remove, or new file to update)
-   **Response:**
    -   **Success (200 OK):** Updated salary transaction data (based on `SalaryTransactionSerializer`)
        ```json
        {
            "id": "integer",
            "farm_member": "integer (user_id)",
            "component": "integer (component_id)",
            "component_name": "string",
            "amount_paid": "decimal string",
            "transaction_date": "date (YYYY-MM-DD)",
            "description": "string or null",
            "receipt_image": "string (url to image or null)"
        }
        ```
    -   **Error (400 Bad Request):** If validation fails.
    -   **Error (403 Forbidden):** If the user is not authorized to edit this transaction.
    -   **Error (404 Not Found):** If the salary transaction, employee, or component does not exist.

---

## 11. Delete Salary Transaction

-   **URL:** `/accounts/api/delete-salary-transaction/<int:pk>/`
-   **Method(s):** `DELETE`
-   **Purpose:** Deletes an existing salary transaction. Also deletes the corresponding expense record.
-   **Permissions:** `IsAuthenticated` (user must own the transaction or be an admin of the farm)
-   **Path Parameters:**
    -   `pk`: integer (ID of the `SalaryTransaction` to delete)
-   **Response:**
    -   **Success (204 No Content):** No content in response body.
    -   **Error (403 Forbidden):** If the user is not authorized to delete this transaction.
    -   **Error (404 Not Found):** If the salary transaction does not exist.

---

## 12. List Salary Components for an Employee

-   **URL:** `/accounts/api/employees/<int:employee_id>/salary_components/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves all salary components for a specific employee.
-   **Permissions:** `IsAuthenticated`
-   **Path Parameters:**
    -   `employee_id`: integer (ID of the `CustomUser` - employee)
-   **Response:**
    -   **Success (200 OK):** A list of salary components (based on `SalaryComponentSerializer`)
        ```json
        [
            {
                "id": "integer",
                "name": "string",
                "amount": "decimal string",
                "duration": "string (e.g., 'Monthly', 'One-Time')",
                "employee_name": "string",
                "employee_id": "integer",
                "profile_id": "integer"
            }
            // ... more components
        ]
        ```
    -   **Error (404 Not Found):** If the employee does not exist.

---

## 13. Create Salary Component

-   **URL:** `/accounts/api/salary_components/create/`
-   **Method(s):** `POST`
-   **Purpose:** Creates a new salary component for an employee.
-   **Permissions:** `IsAuthenticated`
-   **Request Body:** (Based on `SalaryComponentSerializer`)
    ```json
    {
        "employee_id": "integer (ID of the CustomUser - employee, required)",
        "name": "string (required)",
        "amount": "decimal (required)",
        "duration": "string (required, e.g., 'Monthly', 'Daily', 'Hourly', 'One-Time')"
    }
    ```
-   **Response:**
    -   **Success (201 Created):** Created salary component data (based on `SalaryComponentSerializer`)
        ```json
        {
            "id": "integer",
            "name": "string",
            "amount": "decimal string",
            "duration": "string",
            "employee_name": "string",
            "employee_id": "integer",
            "profile_id": "integer"
        }
        ```
    -   **Error (400 Bad Request):** If validation fails (e.g., missing `employee_id`, `employee_id` not found).

---

## 14. Update Salary Component

-   **URL:** `/accounts/api/salary_components/update/<int:pk>/`
-   **Method(s):** `PUT`, `PATCH`
-   **Purpose:** Updates an existing salary component.
-   **Permissions:** `IsAuthenticated`, `IsOwnerOrAdmin` (custom permission: user must be the employee the component belongs to, or an admin)
-   **Path Parameters:**
    -   `pk`: integer (ID of the `SalaryComponent` to update)
-   **Request Body:** (Based on `SalaryComponentSerializer` - fields are optional for `PATCH`)
    ```json
    {
        "name": "string",
        "amount": "decimal",
        "duration": "string"
        // employee_id cannot be changed here
    }
    ```
-   **Response:**
    -   **Success (200 OK):** Updated salary component data (based on `SalaryComponentSerializer`)
        ```json
        {
            "id": "integer",
            "name": "string",
            "amount": "decimal string",
            "duration": "string",
            "employee_name": "string",
            "employee_id": "integer",
            "profile_id": "integer"
        }
        ```
    -   **Error (400 Bad Request):** If validation fails.
    -   **Error (403 Forbidden):** If the user is not authorized to update this component.
    -   **Error (404 Not Found):** If the salary component does not exist.

---

## 15. Delete Salary Component

-   **URL:** `/accounts/api/salary_components/delete/<int:pk>/`
-   **Method(s):** `DELETE`
-   **Purpose:** Deletes an existing salary component.
-   **Permissions:** `IsAuthenticated` (further checks might be in the view, e.g., owner or admin)
-   **Path Parameters:**
    -   `pk`: integer (ID of the `SalaryComponent` to delete)
-   **Response:**
    -   **Success (204 No Content):** No content in response body.
    -   **Error (403 Forbidden):** If the user is not authorized.
    -   **Error (404 Not Found):** If the salary component does not exist.

---

## 16. List Salary Components for a Farm

-   **URL:** `/accounts/api/farms/<int:farm_id>/salary_components/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves all salary components for all employees associated with a specific farm.
-   **Permissions:** `IsAuthenticated`
-   **Path Parameters:**
    -   `farm_id`: integer (ID of the `Farm`)
-   **Response:**
    -   **Success (200 OK):** A list of salary components (based on `SalaryComponentSerializers` - note the plural 'Serializers')
        ```json
        [
            {
                "id": "integer",
                "employee_name": "string",
                "employee_id": "integer",
                "profile_id": "integer",
                "name": "string",
                "amount": "decimal string",
                "duration": "string"
            }
            // ... more components
        ]
        ```
    -   **Error (404 Not Found):** If the farm does not exist.

---

## 17. List Salary Transactions for an Employee

-   **URL:** `/accounts/api/employees/<int:employee_id>/salary_transactions/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves all salary transactions for a specific employee, optionally filtered by date range.
-   **Permissions:** `IsAuthenticated`
-   **Path Parameters:**
    -   `employee_id`: integer (ID of the `CustomUser` - employee)
-   **Query Parameters (Optional):**
    -   `start_date`: date (YYYY-MM-DD)
    -   `end_date`: date (YYYY-MM-DD)
-   **Response:**
    -   **Success (200 OK):** 
        ```json
        {
            "transactions": [
                { // Based on SalaryTransactionSerializer
                    "id": "integer",
                    "farm_member": "integer (user_id)",
                    "component": "integer (component_id)",
                    "component_name": "string",
                    "amount_paid": "decimal string",
                    "transaction_date": "date (YYYY-MM-DD)",
                    "description": "string or null",
                    "receipt_image": "string (url to image or null)"
                }
                // ... more transactions
            ],
            "total_paid": "decimal"
        }
        ```
    -   **Error (404 Not Found):** If the employee does not exist.

---

## 18. Get Salary Info for an Employee

-   **URL:** `/accounts/api/employees/<int:employee_id>/salary_info/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves calculated salary information for a specific employee (total monthly, expected till now, total received, remaining).
-   **Permissions:** `IsAuthenticated`
-   **Path Parameters:**
    -   `employee_id`: integer (ID of the `CustomUser` - employee)
-   **Response:**
    -   **Success (200 OK):**
        ```json
        {
            "total_monthly_salary": "decimal",
            "expected_salary_till_now": {
                "amount": "decimal",
                "days_worked_this_month": "integer",
                "days_in_month": "integer"
            },
            "total_salary_received": "decimal",
            "remaining_salary": "decimal",
            "unpaid_leave_days_this_month": "integer",
            "unpaid_leave_deduction": "decimal"
        }
        ```
    -   **Error (404 Not Found):** If the employee does not exist.

---

## 19. Check Salary Component Details

-   **URL:** `/accounts/api/salary_components/check/<int:component_id>/`
-   **Method(s):** `GET`
-   **Purpose:** Retrieves details for a specific salary component, including the employee it's linked to.
-   **Permissions:** `IsAuthenticated`
-   **Path Parameters:**
    -   `component_id`: integer (ID of the `SalaryComponent`)
-   **Response:**
    -   **Success (200 OK):**
        ```json
        {
            "id": "integer (component_id)",
            "name": "string (component name)",
            "amount": "decimal string",
            "duration": "string",
            "employee_id": "integer (user_id of the employee)",
            "employee_name": "string (employee's full name)"
        }
        ```
    -   **Error (404 Not Found):** If the salary component does not exist.

---

## 20. Delete Account (User Initiated)

-   **URL:** `/accounts/delete-account/` (Note: This URL seems to be from `views_api.DeleteAccountView` but is not prefixed with `/api/` in `urls.py`. It might serve both HTML and JSON depending on Accept header).
-   **Method(s):** `GET`, `POST`
-   **Purpose:** 
    - `GET`: Renders a confirmation page for account deletion.
    - `POST`: Deletes the authenticated user's account after confirmation (e.g., password entry).
-   **Permissions:** `AllowAny` (but `POST` likely requires authentication implicitly or via session to know *which* account to delete, and then password confirmation).
-   **Request (`POST`):** 
    - Typically form data from the confirmation page, including password for verification.
    ```json
    // Example if JSON is accepted for POST
    {
        "password": "string (user's current password)"
    }
    ```
-   **Response (`POST`):**
    -   **Success (200 OK or redirect for HTML; 200 OK or 204 No Content for API):**
        ```json
        // If JSON response
        {
            "message": "Account deleted successfully."
        }
        ```
    -   **Error (400 Bad Request / 401 Unauthorized):** If password verification fails or user not properly identified.

**Note:** Some API endpoints like `/api/login/` and `/api/logout/` in `urls.py` point to `views.LoginView` and `views.LogoutView` respectively, not `views_api`. Their behavior might differ slightly from typical DRF API views if they are standard Django views. The documentation above for Login/Logout assumes they behave like DRF TokenAuthentication views. If they are session-based or use different mechanisms, the request/response for token might not apply directly.
