# Animal Scoring System API Documentation

## Overview
This document provides comprehensive API documentation for the Animal Scoring System endpoints. All APIs require authentication via Token Authentication.

## Base URL
All endpoints are prefixed with `/dairy/api/scores/`

## Authentication
All endpoints require authentication using Token Authentication:
```
Authorization: Token your-api-token-here
```

## API Endpoints

### 1. Farm Score Dashboard API

**Endpoint:** `GET /dairy/api/scores/dashboard/`

**Description:** Get farm-wide scoring statistics and dashboard data.

**Request Parameters:** None

**Response Format:**
```json
{
    "success": true,
    "data": {
        "total_active_animals": 150,
        "average_score": 72.5,
        "excellent_count": 45,
        "good_count": 60,
        "fair_count": 30,
        "poor_count": 15,
        "top_performers": [
            {
                "id": 123,
                "tag": "COW001",
                "dob": "2020-01-15",
                "sex": "female",
                "category": 1,
                "animal_type": "milking",
                "status": "active",
                "image": "/media/animals/cow001.jpg",
                "score": {
                    "id": 456,
                    "animal_id": 123,
                    "animal_tag": "COW001",
                    "photo_score": 33,
                    "weight_score": 33,
                    "milk_score": 34,
                    "total_score": 100,
                    "grade": "A+",
                    "score_color": "success",
                    "last_calculated": "2024-01-15T10:30:00Z",
                    "last_photo_date": "2024-01-10",
                    "last_weight_date": "2024-01-14",
                    "days_since_photo": 5,
                    "days_since_weight": 1,
                    "milk_compliance_rate": 95.2
                }
            }
        ],
        "needs_attention": [
            {
                "id": 124,
                "tag": "COW002",
                "score": {
                    "total_score": 35,
                    "grade": "F",
                    "score_color": "danger"
                }
            }
        ]
    }
}
```

**Response Fields:**
- `total_active_animals`: Total number of active animals in the farm
- `average_score`: Average score across all active animals
- `excellent_count`: Number of animals with scores 80-100
- `good_count`: Number of animals with scores 60-79
- `fair_count`: Number of animals with scores 40-59
- `poor_count`: Number of animals with scores 0-39
- `top_performers`: Array of top 10 performing animals (score ≥ 80)
- `needs_attention`: Array of up to 20 animals needing attention (score < 60)

**Error Response:**
```json
{
    "success": false,
    "message": "Error message here"
}
```

---

### 2. Animal Score Detail API

**Endpoint:** `GET /dairy/api/scores/animal/{animal_id}/`

**Description:** Get detailed scoring information for a specific animal.

**Request Parameters:**
- `animal_id` (URL parameter): ID of the animal

**Response Format:**
```json
{
    "success": true,
    "data": {
        "id": 456,
        "animal_id": 123,
        "animal_tag": "COW001",
        "photo_score": 33,
        "weight_score": 17,
        "milk_score": 34,
        "total_score": 84,
        "grade": "A",
        "score_color": "success",
        "last_calculated": "2024-01-15T10:30:00Z",
        "last_photo_date": "2024-01-10",
        "last_weight_date": "2024-01-05",
        "days_since_photo": 5,
        "days_since_weight": 10,
        "milk_compliance_rate": 88.5
    }
}
```

**Response Fields:**
- `id`: Score record ID
- `animal_id`: Animal ID
- `animal_tag`: Animal tag/identifier
- `photo_score`: Photo component score (0-33 points)
- `weight_score`: Weight component score (0-33 points)
- `milk_score`: Milk component score (0-34 points)
- `total_score`: Total score (0-100 points)
- `grade`: Letter grade (A+, A, B, C, D, F)
- `score_color`: Color classification (success, warning, danger)
- `last_calculated`: When score was last calculated
- `last_photo_date`: Date of last photo update (null if no photo)
- `last_weight_date`: Date of last weight record (null if no weight)
- `days_since_photo`: Days since last photo update (null if no photo)
- `days_since_weight`: Days since last weight record (null if no weight)
- `milk_compliance_rate`: Milk recording compliance percentage (null for non-milking animals)

**Error Responses:**
```json
{
    "success": false,
    "message": "Animal not found or does not belong to your farm"
}
```

---

### 3. Update Animal Scores API

**Endpoint:** `POST /dairy/api/scores/update/`

**Description:** Update scores for all active animals in the farm or specific animals.

**Request Body (Optional):**
```json
{
    "animal_ids": [123, 124, 125]
}
```

**Request Parameters:**
- `animal_ids` (optional): Array of specific animal IDs to update. If not provided, updates all active animals.

**Response Format:**
```json
{
    "success": true,
    "message": "Successfully updated scores for 150 animals",
    "data": {
        "updated_count": 150,
        "total_requested": 150
    }
}
```

**Response Fields:**
- `updated_count`: Number of animals successfully updated
- `total_requested`: Total number of animals requested for update

**Error Responses:**
```json
{
    "success": false,
    "message": "Some animal IDs are invalid or do not belong to your farm"
}
```

---

### 4. Animal Scores List API

**Endpoint:** `GET /dairy/api/scores/list/`

**Description:** Get paginated list of all animal scores with filtering and sorting options.

**Query Parameters:**
- `score_filter` (optional): Filter by score range
  - `excellent`: 80-100 points
  - `good`: 60-79 points
  - `fair`: 40-59 points
  - `poor`: 0-39 points
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 20, max: 100)
- `search` (optional): Search by animal tag
- `order_by` (optional): Sort order
  - `score_desc`: Score descending (default)
  - `score_asc`: Score ascending
  - `tag_asc`: Tag ascending
  - `tag_desc`: Tag descending
- `min_age_months` (optional): Minimum age in months (animals older than this)
- `max_age_months` (optional): Maximum age in months (animals younger than this)

**Example Requests:**
```
# Basic filtering by score
GET /dairy/api/scores/list/?score_filter=excellent&page=1&page_size=20&order_by=score_desc

# Age filtering - animals older than 24 months (2 years)
GET /dairy/api/scores/list/?min_age_months=24&page_size=50

# Age range filtering - animals between 12-36 months with excellent scores
GET /dairy/api/scores/list/?min_age_months=12&max_age_months=36&score_filter=excellent

# Combined filtering - poor performing young animals
GET /dairy/api/scores/list/?score_filter=poor&max_age_months=18&order_by=score_asc
```

**Response Format:**
```json
{
    "success": true,
    "data": {
        "results": [
            {
                "id": 123,
                "tag": "COW001",
                "dob": "2020-01-15",
                "sex": "female",
                "category": 1,
                "animal_type": "milking",
                "status": "active",
                "image": "/media/animals/cow001.jpg",
                "score": {
                    "id": 456,
                    "animal_id": 123,
                    "animal_tag": "COW001",
                    "photo_score": 33,
                    "weight_score": 33,
                    "milk_score": 34,
                    "total_score": 100,
                    "grade": "A+",
                    "score_color": "success",
                    "last_calculated": "2024-01-15T10:30:00Z"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_count": 45,
            "page_size": 20,
            "has_next": true,
            "has_previous": false
        }
    }
}
```

**Response Fields:**
- `results`: Array of animals with their score information
- `pagination`: Pagination metadata
  - `current_page`: Current page number
  - `total_pages`: Total number of pages
  - `total_count`: Total number of items
  - `page_size`: Items per page
  - `has_next`: Whether there's a next page
  - `has_previous`: Whether there's a previous page

## Score Calculation Logic

### Photo Score (33 points)
- **33 points**: Photo updated within last 60 days
- **25 points**: Photo updated within last 90 days
- **17 points**: Photo updated within last 120 days
- **8 points**: Photo updated within last 180 days
- **0 points**: No recent photo update

### Weight Score (33 points)
- **33 points**: Weight recorded within last 30 days
- **17 points**: Weight recorded within last 60 days
- **8 points**: Weight recorded within last 90 days
- **0 points**: No recent weight record

### Milk Score (34 points)
**For milking animals (female, type: milking/preg_milking):**
- Based on **85% compliance rate** for the milking period
- **Grace Period**: First 7 days after changing to milking type (automatic 34 points)
- **Scoring Period**: Lesser of 30 days or days since becoming a milking animal
- **Point Distribution**:
  - **34 points**: ≥85% of expected daily records
  - **26 points**: ≥64% of expected daily records (75% of target)
  - **17 points**: ≥43% of expected daily records (50% of target)
  - **9 points**: ≥21% of expected daily records (25% of target)
  - **0 points**: <21% of expected daily records

**For non-milking animals:** Automatically receives 34 points

## Grade System
- **A+**: 90-100 points
- **A**: 80-89 points
- **B**: 70-79 points
- **C**: 60-69 points
- **D**: 50-59 points
- **F**: Below 50 points

## Color Coding
- **success** (Green): 80-100 points - Excellent management
- **warning** (Yellow): 60-79 points - Good management
- **orange**: 40-59 points - Fair management, needs attention
- **danger** (Red): 0-39 points - Poor management, urgent attention needed

## Error Handling

### Common Error Responses
```json
{
    "success": false,
    "message": "Detailed error message"
}
```

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `401`: Unauthorized (missing or invalid token)
- `403`: Forbidden (animal doesn't belong to user's farm)
- `404`: Not Found (animal not found)
- `500`: Internal Server Error

## Usage Examples

### Get Farm Dashboard
```bash
curl -H "Authorization: Token your-token" \
     http://your-domain/dairy/api/scores/dashboard/
```

### Get Specific Animal Score
```bash
curl -H "Authorization: Token your-token" \
     http://your-domain/dairy/api/scores/animal/123/
```

### Update All Animal Scores
```bash
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     http://your-domain/dairy/api/scores/update/
```

### Update Specific Animals
```bash
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     -d '{"animal_ids": [123, 124, 125]}' \
     http://your-domain/dairy/api/scores/update/
```

### Get Filtered Animals List
```bash
curl -H "Authorization: Token your-token" \
     "http://your-domain/dairy/api/scores/list/?score_filter=excellent&page=1&page_size=10"
```

## Integration Notes

1. **Automatic Score Updates**: Scores are automatically updated when they are older than 1 hour
2. **Performance**: The dashboard API updates outdated scores automatically, which may take some time for large farms
3. **Pagination**: Always use pagination for the scores list API to avoid performance issues
4. **Caching**: Consider implementing client-side caching for dashboard data as it's computationally expensive
5. **Real-time Updates**: Scores are not updated in real-time; they require manual refresh or automatic hourly updates