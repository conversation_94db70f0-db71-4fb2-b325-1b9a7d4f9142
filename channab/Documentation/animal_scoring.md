# Animal Scoring System Documentation

## Overview
The Animal Scoring System is a comprehensive feature that automatically evaluates and scores animals based on key management criteria. This system helps farm managers quickly identify which animals need attention and which are being well-managed.

## Scoring Components

The scoring system evaluates animals on three key components for a total possible score of 100:

### 1. Photo Score (33 points)
- **33 points**: Photo updated within the last 60 days
- **25 points**: Photo updated within the last 90 days  
- **17 points**: Photo updated within the last 120 days
- **8 points**: Photo updated within the last 180 days
- **0 points**: No recent photo update

### 2. Weight Score (33 points)
- **33 points**: Weight recorded within the last 30 days
- **17 points**: Weight recorded within the last 60 days
- **8 points**: Weight recorded within the last 90 days
- **0 points**: No recent weight record

### 3. Milk Score (34 points)
- **For milking animals (female, type: milking/preg_milking)**:
  - Scoring is based on **85% compliance rate** for the milking period
  - **Grace Period**: First 7 days after changing to milking type (automatic 34 points)
  - **Scoring Period**: Lesser of 30 days or days since becoming a milking animal
  - **Point Distribution**:
    - **34 points**: ≥85% of expected daily records
    - **26 points**: ≥64% of expected daily records (75% of target)
    - **17 points**: ≥43% of expected daily records (50% of target)
    - **9 points**: ≥21% of expected daily records (25% of target)
    - **0 points**: <21% of expected daily records
- **For non-milking animals**: Automatically receives 34 points

**Example**: If an animal was changed to "milking" 15 days ago:
- Expected records = 15 days × 0.85 = 13 records
- If they have 10 records, that's 77% compliance = 26 points

## Grade System

Based on the total score, animals receive letter grades:
- **A+**: 90-100 points
- **A**: 80-89 points
- **B**: 70-79 points
- **C**: 60-69 points
- **D**: 50-59 points
- **F**: Below 50 points

## Color Coding

The system uses color codes for quick visual identification:
- **Green** (Success): 80-100 points - Excellent management
- **Yellow** (Warning): 60-79 points - Good management
- **Orange**: 40-59 points - Fair management, needs attention
- **Red** (Danger): 0-39 points - Poor management, urgent attention needed

## Features

### 1. Animal List Integration
- Scores are displayed prominently next to each animal's tag in the animal list
- Mini score component indicators show individual component scores
- Quick access to detailed score breakdown via the chart icon

### 2. Score Dashboard
Access via "Score Dashboard" button in the animal list:
- Overview statistics: total active animals, average score, score distribution
- Visual charts showing score distribution across the farm
- Top performing animals table
- Animals needing attention with specific issue indicators
- One-click access to update all scores
- **Note**: Only active animals are included in scoring calculations

### 3. Individual Score Details
Click on any animal's score or chart icon to view:
- Large circular progress indicator showing overall score
- Detailed breakdown of each scoring component
- Progress bars for each component
- Last update timestamps for each component
- Complete scoring criteria reference table

### 4. Automatic Score Updates
- Scores are automatically recalculated when:
  - Viewing individual animal score details (if older than 1 hour)
  - Accessing the farm score dashboard
  - Manually triggering update via "Update Scores" button

## Implementation Details

### Models
- **AnimalScore**: One-to-one relationship with Animal model
- Tracks individual component scores and metadata
- Automatically calculates total score and grade

### URLs
- `/dairy/scores/update/` - Update all animal scores
- `/dairy/scores/animal/<id>/` - View individual animal score details
- `/dairy/scores/dashboard/` - Farm-wide scoring dashboard

### Templates
- `animal_score_detail.html` - Individual animal score breakdown
- `farm_score_dashboard.html` - Farm-wide scoring overview
- `animal_list_partial.html` - Score display in animal list

## Future Enhancements

The scoring system is designed to be extensible. Future scoring components could include:
- Feed intake tracking
- Medical treatment compliance
- Growth rate tracking
- Reproduction efficiency
- Market readiness indicators
- Health and vaccination tracking
- Breeding performance metrics

## Usage Tips

1. **Regular Updates**: Encourage farm staff to regularly update photos and weight records to maintain accurate scores

2. **Focus on Red Scores**: Animals with red scores (0-39) need immediate attention - check what components are missing

3. **Use Dashboard Weekly**: Review the scoring dashboard weekly to identify trends and animals needing attention

4. **Set Goals**: Aim to maintain an average farm score above 70 for optimal management

5. **Component Analysis**: Use the mini component indicators to quickly identify specific areas needing improvement without opening detail views

6. **Active Animals Only**: Remember that only animals with 'active' status are included in scoring calculations