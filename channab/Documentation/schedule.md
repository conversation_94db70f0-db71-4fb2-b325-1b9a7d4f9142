✅ 1. Single (One-time) Schedule
Description: Runs only once at a specified date and time.

Example: June 10, 2025 @ 10:00 AM

✅ 2. Recurring (Repeated) Schedule
Recurrence can be defined using frequency and pattern:

🔁 a. Daily
Every day

Every N days (e.g., every 2 days)

Every weekday (Monday to Friday)

Every day with gap (e.g., every other day)

🔁 b. Weekly
Every week on a specific day (e.g., every Monday)

Multiple days a week (e.g., Mon, Wed, Fri)

Every N weeks (e.g., every 2 weeks)

🔁 c. Monthly
On a specific date each month (e.g., 10th)

On a specific weekday (e.g., second Monday of each month)

Every N months

🔁 d. Yearly
Once a year on a specific date (e.g., Jan 1st)

Every N years

🔁 e. Custom Interval
Every 30 minutes, 1 hour, 5 hours, etc.

Repeat every X hours/days/weeks/months

🔄 Other Options
Start Date / End Date: Define schedule period

Repeat Until: End after N occurrences or a specific date

Pause / Resume: Temporarily suspend the schedule

Skip Dates: Exclude holidays or specific dates

Time of Execution: Define exact time for each recurrence

