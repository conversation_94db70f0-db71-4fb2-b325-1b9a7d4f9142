# Mobile Dashboard API Documentation

## Overview
The Mobile Dashboard API provides a comprehensive overview of farm operations, designed specifically for mobile applications. It aggregates data from all farm management modules into a single, optimized endpoint.

## Endpoint
**URL:** `/api/mobile-dashboard/`  
**Method:** `GET`  
**Authentication:** Required (Token Authentication)

## Query Parameters

| Parameter | Type | Description | Default | Options |
|-----------|------|-------------|---------|---------|
| `time_filter` | string | Time period for data aggregation | `today` | `today`, `this_week`, `this_month`, `custom_range` |
| `start_date` | string | Start date for custom range (YYYY-MM-DD) | - | Required when `time_filter=custom_range` |
| `end_date` | string | End date for custom range (YYYY-MM-DD) | - | Required when `time_filter=custom_range` |

## Response Structure

### Success Response (200 OK)

```json
{
  "period": {
    "time_filter": "today",
    "start_date": "2025-06-12",
    "end_date": "2025-06-12"
  },
  "animals": {
    "total_count": 150,
    "active_count": 142,
    "inactive_count": 8,
    "average_score": 78.5,
    "top_performers": [
      {
        "animal_id": 123,
        "tag": "BF001",
        "latest_score": 95.5,
        "total_milk_today": 22.5
      }
    ],
    "low_score_alerts": 8
  },
  "milk": {
    "first_time": 850.5,
    "second_time": 720.0,
    "third_time": 450.2,
    "total": 2020.7,
    "daily_average": 2020.7,
    "vs_yesterday": 125.3
  },
  "finances": {
    "income": 5250.00,
    "expense": 2100.00,
    "balance": 3150.00,
    "milk_sales_revenue": 4800.00,
    "pending_payments": 850.00,
    "employee_salaries_due": 3200.00
  },
  "today_activity": {
    "milk_records_count": 142,
    "tasks_completed": 12,
    "pending_tasks": 5,
    "overdue_tasks": 2,
    "animal_events": 3,
    "vaccinations_due": 2
  },
  "performance": {
    "milk_per_animal_avg": 14.2,
    "employee_count": 12,
    "active_fields": 8,
    "crops_in_progress": 5,
    "vaccination_compliance": 87.5
  },
  "alerts": [
    {
      "type": "low_score_animals",
      "message": "8 animals have low performance scores",
      "count": 8,
      "priority": "high"
    },
    {
      "type": "overdue_tasks",
      "message": "2 tasks are overdue",
      "count": 2,
      "priority": "medium"
    },
    {
      "type": "overdue_vaccinations",
      "message": "3 vaccinations are overdue",
      "count": 3,
      "priority": "high"
    }
  ],
  "recent_activities": [
    {
      "type": "milk_record",
      "message": "Animal BF123 produced 22L today",
      "timestamp": "2025-06-12T10:30:00Z",
      "related_id": 1234
    },
    {
      "type": "task_completed",
      "message": "Task \"Field irrigation\" completed",
      "timestamp": "2025-06-12T09:15:00Z",
      "related_id": 567
    },
    {
      "type": "animal_event",
      "message": "Animal BF001: Health checkup",
      "timestamp": "2025-06-12T08:45:00Z",
      "related_id": 890
    }
  ]
}
```

## Response Fields Description

### Period Object
- `time_filter`: The applied time filter
- `start_date`: Start date of the data period
- `end_date`: End date of the data period

### Animals Object
- `total_count`: Total number of animals on the farm
- `active_count`: Number of active animals
- `inactive_count`: Number of inactive animals
- `average_score`: Average performance score of active animals
- `top_performers`: Array of top 5 performing animals with their scores and today's milk production
- `low_score_alerts`: Number of animals with performance score below 60

### Milk Object
- `first_time`: Total first milking session production (liters)
- `second_time`: Total second milking session production (liters)
- `third_time`: Total third milking session production (liters)
- `total`: Total milk production for the period (liters)
- `daily_average`: Daily average milk production (liters)
- `vs_yesterday`: Difference compared to yesterday's production (liters)

### Finances Object
- `income`: Total income for the period
- `expense`: Total expenses for the period
- `balance`: Net balance (income - expense)
- `milk_sales_revenue`: Revenue from milk sales
- `pending_payments`: Outstanding customer payments
- `employee_salaries_due`: Pending employee salary payments

### Today Activity Object
- `milk_records_count`: Number of milk records entered today
- `tasks_completed`: Number of tasks completed today
- `pending_tasks`: Number of pending tasks
- `overdue_tasks`: Number of overdue tasks
- `animal_events`: Number of animal events recorded today
- `vaccinations_due`: Number of vaccinations due within next 7 days

### Performance Object
- `milk_per_animal_avg`: Average milk production per animal for the period
- `employee_count`: Number of active employees
- `active_fields`: Number of active fields
- `crops_in_progress`: Number of crops currently in progress
- `vaccination_compliance`: Vaccination compliance percentage

### Alerts Array
Array of current alerts requiring attention:
- `type`: Alert type identifier
- `message`: Human-readable alert message
- `count`: Number of items requiring attention
- `priority`: Alert priority (`high`, `medium`, `low`)

### Recent Activities Array
Array of recent farm activities (last 5):
- `type`: Activity type (`milk_record`, `task_completed`, `animal_event`)
- `message`: Human-readable activity description
- `timestamp`: Activity timestamp (ISO 8601)
- `related_id`: ID of the related record (optional)

## Alert Types

| Type | Description | Priority |
|------|-------------|----------|
| `low_score_animals` | Animals with performance score < 60 | high |
| `overdue_tasks` | Tasks past their due date | medium |
| `overdue_vaccinations` | Vaccinations past their due date | high |

## Activity Types

| Type | Description |
|------|-------------|
| `milk_record` | New milk production record |
| `task_completed` | Completed farm task |
| `animal_event` | Animal health or management event |

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to fetch dashboard data: [error details]"
}
```

## Usage Examples

### Get Today's Dashboard
```bash
curl -X GET "https://api.channab.com/api/mobile-dashboard/?time_filter=today" \
  -H "Authorization: Token your_auth_token"
```

### Get This Week's Dashboard
```bash
curl -X GET "https://api.channab.com/api/mobile-dashboard/?time_filter=this_week" \
  -H "Authorization: Token your_auth_token"
```

### Get Custom Date Range Dashboard
```bash
curl -X GET "https://api.channab.com/api/mobile-dashboard/?time_filter=custom_range&start_date=2025-06-01&end_date=2025-06-12" \
  -H "Authorization: Token your_auth_token"
```

## Mobile App Integration Notes

1. **Caching**: Consider caching dashboard data for 5-10 minutes to improve performance
2. **Pull to Refresh**: Implement pull-to-refresh functionality for real-time updates
3. **Alert Priorities**: Use color coding for alert priorities (red for high, yellow for medium)
4. **Navigation**: Use `related_id` fields for deep-linking to specific records
5. **Offline Support**: Cache last successful response for offline viewing

## Performance Considerations

- Response time typically under 500ms for farms with <1000 animals
- All database queries are optimized with proper indexing
- Data is aggregated efficiently using Django ORM aggregation functions
- Consider pagination for very large farms (>5000 animals)

## Changelog

### Version 1.0 (2025-06-12)
- Initial release of Mobile Dashboard API
- Comprehensive farm overview with 7 main sections
- Real-time alerts and activity feed
- Support for multiple time periods
- Optimized for mobile applications