from django import template
from django.utils import timezone

register = template.Library()

@register.filter
def get_leave_days(leave):
    """
    Returns the number of days for the unpaid leave (inclusive).
    """
    if not leave.start_date or not leave.end_date:
        return 0
    return (leave.end_date - leave.start_date).days + 1

@register.filter
def get_leave_deduction(leave, member):
    """
    Returns the deduction amount for the unpaid leave period, based on member's salary components.
    """
    from accounts.models import SalaryComponent
    # Calculate total monthly salary
    salary_components = SalaryComponent.objects.filter(member=member)
    total_monthly_salary = 0
    for component in salary_components:
        if component.duration == 'daily':
            total_monthly_salary += component.amount * 30
        elif component.duration == 'monthly':
            total_monthly_salary += component.amount
        elif component.duration == 'yearly':
            total_monthly_salary += component.amount / 12
    per_day_salary = total_monthly_salary / 30 if total_monthly_salary else 0
    leave_days = (leave.end_date - leave.start_date).days + 1
    deduction = leave_days * per_day_salary
    return round(deduction, 2)


def humanize_timedelta(td):
    total_seconds = int(td.total_seconds())
    days, remainder = divmod(total_seconds, 86400)
    hours, remainder = divmod(remainder, 3600)
    minutes, seconds = divmod(remainder, 60)
    parts = []
    if days:
        parts.append(f"{days} day{'s' if days != 1 else ''}")
    if hours:
        parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
    if minutes:
        parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
    if not parts:
        parts.append("less than a minute")
    return ', '.join(parts)

@register.filter
def expires_in(expiry):
    if not expiry:
        return "Never"
    now = timezone.now()
    if expiry < now:
        return "Expired"
    return humanize_timedelta(expiry - now)
