from django.contrib.auth import login, authenticate
from django.contrib.auth.forms import AuthenticationForm
from django.http import HttpResponseForbidden, HttpResponseNotFound, HttpResponseRedirect
from django.views.decorators.http import require_POST
from django.shortcuts import render, redirect
from django.urls import reverse_lazy, reverse
from django.views.generic import CreateView
from django.contrib.auth import logout
from django.contrib.auth.decorators import login_required
from openpyxl import Workbook
from django.http import HttpResponse
from openpyxl import load_workbook
from io import BytesIO
import logging
from django.contrib import messages

logger = logging.getLogger('api')

from farm_finances.models import Expense, ExpenseCategory
from .models import Farm, CustomUser, Profile, SalaryTransaction, SalaryComponent, UserLogin, UnpaidLeave
from .forms import  FarmMemberCreationForm, MobileAuthenticationForm,  ProfileUpdateForm, ResetPasswordForm, SalaryTransactionForm, FarmUpdateForm, UnpaidLeaveForm
from django.shortcuts import render, redirect, get_object_or_404
from django.db.models import Sum, Value, DecimalField
from django.db.models.functions import Coalesce
from decimal import Decimal
from .utils import normalize_phone_number

class SignupView(CreateView):
    form_class = FarmMemberCreationForm
    success_url = reverse_lazy('accounts:login')
    template_name = 'accounts/signup.html'

    def get_form_kwargs(self):
        kwargs = super(SignupView, self).get_form_kwargs()
        kwargs['signup'] = True
        return kwargs

    def form_valid(self, form):
        user = form.save(commit=False)
        
        
        user.role = 'admin'
        user.save()
        farm_name = form.cleaned_data.get("farm_name")
        user.create_farm_and_save(farm_name=farm_name)
        
        
        self.object = user
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        return super().form_invalid(form)


def login_view(request):
    from django.utils import timezone
    if request.method == 'POST':
        mobile_number = request.POST.get('username')
        # Keep this initial info log
        logger.info(f"Login attempt for mobile: {mobile_number}")

        form = MobileAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            # Check is_active_until
            if hasattr(user, 'is_active_until') and user.is_active_until:
                if timezone.now() > user.is_active_until:
                    # Replace logger with print
                    user.is_active = False
                    user.save()
                    messages.error(request, "Your account has expired. Please contact admin.")
                    return render(request, 'accounts/login.html', {'form': form})

            # Check if user is active before attempting login
            if not user.is_active:
                # Replace logger with print
                # Note: The form validation might have already caught this, but logging here ensures it's captured if form logic changes.
                messages.error(request, "This account is inactive.") # Provide feedback
                return render(request, 'accounts/login.html', {'form': form})

            login(request, user)
            # Keep this success log
            logger.info(f"Login successful for user ID: {user.id}")

            # Track login
            UserLogin.objects.create(
                user=user,
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                login_type='web'
            )

            # If 'remember_me' is not checked in the form, set the session to expire when the user closes the browser.
            if not request.POST.get('remember_me', False):
                request.session.set_expiry(0)

            return redirect(reverse('home:dashboard'))
        else:
            # Print specific form errors
            specific_error_printed = False # Flag to track if specific errors were printed
            for field, errors in form.errors.items():
                if field == '__all__': # Non-field errors often contain the main auth failure reason
                    # Check for specific error messages from AuthenticationForm
                    if any("inactive" in error.lower() for error in errors):
                        # Replace logger with print
                        specific_error_printed = True
                    elif any("correct username and password" in error.lower() for error in errors):
                        # Replace logger with print
                        specific_error_printed = True
                    else:
                        # Other non-field errors
                        pass
                else:
                    # Field-specific errors (e.g., missing field, invalid format)
                    pass

            # Fallback general case if specific checks didn't catch it
            if not specific_error_printed:
                 # General case
                 pass
    else:
        form = MobileAuthenticationForm()
    return render(request, 'accounts/login.html', {'form': form})


from django.contrib.auth import authenticate
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from rest_framework.parsers import JSONParser
from rest_framework import permissions

class LoginView(APIView):
    permission_classes = (permissions.AllowAny,)
    parser_classes = (JSONParser,)
    
    def post(self, request, *args, **kwargs):
        from django.utils import timezone
        try:
            
            mobile_number = request.data.get("username")
            password = request.data.get("password")
            
            if not mobile_number or not password:
                logger.warning(f"Login attempt failed - Missing credentials for mobile: {mobile_number}")
                return Response(
                    {"error": "Both username and password are required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Normalize the phone number
            normalized_mobile = normalize_phone_number(mobile_number)
            logger.info(f"Login attempt for mobile: {mobile_number} (normalized: {normalized_mobile})")
            
            if not normalized_mobile:
                logger.warning(f"Login failed - Invalid phone number format: {mobile_number}")
                return Response(
                    {"error": "Invalid phone number format"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Try to find user with normalized number
            try:
                user = CustomUser.objects.filter(mobile__in=[
                    normalized_mobile,
                    normalized_mobile.replace('+', '00'),  # Try with 00 prefix
                    normalized_mobile[3:] if normalized_mobile.startswith('+92') else None,  # Try without country code
                    '0' + normalized_mobile[3:] if normalized_mobile.startswith('+92') else None,  # Try with leading 0
                ]).first()
                
                if not user:
                    logger.warning(f"Login failed - User does not exist: {normalized_mobile}")
                    return Response(
                        {"error": "Invalid credentials"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                
                # Check is_active_until
                if hasattr(user, 'is_active_until') and user.is_active_until:
                    if timezone.now() > user.is_active_until:
                        user.is_active = False
                        user.save()
                        logger.warning(f"Login failed - Account expired: {normalized_mobile}")
                        return Response(
                            {"error": "Your account has expired. Please contact admin."},
                            status=status.HTTP_401_UNAUTHORIZED
                        )
                if not user.is_active:
                    logger.warning(f"Login failed - Inactive user: {normalized_mobile}")
                    return Response(
                        {"error": "Account is inactive"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                
                # Try authentication with the user's actual stored mobile number
                user = authenticate(request, username=user.mobile, password=password)
                
                if user is not None:
                    # login successful
                    token, created = Token.objects.get_or_create(user=user)
                    logger.info(f"Login successful for user ID: {user.id}")
                    
                    # Track login
                    UserLogin.objects.create(
                        user=user,
                        ip_address=request.META.get('REMOTE_ADDR'),
                        user_agent=request.META.get('HTTP_USER_AGENT'),
                        login_type='api'
                    )
                    
                    # Get user's farm information
                    farm_data = None
                    if user.farm:
                        farm_data = {
                            'id': user.farm.id,
                            'name': user.farm.name,
                            'location': user.farm.location
                        }
                    elif hasattr(user, 'owned_farms'):
                        owned_farm = user.owned_farms.first()
                        if owned_farm:
                            farm_data = {
                                'id': owned_farm.id,
                                'name': owned_farm.name,
                                'location': owned_farm.location
                            }

                    response_data = {
                        'token': token.key,
                        'user': {
                            'id': user.id,
                            'mobile': user.mobile,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'role': user.role,
                            'email': user.email,
                            'farm': farm_data
                        }
                    }
                    return Response(response_data, status=status.HTTP_200_OK)
                else:
                    logger.warning(f"Login failed - Invalid credentials for mobile: {normalized_mobile}")
                    return Response(
                        {"error": "Invalid credentials"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
            except Exception as e:
                logger.error(f"Error checking user existence: {str(e)}", exc_info=True)
                raise
                
        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred during login"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


from .forms import ProfileForm, UserActivationPeriodForm
from .models import Profile, CustomUser, SalaryTransaction

def user_profile(request):
    if not request.user.is_authenticated:
        return redirect('accounts:login')

    try:
        profile = request.user.profile
    except Profile.DoesNotExist:
        profile = Profile(user=request.user)
        profile.save()

    if request.method == 'POST':
        form = ProfileUpdateForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('accounts:user_profile')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = ProfileUpdateForm(instance=profile)

    # Fetch farm members if the user is an admin
    farm_members = []
    if request.user.role == 'admin':
        farm_members = CustomUser.objects.filter(farm=request.user.farm)

    return render(request, 'accounts/profile.html', {'form': form, 'profile': profile, 'farm_members': farm_members})

@login_required
def set_user_active_period(request, user_id):
    """
    Admin can set/extend a user's is_active_until.
    """
    if not request.user.is_staff and request.user.role != 'admin':
        return HttpResponseForbidden("Not allowed")
    user = get_object_or_404(CustomUser, pk=user_id)
    if request.method == 'POST':
        form = UserActivationPeriodForm(request.POST, instance=user)
        if form.is_valid():
            user = form.save(commit=False)
            user.is_active = True  # Ensure user is marked active in Django admin
            user.save()
            messages.success(request, f"Active period updated for user {user.first_name} {user.last_name}.")
            return redirect('accounts:admin_users_list')
    else:
        form = UserActivationPeriodForm(instance=user)
    return render(request, 'accounts/set_user_active_period.html', {'form': form, 'target_user': user})


from .forms import ProfileUpdateForm

@login_required
def edit_profile(request):
    try:
        profile = request.user.profile
    except Profile.DoesNotExist:
        profile = Profile(user=request.user)
        profile.save()

    if request.method == 'POST':
        form = ProfileUpdateForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            # Validate social media URLs
            facebook = form.cleaned_data.get('facebook')
            youtube = form.cleaned_data.get('youtube')
            
            if facebook and not facebook.startswith(('http://', 'https://')):
                form.add_error('facebook', 'Please enter a valid URL starting with http:// or https://')
                messages.error(request, 'Please enter a valid Facebook URL.')
                return render(request, 'accounts/edit_profile.html', {'form': form})
            
            if youtube and not youtube.startswith(('http://', 'https://')):
                form.add_error('youtube', 'Please enter a valid URL starting with http:// or https://')
                messages.error(request, 'Please enter a valid YouTube URL.')
                return render(request, 'accounts/edit_profile.html', {'form': form})

            # Handle profile picture
            if 'profile_picture' in request.FILES:
                # Delete old profile picture if it exists
                if profile.profile_picture:
                    try:
                        profile.profile_picture.delete(save=False)
                    except Exception:
                        pass

            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('accounts:user_profile')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = ProfileUpdateForm(instance=profile)
    
    return render(request, 'accounts/edit_profile.html', {'form': form})


@login_required
def create_farm_member(request):
    if request.method == 'POST':
        form = FarmMemberCreationForm(request.POST, signup=False)

        if form.is_valid():
            user = form.save(commit=False)
            user.role = 'labour'
            user.farm = request.user.farm
            user.first_name = form.cleaned_data['first_name']
            user.last_name = form.cleaned_data['last_name']
            user.email = form.cleaned_data.get('email', '')
            user.save()

            # Create or update the Profile for the user
            profile, created = Profile.objects.get_or_create(user=user)
            profile.first_name = form.cleaned_data['first_name']
            profile.last_name = form.cleaned_data['last_name']
            profile.city = form.cleaned_data['location']
            profile.email = form.cleaned_data.get('email', '')
            profile.joining_date = form.cleaned_data.get('join_date')
            profile.save()

            # Redirect to the member detail page
            return redirect(reverse('accounts:member_detail', args=[user.pk]))
        else:
            pass
    else:
        form = FarmMemberCreationForm(signup=False)

    return render(request, 'accounts/create_farm_member.html', {'form': form})



@login_required
def reset_password(request, pk):
    if request.user.role != 'admin':
        return HttpResponseForbidden("You are not allowed to perform this action")

    member = get_object_or_404(CustomUser, pk=pk)
    if request.method == 'POST':
        form = ResetPasswordForm(request.POST)
        if form.is_valid():
            member.set_password(form.cleaned_data["new_password1"])
            member.save()
            return redirect('accounts:member_detail', member_id=pk)
    else:
        form = ResetPasswordForm()

    context = {
        'form': form,
        'member': member,
    }
    return render(request, 'accounts/reset_password.html', context)

def edit_member(request, pk):
    user = get_object_or_404(CustomUser, pk=pk)

    if not hasattr(user, 'profile'):
        Profile.objects.create(user=user)

    if request.method == 'POST':
        form = ProfileUpdateForm(request.POST, request.FILES, instance=user.profile)

        if form.is_valid():
            form.save()
            return redirect('accounts:member_detail', member_id=pk)

    else:
        form = ProfileUpdateForm(instance=user.profile)

    context = {
        'form': form,
        'member': user
    }

    return render(request, 'accounts/edit_member.html', context)

def delete_member(request, pk):
    member = get_object_or_404(CustomUser, pk=pk)

    if request.method == 'POST':
        member.delete()
        # Redirect to the member list page or any other page after successful deletion
        return redirect('accounts:farm_member_list')

    context = {
        'member': member
    }

    return render(request, 'accounts/delete_member_confirm.html', context)

@login_required
def farm_member_list(request):
    if request.user.role != 'admin':
        return redirect('home:home')

    retired = request.GET.get('retired') == 'true'
    if retired:
        farm_members_qs = CustomUser.objects.filter(farm=request.user.farm, profile__end_date__isnull=False)
        farm_members_list = []
        for member in farm_members_qs:
            salary_status = calculate_salary_status(member)
            member.total_salary_expected = Decimal(salary_status.get('expected_salary_till_now', {}).get('amount', 0))
            member.total_salary_received = Decimal(salary_status.get('total_salary_received', 0))
            member.remaining_salary = Decimal(salary_status.get('remaining_salary', 0))
            farm_members_list.append(member)
        farm_members = farm_members_list
    else:
        farm_members_qs = CustomUser.objects.filter(farm=request.user.farm, profile__end_date__isnull=True)
        farm_members_list = []
        for member in farm_members_qs:
            salary_status = calculate_salary_status(member)
            member.total_salary_expected = Decimal(salary_status.get('expected_salary_till_now', {}).get('amount', 0))
            member.total_salary_received = Decimal(salary_status.get('total_salary_received', 0))
            member.remaining_salary = Decimal(salary_status.get('remaining_salary', 0))
            farm_members_list.append(member)
        farm_members = farm_members_list

    context = {
        'farm_members': farm_members,
        'retired': retired,
    }

    return render(request, 'accounts/farm_member_list.html', context)


from datetime import date
from dateutil.relativedelta import relativedelta
import calendar

def calculate_salary_status(member):
    from datetime import date, timedelta
    from accounts.models import SalaryTransaction, SalaryComponent, UnpaidLeave
    import calendar

    salary_transactions = SalaryTransaction.objects.filter(farm_member=member)
    salary_components = SalaryComponent.objects.filter(member=member)

    salary_status = {}
    total_salary_received = 0
    total_monthly_salary = 0

    today = date.today()
    _, days_in_month = calendar.monthrange(today.year, today.month)

    # Calculate per-day salary for deduction (sum of all daily equivalents)
    def get_per_day_salary(dt):
        per_day = 0
        for component in salary_components:
            if component.duration == 'daily':
                per_day += float(component.amount)
            elif component.duration == 'yearly':
                per_day += float(component.amount) / 365.0
            else:  # monthly
                # Use actual days in the month for the date
                _, days_in_this_month = calendar.monthrange(dt.year, dt.month)
                per_day += float(component.amount) / days_in_this_month
        return per_day

    for component in salary_components:
        if component.duration == 'daily':
            monthly_equivalent = float(component.amount) * days_in_month
        elif component.duration == 'yearly':
            monthly_equivalent = float(component.amount) / 12
        else:  # monthly
            monthly_equivalent = float(component.amount)
        total_monthly_salary += monthly_equivalent

    for transaction in salary_transactions:
        component_name = transaction.component.name
        if component_name not in salary_status:
            salary_status[component_name] = float(transaction.amount_paid)
        else:
            salary_status[component_name] += float(transaction.amount_paid)
        total_salary_received += float(transaction.amount_paid)

    salary_status["total_salary_received"] = total_salary_received

    for key, value in salary_status.items():
        if key != "total_salary_received":
            salary_status[key] = {"received_amount": value, "sum_with_total": value + total_salary_received}

    # Calculate unpaid leave days and deduction for each leave
    unpaid_leaves = UnpaidLeave.objects.filter(employee=member)
    unpaid_leave_periods = []
    total_unpaid_leave_days = 0
    total_unpaid_leave_amount = 0
    for leave in unpaid_leaves:
        # Calculate days (inclusive)
        days = (leave.end_date - leave.start_date).days + 1
        # Calculate deduction for each day in the leave
        deduction = 0
        for i in range(days):
            dt = leave.start_date + timedelta(days=i)
            deduction += get_per_day_salary(dt)
        unpaid_leave_periods.append({
            'start_date': leave.start_date,
            'end_date': leave.end_date,
            'days': days,
            'amount': round(deduction, 2),
            'reason': leave.reason,
        })
        total_unpaid_leave_days += days
        total_unpaid_leave_amount += deduction

    if hasattr(member, 'profile') and member.profile.joining_date is not None:
        joining_date = member.profile.joining_date
        end_date = member.profile.end_date if member.profile.end_date and member.profile.end_date < today else today
        delta = end_date - joining_date
        full_months = delta.days // 30
        remaining_days = delta.days % 30
        expected_salary_till_now = round(full_months * total_monthly_salary + (remaining_days * (total_monthly_salary / days_in_month)))
        # Deduct unpaid leave amount
        expected_salary_till_now -= total_unpaid_leave_amount
        remaining_salary = expected_salary_till_now - total_salary_received
    else:
        full_months = 0
        remaining_days = 0
        expected_salary_till_now = 0
        remaining_salary = 0

    salary_status["expected_salary_till_now"] = {
        "amount": expected_salary_till_now,
        "details": f"{expected_salary_till_now} for the {full_months} months & {remaining_days} days (Unpaid leave: {total_unpaid_leave_days} days, -{total_unpaid_leave_amount:.2f})"
    }
    salary_status["remaining_salary"] = remaining_salary
    salary_status["total_monthly_salary"] = round(total_monthly_salary, 2)
    salary_status["unpaid_leave_days"] = total_unpaid_leave_days
    salary_status["unpaid_leave_amount"] = round(total_unpaid_leave_amount, 2)
    salary_status["unpaid_leave_periods"] = unpaid_leave_periods

    # Add new fields for enhanced salary tab display
    salary_status['current_daily_salary'] = round(get_per_day_salary(today), 2)
    if hasattr(member, 'profile') and member.profile.joining_date is not None:
        salary_status['total_employment_days'] = delta.days
        salary_status['effective_working_days'] = delta.days - total_unpaid_leave_days
    else:
        salary_status['total_employment_days'] = 0
        salary_status['effective_working_days'] = 0

    return salary_status




@login_required
def member_detail(request, member_id):
    member = get_object_or_404(CustomUser, pk=member_id)
    salary_transactions = SalaryTransaction.objects.filter(farm_member=member)
    salary_status = calculate_salary_status(member)
    unpaid_leaves = UnpaidLeave.objects.filter(employee=member).order_by('-start_date')
    excluded_keys = ["total_salary_received", "expected_salary_till_now", "remaining_salary"]

    if request.method == 'POST':
        if 'add_unpaid_leave' in request.POST:
            leave_form = UnpaidLeaveForm(request.POST)
            form = SalaryComponentForm()  # keep salary form empty
            if leave_form.is_valid():
                unpaid_leave = leave_form.save(commit=False)
                unpaid_leave.employee = member
                unpaid_leave.save()
                return redirect('accounts:member_detail', member_id=member_id)
        else:
            form = SalaryComponentForm(request.POST)
            leave_form = UnpaidLeaveForm()
            if form.is_valid():
                salary_component = form.save(commit=False)
                salary_component.member = member
                salary_component.save()
                return redirect('accounts:member_detail', member_id=member_id)
    else:
        form = SalaryComponentForm()
        leave_form = UnpaidLeaveForm()

    return render(request, 'accounts/member_detail.html', {
        'member': member,
        'form': form,
        'leave_form': leave_form,
        'unpaid_leaves': unpaid_leaves,
        'salary_transactions': salary_transactions,
        'salary_status': salary_status,
        'excluded_keys': excluded_keys
    })


@login_required
def edit_unpaid_leave(request, leave_id):
    leave = get_object_or_404(UnpaidLeave, pk=leave_id)
    member_id = leave.employee.id
    if request.method == 'POST':
        form = UnpaidLeaveForm(request.POST, instance=leave)
        if form.is_valid():
            form.save()
            return redirect('accounts:member_detail', member_id=member_id)
    else:
        form = UnpaidLeaveForm(instance=leave)
    return render(request, 'accounts/edit_unpaid_leave_form.html', {'form': form, 'leave': leave})

@login_required
def delete_unpaid_leave(request, leave_id):
    leave = get_object_or_404(UnpaidLeave, pk=leave_id)
    member_id = leave.employee.id
    if request.method == 'POST':
        leave.delete()
        return redirect('accounts:member_detail', member_id=member_id)
    return render(request, 'accounts/delete_unpaid_leave_confirm.html', {'leave': leave})



@login_required
def export_salary_excel(request, member_id):
    response = HttpResponse(content_type='application/ms-excel')
    response['Content-Disposition'] = f'attachment; filename="salary_transactions_{member_id}.xlsx"'

    wb = Workbook()
    ws = wb.active
    ws.title = "Salaries"

    transactions = SalaryTransaction.objects.filter(farm_member__id=member_id)

    ws.append(['Date', 'Component', 'Amount Paid', 'Description'])
    for transaction in transactions:
        ws.append([transaction.transaction_date, transaction.component.name, transaction.amount_paid, transaction.description])

    wb.save(response)
    return response

@login_required
def import_salary_form(request, member_id):
    member = get_object_or_404(CustomUser, pk=member_id)
    return render(request, 'accounts/import_employee_salery.html', {'member': member})


@login_required
def import_salary_excel(request, member_id):
    if request.method == 'POST':
        file = request.FILES.get('file')
        if not file:
            return HttpResponse("No file was uploaded.", status=400)

        try:
            wb = load_workbook(filename=BytesIO(file.read()))
            ws = wb.active
            farm = request.user.farm
            salary_category, created = ExpenseCategory.objects.get_or_create(farm=farm, name='Salary')

            for row in ws.iter_rows(min_row=2, values_only=True):
                component = SalaryComponent.objects.filter(name=row[1], member_id=member_id).first()
                if not component:
                    return HttpResponse(f"Salary component '{row[1]}' not found.", status=400)

                transaction = SalaryTransaction.objects.create(
                    farm_member_id=member_id,
                    transaction_date=row[0],
                    component=component,
                    amount_paid=row[2],
                    description=row[3]
                )

                # Handle expense related to the salary transaction
                expense = Expense(
                    user=request.user,
                    farm=farm,
                    date=transaction.transaction_date,
                    description=f'Salary for {transaction.farm_member} - {transaction.component.name}',
                    amount=transaction.amount_paid,
                    category=salary_category,
                    salary_transaction=transaction
                )
                expense.save()

            return redirect('accounts:member_detail', member_id=member_id)
        except Exception as e:
            return HttpResponse(f"An error occurred: {str(e)}", status=400)
    return HttpResponse("Invalid request", status=400)

from .forms import SalaryComponentForm, UnpaidLeaveForm
from .models import SalaryComponent, UnpaidLeave

@login_required
def salary_components(request, member_id):
    member = get_object_or_404(CustomUser, pk=member_id)
    components = SalaryComponent.objects.filter(member=member)

    context = {
        'member': member,
        'components': components,
    }
    return render(request, 'accounts/salary_components.html', context)

@login_required
def add_salary_component(request, member_id):
    member = get_object_or_404(CustomUser, pk=member_id)

    if request.method == 'POST':
        form = SalaryComponentForm(request.POST)
        if form.is_valid():
            component = form.save(commit=False)
            component.member = member
            component.save()
            messages.success(request, f'Salary component "{component.name}" added successfully.')
            return redirect('accounts:member_detail', member_id=member.pk)
    else:
        form = SalaryComponentForm()

    context = {
        'form': form,
        'member': member
    }
    return render(request, 'accounts/add_salary_component.html', context)

@login_required
def update_salary_component(request, member_id, component_id):
    member = get_object_or_404(CustomUser, pk=member_id)
    component = get_object_or_404(SalaryComponent, pk=component_id, member=member)

    if request.method == 'POST':
        form = SalaryComponentForm(request.POST, instance=component)
        if form.is_valid():
            component = form.save()
            messages.success(request, f'Salary component "{component.name}" updated successfully.')
            return redirect('accounts:member_detail', member_id=member.pk)
    else:
        form = SalaryComponentForm(instance=component)

    context = {
        'form': form,
        'member': member,
        'component': component
    }
    return render(request, 'accounts/update_salary_component.html', context)

@login_required
def delete_salary_component(request, member_id, component_id):
    member = get_object_or_404(CustomUser, pk=member_id)
    component = get_object_or_404(SalaryComponent, pk=component_id, member=member)

    if request.method == 'POST':
        component_name = component.name
        component.delete()
        messages.success(request, f'Salary component "{component_name}" deleted successfully.')
        return redirect('accounts:member_detail', member_id=member.pk)  # Redirecting to member_detail
    
    context = {
        'member': member,
        'component': component
    }
    return render(request, 'accounts/delete_salary_component_confirm.html', context)



@login_required
def salary_transaction_list(request):
    # Get the user's farm
    farm = request.user.farm

    # Filter the transactions by the user's farm using the farm_member relationship
    transactions = SalaryTransaction.objects.filter(farm_member__farm=farm)

    return render(request, 'accounts/salary_transaction_list.html', {'salary_transactions': transactions})




from django.http import JsonResponse

def get_salary_components(request, user_id):
    components = SalaryComponent.objects.filter(member_id=user_id).values('id', 'name')
    return JsonResponse(list(components), safe=False)


@login_required
def salary_transaction_update(request, pk=None):
    farm = request.user.farm
    employee_id = request.GET.get('employee_id') # Read employee_id from GET params
    initial_data = {}

    if pk is not None:
        transaction = get_object_or_404(SalaryTransaction, pk=pk, farm_member__farm=farm) # Ensure transaction belongs to user's farm
        edit_mode = True
    else:
        transaction = None
        edit_mode = False
        if employee_id: # If creating new and employee_id is present
            try:
                employee = CustomUser.objects.get(pk=employee_id, farm=farm) # Ensure employee belongs to user's farm
                initial_data['farm_member'] = employee
            except CustomUser.DoesNotExist:
                messages.error(request, "Selected employee not found or does not belong to your farm.")
                # Decide how to handle: redirect, or let form show error, or clear initial_data
                initial_data = {} # Clear if employee not valid

    if request.method == 'POST':
        form = SalaryTransactionForm(request.POST, instance=transaction, farm=farm, initial=initial_data if not edit_mode else None) # Pass initial only for new
        if form.is_valid():
            transaction_instance = form.save(commit=False) # Use a different variable name to avoid confusion with the loop variable 'transaction'
            if not hasattr(transaction_instance, 'farm_member') or not transaction_instance.farm_member: # If farm_member wasn't set by initial or form
                 if employee_id and not edit_mode: # Re-check and assign if still new and employee_id was there
                    try:
                        employee = CustomUser.objects.get(pk=employee_id, farm=farm)
                        transaction_instance.farm_member = employee
                    except CustomUser.DoesNotExist:
                        pass # Or handle error
            
            transaction_instance.save() # Save after potential farm_member assignment
            
            # Create or retrieve the salary expense category for the farm
            salary_category, created = ExpenseCategory.objects.get_or_create(farm=farm, name='Salary')

            # Try to get the associated expense
            try:
                expense = Expense.objects.get(salary_transaction=transaction_instance)
            except Expense.DoesNotExist:
                # If not found, create a new expense instance
                expense = Expense(user=request.user, farm=farm)

            # Update the expense instance with the salary transaction data
            expense.date = transaction_instance.transaction_date
            expense.description = f'Salary for {transaction_instance.farm_member} - {transaction_instance.component.name if transaction_instance.component else "General Salary"}' # Handle if component is None
            expense.amount = transaction_instance.amount_paid
            expense.category = salary_category
            expense.salary_transaction = transaction_instance

            # Save the expense instance
            expense.save()
            messages.success(request, "Salary transaction saved successfully.")

            # Get employee_id from POST if it was submitted via hidden field
            redirect_employee_id_from_post = request.POST.get('employee_id_for_redirect')

            if not edit_mode and redirect_employee_id_from_post:
                # Creating new transaction, came via an employee_id link (id from hidden field)
                return redirect('accounts:member_detail', member_id=redirect_employee_id_from_post)
            elif edit_mode and transaction_instance.farm_member:
                # Editing an existing transaction, redirect to its member's detail page
                return redirect('accounts:member_detail', member_id=transaction_instance.farm_member.id)
            else:
                # Fallback: if creating without employee_id or other cases
                return redirect('accounts:salary_transaction_list')
        else:
            messages.error(request, "Please correct the errors below.")


    else: # GET request
        form = SalaryTransactionForm(instance=transaction, farm=farm, initial=initial_data if not edit_mode else None)
    
    # Determine cancel URL
    cancel_redirect_url = reverse('accounts:salary_transaction_list') # Default
    if edit_mode and transaction and transaction.farm_member:
        cancel_redirect_url = reverse('accounts:member_detail', kwargs={'member_id': transaction.farm_member.id})
    elif not edit_mode and employee_id: # employee_id from request.GET for new transaction form
        try:
            # Ensure employee_id is valid for URL generation
            int(employee_id)
            cancel_redirect_url = reverse('accounts:member_detail', kwargs={'member_id': employee_id})
        except ValueError:
            # If employee_id is not a valid int, fallback to list (should ideally not happen if routing is correct)
            pass 

    context = {
        'form': form,
        'edit_mode': edit_mode,
        'salary_transaction': transaction,
        'cancel_redirect_url': cancel_redirect_url,
        'employee_id_for_hidden_field': employee_id # Pass employee_id from GET to template
    }
    return render(request, 'accounts/salary_transaction_form.html', context)

@login_required
def salary_transaction_update_member(request, member_id, pk):
    farm = request.user.farm
    try:
        member = CustomUser.objects.get(pk=member_id)
    except CustomUser.DoesNotExist:
        return HttpResponseNotFound("FarmMember not found")

    if pk is not None:
        try:
            transaction = SalaryTransaction.objects.get(farm_member=member, pk=pk)
            edit_mode = True
        except SalaryTransaction.DoesNotExist:
            transaction = None
            edit_mode = False

    if request.method == 'POST':
        form = SalaryTransactionForm(request.POST, instance=transaction, farm=farm, member=member)
        if form.is_valid():
            if transaction is not None:
                transaction.transaction_date = form.cleaned_data['transaction_date']
                transaction.farm_member = form.cleaned_data['farm_member']
                transaction.component = form.cleaned_data['component']
                transaction.amount_paid = form.cleaned_data['amount_paid']
                transaction.save()
            else:
                transaction = form.save(commit=False)
                transaction.farm_member = member
                transaction.save()

            # Create or retrieve the salary expense category for the farm
            salary_category, created = ExpenseCategory.objects.get_or_create(farm=farm, name='Salary')

            # other code here...

            return redirect('accounts:salary_transaction_list')
    else:
        form = SalaryTransactionForm(instance=transaction, farm=farm, member=member)
        if not edit_mode:
            form.fields['farm_member'].initial = member

    context = {
        'form': form,
        'edit_mode': edit_mode,
        'salary_transaction': transaction,
        'member': member
    }
    
    return render(request, 'accounts/salary_transaction_form.html', context)


from django.urls import reverse_lazy
from django.views.generic.edit import UpdateView

# Your other views...

class SalaryTransactionUpdateView(UpdateView):
    model = SalaryTransaction
    form_class = SalaryTransactionForm
    template_name = 'accounts/salary_transaction_form.html'

    def get_form_kwargs(self):
        kwargs = super(SalaryTransactionUpdateView, self).get_form_kwargs()
        kwargs['farm'] = self.request.user.farm  # Replace `farm` with the correct attribute to get the farm instance for the logged-in user
        return kwargs

    def get_success_url(self):
        return reverse_lazy('accounts:salary_transaction_list')


@login_required
@require_POST # Ensure this view only accepts POST requests
def salary_transaction_delete(request, pk):
    # Ensure the transaction belongs to the user's farm
    transaction = get_object_or_404(SalaryTransaction, pk=pk, farm_member__farm=request.user.farm)
    member_id_redirect = transaction.farm_member.id # Store for redirect before deleting transaction
    transaction_pk_log = transaction.pk # For logging

    try:
        # Find and delete related Expense objects
        expenses = Expense.objects.filter(salary_transaction=transaction)
        if expenses.exists():
            for expense in expenses:
                expense_pk_log = expense.pk
                expense.delete()
        else:
            pass

        # Delete the SalaryTransaction itself
        transaction.delete()
        messages.success(request, 'Salary transaction deleted successfully.')

    except Exception as e:
        messages.error(request, f'Error deleting transaction: {e}')
        # If we still have member_id_redirect, try to redirect, otherwise to a general list
        if member_id_redirect:
            return redirect('accounts:member_detail', member_id=member_id_redirect)
        return redirect('accounts:salary_transaction_list')


    # Redirect back to the member detail page
    return redirect('accounts:member_detail', member_id=member_id_redirect)

def farm_view(request):
    if not request.user.is_authenticated:
        return redirect('accounts:login')  # Redirect to login page if the user is not authenticated

    farm = get_object_or_404(Farm, admin=request.user)  # Get the farm that is associated with the logged-in user
    farm_members = []
    if request.user.role == 'admin':
        farm_members = CustomUser.objects.filter(farm=request.user.farm)

    context = {
        'farm': farm,
        'farm_members': farm_members
    }
    return render(request, 'accounts/farm_detail.html', context)

def edit_farm(request, farm_id):
    farm = get_object_or_404(Farm, pk=farm_id)
    
    if request.user != farm.admin:
        return HttpResponseForbidden("You don't have permission to edit this farm")

    if request.method == "POST":
        form = FarmUpdateForm(request.POST, request.FILES, instance=farm)
        if form.is_valid():
            form.save()
            return redirect('accounts:farm_detail')  # Redirect to farm detail page
    else:
        form = FarmUpdateForm(instance=farm)

    return render(request, 'accounts/edit_farm.html', {'form': form})

def logout_view(request):
    logout(request)
    return redirect(reverse('home:home'))

class LogoutView(APIView):
    def post(self, request):
        logout(request)
        return Response({"detail": "Logout successful"}, status=status.HTTP_200_OK)

@login_required
def admin_dashboard(request):
    if not request.user.is_staff:
        return HttpResponseForbidden("You don't have permission to access this page.")
    
    from django.utils import timezone
    from django.db.models import Count, Q
    from datetime import timedelta
    
    # Get all farms with animal counts
    farms = Farm.objects.all().select_related('admin').annotate(
        total_animals=Count('animals'),
        active_animals=Count('animals', filter=Q(animals__status='active')),
        dairy_animals=Count('animals', filter=Q(animals__category='dairy', animals__status='active')),
        buffalo_animals=Count('animals', filter=Q(animals__category='buffalo', animals__status='active')),
        cow_animals=Count('animals', filter=Q(animals__category='cow', animals__status='active')),
        goat_animals=Count('animals', filter=Q(animals__category='goat', animals__status='active')),
        sheep_animals=Count('animals', filter=Q(animals__category='sheep', animals__status='active')),
        beef_animals=Count('animals', filter=Q(animals__category='beef', animals__status='active')),
        other_animals=Count('animals', filter=Q(animals__category='other', animals__status='active'))
    )
    
    # Get all users
    users = CustomUser.objects.all()
    
    # Get user statistics
    total_users = users.count()
    total_farms = farms.count()
    total_admins = users.filter(role='admin').count()
    total_labours = users.filter(role='labour').count()
    
    # Get login statistics
    now = timezone.now()
    thirty_days_ago = now - timedelta(days=30)
    today = now.date()
    
    # Recent logins
    recent_logins = UserLogin.objects.select_related('user').order_by('-login_time')[:10]
    
    # Login counts in last 30 days
    login_counts = UserLogin.objects.filter(
        login_time__gte=thirty_days_ago
    ).values('user__mobile', 'user__first_name', 'user__last_name', 'user__role'
    ).annotate(
        login_count=Count('id')
    ).order_by('-login_count')[:10]
    
    # Today's login count
    todays_logins = UserLogin.objects.filter(login_time__date=today).count()
    
    # Last 7 days login trend
    last_7_days = now - timedelta(days=7)
    daily_logins = UserLogin.objects.filter(
        login_time__gte=last_7_days
    ).extra(
        select={'day': 'DATE(login_time)'}
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    context = {
        'total_users': total_users,
        'total_farms': total_farms,
        'total_admins': total_admins,
        'total_labours': total_labours,
        'farms': farms,
        'recent_users': users.order_by('-id')[:5],
        'recent_logins': recent_logins,
        'login_counts': login_counts,
        'todays_logins': todays_logins,
        'daily_logins': daily_logins,
    }
    
    return render(request, 'accounts/admin_dashboard.html', context)

@login_required
def admin_users_list(request):
    if not request.user.is_staff:
        return HttpResponseForbidden("You don't have permission to access this page.")
    
    users = CustomUser.objects.all().select_related('farm')
    context = {
        'users': users,
        'title': 'All Users'
    }
    return render(request, 'accounts/admin_list_view.html', context)

@login_required
def admin_farms_list(request):
    if not request.user.is_staff:
        return HttpResponseForbidden("You don't have permission to access this page.")
    
    from django.db.models import Count, Q
    
    farms = Farm.objects.all().select_related('admin').annotate(
        total_animals=Count('animals'),
        active_animals=Count('animals', filter=Q(animals__status='active')),
        dairy_animals=Count('animals', filter=Q(animals__category='dairy', animals__status='active')),
        buffalo_animals=Count('animals', filter=Q(animals__category='buffalo', animals__status='active')),
        cow_animals=Count('animals', filter=Q(animals__category='cow', animals__status='active')),
        goat_animals=Count('animals', filter=Q(animals__category='goat', animals__status='active')),
        sheep_animals=Count('animals', filter=Q(animals__category='sheep', animals__status='active')),
        beef_animals=Count('animals', filter=Q(animals__category='beef', animals__status='active')),
        other_animals=Count('animals', filter=Q(animals__category='other', animals__status='active'))
    )
    
    context = {
        'farms': farms,
        'title': 'All Farms'
    }
    return render(request, 'accounts/admin_list_view.html', context)

@login_required
def admin_admins_list(request):
    if not request.user.is_staff:
        return HttpResponseForbidden("You don't have permission to access this page.")
    
    admins = CustomUser.objects.filter(role='admin').select_related('farm')
    # Annotate each admin with is_temporarily_active and is_active_until for template
    for admin in admins:
        admin.is_temporarily_active = admin.is_temporarily_active()
        admin.is_active_until = admin.is_active_until
    context = {
        'users': admins,
        'title': 'All Admins'
    }
    return render(request, 'accounts/admin_list_view.html', context)

@login_required
def admin_labours_list(request):
    if not request.user.is_staff:
        return HttpResponseForbidden("You don't have permission to access this page.")
    
    labours = CustomUser.objects.filter(role='labour').select_related('farm')
    context = {
        'users': labours,
        'title': 'All Labours'
    }
    return render(request, 'accounts/admin_list_view.html', context)
