# Generated by Django 3.2.16 on 2025-03-22 16:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0015_userlogin_login_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='disable_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='disable_until',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='disabled_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='disabled_users', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customuser',
            name='is_temporarily_disabled',
            field=models.<PERSON><PERSON>an<PERSON>ield(default=False),
        ),
    ]
