# Generated by Django 3.2.16 on 2025-04-19 00:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0017_auto_20250322_1646'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='is_active_until',
            field=models.DateTimeField(blank=True, help_text='User is active until this datetime. If null, user is always active.', null=True),
        ),
    ]
