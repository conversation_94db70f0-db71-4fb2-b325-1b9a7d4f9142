from crops.models import CropNote
from crops.serializers import CropNoteSerializer
from .views import calculate_salary_status
from farm_finances.models import Expense, ExpenseCategory
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from .models import Farm, Profile, SalaryComponent, CustomUser, SalaryTransaction
from .serializers import CustomUserSerializer, SalaryComponentSerializer, ActiveEmployeeSerializer, SalaryComponentSerializers, SalaryTransactionSerializer
from rest_framework import generics, permissions, status
from .models import Profile
from .serializers import ProfileSerializer
from django.core.exceptions import PermissionDenied
from django.http import Http404
from rest_framework import generics, permissions
from rest_framework.response import Response
from rest_framework.views import APIView

from .models import Profile
from .serializers import ActiveEmployeeSerializer

from rest_framework.parsers import MultiPartParser, FormParser
from .utils import normalize_phone_number

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_active_employees(request):
    farm = request.user.farm
    active_employees = Profile.objects.filter(user__farm=farm, end_date__isnull=True)
    
    response_data = []
    for employee in active_employees:
        employee_data = ActiveEmployeeSerializer(employee).data
        # Calculate salary information
        salary_status = calculate_salary_status(employee.user)
        employee_data.update({
            'salary_info': {
                'total_monthly_salary': salary_status['total_monthly_salary'],
                'expected_salary_till_now': salary_status['expected_salary_till_now']['amount'],
                'total_salary_received': salary_status['total_salary_received'],
                'remaining_salary': salary_status['remaining_salary']
            }
        })
        response_data.append(employee_data)
    
    return Response(response_data, status=200)


@api_view(['POST', 'GET'])
@permission_classes([permissions.IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def add_salary_transaction(request):
    if request.method == 'GET':
        farm_member_id = request.GET.get('farm_member')
        if farm_member_id:
            try:
                member = CustomUser.objects.get(pk=farm_member_id, farm=request.user.farm)
                components = SalaryComponent.objects.filter(member=member)
                return Response({
                    'components': SalaryComponentSerializer(components, many=True).data
                })
            except CustomUser.DoesNotExist:
                return Response({"error": "Employee not found"}, status=404)
        return Response({"error": "farm_member parameter is required"}, status=400)

    farm = request.user.farm
    data = request.data.copy()

    try:
        member = CustomUser.objects.get(pk=data['farm_member'], farm=farm)
    except CustomUser.DoesNotExist:
        return Response({"error": "Employee not found"}, status=404)

    try:
        component = SalaryComponent.objects.get(pk=data['component'], member=member)
    except SalaryComponent.DoesNotExist:
        return Response({"error": "Component not found"}, status=404)

    serializer = SalaryTransactionSerializer(data=data)
    if serializer.is_valid():
        salary_transaction = serializer.save()
        
        # Create or retrieve the salary expense category for the farm
        salary_category, created = ExpenseCategory.objects.get_or_create(farm=farm, name='Salary')

        # Try to get the associated expense
        try:
            expense = Expense.objects.get(salary_transaction=salary_transaction)
        except Expense.DoesNotExist:
            # If not found, create a new expense instance
            expense = Expense(user=request.user, farm=farm)

        # Update the expense instance with the salary transaction data
        expense.date = salary_transaction.transaction_date
        expense.description = f'Salary for {salary_transaction.farm_member} - {salary_transaction.component.name}'
        expense.amount = salary_transaction.amount_paid
        expense.category = salary_category
        expense.salary_transaction = salary_transaction

        # Save the expense instance
        expense.save()

        return Response(serializer.data, status=201)
    else:
        return Response(serializer.errors, status=400)


class UserProfileView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ProfileSerializer

    def get_object(self):
        return Profile.objects.get(user=self.request.user)


from rest_framework import serializers, generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import Http404
from rest_framework.exceptions import PermissionDenied
from .models import CustomUser, SalaryComponent

class SalaryComponentListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, employee_id):
        try:
            employee = CustomUser.objects.get(id=employee_id)
        except CustomUser.DoesNotExist:
            return Response({"error": "Employee not found"}, status=404)

        salary_components = SalaryComponent.objects.filter(member=employee)
        serializer = SalaryComponentSerializer(salary_components, many=True)
        return Response(serializer.data)


from django.shortcuts import get_object_or_404

class FarmSalaryComponentListView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, farm_id):
        farm = get_object_or_404(Farm, id=farm_id)
        salary_components = SalaryComponent.objects.filter(member__farm=farm)
        serializer = SalaryComponentSerializers(salary_components, many=True)
        
        return Response(serializer.data)


class SalaryComponentCreateView(generics.CreateAPIView):
    queryset = SalaryComponent.objects.all()
    serializer_class = SalaryComponentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        employee_id = self.request.data.get('employee_id')
        try:
            employee = CustomUser.objects.get(id=employee_id)
        except CustomUser.DoesNotExist:
            raise serializers.ValidationError({"employee_id": "Employee not found"})

        serializer.save(member=employee)

class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or admins to edit it.
    """
    def has_object_permission(self, request, view, obj):
        # Only allow the owner of the component or admin users to update it
        return obj.member == request.user or request.user.role == 'admin'

class SalaryComponentUpdateView(generics.UpdateAPIView):
    queryset = SalaryComponent.objects.all()
    serializer_class = SalaryComponentSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]

    def get_object(self):
        component_id = self.kwargs.get("pk")
        try:
            component = SalaryComponent.objects.get(id=component_id)
        except SalaryComponent.DoesNotExist:
            raise Http404("Salary Component not found")

        # The permission check is now handled by the custom permission class
        self.check_object_permissions(self.request, component)

        return component

class SalaryComponentDeleteView(generics.DestroyAPIView):
    queryset = SalaryComponent.objects.all()
    serializer_class = SalaryComponentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        component_id = self.kwargs.get("pk")
        try:
            component = SalaryComponent.objects.get(id=component_id)
            # Check if there are any transactions using this component
            transactions = SalaryTransaction.objects.filter(component=component)
            if transactions.exists():
                raise serializers.ValidationError({
                    "error": "Cannot delete this salary component. Please delete all related transactions first.",
                    "transactions": [
                        {
                            "id": t.id,
                            "date": t.transaction_date,
                            "amount": t.amount_paid
                        } for t in transactions
                    ]
                })
            return component
        except SalaryComponent.DoesNotExist:
            raise Http404("Salary Component not found")

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response({
                "message": "Salary component deleted successfully"
            })
        except serializers.ValidationError as e:
            return Response(e.detail, status=400)


class SalaryTransactionListView(generics.ListAPIView):
    serializer_class = SalaryTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        employee_id = self.kwargs.get('employee_id')
        return SalaryTransaction.objects.filter(farm_member_id=employee_id)

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        member = CustomUser.objects.get(id=self.kwargs.get('employee_id'))
        salary_status = calculate_salary_status(member)

        response_data = {
            'transactions': serializer.data,
            'salary_status': salary_status
        }
        return Response(response_data)

@api_view(['GET'])
def get_salary_info(request, employee_id):
    try:
        member = CustomUser.objects.get(id=employee_id)
    except CustomUser.DoesNotExist:
        return Response({'error': 'Employee not found'}, status=404)

    salary_status = calculate_salary_status(member)

    return Response(salary_status)

@api_view(['PUT', 'GET'])
@permission_classes([permissions.IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def edit_salary_transaction(request, pk):
    try:
        salary_transaction = SalaryTransaction.objects.get(pk=pk)
    except SalaryTransaction.DoesNotExist:
        return Response({"error": "Salary transaction not found"}, status=404)

    if request.method == 'GET':
        # Return the transaction details and available components
        member = salary_transaction.farm_member
        components = SalaryComponent.objects.filter(member=member)
        return Response({
            'transaction': SalaryTransactionSerializer(salary_transaction).data,
            'components': SalaryComponentSerializer(components, many=True).data
        })

    # PUT method handling
    farm = request.user.farm
    data = request.data.copy()

    try:
        member = CustomUser.objects.get(pk=data['farm_member'], farm=farm)
    except CustomUser.DoesNotExist:
        return Response({"error": "Employee not found"}, status=404)

    try:
        component = SalaryComponent.objects.get(pk=data['component'], member=member)
    except SalaryComponent.DoesNotExist:
        return Response({"error": "Component not found"}, status=404)

    # Handle image update
    if 'receipt_image' in request.FILES:
        # If a new image is uploaded, use it
        data['receipt_image'] = request.FILES['receipt_image']
    elif 'clear_image' in data and data['clear_image'] == 'true':
        # If clear_image flag is set, remove the image
        data['receipt_image'] = None
    else:
        # If no new image and no clear flag, don't modify the existing image
        data.pop('receipt_image', None)  # Remove receipt_image from data if present

    serializer = SalaryTransactionSerializer(salary_transaction, data=data, partial=True)
    if serializer.is_valid():
        salary_transaction = serializer.save()

        # Update the related expense entry
        salary_category, created = ExpenseCategory.objects.get_or_create(farm=farm, name='Salary')
        
        # Get the existing expense
        expense = Expense.objects.filter(salary_transaction=salary_transaction).first()
        
        if expense:  # Update existing expense
            expense.date = salary_transaction.transaction_date
            expense.description = f'Salary for {salary_transaction.farm_member} - {salary_transaction.component.name}'
            expense.amount = salary_transaction.amount_paid
            expense.category = salary_category
            expense.save()
        else:  # Create new expense if it doesn't exist
            expense = Expense(
                user=request.user, 
                farm=farm,
                date=salary_transaction.transaction_date,
                description=f'Salary for {salary_transaction.farm_member} - {salary_transaction.component.name}',
                amount=salary_transaction.amount_paid,
                category=salary_category,
                salary_transaction=salary_transaction
            )
            expense.save()

        return Response(serializer.data)
    return Response(serializer.errors, status=400)


@api_view(['DELETE', 'GET'])
@permission_classes([permissions.IsAuthenticated])
def delete_salary_transaction(request, pk):
    try:
        salary_transaction = SalaryTransaction.objects.get(pk=pk)
    except SalaryTransaction.DoesNotExist:
        return Response({"error": "Salary transaction not found"}, status=404)

    if request.method == 'GET':
        # Return the transaction details for confirmation
        return Response({
            'transaction': SalaryTransactionSerializer(salary_transaction).data,
            'message': "Are you sure you want to delete this salary transaction?"
        })

    # DELETE method handling
    try:
        # Delete associated expense first
        Expense.objects.filter(salary_transaction=salary_transaction).delete()
        
        # Delete the salary transaction
        salary_transaction.delete()
        
        return Response({
            "message": "Salary transaction and associated expense have been deleted successfully"
        })
    except Exception as e:
        return Response({
            "error": "Failed to delete salary transaction",
            "details": str(e)
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_employee_api(request):
    if request.method == 'POST':
        
        data = request.data.copy()

        # Normalize mobile number if provided
        if 'mobile' in data:
            original_mobile = data['mobile']
            normalized_mobile = normalize_phone_number(original_mobile)
            if not normalized_mobile:
                return Response(
                    {'error': 'Invalid phone number format'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            data['mobile'] = normalized_mobile

        # Check if mobile number already exists
        if 'mobile' in data:
            existing_user = CustomUser.objects.filter(mobile__in=[
                normalized_mobile,
                normalized_mobile.replace('+', '00'),
                normalized_mobile[3:] if normalized_mobile.startswith('+92') else None,
                '0' + normalized_mobile[3:] if normalized_mobile.startswith('+92') else None,
            ]).first()
            
            if existing_user:
                return Response(
                    {'error': 'Mobile number already exists'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Ensure profile data contains required fields
        profile_data = {
            'first_name': data.get('first_name', ''),
            'last_name': data.get('last_name', ''),
            'city': data.get('city', ''),
            'email': data.get('email', ''),
            'joining_date': data.get('joining_date', ''),
            'end_date': None
        }


        # Handle profile picture if provided
        if 'profile_picture' in request.FILES:
            profile_data['profile_picture'] = request.FILES['profile_picture']

        # Create the user data structure
        user_data = {
            'mobile': data.get('mobile', ''),
            'email': data.get('email', ''),
            'role': data.get('role', ''),
            'first_name': data.get('first_name', ''),  
            'last_name': data.get('last_name', ''),    
            'profile': profile_data
        }


        serializer = CustomUserSerializer(data=user_data)

        if serializer.is_valid():
            farm = request.user.farm
            try:
                instance = serializer.save(farm=farm)
                # Refresh the instance to get updated data
                instance.refresh_from_db()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_employee_api(request, pk):
    try:
        employee = CustomUser.objects.get(pk=pk, farm=request.user.farm)
        serializer = CustomUserSerializer(employee)
        return Response(serializer.data)
    except CustomUser.DoesNotExist:
        return Response({'error': 'Employee not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def edit_employee_api(request, pk):
    
    try:
        employee = CustomUser.objects.get(pk=pk, farm=request.user.farm)
    except CustomUser.DoesNotExist:
        return Response({'error': 'Employee not found'}, status=status.HTTP_404_NOT_FOUND)

    data = request.data.copy()

    # Handle profile picture first
    if 'profile_picture' in request.FILES:
        # Update the profile picture directly
        employee.profile.profile_picture = request.FILES['profile_picture']
        employee.profile.save()

    # Get existing profile data
    existing_profile = {
        'first_name': employee.profile.first_name,
        'last_name': employee.profile.last_name,
        'city': employee.profile.city,
        'email': employee.profile.email,
        'joining_date': employee.profile.joining_date,
        'end_date': employee.profile.end_date,
    }

    # Only update fields that are provided and not empty
    profile_data = {
        'first_name': data.get('profile.first_name') if data.get('profile.first_name') else existing_profile['first_name'],
        'last_name': data.get('profile.last_name') if data.get('profile.last_name') else existing_profile['last_name'],
        'city': data.get('profile.city') if data.get('profile.city') else existing_profile['city'],
        'email': data.get('profile.email') if data.get('profile.email') else existing_profile['email'],
        'joining_date': data.get('profile.joining_date') if data.get('profile.joining_date') else existing_profile['joining_date'],
        'end_date': data.get('profile.end_date') if 'profile.end_date' in data else existing_profile['end_date'],
    }


    # Create the user data structure
    user_data = {
        'mobile': data.get('mobile', employee.mobile),
        'email': data.get('email', employee.email),
        'role': data.get('role', employee.role),
        'profile': profile_data
    }


    try:
        serializer = CustomUserSerializer(employee, data=user_data, partial=True)
        if serializer.is_valid():
            instance = serializer.save()
            # Refresh the instance to get updated data
            instance.refresh_from_db()
            return Response(serializer.data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


from rest_framework.parsers import JSONParser
from django.contrib.auth import login
from .forms import FarmMemberCreationForm
import logging

# Configure logger for API
logger = logging.getLogger('api')

class SignupAPIView(APIView):
    permission_classes = (permissions.AllowAny,)
    parser_classes = (JSONParser,)

    def post(self, request, *args, **kwargs):
        try:
            
            data = request.data.copy()  # Make a mutable copy of the data
            logger.info(f"New signup attempt with data: {data}")

            # Extract farm name
            farm_name = data.pop('farm_name', None)
            if not farm_name:
                return Response(
                    {'error': 'Farm name is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get country code from request, default to PK if not provided
            country = data.pop('country', 'PK')

            # Normalize mobile number if provided
            if 'mobile' in data:
                original_mobile = data['mobile']
                normalized_mobile = normalize_phone_number(original_mobile, default_region=country)
                
                if not normalized_mobile:
                    logger.warning(f"Signup failed - Invalid phone number format: {original_mobile}")
                    return Response(
                        {'error': 'Invalid phone number format'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                data['mobile'] = normalized_mobile
                logger.info(f"Normalized mobile number from {original_mobile} to {normalized_mobile}")
            else:
                logger.warning("Signup failed - Mobile number is required")
                return Response(
                    {'error': 'Mobile number is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if mobile number already exists
            if CustomUser.objects.filter(mobile=data['mobile']).exists():
                return Response(
                    {'error': 'Mobile number already registered'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create user instance
            from django.contrib.auth.hashers import make_password
            user_data = {
                'mobile': data['mobile'],
                'password': make_password(data['password1']),
                'first_name': data.get('first_name', ''),
                'last_name': data.get('last_name', ''),
                'role': 'admin'
            }
            
            user = CustomUser.objects.create(**user_data)
            
            # Create farm
            from dairy.models import Farm
            farm = Farm.objects.create(
                name=farm_name,
                admin=user
            )
            
            # Associate farm with user
            user.farm = farm
            user.save()
            
            # Create profile
            from accounts.models import Profile
            from django.utils import timezone
            profile = Profile.objects.create(
                user=user,
                first_name=data.get('first_name', ''),
                last_name=data.get('last_name', ''),
                city='Not Specified',  # Required field
                email=user.email or '',  # Required field
                joining_date=timezone.now().date()  # Required field
            )
            
            return Response({
                'message': 'User, farm and profile created successfully',
                'user_id': user.id,
                'farm_id': farm.id
            }, status=status.HTTP_201_CREATED)
                
        except Exception as e:
            import traceback
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_salary_component_details(request, component_id):
    try:
        component = SalaryComponent.objects.get(id=component_id)
        return Response({
            'component_id': component.id,
            'component_name': component.name,
            'member_name': f"{component.member.first_name} {component.member.last_name}",
            'member_farm': component.member.farm.name,
            'your_farm': request.user.farm.name,
            'can_delete': component.member.farm == request.user.farm
        })
    except SalaryComponent.DoesNotExist:
        return Response({"error": "Salary Component not found"}, status=404)

from django.contrib.auth import authenticate
from django.contrib import messages
from django.shortcuts import redirect, render
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from rest_framework.renderers import TemplateHTMLRenderer, JSONRenderer
from rest_framework.authentication import SessionAuthentication, TokenAuthentication

@method_decorator(csrf_exempt, name='dispatch')
class DeleteAccountView(APIView):
    renderer_classes = [TemplateHTMLRenderer, JSONRenderer]
    authentication_classes = [SessionAuthentication, TokenAuthentication]
    permission_classes = [AllowAny]
    template_name = 'accounts/delete_account.html'

    def get(self, request, *args, **kwargs):
        # Check if it's an API request
        if request.accepted_renderer.format == 'json':
            if not request.user.is_authenticated:
                return Response({"error": "Authentication required"}, status=401)
            return Response({"message": "Account deletion confirmation required"})

        # Handle web request
        if not request.user.is_authenticated:
            return redirect(f'/accounts/login/?next={request.path}')
        return Response({}, template_name=self.template_name)

    def post(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return Response({"error": "You must be logged in to delete your account."}, status=401)
            
        confirmation = request.data.get('confirmation')
        password = request.data.get('password')
        
        if confirmation != 'DELETE':
            return Response({"error": "Please type 'DELETE' to confirm account deletion."}, status=400)
        
        user = authenticate(username=request.user.username, password=password)
        if user is None:
            return Response({"error": "Invalid password. Please try again."}, status=400)
        
        try:
            # Delete related data
            if hasattr(user, 'profile'):
                user.profile.delete()
            
            # Delete the user account
            user.delete()
            return Response({"message": "Your account has been successfully deleted."})
            
        except Exception as e:
            return Response(
                {"error": "An error occurred while deleting your account. Please try again.", 
                 "details": str(e)}, 
                status=500
            )

    def options(self, request, *args, **kwargs):
        response = super().options(request, *args, **kwargs)
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        return response

class LoginView(APIView):
    permission_classes = (permissions.AllowAny,)
    parser_classes = (JSONParser,)
    
    def post(self, request, *args, **kwargs):
        try:
            mobile_number = request.data.get("username")
            password = request.data.get("password")
            country = request.data.get("country", "PK")  # Default to Pakistan if not specified
            
            if not mobile_number or not password:
                logger.warning(f"Login attempt failed - Missing credentials for mobile: {mobile_number}")
                return Response(
                    {"error": "Both username and password are required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Normalize the phone number with the specified country
            normalized_mobile = normalize_phone_number(mobile_number, default_region=country)
            logger.info(f"Login attempt for mobile: {mobile_number} (normalized: {normalized_mobile})")
            
            if not normalized_mobile:
                logger.warning(f"Login failed - Invalid phone number format: {mobile_number}")
                return Response(
                    {"error": "Invalid phone number format"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Try to find user with normalized number
            try:
                # First try exact match with normalized number
                user = CustomUser.objects.filter(mobile=normalized_mobile).first()
                
                if not user:
                    # For backward compatibility, try alternative formats
                    country_code = get_country_code_from_number(normalized_mobile)
                    if country_code:
                        local_number = normalized_mobile.replace(country_code, '')
                        possible_formats = [
                            normalized_mobile,
                            normalized_mobile.replace('+', '00'),
                            local_number,
                            '0' + local_number,
                        ]
                        user = CustomUser.objects.filter(mobile__in=possible_formats).first()
                
                if not user:
                    logger.warning(f"Login failed - User does not exist: {normalized_mobile}")
                    return Response(
                        {"error": "Invalid credentials"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                
                if not user.is_active:
                    logger.warning(f"Login failed - Inactive user: {normalized_mobile}")
                    return Response(
                        {"error": "Account is inactive"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
                
                # Try authentication with the user's actual stored mobile number
                user = authenticate(request, username=user.mobile, password=password)
                
                if user is not None:
                    # login successful
                    token, created = Token.objects.get_or_create(user=user)
                    logger.info(f"Login successful for user ID: {user.id}")
                    
                    # Track login
                    UserLogin.objects.create(
                        user=user,
                        ip_address=request.META.get('REMOTE_ADDR'),
                        user_agent=request.META.get('HTTP_USER_AGENT'),
                        login_type='api'
                    )
                    
                    # Get user's farm information
                    farm_data = None
                    if user.farm:
                        farm_data = {
                            'id': user.farm.id,
                            'name': user.farm.name,
                            'location': user.farm.location
                        }
                    elif hasattr(user, 'owned_farms'):
                        owned_farm = user.owned_farms.first()
                        if owned_farm:
                            farm_data = {
                                'id': owned_farm.id,
                                'name': owned_farm.name,
                                'location': owned_farm.location
                            }

                    response_data = {
                        'token': token.key,
                        'user': {
                            'id': user.id,
                            'mobile': user.mobile,
                            'first_name': user.first_name,
                            'last_name': user.last_name,
                            'role': user.role,
                            'email': user.email,
                            'farm': farm_data
                        }
                    }
                    return Response(response_data, status=status.HTTP_200_OK)
                else:
                    logger.warning(f"Login failed - Invalid credentials for mobile: {normalized_mobile}")
                    return Response(
                        {"error": "Invalid credentials"}, 
                        status=status.HTTP_401_UNAUTHORIZED
                    )
            except Exception as e:
                logger.error(f"Error checking user existence: {str(e)}", exc_info=True)
                raise
                
        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}", exc_info=True)
            return Response(
                {"error": "An error occurred during login"}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
