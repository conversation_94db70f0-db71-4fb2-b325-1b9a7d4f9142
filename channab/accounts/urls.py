from django.urls import path
from . import views, views_api

app_name = 'accounts'

urlpatterns = [
    path('unpaid_leave/<int:leave_id>/edit/', views.edit_unpaid_leave, name='edit_unpaid_leave'),
    path('unpaid_leave/<int:leave_id>/delete/', views.delete_unpaid_leave, name='delete_unpaid_leave'),
    # Web Views
    path('profile/', views.user_profile, name='user_profile'),
    path('edit-profile/', views.edit_profile, name='edit_profile'),
    path('signup/', views.SignupView.as_view(), name='signup'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('delete-account/', views_api.DeleteAccountView.as_view(), name='delete_account'),

    # Farm Member Management
    path('create_farm_member/', views.create_farm_member, name='create_farm_member'),
    path('edit_member/<int:pk>/', views.edit_member, name='edit_member'),
    path('member/<int:member_id>/', views.member_detail, name='member_detail'),
    path('reset_password/<int:pk>/', views.reset_password, name='reset_password'),
    path('delete_member/<int:pk>/', views.delete_member, name='delete_member'),
    path('farm_member_list/', views.farm_member_list, name='farm_member_list'),

    # Farm Management
    path('farm/', views.farm_view, name='farm_detail'),
    path('edit_farm/<int:farm_id>/', views.edit_farm, name='edit_farm'),

    # Salary Management
    path('member/<int:member_id>/salary/', views.salary_components, name='salary_components'),
    path('member/<int:member_id>/salary/add/', views.add_salary_component, name='add_salary_component'),
    path('member/<int:member_id>/salary/<int:component_id>/update/', views.update_salary_component, name='update_salary_component'),
    path('member/<int:member_id>/salary/<int:component_id>/delete/', views.delete_salary_component, name='delete_salary_component'),
    path('salary_transactions/', views.salary_transaction_list, name='salary_transaction_list'),
    path('get_salary_components/<int:user_id>/', views.get_salary_components, name='get_salary_components'),
    path('salary_transactions/update/<int:member_id>/<int:pk>/', views.salary_transaction_update_member, name='salary_transaction_update_member'),
    path('salary_transactions/create/', views.salary_transaction_update, name='salary_transaction_create'),
    path('salary_transactions/update/<int:pk>/', views.salary_transaction_update, name='salary_transaction_update'),
    path('salary_transactions/delete/<int:pk>/', views.salary_transaction_delete, name='salary_transaction_delete'),
    path('export-salary/<int:member_id>/', views.export_salary_excel, name='export_salary_excel'),
    path('import-salary/<int:member_id>/', views.import_salary_excel, name='import_salary_excel'),
    path('import-salary-form/<int:member_id>/', views.import_salary_form, name='import_salary_form'),

    # Admin Views
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/users-list/', views.admin_users_list, name='admin_users_list'),
    path('admin/set-user-active-period/<int:user_id>/', views.set_user_active_period, name='set_user_active_period'),
    path('admin/farms-list/', views.admin_farms_list, name='admin_farms_list'),
    path('admin/admins-list/', views.admin_admins_list, name='admin_admins_list'),
    path('admin/labours-list/', views.admin_labours_list, name='admin_labours_list'),

    # API Endpoints
    path('api/signup/', views_api.SignupAPIView.as_view(), name='api_signup'),
    path('api/login/', views.LoginView.as_view(), name='api_login'),
    path('api/logout/', views.LogoutView.as_view(), name='api_logout'),
    path('api/user-profile/', views_api.UserProfileView.as_view(), name='api_user_profile'),
    path('api/create_employee/', views_api.create_employee_api, name='create_employee_api'),
    path('api/add-salary-transaction/', views_api.add_salary_transaction, name='add_salary_transaction'),
    path('api/edit-salary-transaction/<int:pk>/', views_api.edit_salary_transaction, name='edit_salary_transaction'),
    path('api/delete-salary-transaction/<int:pk>/', views_api.delete_salary_transaction, name='delete_salary_transaction'),
    path('api/active-employees/', views_api.list_active_employees, name='list_active_employees'),
    path('api/employees/<int:employee_id>/salary_components/', views_api.SalaryComponentListView.as_view(), name='salary_component_list'),
    path('api/salary_components/create/', views_api.SalaryComponentCreateView.as_view(), name='salary_component_create'),
    path('api/salary_components/update/<int:pk>/', views_api.SalaryComponentUpdateView.as_view(), name='salary_component_update'),
    path('api/salary_components/delete/<int:pk>/', views_api.SalaryComponentDeleteView.as_view(), name='salary_component_delete'),
    path('api/farms/<int:farm_id>/salary_components/', views_api.FarmSalaryComponentListView.as_view(), name='farm_salary_component_list'),
    path('api/employees/<int:employee_id>/salary_transactions/', views_api.SalaryTransactionListView.as_view(), name='salary_transaction_list'),
    path('api/employees/<int:employee_id>/salary_info/', views_api.get_salary_info, name='salary-info'),
    path('api/edit_employee/<int:pk>/', views_api.edit_employee_api, name='edit_employee_api'),
    path('api/employee/<int:pk>/', views_api.get_employee_api, name='get_employee_api'),
    path('api/salary_components/check/<int:component_id>/', views_api.check_salary_component_details, name='check_salary_component'),
]
