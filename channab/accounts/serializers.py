from rest_framework import serializers
from .models import Profile, Salary<PERSON>ransaction, CustomUser, SalaryComponent

from rest_framework import serializers
from accounts.models import SalaryTransaction, SalaryComponent

class SalaryTransactionSerializer(serializers.ModelSerializer):
    component_name = serializers.SerializerMethodField()

    class Meta:
        model = SalaryTransaction
        fields = ['id', 'farm_member', 'component', 'component_name', 'amount_paid', 'transaction_date', 'description', 'receipt_image']
        read_only_fields = ['component_name']

    def get_component_name(self, obj):
        return obj.component.name


class SalaryComponentSerializer(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    employee_id = serializers.SerializerMethodField()
    profile_id = serializers.SerializerMethodField()

    class Meta:
        model = SalaryComponent
        fields = ['id', 'name', 'amount', 'duration', 'employee_name', 'employee_id', 'profile_id']

    def get_employee_name(self, obj):
        return f"{obj.member.first_name} {obj.member.last_name}"

    def get_employee_id(self, obj):
        return obj.member.id

    def get_profile_id(self, obj):
        return obj.member.profile.id


class ProfileSerializer(serializers.ModelSerializer):
    profile_picture = serializers.ImageField(required=False, allow_null=True)
    
    class Meta:
        model = Profile
        fields = ['profile_picture', 'first_name', 'last_name', 'city', 'email', 'joining_date', 'end_date']
        extra_kwargs = {
            'first_name': {'required': True},  
            'last_name': {'required': False, 'allow_blank': True},
            'city': {'required': True},  
            'email': {'required': False, 'allow_blank': True},
            'joining_date': {'required': True},  
            'end_date': {'required': False, 'allow_null': True},
        }

class CustomUserSerializer(serializers.ModelSerializer):
    profile = ProfileSerializer()
    is_active_until = serializers.DateTimeField(required=False, allow_null=True)
    is_temporarily_active = serializers.SerializerMethodField()

    class Meta:
        model = CustomUser
        fields = ['id', 'mobile', 'email', 'first_name', 'last_name', 'farm', 'role', 'profile', 'is_active', 'is_active_until', 'is_temporarily_active']

    def get_is_temporarily_active(self, obj):
        return obj.is_temporarily_active()

    def create(self, validated_data, farm=None):
        print("\n=== CustomUserSerializer Create ===")
        profile_data = validated_data.pop('profile')
        print(f"Profile data: {profile_data}")
        # Handle is_active_until default
        if 'is_active_until' not in validated_data or validated_data['is_active_until'] is None:
            from django.utils import timezone
            from datetime import timedelta
            validated_data['is_active_until'] = timezone.now() + timedelta(days=3)
        if farm is not None:
            validated_data['farm'] = farm
        user = CustomUser.objects.create(**validated_data)
        print(f"Created user: {user.email}")
        profile = Profile.objects.create(user=user, **profile_data)
        print(f"Created profile. Profile picture path: {profile.profile_picture}")
        return user

    def update(self, instance, validated_data):
        print(f"\n=== CustomUserSerializer Update ===")
        print(f"Validated data: {validated_data}")
        profile_data = validated_data.pop('profile')
        print(f"Profile data: {profile_data}")
        profile = instance.profile
        instance.mobile = validated_data.get('mobile', instance.mobile)
        instance.email = validated_data.get('email', instance.email)
        instance.first_name = validated_data.get('first_name', instance.first_name)
        instance.last_name = validated_data.get('last_name', instance.last_name)
        instance.role = validated_data.get('role', instance.role)
        if 'is_active' in validated_data:
            instance.is_active = validated_data.get('is_active', instance.is_active)
        if 'is_active_until' in validated_data:
            instance.is_active_until = validated_data.get('is_active_until', instance.is_active_until)
        profile.first_name = profile_data.get('first_name', profile.first_name)
        profile.last_name = profile_data.get('last_name', profile.last_name)
        profile.city = profile_data.get('city', profile.city)
        profile.email = profile_data.get('email', profile.email)
        profile.joining_date = profile_data.get('joining_date', profile.joining_date)
        profile.end_date = profile_data.get('end_date', profile.end_date)
        if 'profile_picture' in profile_data:
            print(f"New profile picture received: {profile_data['profile_picture']}")
            profile.profile_picture = profile_data['profile_picture']
        print(f"Profile picture after update: {profile.profile_picture}")
        instance.save()
        profile.save()
        print(f"Profile saved. Profile picture path: {profile.profile_picture}")
        return instance

    def create(self, validated_data, farm=None):
        print("\n=== CustomUserSerializer Create ===")
        profile_data = validated_data.pop('profile')
        print(f"Profile data: {profile_data}")
        
        # Create user
        if farm is not None:
            validated_data['farm'] = farm
        
        user = CustomUser.objects.create(**validated_data)
        print(f"Created user: {user.email}")
        
        # Create profile with all data including profile picture if present
        profile = Profile.objects.create(user=user, **profile_data)
        print(f"Created profile. Profile picture path: {profile.profile_picture}")
        
        return user

    def update(self, instance, validated_data):
        print(f"\n=== CustomUserSerializer Update ===")
        print(f"Validated data: {validated_data}")
        profile_data = validated_data.pop('profile')
        print(f"Profile data: {profile_data}")
        profile = instance.profile

        instance.mobile = validated_data.get('mobile', instance.mobile)
        instance.email = validated_data.get('email', instance.email)
        instance.first_name = validated_data.get('first_name', instance.first_name)
        instance.last_name = validated_data.get('last_name', instance.last_name)
        instance.role = validated_data.get('role', instance.role)
        
        # Preserve current is_active status unless end_date is set
        if 'is_active' in validated_data:
            instance.is_active = validated_data.get('is_active', instance.is_active)
        
        profile.first_name = profile_data.get('first_name', profile.first_name)
        profile.last_name = profile_data.get('last_name', profile.last_name)
        profile.city = profile_data.get('city', profile.city)
        profile.email = profile_data.get('email', profile.email)
        profile.joining_date = profile_data.get('joining_date', profile.joining_date)
        profile.end_date = profile_data.get('end_date', profile.end_date)

        # Explicitly handle profile picture
        if 'profile_picture' in profile_data:
            print(f"New profile picture received: {profile_data['profile_picture']}")
            profile.profile_picture = profile_data['profile_picture']
        
        print(f"Profile picture after update: {profile.profile_picture}")

        # Only deactivate if end_date is provided and valid
        if profile.end_date:
            instance.is_active = False
        else:
            instance.is_active = True

        instance.save()
        profile.save()
        print(f"Profile saved. Profile picture path: {profile.profile_picture}")
        return instance

from rest_framework import serializers
from .models import Profile

class ActiveEmployeeSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source='user.id', read_only=True)
    employee_id = serializers.IntegerField(source='user.id', read_only=True)
    mobile = serializers.CharField(source='user.mobile')
    email = serializers.EmailField(source='user.email')
    role = serializers.CharField(source='user.role')
    joining_date = serializers.DateField()
    end_date = serializers.DateField()
    status = serializers.SerializerMethodField()
    profile_picture = serializers.ImageField(use_url=True)

    class Meta:
        model = Profile
        fields = ['employee_id', 'first_name', 'last_name', 'mobile', 'email', 'user_id', 'role', 'joining_date', 'end_date', 'status', 'profile_picture', 'city']

    def get_status(self, obj):
        return 'Active' if obj.user.is_active else 'Inactive'

from rest_framework import permissions, serializers, generics, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from .models import Farm, SalaryComponent, CustomUser
from .serializers import SalaryComponentSerializer
from .models import SalaryComponent

class SalaryComponentSerializers(serializers.ModelSerializer):
    employee_name = serializers.SerializerMethodField()
    employee_id = serializers.SerializerMethodField()
    profile_id = serializers.SerializerMethodField()

    class Meta:
        model = SalaryComponent
        fields = ['id', 'name', 'amount', 'duration', 'employee_name', 'employee_id', 'profile_id']

    def get_employee_name(self, obj):
        return f"{obj.member.first_name} {obj.member.last_name}"

    def get_employee_id(self, obj):
        return obj.member.id

    def get_profile_id(self, obj):
        profile = Profile.objects.filter(user=obj.member).first()
        return profile.id if profile else None
