from django import forms
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm
from .models import CustomUser,  SalaryTransaction
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from .utils import normalize_phone_number

class MobileAuthenticationForm(AuthenticationForm):
    mobile = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'name': 'mobile'}),
        label='Mobile',
    )

    def __init__(self, *args, **kwargs):
        super(MobileAuthenticationForm, self).__init__(*args, **kwargs)
        self.fields['username'] = self.fields.pop('mobile')

    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if username and password:
            # Normalize the phone number
            normalized_mobile = normalize_phone_number(username)
            if not normalized_mobile:
                raise forms.ValidationError(
                    "Invalid phone number format.",
                    code='invalid_phone'
                )

            # Try to find user with normalized number
            user = CustomUser.objects.filter(mobile__in=[
                normalized_mobile,
                normalized_mobile.replace('+', '00'),
                normalized_mobile[3:] if normalized_mobile.startswith('+92') else None,
                '0' + normalized_mobile[3:] if normalized_mobile.startswith('+92') else None,
            ]).first()

            if user:
                # Use the actual stored mobile number for authentication
                self.user_cache = authenticate(
                    self.request,
                    username=user.mobile,
                    password=password
                )
                if self.user_cache is None:
                    raise forms.ValidationError(
                        "Please enter a correct mobile and password. Note that both fields may be case-sensitive.",
                        code='invalid_login'
                    )
            else:
                raise forms.ValidationError(
                    "Please enter a correct mobile and password. Note that both fields may be case-sensitive.",
                    code='invalid_login'
                )

            return self.cleaned_data


from .models import CustomUser

from django.utils import timezone

class UserActivationPeriodForm(forms.ModelForm):
    is_active_until = forms.DateTimeField(
        required=True,
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'form-control'}),
        help_text='User will be active until this date/time.'
    )

    class Meta:
        model = CustomUser
        fields = ['is_active_until']

class CustomUserCreationForm(UserCreationForm):
    
    mobile = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'name': 'mobile'}),
        label='Mobile',
    )
    password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'name': 'password1'}),
        label='Password',
    )
    password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'name': 'password2'}),
        label='Confirm Password',
    )


    class Meta:
        model = CustomUser
        fields = ('mobile', 'password1', 'password2')  # Add 'mobile' to the fields


from .models import Profile

class ProfileForm(forms.ModelForm):
    class Meta:
        model = Profile
        fields = ['profile_picture', 'first_name', 'last_name', 'city', 'email', 'facebook', 'youtube', 'joining_date']

class ProfileUpdateForm(forms.ModelForm):
    role = forms.ChoiceField(choices=CustomUser.ROLE_CHOICES, initial='read_only', required=False)
    last_name = forms.CharField(max_length=30, required=False)
    mobile = forms.CharField(max_length=17, required=False)
    facebook = forms.URLField(required=False, widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://facebook.com/your-profile'}))
    youtube = forms.URLField(required=False, widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://youtube.com/your-channel'}))

    class Meta:
        model = Profile
        fields = ('profile_picture', 'first_name', 'last_name', 'city', 'facebook', 'youtube', 'joining_date', 'end_date')

    joining_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        required=False
    )
    end_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
        required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.user:
            self.fields['role'].initial = self.instance.user.role

    def clean_profile_picture(self):
        profile_picture = self.cleaned_data.get('profile_picture')
        if profile_picture:
            # Check file size (limit to 5MB)
            if profile_picture.size > 5 * 1024 * 1024:
                raise forms.ValidationError("Image file size must be less than 5MB.")
            
            # Check file type
            allowed_types = ['image/jpeg', 'image/png', 'image/jpg']
            if profile_picture.content_type not in allowed_types:
                raise forms.ValidationError("Only JPEG and PNG files are allowed.")
        
        return profile_picture

    def clean_facebook(self):
        facebook = self.cleaned_data.get('facebook')
        if facebook:
            if not facebook.startswith(('http://', 'https://')):
                raise forms.ValidationError("Please enter a valid URL starting with http:// or https://")
            if 'facebook.com' not in facebook:
                raise forms.ValidationError("Please enter a valid Facebook URL")
        return facebook

    def clean_youtube(self):
        youtube = self.cleaned_data.get('youtube')
        if youtube:
            if not youtube.startswith(('http://', 'https://')):
                raise forms.ValidationError("Please enter a valid URL starting with http:// or https://")
            if 'youtube.com' not in youtube and 'youtu.be' not in youtube:
                raise forms.ValidationError("Please enter a valid YouTube URL")
        return youtube

    def save(self, commit=True):
        profile = super().save(commit=False)
        if commit:
            profile.save()
            if self.cleaned_data.get('role'):
                profile.user.role = self.cleaned_data['role']
                profile.user.save()
        return profile


class FarmMemberCreationForm(forms.ModelForm):
    password1 = forms.CharField(
        label="Password",
        widget=forms.PasswordInput(attrs={'class': 'form-control'})
    )
    password2 = forms.CharField(
        label="Confirm Password",
        widget=forms.PasswordInput(attrs={'class': 'form-control'})
    )
    farm_name = forms.CharField(
        label="Farm Name",
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    mobile = forms.CharField(
        max_length=17,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        error_messages={
            'required': 'Mobile number is required',
            'invalid': 'Please enter a valid mobile number'
        }
    )
    first_name = forms.CharField(
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        error_messages={
            'required': 'First name is required'
        }
    )
    last_name = forms.CharField(
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        error_messages={
            'required': 'Last name is required'
        }
    )
    location = forms.CharField(
        label="City",
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control'}),
        error_messages={
            'required': 'City is required'
        }
    )
    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )
    join_date = forms.DateField(
        label="Join Date",
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    role = forms.ChoiceField(
        choices=CustomUser.ROLE_CHOICES,
        initial="labour",
        required=False,
        widget=forms.HiddenInput
    )

    class Meta:
        model = CustomUser
        fields = ('mobile', 'email', 'first_name', 'last_name', 'farm_name', 'location', 'join_date', 'role')
        labels = {
            'mobile': 'Mobile Number',
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'location': 'City',
            'email': 'Email Address (Optional)'
        }

    def __init__(self, *args, **kwargs):
        self.is_signup = kwargs.pop('signup', False)
        super(FarmMemberCreationForm, self).__init__(*args, **kwargs)
        
        if self.is_signup:
            self.fields['mobile'].required = True
            self.fields['password1'].required = True
            self.fields['password2'].required = True
            del self.fields['location']
            del self.fields['join_date']
            del self.fields['role']
        else:
            del self.fields['password1']
            del self.fields['password2']
            del self.fields['farm_name']

    def clean_mobile(self):
        mobile = self.cleaned_data.get('mobile')
        if not mobile:
            raise forms.ValidationError("Mobile number is required")
        normalized_mobile = normalize_phone_number(mobile)
        if not normalized_mobile:
            raise forms.ValidationError("Invalid phone number format")
        return normalized_mobile

    def clean(self):
        cleaned_data = super().clean()
        if not self.is_signup:
            required_fields = ['first_name', 'last_name', 'mobile', 'location']
            for field in required_fields:
                if not cleaned_data.get(field):
                    raise forms.ValidationError({field: f'{self.fields[field].label} is required'})


class ResetPasswordForm(forms.Form):
    new_password1 = forms.CharField(label="New password", widget=forms.PasswordInput)
    new_password2 = forms.CharField(label="Confirm new password", widget=forms.PasswordInput)

    def clean_new_password2(self):
        password1 = self.cleaned_data.get("new_password1")
        password2 = self.cleaned_data.get("new_password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("Passwords don't match")
        return password2


from django import forms
from .models import SalaryComponent, Farm, UnpaidLeave

from django.utils import timezone

class UnpaidLeaveForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        today = timezone.now().date()
        self.fields['start_date'].widget = forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
        self.fields['end_date'].widget = forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
        self.fields['reason'].widget = forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'Reason (optional)'})
        self.fields['start_date'].initial = today
        self.fields['end_date'].initial = today

    class Meta:
        model = UnpaidLeave
        fields = ['start_date', 'end_date', 'reason']


class SalaryComponentForm(forms.ModelForm):
    class Meta:
        model = SalaryComponent
        fields = ['name', 'amount', 'duration']

class SalaryTransactionForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        member = kwargs.pop('member', None)
        farm = kwargs.pop('farm', None)
        super(SalaryTransactionForm, self).__init__(*args, **kwargs)

        if member:
            self.fields['farm_member'].queryset = CustomUser.objects.filter(pk=member.pk)
        elif farm:
            self.fields['farm_member'].queryset = CustomUser.objects.filter(farm=farm)
            self.fields['component'].queryset = SalaryComponent.objects.filter(member__farm=farm)

    class Meta:
        model = SalaryTransaction
        fields = ('farm_member', 'component', 'amount_paid', 'transaction_date', 'description' )

class FarmUpdateForm(forms.ModelForm):
    class Meta:
        model = Farm
        fields = ['name', 'description', 'location', 'profile_picture']
