import phonenumbers

def normalize_phone_number(phone_number, default_region="PK"):
    """
    Normalize phone numbers to international format while maintaining backward compatibility.
    Handles numbers with:
    - International format with + (e.g., +923407425673, +1234567890)
    - International format with 00 (e.g., 00923407425673)
    - Local format with leading zero (e.g., 03407425673)
    - Local format without leading zero (e.g., 3407425673)
    
    Args:
        phone_number: The phone number to normalize
        default_region: The default region code (ISO 3166-1 alpha-2) to use if no country code is provided
    """
    if not phone_number:
        return None

    try:
        # First, handle the legacy format (maintain backward compatibility)
        cleaned = ''.join(c for c in str(phone_number) if c.isdigit() or c == '+')
        
        # If number starts with 00, replace with +
        if cleaned.startswith('00'):
            cleaned = '+' + cleaned[2:]

        # Try to parse the number with phonenumbers library
        try:
            # If the number already has a + prefix, don't use default_region
            if cleaned.startswith('+'):
                parsed = phonenumbers.parse(cleaned)
            else:
                # For numbers without country code, use the default_region
                parsed = phonenumbers.parse(cleaned, default_region)

            # Validate the parsed number
            if phonenumbers.is_valid_number(parsed):
                # Format in E.164 format (e.g., +923407425673)
                return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)

        except phonenumbers.NumberParseException:
            pass

        # Fallback to legacy format for backward compatibility
        # This ensures existing Pakistani numbers still work
        if not cleaned.startswith('+'):
            if cleaned.startswith('0'):
                cleaned = '+92' + cleaned[1:]
            else:
                cleaned = '+92' + cleaned

        return cleaned

    except Exception as e:
        print(f"Error normalizing phone number: {str(e)}")
        return None

def get_country_code_from_number(phone_number):
    """
    Extract the country code from a phone number.
    Returns None if the number is invalid or country code cannot be determined.
    """
    try:
        parsed = phonenumbers.parse(phone_number)
        if phonenumbers.is_valid_number(parsed):
            return f"+{parsed.country_code}"
        return None
    except phonenumbers.NumberParseException:
        return None
