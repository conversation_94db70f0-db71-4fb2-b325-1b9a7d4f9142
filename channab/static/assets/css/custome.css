/*! CSS Used from: https://sego.dexignzone.com/xhtml/css/style.css */
/*! @import https://sego.dexignzone.com/xhtml/icons/font-awesome-old/css/font-awesome.min.css */
/*Animal list Card Style */

/*Animal list Card Style end*/
.search-dropdown {
  position: absolute;
  background-color: #f9f9f9;
  min-width: 150px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  top: 100%; /* Position it at the bottom of search box */
  left: 0; /* Align it to the left side of search box */
  width: 100%; /* Make it the same width as the search box */
  border-radius: 0 0 3px 3px; /* Round the bottom corners */
}

.search-dropdown li {
  border-bottom: 1px solid #ccc; /* Add a divider line */
  padding: 10px 0; /* Add some padding */
  display: block; /* Display as block to ensure each item is on a new line */
}

.search-dropdown li:last-child {
  border-bottom: none; /* Remove the bottom border from the last item */
}

.search-dropdown a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.search-dropdown a:hover {
  background-color: #f1f1f1;
}

.search-box {
  width: 100%;
  max-width: 150px !important;
  height: 50px;
  border: 1px solid #0da487;
  border-radius: 3px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  padding: 0 5px;
  position: relative; /* Set position to relative */
}

@media screen and (max-width: 600px) {
  .search-box {
    width: 120px !important;
    height: 40px !important;
  }
}

.search-input {
  width: 100%;
  border: none;
  color: #0da487;
  line-height: 1;
  overflow: hidden;
}

.search-icon {
  position: absolute;
  right: 5px;
  color: #0da487;
}

.search-input:focus {
  outline: none;
}

.image-section img {
  max-width: 100%;
  max-height: 250px;
}
.product-image {
  width: 100% !important;
  height: 250px !important;
}

.scrolling-wrapper-flexbox {
  overflow-x: auto;
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
}
.main-box.no-header {
  padding-top: 20px;
}
.main-box {
  background: #ffffff;
  -webkit-box-shadow: 1px 1px 2px 0 #cccccc;
  -moz-box-shadow: 1px 1px 2px 0 #cccccc;
  -o-box-shadow: 1px 1px 2px 0 #cccccc;
  -ms-box-shadow: 1px 1px 2px 0 #cccccc;
  box-shadow: 1px 1px 2px 0 #cccccc;
  margin-bottom: 16px;
  -webikt-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.table a.table-link.danger {
  color: #e74c3c;
}
.label {
  border-radius: 3px;
  font-size: 0.875em;
  font-weight: 600;
}
.user-list tbody td .user-subhead {
  font-size: 0.875em;
  font-style: italic;
}
.user-list tbody td .user-link {
  display: block;
  font-size: 1.25em;
  padding-top: 3px;
  margin-left: 60px;
}
a {
  color: #3498db;
  outline: none !important;
}
.user-list tbody td > img {
  position: relative;
  max-width: 50px;
  float: left;
  margin-right: 15px;
}

.table thead tr th {
  text-transform: uppercase;
  font-size: 0.875em;
}
.table thead tr th {
  border-bottom: 2px solid #e7ebee;
}
.table tbody tr td:first-child {
  font-size: 1.125em;
  font-weight: 300;
}
.table tbody tr td {
  font-size: 0.875em;
  vertical-align: middle;
  border-top: 1px solid #e7ebee;
  padding: 12px 8px;
}
a:hover {
  text-decoration: none;
}

.nav-link {
  flex: 1;
  text-align: center;
  padding: 5px 0;
  margin: 0 5px;
  color: #0da487; /* text color for non-active tabs */
  border: 2px solid #0da487; /* border color for non-active tabs */
  background-color: transparent; /* make non-active tab background transparent */
  border-radius: 5px;
  transition: all 0.3s ease-in-out;
}

.nav-link:hover,
.nav-link:focus,
.nav-link.active {
  color: #fff; /* text color for active tab */
  background-color: #0da487; /* background color for active tab */
  border: 2px solid #0da487; /* border color for active tab */
}

.nav-link:not(.active) {
  background-color: transparent;
  color: #0da487;
  /* make non-active tab background transparent */
}

.nav-pills.nav-justified > .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.nav-pills.nav-justified > .nav-item > .nav-link {
  padding: 10px;
  border-radius: 0;
}
.nav-pills .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

@media (max-width: 576px) {
  .nav-pills {
    overflow-x: auto;
    flex-wrap: nowrap;
  }

  .nav-pills .nav-item {
    display: inline-block;
    float: none;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .nav-pills::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .nav-pills {
    overflow-x: auto;
    white-space: nowrap;
  }
}
.scrolling-navbar {
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}
.scrolling-navbar::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.nav-item {
  flex: 0 0 auto;
}
.nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  max-width: 10rem;
}

@media (min-width: 601px) {
  .image-section img {
    width: 100%;
    max-width: 540px;
    object-fit: cover;
  }
}

.action-icons a {
  margin-left: 15px;
}

.nav-pills {
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 0;
  padding: 10px 0;
}
.nav-link {
  text-decoration: none;
}
.table-responsive {
  margin: auto;
}

.hiddenRow {
  padding: 0 !important;
}

.nav-pills .nav-item {
  display: inline-block;
  flex: none;
}
@media (max-width: 768px) {
  .action-icons {
    flex-direction: row-reverse;
  }
}

.overview-content {
  margin-top: 15px;
}

.info-table-wrapper {
  display: none;
}

.name-section {
  display: none;
}
.image-section img {
  max-width: 100%;
  max-height: 250px;
}

@media (min-width: 601px) {
  .image-section img {
    width: 100%;
    max-width: 540px;
    object-fit: cover;
  }
}

@media (min-width: 768px) {
  .info-table-wrapper {
    display: block;
  }
  .info-tab-mobile {
    display: none;
  }
  .name-section {
    display: block;
  }
}

@media (max-width: 767px) {
  .info-tab-mobile .info-table-wrapper {
    display: block;
  }
}

@media only screen and (max-width: 600px) {
  .animal-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
  }

  .animal-image img {
    width: 150px !important;
    height: 150px !important;
    object-fit: cover !important;
  }

  .animal-details {
    width: 100% !important;
  }

  .product-detail-box {
    display: flex !important;
    justify-content: space-between !important;
    border: 1px solid green !important;
    padding: 10px !important;
    margin: 10px 0 !important;
  }
}

@media (max-width: 767px) {
  /* Mobile view */
  .detail-page-image {
    width: 100%;
    height: auto;
  }
}
.image-section img {
  max-width: 100%;
  max-height: 250px;
}

@media (min-width: 601px) {
  .image-section img {
    width: 100%;
    max-width: 540px;
    object-fit: cover;
  }
}

.action-icons a {
  margin-left: 15px;
}

.nav-pills {
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 0;
  padding: 10px 0;
}
.nav-link {
  text-decoration: none;
}
.table-responsive {
  margin: auto;
}

.hiddenRow {
  padding: 0 !important;
}

.nav-pills .nav-item {
  display: inline-block;
  flex: none;
}
@media (max-width: 768px) {
  .action-icons {
    flex-direction: row-reverse;
  }
}

.overview-content {
  margin-top: 15px;
}

@media (min-width: 768px) {
  /* Desktop view */
  .detail-page-image {
    max-height: 250px;
    max-width: 450px;
  }
  .info-tab-mobile {
    display: none;
  }
}

/* styles.css */
.list-container {
  width: 100%;
  max-width: 880px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
  padding: 0 10px;
}

.list-header {
  margin: 0;
  color: #0da487;
}

.add-animal-link {
  text-decoration: none;
  color: inherit;
}

.add-animal-button {
  width: auto;
  height: 30px;
  border: 1px solid #0da487;
  border-radius: 3px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  padding: 0 5px;
}
.list-subcontainer {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  scroll-snap-align: start;
}
.list-subcontainer.selected {
  background-color: #0da487;
  margin-right: 10px;
}
.detail-page-image {
  max-height: 250px;
  max-width: 450px;
}

.list-subcontainer.selected .animal-type,
.list-subcontainer.selected .animal-count {
  color: white;
}

.add-animal-button span {
  color: #0da487;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.compact-table td,
.compact-table th {
  padding: 0.25rem; /* Adjust this value to your liking */
}

.compact-table {
  border-collapse: separate;
  border-spacing: 0; /* Remove space between cells */
}
.styled-container {
  width: 100%;
  max-width: 880px;
  display: flex;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
}

.filter-icon {
  color: #0da487;
  margin-right: 5px;
}

.filter-select {
  background-color: white;
  color: #0da487;
  border: 1px solid #0da487;
  height: 30px;
}

.filter-select-option {
  background-color: white;
  color: #0da487;
}

@import url("https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900&display=swap");
body {
  height: 100%;
  margin: 0;
}

a {
  color: #007bff;
  text-decoration: none;
}
button:focus,
input:focus {
  outline: none;
  box-shadow: none;
}
a,
a:hover {
  text-decoration: none;
}

body {
  font-family: "Roboto", sans-serif;
  background-color: #e0e3e8;
}

/*------------*/
.form-area {
  background-color: #fff;
  box-shadow: 0px 5px 10px rgba(90, 116, 148, 0.3);
  padding: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.form-area .form-inner {
  width: 100%;
}
.form-control {
  display: block;
  width: 100%;
  height: auto;
  padding: 15px 19px;
  font-size: 1rem;
  line-height: 1.4;
  color: #475f7b;
  background-color: #fff;
  border: 1px solid #dfe3e7;
  border-radius: 0.267rem;
  -webkit-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-control:focus {
  color: #475f7b;
  background-color: #fff;
  border-color: #5a8dee;
  outline: 0;
  box-shadow: 0 3px 8px 0 rgb(0 0 0 / 10%);
}
.intl-tel-input,
.iti {
  width: 100%;
}
.animal-list-header {
  width: 100%;
  max-width: 880px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
  padding: 0 10px;
}

.animal-list-header h2 {
  margin: 0;
  color: #0da487;
}

.animal-list-header a {
  text-decoration: none;
  color: inherit;
}

.add-animal-button {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.add-animal-button div {
  width: auto;
  height: 30px;
  border: 1px solid #0da487;
  border-radius: 3px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  padding: 0 5px;
}

.add-animal-button span {
  color: #0da487;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-select {
  background-color: white;
  color: #0da487;
  border: 1px solid #0da487;
  height: 30px;
}
.filter-icon {
  color: #0da487;
  margin-right: 5px;
}

.animal-card {
  width: 100%;
  max-width: 880px;
  height: 150px;
  display: flex;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
}

.animal-card a.image-link {
  width: 150px;
  height: 150px;
  display: block;
}

.animal-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.animal-card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px;
}

.animal-card-header {
  display: flex;
  justify-content: space-between;
}

.animal-card-header a {
  color: inherit;
  text-decoration: none;
}

.animal-card-header h4 {
  margin: 0;
}

.animal-card-actions {
  display: flex;
}

.animal-card-actions a {
  margin-right: 5px;
}

.animal-card-details {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  margin-left: 15px;
}

.animal-card-details span {
  display: inline-block;
  width: 30%;
  height: 30px;
  border: 1px solid #33a137;
  color: #33a137;
  line-height: 30px;
  text-align: center;
  border-radius: 6px;
}

.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.fa-star:before {
  content: "\f005";
}
md-chips {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
}

.tab-veiw {
  padding: 0.15rem;
  display: flex;
  flex-direction: row; /* Add this line */
  align-items: center;
  cursor: pointer;
  margin-right: 0.5rem;
  border-radius: 0.8rem;
  margin-bottom: 0.5rem;
  border: 0.1rem solid #0da487;
}

.tab-veiw1 {
  min-width: 2.6rem;
  margin-top: 0.2rem;
  margin-bottom: 0.2rem;
  margin-left: 0.6rem;
  display: inline-block;
  border-radius: 50%;
}

.tab-veiw2 {
  margin-left: 0.6rem;
  margin-right: 0.6rem;
  letter-spacing: 0;
}
.list-container.slider {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

.tab-slider {
  display: flex;
  place-content: space-between;
  overflow-x: auto;
  mix-blend-mode: normal;
  opacity: 1;
  margin-right: -1.6rem;
  margin-left: -1.6rem;
  padding-right: 0.8rem;
  padding-left: 1.6rem;
}

.tab-slider::-webkit-scrollbar {
  display: none;
}

.card-img-top {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-title {
  font-size: 20px;
  margin-bottom: 10px;
}

.card-text {
  font-size: 16px;
}

/*! end @import */
/*! @import https://sego.dexignzone.com/xhtml/icons/line-awesome/css/line-awesome.min.css */
.la {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}
.la-angle-left:before {
  content: "\f104";
}
.la-angle-right:before {
  content: "\f105";
}
.la {
  font-family: "Line Awesome Free";
  font-weight: 900;
}
/*! end @import */
*,
*::before,
*::after {
  box-sizing: border-box;
}
nav {
  display: block;
}
h4,
h5 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
ul {
  margin-top: 0;
  margin-bottom: 1rem;
}
ul ul {
  margin-bottom: 0;
}
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
a {
  color: #ea7a9a;
  text-decoration: none;
  background-color: transparent;
}
a:hover {
  color: #e03868;
  text-decoration: underline;
}
img {
  vertical-align: middle;
  border-style: none;
}
svg {
  overflow: hidden;
  vertical-align: middle;
}
h4,
h5 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
  color: #3d4465;
}
h4 {
  font-size: 1.125rem;
}
h5 {
  font-size: 1rem;
}
small {
  font-size: 80%;
  font-weight: 400;
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
.col-lg-6,
.col-xl-9,
.col-xxl-12 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}
@media (min-width: 992px) {
  .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 1200px) {
  .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }
}
@media (min-width: 1440) {
  .col-xxl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}
.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}
.nav-link:hover,
.nav-link:focus {
  text-decoration: none;
}
.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}
.nav-tabs .nav-item {
  margin-bottom: -1px;
}
.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
}
.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #f9f9f9;
  border-color: #dee2e6 #dee2e6 #f9f9f9;
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.75rem;
}
.card-body {
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}
.card-title {
  margin-bottom: 0.75rem;
}
.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.card-header:first-child {
  border-radius: calc(0.75rem - 1px) calc(0.75rem - 1px) 0 0;
}
.card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.75rem - 1px) calc(0.75rem - 1px);
}
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.75rem;
}
.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #ea7a9a;
  background-color: #fff;
  border: 1px solid #dee2e6;
}
.page-link:hover {
  z-index: 2;
  color: #e03868;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(234, 122, 154, 0.25);
}
.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #ea7a9a;
  border-color: #ea7a9a;
}
.media {
  display: flex;
  align-items: flex-start;
}
.media-body {
  flex: 1;
}
.border-0 {
  border: 0 !important;
}
.rounded {
  border-radius: 0.75rem !important;
}
.d-inline-block {
  display: inline-block !important;
}
.d-block {
  display: block !important;
}
.d-flex {
  display: flex !important;
}
@media (min-width: 992px) {
  .d-lg-flex {
    display: flex !important;
  }
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.align-items-center {
  align-items: center !important;
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mr-1 {
  margin-right: 0.25rem !important;
}
.mr-2 {
  margin-right: 0.5rem !important;
}
.mb-2 {
  margin-bottom: 0.5rem !important;
}
.mt-3 {
  margin-top: 1rem !important;
}
.mr-3 {
  margin-right: 1rem !important;
}
.mb-3 {
  margin-bottom: 1rem !important;
}
.ml-3 {
  margin-left: 1rem !important;
}
.mb-4 {
  margin-bottom: 1.5rem !important;
}
.pb-0 {
  padding-bottom: 0 !important;
}
.pb-2 {
  padding-bottom: 0.5rem !important;
}
@media (min-width: 576px) {
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
}
@media (min-width: 992px) {
  .mt-lg-0 {
    margin-top: 0 !important;
  }
}
.text-dark {
  color: #b1b1b1 !important;
}
@media print {
  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.btn) {
    text-decoration: underline;
  }
  img {
    page-break-inside: avoid;
  }
  p {
    orphans: 3;
    widows: 3;
  }
}
* {
  outline: none;
  padding: 0;
}
*::after {
  margin: 0;
  padding: 0;
}
*::before {
  margin: 0;
  padding: 0;
}
::selection {
  color: #fff;
  background: #ea7a9a;
}
p {
  line-height: 1.8;
}
ul {
  padding: 0;
  margin: 0;
}
li {
  list-style: none;
}
a {
  color: #7e7e7e;
}
a:hover,
a:focus,
a.active {
  text-decoration: none;
}
.text-orange {
  color: #ff9900;
}
.text-black {
  color: #000 !important;
}
.fs-12 {
  font-size: 12px !important;
  line-height: 1.3;
}
.fs-14 {
  font-size: 14px !important;
  line-height: 1.5;
}
@media only screen and (min-width: 1200px) and (max-width: 1600px) {
  .col-xxl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
.card {
  margin-bottom: 1.875rem;
  background-color: #fff;
  transition: all 0.5s ease-in-out;
  position: relative;
  border: 0px solid transparent;
  border-radius: 1.25rem;
  box-shadow: 0px 12px 23px 0px rgba(62, 63, 122, 0.04);
  height: calc(100% - 30px);
}
@media only screen and (max-width: 575px) {
  .card {
    margin-bottom: 0.938rem;
    height: calc(100% - 0.938rem);
  }
}
.card-body {
  padding: 1.875rem;
}
@media only screen and (max-width: 575px) {
  .card-body {
    padding: 1rem;
  }
}
.card-title {
  font-size: 20px;
  font-weight: 500;
  color: #000;
  text-transform: capitalize;
}
.card-header {
  border-color: #f0f1f5;
  position: relative;
  background: transparent;
  padding: 1.5rem 1.875rem 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (max-width: 575px) {
  .card-header {
    padding: 1.25rem 1rem 1.25rem;
  }
}
.card-header .card-title {
  margin-bottom: 0px;
}
.card-footer {
  border-color: #f0f1f5;
  background: transparent;
  padding: 1.25rem 1.875rem 1.25rem;
}
@media only screen and (max-width: 575px) {
  .card-footer {
    padding: 1rem;
  }
}
.media img {
  border-radius: 3px;
}
.pagination .page-item .page-link:hover {
  background: #ea7a9a;
  border-color: #ea7a9a;
  color: #fff;
}
.pagination .page-item.active .page-link {
  background: #ea7a9a;
  border-color: #ea7a9a;
  color: #fff;
}
.pagination {
  margin-bottom: 20px;
}
.pagination .page-item.page-indicator .page-link {
  padding: 0.65rem 0.8rem;
  font-size: 14px;
}
.pagination .page-item.page-indicator:hover .page-link {
  color: #b1b1b1;
}
.pagination .page-item .page-link {
  text-align: center;
  z-index: 2;
  padding: 0.55rem 1rem;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.15);
  color: #b1b1b1;
  border: 1px solid #f0f1f5;
}
.pagination .page-item .page-link:hover i {
  color: #fff;
}
.pagination .page-item .page-link:focus {
  outline: 0;
  box-shadow: none;
}
.pagination .page-item .page-link:hover {
  background: #ea7a9a;
  color: #fff;
  border-color: #ea7a9a;
}
.pagination .page-item.active .page-link {
  background-color: #ea7a9a;
  border-color: #ea7a9a;
  color: #fff;
  box-shadow: 0 10px 20px 0px rgba(234, 122, 154, 0.2);
}
.pagination .page-item .page-link {
  color: #b1b1b1;
  -webkit-transition: all 0.5s;
  -ms-transition: all 0.5s;
  transition: all 0.5s;
}
.pagination .page-item:last-child .page-link {
  margin-right: 0;
}
.star-review2 i {
  font-size: 14px;
}
.star-review2 i.text-gray {
  color: #d9d9d9;
}
.star-review2 > div {
  border: 1px solid #eeeeee;
  padding: 4px 8px;
  border-radius: 30px;
}
@media only screen and (max-width: 575px) {
  .most-favourite-items .media {
    display: block;
  }
  .most-favourite-items .media img {
    float: left;
    width: 68px;
    margin-bottom: 15px;
  }
}
.rounded {
  border-radius: 1.25rem !important;
}
.card-tabs .nav-tabs {
  border-bottom: 0px;
  background: transparent;
  padding: 5px;
  border-radius: 0;
  flex-wrap: unset;
}
.card-tabs .nav-tabs .nav-link {
  border-radius: 0;
  background: transparent;
  padding: 10px 14px;
  font-weight: 500;
  border-bottom: 1px solid #eeeeee !important;
  font-size: 14px;
  border: 0;
  color: #000;
  position: relative;
}
.card-tabs .nav-tabs .nav-link:after {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 0px;
  width: 100%;
  background: #ea7a9a;
  content: "";
}
.card-tabs .nav-tabs .nav-link.active {
  background: transparent;
  color: #ea7a9a;
}
.card-tabs .nav-tabs .nav-link.active:after {
  height: 2px;
}
@media only screen and (max-width: 1400px) {
  .card-tabs .nav-tabs .nav-link {
    padding: 8px 15px;
    font-weight: 400;
    font-size: 14px;
  }
}
@media only screen and (max-width: 767px) {
  .card-tabs .nav-tabs {
    flex-wrap: wrap;
  }
}
.donut-chart-sale {
  position: relative;
}
.donut-chart-sale small {
  font-size: 16px;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  display: flex;
  align-items: center;
  top: 0;
  justify-content: center;
  font-weight: 600;
}
.donut-chart-sale {
  z-index: 1;
}
.donut-chart-sale small {
  font-size: 14px;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  display: flex;
  align-items: center;
  top: 0;
  justify-content: center;
  font-weight: 600;
}
.pagination.style-1 {
  display: flex;
  justify-content: space-between;
}
.pagination.style-1 > li ul {
  display: flex;
  border-radius: 5rem;
  border: 1px solid #eeeeee;
}
.pagination.style-1 > li ul li a {
  border-width: 0 !important;
  height: 53px;
  width: 53px;
  padding: 0 !important;
  line-height: 53px;
  border-radius: 100% !important;
}
.pagination.style-1 > li > a {
  height: 53px;
  width: 53px;
  border-radius: 100% !important;
  line-height: 53px;
  font-size: 25px !important;
  background: #e0e0e0 !important;
  padding: 0 !important;
  color: #fff !important;
  border-width: 0 !important;
}
.pagination.style-1 > li > a:hover {
  background: #ea7a9a !important;
}
@media only screen and (max-width: 575px) {
  .pagination.style-1 > li ul li a {
    height: 40px;
    width: 40px;
    line-height: 40px;
  }
  .pagination.style-1 > li > a {
    height: 40px;
    width: 40px;
    font-size: 16px !important;
    line-height: 40px;
  }
}
/*! CSS Used fontfaces */
@font-face {
  font-family: "Line Awesome Free";
  font-style: normal;
  font-weight: 400;
  font-display: auto;
  src: url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-regular-400.eot);
  src: url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-regular-400.eot?#iefix) format("embedded-opentype"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-regular-400.woff2) format("woff2"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-regular-400.woff) format("woff"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-regular-400.ttf) format("truetype"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-regular-400.svg#lineawesome) format("svg");
}
@font-face {
  font-family: "Line Awesome Free";
  font-style: normal;
  font-weight: 900;
  font-display: auto;
  src: url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-solid-900.eot);
  src: url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-solid-900.eot?#iefix) format("embedded-opentype"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-solid-900.woff2) format("woff2"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-solid-900.woff) format("woff"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-solid-900.ttf) format("truetype"), url(https://sego.dexignzone.com/xhtml/icons/line-awesome/fonts/la-solid-900.svg#lineawesome) format("svg");
}

.date-inputs-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.date-inputs-wrapper input {
  width: calc(50% - 0.5em);
}

.info-div {
  margin-bottom: 10px; /* Add space between divs */
  padding: 5px;
  border-radius: 5px; /* Rounded border */
}
.category-title-div {
  margin-bottom: 10px; /* Add space between divs */
  padding: 5px;
  border-radius: 5px; /* Rounded border */
  background-color: #f0d0d0; /* Background color for first div */
  border: 2px solid #c0a0a0; /* Border color for first div */
  color: #c0a0a0; /* Text color for first div */
}

.sex-display-div {
  margin-bottom: 10px; /* Add space between divs */
  padding: 5px;
  border-radius: 5px; /* Rounded border */
  background-color: #d0f0d0; /* Background color for second div */
  border: 2px solid #a0c0a0; /* Border color for second div */
  color: #a0c0a0; /* Text color for second div */
}

.animal-type-display-div {
  margin-bottom: 10px; /* Add space between divs */
  padding: 5px;
  border-radius: 5px; /* Rounded border */
  background-color: #d0d0f0; /* Background color for third div */
  border: 2px solid #a0a0c0; /* Border color for third div */
  color: #a0a0c0; /* Text color for third div */
}

@media screen and (max-width: 576px) {
  /* For mobile devices with max width of 576px */
  .main-tiles .media-body h4 {
    font-size: 12;
  }
  .main-tiles .media-body span {
    font-size: 0.8rem; /* Adjust as needed */
  }
}

.custom-image-style {
  width: 68px !important;
  height: 68px !important;
  object-fit: cover;
  display: block;
  margin: auto;
  border-radius: 10%;
}

@media (max-width: 576px) {
  .date-inputs-wrapper input {
    width: 100%;
  }
}
.filter-form .form-control,
.filter-form .btn {
  height: 38px;
}

.filter-form .btn i {
  font-size: 18px;
}

.btn-form {
  margin-right: 20px; /* Adjust the value to increase/decrease space between the buttons */
  width: 150px; /* Adjust the width as needed */
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.grid-item {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 1rem;
  align-items: center;
}

.table-image img {
  max-width: 100%;
  height: auto;
}

/* Mobile view */
@media (max-width: 767px) {
  .grid-item {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .table-image {
    grid-row: 1;
    grid-column: 1;
  }

  .animal-info {
    grid-row: 2;
    grid-column: 1;
  }

  .animal-options {
    grid-row: 3;
    grid-column: 1;
  }
}

/* Web view */
@media (min-width: 768px) {
  .table-image img {
    max-width: 150%;
    height: auto;
  }
}

.btn-primary {
  background-color: #0da487;
}
.status-active span {
  background-color: greenish;
  color: green;
  padding: 2px 6px;
  border-radius: 4px;
}

.date-input::-webkit-calendar-picker-indicator {
  display: none;
}

.date-input::-webkit-inner-spin-button,
.date-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.dropdown-menu {
  min-width: auto;
}

.btn-soft-secondary {
  --vz-btn-color: #3577f1;
  --vz-btn-bg: rgba(53, 119, 241, 0.1);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: #fff;
  --vz-btn-hover-bg: #306bd9;
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: 53, 119, 241;
  --vz-btn-active-color: var(--vz-btn-hover-color);
  --vz-btn-active-bg: #2a5fc1;
  --vz-btn-active-border-color: transparent;
}

.btn-sm,
.btn-group-sm > .btn {
  --vz-btn-padding-y: 0.25rem;
  --vz-btn-padding-x: 0.5rem;
  --vz-btn-font-size: 0.7109375rem;
  --vz-btn-border-radius: 0.2rem;
}
.custom-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 8px 10px;
}

.custom-select::-ms-expand {
  display: none;
}

.status-danger span {
  background-color: redish;
  color: black;
  padding: 2px 6px;
  border-radius: 4px;
}

.status-warning span {
  background-color: yellow;
  color: black;
  padding: 2px 6px;
  border-radius: 4px;
}

/* Custom file input label */
.custom-file-label::after {
  content: "Browse";
}

/* Margin for the form elements */
.form-group {
  margin-bottom: 1rem;
}

/* Space between form elements */
.row > .col-md-6 {
  margin-bottom: 1rem;
}

.btn-custom {
  width: 100%;
  max-width: 200px;
}
.modal-bottom .modal-dialog {
  position: absolute;
  top: auto;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  margin: 0;
}
.scrollable-table {
  overflow-x: auto;
}
.header .navbar-top .middle-box a.service-price-link {
  text-decoration: none !important;
  color: inherit !important;
  margin: 0 15px !important;
  padding-right: 25px !important;
}

.select2-results__option {
  display: block;
  width: 100%;
  padding: 6px 12px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}

/* External CSS file: style.css */

.filter-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: auto; /* Enables horizontal scrolling */
  white-space: nowrap; /* Keeps items in a single line */
  padding: 10px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.filter-link {
  margin-right: 20px;
  color: #007bff;
  text-decoration: none;
  padding: 5px 15px;
  border-radius: 15px;
  border: 1px solid #007bff;
}

.date-input {
  padding: 5px;
  margin-right: 5px;
  border-radius: 5px;
  border: 1px solid #ccc;
}

.custom-date-filter {
  display: flex;
  align-items: center;
}
