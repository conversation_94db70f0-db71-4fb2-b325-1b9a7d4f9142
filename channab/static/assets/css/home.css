/* Filter Range CSS for home PAGE custom-styles.css */
.filter-bar {
  display: flex;
  align-items: center;
  overflow-x: auto; /* Allows horizontal scrolling */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  background-color: transparent;
  padding: 8px 16px;
  scroll-behavior: smooth;
  justify-content: flex-start; /* Aligns content to the start */
}

.filter-bar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.filter-title {
  color: #0da487;
  margin-right: 20px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.filter-link {
  display: inline-block;
  margin-right: 10px;
  padding: 4px 8px;
  background-color: transparent;
  border: 1px solid #0da487;
  color: #0da487;
  border-radius: 4px;
  text-decoration: none;
  transition: background-color 0.3s, color 0.3s;
}

.filter-link:hover,
.filter-link.active {
  background-color: #0da487;
  color: white;
}

.input-date {
  padding: 4px;
  margin-right: 10px;
  border: 1px solid #0da487;
  color: #0da487;
  border-radius: 4px;
  appearance: none; /* Removes default styles from date inputs */
  cursor: pointer; /* Indicates the input can be interacted with */
}

.input-date:hover {
  border-color: darken(#0da487, 10%);
}

.submit-btn {
  padding: 4px 8px;
  background-color: #0da487;
  color: white;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.submit-btn:hover {
  background-color: darken(#0da487, 10%);
}

/* FilterRange Row CSS for home PAGE end */

.button-shared {
  padding: 8px 16px; /* Consistent padding */
  font-size: 16px; /* Consistent font size */
  border-radius: 8px; /* Rounded corners for modern look */
  font-weight: bold; /* Bold text */
  text-align: center; /* Center text */
  display: inline-block; /* Inline block for proper placement */
  width: auto; /* Auto width based on content */
  transition: background-color 0.2s, box-shadow 0.2s; /* Smooth transitions for interactions */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
  border: none; /* No border */
  cursor: pointer; /* Pointer cursor on hover */
}

.save-btn {
  background-color: #0da487; /* Specific green shade */
  color: white;
}

.save-btn:hover,
.save-btn:focus {
  background-color: #088673; /* Slightly darker green on hover */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

.close-btn {
  background-color: #6c757d; /* Gray background */
  color: white;
}

.close-btn:hover,
.close-btn:focus {
  background-color: #5a6268; /* Slightly darker gray on hover */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

/* Add these styles to your Tailwind CSS configuration file under `extend` in `theme` */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .file-upload-container {
    @apply flex justify-center items-center w-full;
  }

  .file-upload-label {
    @apply flex flex-col justify-center items-center w-full h-32 bg-white rounded-lg border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-100;
  }

  .file-upload-content {
    @apply flex flex-col justify-center items-center pt-5 pb-6;
  }

  .file-upload-icon {
    @apply w-10 h-10 text-blue-500;
  }

  .file-upload-instructions {
    @apply mb-2 text-sm text-gray-500;
  }

  .file-upload-filetypes {
    @apply text-xs text-gray-500;
  }

  .hidden-input {
    @apply hidden;
  }
}
