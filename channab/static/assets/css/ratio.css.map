{"version": 3, "sources": ["ratio.scss"], "names": [], "mappings": "AAGA,0BAGY,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,0BAOG,kBAAmB,CACnB,UAAW,CACX,aAAc,CACjB,0BAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,iCAOG,eAAgB,CAChB,UAAW,CACX,aAAc,CACjB,8BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,4BAOG,wBAAyB,CACzB,UAAW,CACX,aAAc,CACjB,gCAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,0BAOG,gBAAiB,CACjB,UAAW,CACX,aAAc,CACjB,gBAKL,kCAAmC,CACnC,2BAA4B", "file": "ratio.css", "sourcesContent": ["/**=====================\n   Ratio CSS Start\n==========================**/\n.ratio_40 {\n    .bg-size {\n        &:before {\n            padding-top: 40%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_45 {\n    .bg-size {\n        &:before {\n            padding-top: 45%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio2_1 {\n    .bg-size {\n        &:before {\n            padding-top: 50%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_55 {\n    .bg-size {\n        &:before {\n            padding-top: 55%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_59 {\n    .bg-size {\n        &:before {\n            padding-top: 59%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio2_3 {\n    .bg-size {\n        &:before {\n            padding-top: 60%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio3_2 {\n    .bg-size {\n        &:before {\n            padding-top: 66.66%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_90 {\n    .bg-size {\n        &:before {\n            padding-top: 93%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_landscape {\n    .bg-size {\n        &:before {\n            padding-top: 75%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_square {\n    .bg-size {\n        &:before {\n            padding-top: 100%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_asos {\n    .bg-size {\n        &:before {\n            padding-top: 127.7777778%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio_portrait {\n    .bg-size {\n        &:before {\n            padding-top: 150%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.ratio1_2 {\n    .bg-size {\n        &:before {\n            padding-top: 200%;\n            content: \"\";\n            display: block;\n        }\n    }\n}\n\n.b_size_content {\n    background-size: contain !important;\n    background-repeat: no-repeat;\n}\n\n/**=====================\n     Ratio CSS End\n  ==========================**/\n"]}