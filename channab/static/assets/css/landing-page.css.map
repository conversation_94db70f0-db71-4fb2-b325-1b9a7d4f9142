{"version": 3, "sources": ["utils/_variables.scss", "landing-page.scss", "utils/mixin/_common.scss", "utils/mixin/_breakpoints.scss"], "names": [], "mappings": "AAAA,MACI,sBAAc,CACd,+BAAkB,CAClB,uBAAe,CACf,gCAAmB,CACnB;AAAe,CCAnB,KACI,oCDaM,CCZN,iBAAkB,CAClB,cAAe,CACf,UDCiB,CCAjB,QAAS,CACT,qBDHW,CCIX,uCAAgC,CAAhC,+BAAgC,CAChC,0BAA2B,CAR/B,sBAWQ,UDRO,CCSP,mCAAoC,CAZ5C,iBAWQ,UDRO,CCSP,mCAAoC,CAZ5C,iBCuBI,WDPmC,CCQnC,YDRmD,CAC/C,qBDZa,CCab,cAAe,CACf,SAAU,CACV,KAAM,CACN,SAAU,CACV,iBAAkB,CAClB,uBAAY,CAAZ,eAAgB,CAvBxB,sBA0BY,kBAAmB,CACnB,WAAY,CACf,GAKL,cAAe,CACf,eAAgB,CACnB,GAGG,oBAAqB,CACrB,cAAe,CAClB,EAGG,cAAe,CACf,gBAAiB,CACpB,EAGG,wBAAyB,CACzB,4BAAqB,CAArB,oBAAqB,CACrB,oBAAqB,CAHzB,QAMQ,oBAAqB,CACrB,4BAAY,CAAZ,oBAAqB,CAP7B,QAWQ,YAAa,CAChB,aAKG,YAAa,CAChB,iBAKG,uBAAY,CAAZ,eAAgB,CACnB,OAID,YAAa,CAChB,cAGG,qBD5EW,CC2Ef,oBAIQ,uBAAgB,CAAhB,eAAgB,CAChB,+BAAgC,CACnC,GAID,mEAAoE,CACpE,eAAgB,CAChB,eAAgB,CAChB,yBAA0B,CAC1B,QAAS,CACZ,GAGG,mEAAoE,CACpE,eAAgB,CAChB,aAAc,CACd,yBAA0B,CAC1B,QAAS,CACZ,GAGG,mEAAoE,CACpE,eAAgB,CAChB,eAAgB,CAChB,QAAS,CACZ,GAGG,mEAAoE,CACpE,eAAgB,CAChB,QAAS,CACT,eAAgB,CACnB,GAGG,mEAAoE,CACpE,eAAgB,CAChB,QAAS,CACT,eAAgB,CACnB,GAGG,mEAAoE,CACpE,eAAgB,CAChB,QAAS,CACT,eAAgB,CACnB,KAGG,oBAAqB,CACxB,aAGG,mCAAoC,CACvC,gBAGG,wCAAyC,CAC5C,gCAIO,8BD5Ia,CC6IhB,yBAKD,GACI,+BAAW,CAAX,uBAAwB,CAG5B,KACI,mCAAW,CAAX,2BAA4B,CAAA,CAV/B,iBAKD,GACI,+BAAW,CAAX,uBAAwB,CAG5B,KACI,mCAAW,CAAX,2BAA4B,CAAA,CAIpC,4BACI,GACI,uBAAwB,CAG5B,KACI,4BAA6B,CAAA,CANrC,oBACI,GACI,uBAAwB,CAG5B,KACI,4BAA6B,CAAA,CAKrC,yBAEI,qEAAsE,CACzE,iBAGG,wEAAyE,CAC5E,mBAGG,sEAAuE,CAC1E,oBAGG,oEAAqE,CACxE,GAGG,cAAe,CACf,eAAgB,CACnB,GAGG,oBAAqB,CACrB,cAAe,CAClB,EAGG,cAAe,CACf,gBAAiB,CACpB,EAGG,wBAAyB,CACzB,4BAAqB,CAArB,oBAAqB,CACrB,oBAAqB,CAHzB,QAMQ,oBAAqB,CACrB,4BAAY,CAAZ,oBAAqB,CAP7B,QAWQ,YAAa,CAChB,KCvND,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CD8N5D,UDxNiB,CCyNjB,yHAA0H,CAC1H,eAAgB,CAChB,sBAAuB,CACvB,gCAAyB,CAAzB,wBAAyB,CACzB,iBAAkB,CAClB,WAAY,CACZ,mEAAoE,CACpE,SAAU,CACV,kBAAmB,CAXvB,WAcQ,uBAAY,CAAZ,eAAgB,CAdxB,iBAmBY,iCAAW,CAAX,yBAA0B,CAC7B,OAID,yHAA0H,CAC1H,eAAgB,CAChB,mEAAoE,CACvE,QCtPD,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CD4P5D,UDtPiB,CCuPjB,yHAA0H,CAC1H,eAAgB,CAChB,sBAAuB,CACvB,gCAAyB,CAAzB,wBAAyB,CACzB,iBAAkB,CAClB,WAAY,CACZ,cAAe,CACf,SAAU,CACV,kBAAmB,CACnB,oBAAiB,CAAjB,gBAAiB,CACjB,0DAA2D,CAb/D,cAgBQ,uBAAY,CAAZ,eAAgB,CAhBxB,cAoBQ,mEAAoE,CACpE,4BAAY,CAAZ,oBAAqB,CAMxB,0BAHO,iBAAkB,CAClB,oEAAqE,CACxE,kBCnRL,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CDyRxD,eAAgB,CAChB,UDtRO,CCuRP,iBAAkB,CAClB,eAAgB,CAChB,sBAAuB,CACvB,gCAAyB,CAAzB,wBAAyB,CACzB,iBAAkB,CAClB,kBAAmB,CACnB,oFAA4D,CAA5D,2DAA4D,CAC5D,WAAY,CACZ,SAAU,CAZb,oBAeO,4BAAY,CAAZ,oBAAqB,CAf5B,yBCpQD,UADmC,CAEnC,iBAFqB,CAMrB,UDmRsC,CClRtC,QDkRkD,CAC1C,QAAS,CACT,MAAO,CACP,UAAW,CACX,iBAAkB,CAClB,kBAAmB,CACnB,oFAA4D,CAA5D,2DAA4D,CAC5D,gCAAY,CAAZ,wBAAyB,CA3BhC,wBA+BO,UDlTG,CCmRV,+BAkCW,KAAM,CACN,WAAY,CACf,0BASD,UAAW,CACX,eAAgB,CAChB,aAAc,CACjB,0BAOD,cAAe,CAmBlB,0BArBL,0BAKY,aAAc,CAgBrB,CArBL,2CAUgB,aAAc,CASjB,uDANO,aAAc,CACjB,0BAdjB,2CAiBoB,YAAa,CAEpB,CAnBb,2BAwBQ,eAAgB,CACnB,kFC7UD,UDqVsC,CCpVtC,WDoVqD,CAC7C,WAAY,CACZ,SAAU,CACV,uBAAgB,CAAhB,eAAgB,CAChB,SAAU,CATtB,kGCtTI,iCFpB+B,CEqB/B,eAFoB,CDoUR,UAAW,CACX,UDhXK,CCiXL,SAAU,CAf1B,yCAoBY,QAAS,CApBrB,iDAuBgB,eAAgB,CAvBhC,yCA4BY,SAAU,CA5BtB,iDA+BgB,eAAgB,CA/BhC,6BCvWI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CD8YxD,iBAAkB,CAClB,QAAS,CACT,oEAAqE,CAxC7E,gCChVI,UD2XsC,CC1XtC,WD0XqD,CA3CzD,uCChVI,6DD8X6F,CC7X7F,8DD8XgF,CACpE,4BAA6B,CAC7B,kBAAmB,CACnB,SAAU,CACV,QAAS,CACT,mCAAoC,CACpC,gCAAY,CAAZ,wBAAyB,CArDzC,+CAwDoB,YAAa,CAxDjC,oDChVI,6DD8YiG,CC7YjG,8DD8YoF,CACpE,QAAS,CACT,qBDraL,CCsaK,mCAAoC,CACpC,gCAAyB,CAAzB,wBAAyB,CACzB,kBAAmB,CApEvC,wBA2EQ,iBAAkB,CAClB,QAAS,CACT,MAAO,CACP,OAAQ,CACR,iBAAkB,CACrB,OAKD,uEAAwE,CACxE,iBAAkB,CAFtB,wBAKQ,kEAAmE,CAL3E,UASQ,mEAAoE,CACpE,eAAgB,CAChB,qEAAsE,CACtE,eAAgB,CAZxB,UAgBQ,eAAgB,CAChB,iBAAkB,CAjB1B,SAqBQ,SAAU,CACV,cAAe,CACf,gBAAiB,CACjB,gBAAiB,CACjB,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,mEAAoE,CA5B5E,sBA+BY,SAAU,CAKb,0BApCT,sBAkCgB,UAAW,CAElB,CE7cD,2BFyaR,SAuCY,SAAU,CAMjB,CEtdG,0BFyaR,SA2CY,UAAW,CAElB,CAIL,OACI,iBAAkB,CAClB,QAAS,CACT,MAAO,CACP,UAAW,CACX,SAAU,CALd,cAQQ,eAAgB,CAChB,KAAM,CACN,MAAO,CACP,UAAW,CACX,qBDrfO,CCsfP,iDDpfM,CCofN,yCDpfa,CCuerB,oCAkBY,QAAS,CACT,SAAU,CACV,WAAY,CApBxB,wCCrdI,kED4ekG,CC3elG,WD2eiH,CAvBrH,sCA4BY,sEAAuE,CACvE,WAAY,CACZ,SAAU,CA9BtB,2DCrdI,UDsf0C,CCrf1C,WDqfyD,CAjC7D,4CAqCgB,uBAAY,CAAZ,eAAgB,CArChC,mDCneI,mBD8gByC,CC9gBzC,mBD8gByC,CC9gBzC,YD8gByC,CC7gBzC,oBD8gB6B,CC9gB7B,gBD8gB6B,CC7gB7B,+DD8gBiF,CAsBxE,2BAnEb,mDAgDoB,2HAA4H,CAC5H,kBAAmB,CACnB,iBAAkB,CAClB,cAAe,CACf,0DAA2D,CAelE,CAnEb,uEAyDwB,SAAU,CACV,UDjiBH,CCkiBG,mEAAoE,CA3D5F,8EA8D4B,wBAAyB,CACzB,eAAgB,CACnB,cAUrB,iBAAkB,CAClB,oDAAqD,CACrD,2BAA4B,CAC5B,0BAA2B,CAC3B,qBAAsB,CACtB,SAAU,CANd,6BAUY,iBAAkB,CAClB,6CAAsC,CAAtC,qCAAsC,CACtC,UAAW,CA+Dd,2BA3ET,6BAegB,YAAa,CA4DpB,CA3ET,qCAmBgB,SAAU,CACV,OAAQ,CACR,0BAAiB,CAAjB,kBAAmB,CArBnC,qCAyBgB,KAAM,CACN,MAAO,CACP,0BAAiB,CAAjB,kBAAmB,CAKtB,2BAhCb,qCA8BoB,YAAa,CAEpB,CAhCb,qCAmCgB,YAAa,CACb,UAAW,CACX,0BAAiB,CAAjB,kBAAmB,CArCnC,qCAyCgB,SAAU,CACV,WAAY,CACZ,0BAAiB,CAAjB,kBAAmB,CA3CnC,qCA+CgB,SAAU,CACV,WAAY,CACZ,0BAAiB,CAAjB,kBAAmB,CAjDnC,qCAqDgB,SAAU,CACV,WAAY,CACZ,0BAAiB,CAAjB,kBAAmB,CAvDnC,qCA2DgB,SAAU,CACV,WAAY,CACZ,0BAAiB,CAAjB,kBAAmB,CA7DnC,qCAiEgB,SAAU,CACV,UAAW,CACX,0BAAiB,CAAjB,kBAAmB,CAnEnC,qCAuEgB,SAAU,CACV,UAAW,CACX,0BAAiB,CAAjB,kBAAmB,CAzEnC,0BA+EQ,uEAAwE,CA/EhF,4BCnhBQ,QAAS,CACT,kCAA2B,CAA3B,0BAA2B,CDsmB3B,iBAAkB,CAClB,8DAA+D,CAC/D,iBAAkB,CAuErB,0BA7JL,4BAyFY,SAAU,CAoEjB,CEhsBG,0BFmiBR,4BA6FY,UAAW,CAgElB,CA7JL,2CCrjBI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CDwpBpD,kBAAe,CAAf,cAAe,CACf,2DAA4D,CAnGxE,8CC5iBI,mBDkpByC,CClpBzC,mBDkpByC,CClpBzC,YDkpByC,CCjpBzC,oBDipBwD,CCjpBxD,gBDipBwD,CChpBxD,QDgpBoE,CACxD,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CAvGnC,kDA0GoB,eAAgB,CA1GpC,iDA8GoB,mEAAoE,CA9GxF,+BAoHY,oEAAqE,CACrE,eAAgB,CAChB,eAAgB,CAGZ,mEAA+D,CAC/D,eAAW,CA1H3B,oCA8HgB,iBAAkB,CAClB,cAAe,CACf,SAAU,CAhI1B,wCCxhBQ,OAAQ,CACR,kCAA2B,CAA3B,0BAA2B,CAP/B,UADwB,CAExB,WAFuC,CDoqBvB,UAAW,CACX,iBAAkB,CAClB,MAAO,CAvI3B,+CA6IY,oEAAqE,CA7IjF,kDC5iBI,mBD4rByC,CC5rBzC,mBD4rByC,CC5rBzC,YD4rByC,CC3rBzC,kBD4rB2B,CC5rB3B,cD4rB2B,CC3rB3B,2DD4rB6E,CACjE,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CAnJvC,kEAuJwB,mEAAoE,CACpE,aAAc,CACjB,iCAUb,SAAU,CACV,qBDttBO,CCutBP,iBAAkB,CAClB,gCAAY,CAAZ,wBAAyB,CAqG5B,0BA1GL,iCAQY,eAAgB,CAkGvB,CA1GL,uCAYY,kCAA2B,CAA3B,0BAA2B,CAC3B,gCAAY,CAAZ,wBAAyB,CAbrC,oEAkBwB,0BAAW,CAAX,kBAAmB,CAlB3C,oDAwBgB,wBAAyB,CAxBzC,2DA2BoB,SAAU,CA3B9B,+CAiCY,iBAAkB,CAjC9B,iDAoCgB,aAAc,CACd,WAAY,CACZ,qBAAmB,CAAnB,kBAAmB,CACnB,iBAAkB,CAvClC,8DCttBI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CAwB5D,UADwB,CAExB,WAFuC,CD4uBvB,iBAAkB,CAClB,KAAM,CACN,MAAO,CAEP,iBAAkB,CAClB,uCAAgC,CAAhC,+BAAgC,CAChC,+BAAwB,CAAxB,uBAAwB,CACxB,0BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CApD9B,qEAuDwB,UAAW,CACX,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,UAAW,CACX,WAAY,CACZ,mCAAoC,CACpC,WAAY,CACZ,UAAW,CA/DnC,qDAoEoB,YAAa,CACb,SAAU,CACV,qBDzxBL,CC0xBK,mBAAiB,CAAjB,gBAAiB,CACjB,2BAAyB,CAAzB,wBAAyB,CACzB,kDD1xBN,CC0xBM,0CD1xBC,CC4xBD,2BA3EpB,qDA4EwB,YAAa,CAcpB,CAXG,0BA/EpB,qDAgFwB,YAAa,CAUpB,CAPG,0BAnFpB,qDAoFwB,YAAa,CAMpB,CAHG,0BAvFpB,qDAwFwB,YAAa,CAEpB,CA1FjB,8CA+FY,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,oEAAqE,CACrE,UAAW,CAnGvB,iDAsGgB,mEAAoE,CACpE,eAAgB,CAvGhC,kCA6GQ,QAAS,CACT,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CACnB,kBAAmB,CAhH3B,2DAqHoB,UDx0BL,CCy0BK,mCAAoC,CAtHxD,sDA2HgB,QAAS,CACT,wBAAyB,CACzB,WAAY,CACZ,iBAAkB,CAClB,mEAAoE,CACpE,aDh1BO,CCi1BP,iBAAkB,CAjIlC,6DAoIoB,UDv1BL,CCw1BK,mCAAoC,CACvC,iBAQb,4DAA6D,CAC7D,uBAAwB,CACxB,2BAA4B,CAC5B,qBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAwJnB,0BA9JD,+BAUY,YAAa,CAyBpB,CAnCL,mCAcY,iBAAkB,CAClB,6CAAW,CAAX,qCAAsC,CAflD,yCAkBgB,WAAY,CACZ,UAAW,CACX,0BAAiB,CAAjB,kBAAmB,CApBnC,yCAwBgB,SAAU,CACV,SAAU,CACV,2BAAiB,CAAjB,mBAAoB,CA1BpC,yCA8BgB,YAAa,CACb,SAAU,CACV,2BAAiB,CAAjB,mBAAoB,CAhCpC,+CAwCgB,iBAAkB,CAKrB,2BA7Cb,+CA2CoB,aAAc,CAErB,CA7Cb,8CAgDgB,iBAAkB,CAKrB,2BArDb,8CAmDoB,aAAc,CAErB,CArDb,6BA0DQ,iBAAkB,CAClB,SAAU,CA3DlB,yCA8DY,eAAgB,CA9D5B,qCCj1BI,UADmC,CAEnC,iBAFqB,CDq5Bb,UAAW,CACX,QAAS,CCh5BjB,SDi5BqC,CCh5BrC,UDg5BmD,CAC3C,oCDp6BS,CCq6BT,UAAW,CAvEvB,uCC50BI,SDw5ByC,CCv5BzC,UDu5BuD,CC75BvD,UADmC,CAEnC,iBAFqB,CAiBjB,QAAS,CACT,kCAA2B,CAA3B,0BAA2B,CD+4BnB,UAAW,CACX,oCD96BK,CC+6BL,UAAW,CAjF3B,uCCj1BI,UADmC,CAEnC,iBAFqB,CD06BT,UAAW,CACX,SAAU,CACV,UAAW,CCt6BvB,SDu6ByC,CCt6BzC,UDs6BuD,CAC3C,oCD17BK,CC27BL,UAAW,CA7F3B,0CAkGY,2HAA4H,CAC5H,eDn8BG,CCo8BH,iBAAkB,CAClB,SAAU,CACV,sEAA2C,CAA3C,8DAAkE,CAtG9E,yDAyGgB,iBAAkB,CAzGlC,mEC50BI,UDw7B8C,CCv7B9C,mEDw7ByF,CACzE,qBAAY,CAAZ,kBAAmB,CACtB,0BA/GjB,wEAmHwB,YAAa,CAmBpB,CAtIjB,4EAuHwB,iBAAkB,CAClB,6CAAW,CAAX,qCAAsC,CAxH9D,uFA2H4B,QAAS,CACT,UAAW,CACX,0BAAiB,CAAjB,kBAAmB,CA7H/C,uFAiI4B,YAAa,CACb,WAAY,CACZ,0BAAiB,CAAjB,kBAAmB,CAnI/C,2DA0IgB,eAAgB,CA1IhC,8DA6IoB,eAAgB,CAChB,kBAAmB,CA9IvC,8DAkJoB,mEAAoE,CACpE,eAAgB,CAChB,iBAAkB,CApJtC,6DAwJoB,eAAgB,CAChB,QAAS,CACZ,sCAST,yDAA0D,CAC1D,8BAA+B,CAC/B,2BAA4B,CAC5B,qBAAsB,CACtB,yEAA0E,CANlF,qDASY,WAAY,CACZ,uEAAwE,CACxE,kBAAW,CAAX,cAAe,CAX3B,yEAeoB,mEAAoE,CACpE,eAAgB,CAChB,UDlhCL,CCmhCK,4BAA6B,CAC7B,WAAY,CACZ,kBAAmB,CACnB,mCAAoC,CACpC,sEAAuE,CAtB3F,gFAyBwB,4BD1hCT,CC2hCS,sBAAuB,CACvB,iBAAkB,CAClB,sBAAuB,CA5B/C,kCAoCQ,wEAAyE,CApCjF,gDAuCY,mBAAoB,CACpB,uCAAgC,CAAhC,+BAAgC,CAChC,gBAAiB,CAzC7B,sDA4CgB,kCAAW,CAAX,0BAA2B,CA5C3C,wEAgDwB,0BAAW,CAAX,kBAAmB,CAhD3C,oDAsDgB,iBAAkB,CAtDlC,sDAyDoB,aAAc,CAzDlC,0DA4DwB,qED3jCV,CC2jCU,6DD3jCH,CC+/BrB,kECpgCI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CAwB5D,UADwB,CAExB,WAFuC,CDijCvB,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,qCDrkCC,CCskCD,+BAAwB,CAAxB,uBAAwB,CACxB,0BAAmB,CAAnB,kBAAmB,CACnB,uCAAY,CAAZ,+BAAgC,CACnC,sCAST,6EAA8E,CAC9E,0BAA2B,CAC3B,2BAA4B,CAC5B,qBAAsB,CACtB,+CAAgD,CAChD,qCAAsC,CACtC,iEAAkE,CACrE,sBAMG,UAAW,CAFnB,oCClmCI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CDymCpD,UAAW,CACX,iBAAkB,CAClB,iBAAkB,CACrB,6GAYW,mEAAoE,CAPxF,uDAWoB,oEAAqE,CAXzF,uDAeoB,mEAAoE,CAfxF,qDAmBoB,mEAAoE,CAnBxF,mJCzlCI,kEDknC0G,CCjnC1G,mEDknC6F,CA1BjG,sJCzlCI,kED0nC0G,CCznC1G,mED0nC6F,CAlCjG,sJCzlCI,kEDkoC0G,CCjoC1G,mEDkoC6F,CA1CjG,0DAmDY,oEAAqE,CAnDjF,kEAwDgB,mDDnqCK,CCmqCL,2CDnqCK,CCoqCL,kCAAW,CAAX,0BAA2B,CAzD3C,4DChnCI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CDgrCpD,oDD1qCS,CC0qCT,4CD1qCS,CC2qCT,qBD7qCG,CC8qCH,kBAAmB,CACnB,aAAc,CACd,+BAAY,CAAZ,uBAAwB,CAnEpC,gECzlCI,+DD+pC+F,CC9pC/F,gED+pCkF,CACtE,+BAAwB,CAAxB,uBAAwB,CACxB,qBAAY,CAAZ,kBAAmB,CAzEnC,2DA8EY,oEAAqE,CACrE,iBAAkB,CA/E9B,8DAkFgB,yBAA0B,CAC1B,mEAAoE,CACvE,OAOT,wDAAyD,CACzD,0BAA2B,CAC3B,2BAA4B,CAC5B,qBAAsB,CACtB,UD5sCW,CC6sCX,iBAAkB,CAClB,SAAU,CAPd,oBAUQ,mEAAoE,CACpE,iBAAkB,CAX1B,uBAcY,mEAAoE,CACpE,uEAAwE,CAfpF,kCC1sCI,mBAD0B,CAC1B,mBAD0B,CAC1B,YAD0B,CAE1B,wBAF0C,CAE1C,qBAF0C,CAE1C,kBAF0C,CAG1C,uBAH4D,CAG5D,oBAH4D,CAG5D,sBAH4D,CD+tCpD,kBAAe,CAAf,cAAe,CACf,2DAA4D,CAC5D,uEAAwE,CAtBpF,qCAyBgB,mEAAoE,CAzBpF,sDCnrCI,+DDktCuG,CCjtCvG,gEDktC0F,CACtE,YAAa,CACb,cAAe,CAlCvC,mBA0CQ,mEAAoE,CACpE,0CDlvCO,CCmvCP,iBAAkB,CA5C1B,sBA+CY,mEAAoE,CACvE,6BAMD,YAAa,CAFrB,8DAKY,mCAAoC", "file": "landing-page.css", "sourcesContent": [":root {\n    --theme-color: #0da487;\n    --theme-color-rgb: 13, 164, 135;\n    --theme-color1: #0e947a;\n    --theme-color1-rgb: 14, 148, 122;\n    --theme-color2: linear-gradient(90.56deg, var(--theme-color1) 8.46%, var(--theme-color) 62.97%)\n}\n\n$white: #ffffff;\n$black: #000000;\n$title-color: #222222;\n$content-color: #4a5568;\n$light-gray: #f8f8f8;\n$danger-color: #ff4f4f;\n$rating-color: #ffb321;\n$border-color: #ececec;\n\n// font family\n$public-sans: 'Public Sans',\nsans-serif;\n$exo-sarif:'Exo 2',\nsans-serif;\n$russo-sarif: 'Russo One',\nsans-serif;\n$pacifico: 'Pacifico',\ncursive;\n$kaushan: '<PERSON><PERSON><PERSON>',\ncursive;\n$indie: 'Indie Flower',\ncursive;\n$great: 'Great Vibes',\ncursive;\n$qwitcher: 'Qwitcher Grypen',\ncursive;\n$fontawesome: 'Font Awesome 6 Free';\n\n// Breakepoints\n$min-breakpoints: (lg: 992px,\n    xl: 1200px,\n    2xl: 1366px,\n);\n\n$max-breakpoints: (2xs: 360px,\n    xs: 480px,\n    sm: 575px,\n    md: 767px,\n    lg: 991px,\n    xl: 1199px,\n    2xl: 1366px,\n    3xl: 1460px,\n    4xl: 1660px,\n);", "@import \"utils/variables\";\n@import \"utils/mixin/breakpoints\";\n@import \"utils/mixin/common\";\n\n//  typography scss\nbody {\n    font-family: $public-sans;\n    position: relative;\n    font-size: 14px;\n    color: $title-color;\n    margin: 0;\n    background-color: $white;\n    transition: all 0.3s ease-in-out;\n    padding-right: 0 !important;\n\n    ::selection {\n        color: $white;\n        background-color: var(--theme-color);\n    }\n\n    .bg-overlay {\n        @include pseudowh($width: 100vw, $height: 100vh);\n        background-color: $title-color;\n        position: fixed;\n        z-index: 2;\n        top: 0;\n        opacity: 0;\n        visibility: hidden;\n        transition: 0.5s;\n\n        &.show {\n            visibility: visible;\n            opacity: 0.5;\n        }\n    }\n}\n\nul {\n    padding-left: 0;\n    margin-bottom: 0;\n}\n\nli {\n    display: inline-block;\n    font-size: 14px;\n}\n\np {\n    font-size: 14px;\n    line-height: 18px;\n}\n\na {\n    color: var(--theme-color);\n    transition: 0.5s ease;\n    text-decoration: none;\n\n    &:hover {\n        text-decoration: none;\n        transition: 0.5s ease;\n    }\n\n    &:focus {\n        outline: none;\n    }\n}\n\nbutton {\n    &:focus {\n        outline: none;\n    }\n}\n\n.btn-close {\n    &:focus {\n        box-shadow: none;\n    }\n}\n\n:focus {\n    outline: none;\n}\n\n.form-control {\n    background-color: $white;\n\n    &:focus {\n        box-shadow: none;\n        border-color: var(--theme-color);\n    }\n}\n\nh1 {\n    font-size: calc(40px + (70 - 40) * ((100vw - 320px) / (1920 - 320)));\n    font-weight: 600;\n    line-height: 1.1;\n    text-transform: capitalize;\n    margin: 0;\n}\n\nh2 {\n    font-size: calc(22px + (28 - 22) * ((100vw - 320px) / (1920 - 320)));\n    font-weight: 600;\n    line-height: 1;\n    text-transform: capitalize;\n    margin: 0;\n}\n\nh3 {\n    font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n    font-weight: 500;\n    line-height: 1.2;\n    margin: 0;\n}\n\nh4 {\n    font-size: calc(17px + (18 - 17) * ((100vw - 320px) / (1920 - 320)));\n    line-height: 1.2;\n    margin: 0;\n    font-weight: 400;\n}\n\nh5 {\n    font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));\n    line-height: 1.2;\n    margin: 0;\n    font-weight: 400;\n}\n\nh6 {\n    font-size: calc(13px + (14 - 13) * ((100vw - 320px) / (1920 - 320)));\n    line-height: 1.2;\n    margin: 0;\n    font-weight: 400;\n}\n\nspan {\n    display: inline-block;\n}\n\n.theme-color {\n    color: var(--theme-color) !important;\n}\n\n.theme-bg-color {\n    background: var(--theme-color) !important;\n}\n\n.swiper-3d {\n    .swiper-slide-shadow {\n        background: rgba($title-color, 0.08);\n    }\n}\n\n// animation Scss\n@keyframes mover {\n    0% {\n        transform: translateY(0);\n    }\n\n    100% {\n        transform: translateY(-10px);\n    }\n}\n\n@keyframes floating {\n    0% {\n        background-position-x: 0;\n    }\n\n    100% {\n        background-position-x: 1920px;\n    }\n}\n\n// Section Space Scss\nsection,\n.section-t-space {\n    padding-top: calc(30px + (70 - 30) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.section-b-space {\n    padding-bottom: calc(30px + (70 - 30) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.section-big-space {\n    padding-top: calc(30px + (150 - 30) * ((100vw - 320px) / (1920 - 320)));\n}\n\n.container-fluid-lg {\n    padding: 0 calc(12px + (160 - 12) * ((100vw - 320px) / (1920 - 320)));\n}\n\nul {\n    padding-left: 0;\n    margin-bottom: 0;\n}\n\nli {\n    display: inline-block;\n    font-size: 14px;\n}\n\np {\n    font-size: 14px;\n    line-height: 18px;\n}\n\na {\n    color: var(--theme-color);\n    transition: 0.5s ease;\n    text-decoration: none;\n\n    &:hover {\n        text-decoration: none;\n        transition: 0.5s ease;\n    }\n\n    &:focus {\n        outline: none;\n    }\n}\n\n// Button Scss\n.btn {\n    @include flex_common;\n    color: $title-color;\n    padding: calc(7px + (14 - 7) * ((100vw - 320px) / (1920 - 320))) calc(14px + (32 - 14) * ((100vw - 320px) / (1920 - 320)));\n    font-weight: 500;\n    background: transparent;\n    transition: all 0.3s ease;\n    position: relative;\n    border: none;\n    font-size: calc(14px + (18 - 14) * ((100vw - 320px) / (1920 - 320)));\n    z-index: 0;\n    white-space: nowrap;\n\n    &:focus {\n        box-shadow: none;\n    }\n\n    &:hover {\n        .icon {\n            transform: translateX(3px);\n        }\n    }\n\n    &-2 {\n        padding: calc(7px + (15 - 7) * ((100vw - 320px) / (1920 - 320))) calc(14px + (34 - 14) * ((100vw - 320px) / (1920 - 320)));\n        font-weight: 700;\n        font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n    }\n}\n\n.btn-md {\n    @include flex_common;\n    color: $title-color;\n    padding: calc(9px + (12 - 9) * ((100vw - 320px) / (1920 - 320))) calc(11px + (19 - 11) * ((100vw - 320px) / (1920 - 320)));\n    font-weight: 500;\n    background: transparent;\n    transition: all 0.3s ease;\n    position: relative;\n    border: none;\n    font-size: 16px;\n    z-index: 0;\n    white-space: nowrap;\n    flex-wrap: nowrap;\n    gap: calc(3px + (8 - 3) * ((100vw - 320px) / (1920 - 320)));\n\n    &:focus {\n        box-shadow: none;\n    }\n\n    .icon {\n        margin-left: calc(6px + (12 - 6) * ((100vw - 320px) / (1920 - 320)));\n        transition: 0.3s ease;\n\n        [dir=\"rtl\"] & {\n            margin-left: unset;\n            margin-right: calc(6px + (12 - 6) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n\n    &-animation {\n        @include flex_common;\n        overflow: hidden;\n        color: $white;\n        border-radius: 5px;\n        font-weight: 600;\n        background: transparent;\n        transition: all 0.3s ease;\n        position: relative;\n        background: #ff6b6b;\n        background: linear-gradient(90deg, #ff6b6b 0%, #ff4f4f 100%);\n        border: none;\n        z-index: 0;\n\n        i {\n            transition: 0.3s ease;\n        }\n\n        &::after {\n            @include pos;\n            @include pseudowh($width: 100%, $height: 0);\n            bottom: 0;\n            left: 0;\n            z-index: -1;\n            border-radius: 5px;\n            background: #ff4f4f;\n            background: linear-gradient(90deg, #ff4f4f 0%, #ff6b6b 100%);\n            transition: all 0.3s ease;\n        }\n\n        &:hover {\n            color: $white;\n\n            &::after {\n                top: 0;\n                height: 100%;\n            }\n        }\n    }\n}\n\n// Ratio Scss\n.ratio_46 {\n    .bg-size {\n        &:before {\n            content: \"\";\n            padding-top: 46%;\n            display: block;\n        }\n    }\n}\n\n// Slick slide scss\n.slick-slider {\n    .slick-list {\n        margin: 0 -10px;\n\n        @include mq-max(sm) {\n            margin: 0 -6px;\n        }\n\n        .slick-slide {\n            >div {\n                margin: 0 10px;\n\n                [dir=\"rtl\"] & {\n                    direction: rtl;\n                }\n\n                @include mq-max(sm) {\n                    margin: 0 6px;\n                }\n            }\n        }\n    }\n\n    &.slick-dotted {\n        margin-bottom: 0;\n    }\n}\n\n.landing-wrapper {\n    .slick-arrow {\n\n        &.slick-prev,\n        &.slick-next {\n            @include pseudowh($width: auto, $height: auto);\n            bottom: -5px;\n            top: unset;\n            transform: unset;\n            z-index: 1;\n\n            &::before {\n                @include font;\n                content: \"\";\n                color: $title-color;\n                opacity: 1;\n            }\n        }\n\n        &.slick-prev {\n            left: 44%;\n\n            &::before {\n                content: \"\\f060\";\n            }\n        }\n\n        &.slick-next {\n            right: 44%;\n\n            &::before {\n                content: \"\\f061\";\n            }\n        }\n    }\n\n    .slick-dots {\n        @include flex_common;\n        position: relative;\n        bottom: 0;\n        margin-top: calc(18px + (35 - 18) * ((100vw - 320px) / (1920 - 320)));\n\n        li {\n            @include pseudowh($width: auto, $height: auto);\n\n            button {\n                @include pseudowh($width: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320))),\n                    $height: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320))));\n                border: 1px solid transparent;\n                border-radius: 100%;\n                padding: 0;\n                margin: 0;\n                background-color: var(--theme-color);\n                transition: all 0.5s ease;\n\n                &::before {\n                    display: none;\n                }\n            }\n\n            &.slick-active {\n                button {\n                    @include pseudowh($width: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320))),\n                        $height: calc(8px + (10 - 8) * ((100vw - 320px) / (1920 - 320))));\n                    margin: 0;\n                    background-color: $white;\n                    border: 1px solid var(--theme-color);\n                    transition: all 0.5s ease;\n                    border-radius: 100%;\n                }\n            }\n        }\n    }\n\n    .timer {\n        position: absolute;\n        top: 15px;\n        left: 0;\n        right: 0;\n        text-align: center;\n    }\n}\n\n// Title Start Scss\n.title {\n    margin-bottom: calc(25px + (40 - 25) * ((100vw - 320px) / (1920 - 320)));\n    text-align: center;\n\n    &.main-small-space {\n        margin: calc(16px + (28 - 16) * ((100vw - 320px) / (1920 - 320))) 0;\n    }\n\n    h2 {\n        font-size: calc(23px + (40 - 23) * ((100vw - 320px) / (1920 - 320)));\n        font-weight: 700;\n        margin-bottom: calc(8px + (15 - 8) * ((100vw - 320px) / (1920 - 320)));\n        line-height: 1.2;\n    }\n\n    h3 {\n        font-weight: 500;\n        margin-bottom: 3px;\n    }\n\n    p {\n        width: 60%;\n        font-size: 14px;\n        line-height: 25px;\n        margin-left: auto;\n        margin-right: auto;\n        margin-top: 10px;\n        line-height: 1.5;\n        font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n\n        &.heading-foot {\n            width: 80%;\n\n            @include mq-max(lg) {\n                width: 100%;\n            }\n        }\n\n        @include mq-max(3xl) {\n            width: 77%;\n        }\n\n        @include mq-max(lg) {\n            width: 100%;\n        }\n    }\n}\n\n// Header Scss\nheader {\n    position: absolute;\n    top: 10px;\n    left: 0;\n    width: 100%;\n    z-index: 2;\n\n    &.active {\n        position: sticky;\n        top: 0;\n        left: 0;\n        width: 100%;\n        background-color: $white;\n        box-shadow: 0 8px 10px rgba($title-color, 0.05);\n    }\n\n    .custom-navbar {\n        .navbar-brand {\n            margin: 0;\n            padding: 0;\n            font-size: 0;\n\n            img {\n                @include pseudowh($width: calc(127px + (204 - 127) * ((100vw - 320px) / (1920 - 320))), $height: auto);\n            }\n        }\n\n        .navbar-toggler {\n            margin-right: calc(10px + (26 - 10) * ((100vw - 320px) / (1920 - 320)));\n            border: none;\n            padding: 0;\n\n            .navbar-toggler-icon {\n                @include pseudowh($width: 23px, $height: 23px);\n            }\n\n            &:focus {\n                box-shadow: none;\n            }\n        }\n\n        .navbar-collapse {\n            .navbar-nav {\n                @include flex_wrap($dis: flex,\n                    $wrap: nowrap,\n                    $gap: calc(22px + (37 - 22) * ((100vw - 1200px) / (1920 - 1200))));\n\n                @include mq-max(xl) {\n                    padding: calc(11px + (20 - 11) * ((100vw - 320px) / (1199 - 320))) calc(19px + (26 - 19) * ((100vw - 320px) / (1199 - 320)));\n                    background: #f1f1f1;\n                    border-radius: 8px;\n                    margin-top: 9px;\n                    gap: calc(6px + (9 - 6) * ((100vw - 320px) / (1920 - 320)));\n                }\n\n                .nav-item {\n                    .nav-link {\n                        padding: 0;\n                        color: $title-color;\n                        font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n\n                        &.active {\n                            color: var(--theme-color);\n                            font-weight: 600;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n\n// Home Section Scss\n.home-section {\n    position: relative;\n    background-image: url(../images/landing-image/bg.jpg);\n    background-repeat: no-repeat;\n    background-position: center;\n    background-size: cover;\n    z-index: 0;\n\n    .home-icon {\n        img {\n            position: absolute;\n            animation: mover 2s infinite alternate;\n            z-index: -1;\n\n            @include mq-max(xl) {\n                display: none;\n            }\n\n            &.image-1 {\n                top: 213px;\n                right: 0;\n                animation-delay: 1s;\n            }\n\n            &.image-2 {\n                top: 0;\n                left: 0;\n                animation-delay: 2s;\n\n                @include mq-max(3xl) {\n                    display: none;\n                }\n            }\n\n            &.image-3 {\n                bottom: 165px;\n                right: 21px;\n                animation-delay: 3s;\n            }\n\n            &.image-4 {\n                top: 105px;\n                right: 215px;\n                animation-delay: 4s;\n            }\n\n            &.image-5 {\n                top: 263px;\n                right: 312px;\n                animation-delay: 5s;\n            }\n\n            &.image-6 {\n                top: 166px;\n                right: 575px;\n                animation-delay: 6s;\n            }\n\n            &.image-7 {\n                top: 275px;\n                right: 435px;\n                animation-delay: 7s;\n            }\n\n            &.image-8 {\n                top: 100px;\n                left: 360px;\n                animation-delay: 8s;\n            }\n\n            &.image-9 {\n                top: 320px;\n                left: 165px;\n                animation-delay: 9s;\n            }\n        }\n    }\n\n    .home-image {\n        margin-top: calc(220px + (420 - 220) * ((100vw - 320px) / (1920 - 320)));\n    }\n\n    .home-contain {\n        @include center(horizontal);\n        position: absolute;\n        top: calc(82px + (180 - 82) * ((100vw - 320px) / (1920 - 320)));\n        text-align: center;\n\n        @include mq-max(lg) {\n            width: 70%;\n        }\n\n        @include mq-max(xs) {\n            width: 100%;\n        }\n\n        .check-landing {\n            @include flex_common;\n            flex-wrap: wrap;\n            gap: calc(6px + (15 - 6) * ((100vw - 320px) / (1920 - 320)));\n\n            li {\n                @include flex_wrap($dis: flex, $wrap: nowrap, $gap: 10px);\n                align-items: center;\n\n                img {\n                    margin-top: -2px;\n                }\n\n                h4 {\n                    font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n                }\n            }\n        }\n\n        h1 {\n            margin-top: calc(11px + (36 - 11) * ((100vw - 320px) / (1920 - 320)));\n            line-height: 1.5;\n            margin-bottom: 0;\n\n            font: {\n                size: calc(18px + (50 - 18) * ((100vw - 320px) / (1920 - 320)));\n                weight: 700;\n            }\n\n            span {\n                position: relative;\n                padding: 0 18px;\n                z-index: 0;\n\n                img {\n                    @include center(vertical);\n                    @include pseudowh;\n                    z-index: -1;\n                    position: absolute;\n                    left: 0;\n                }\n            }\n        }\n\n        .page-button-group {\n            margin-top: calc(14px + (21 - 14) * ((100vw - 320px) / (1920 - 320)));\n\n            ul {\n                @include flex_wrap($dis: flex,\n                    $wrap: wrap,\n                    $gap: calc(9px + (15 - 9) * ((100vw - 320px) / (1920 - 320))));\n                justify-content: center;\n\n                li {\n                    .page-button {\n                        font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n                        line-height: 1;\n                    }\n                }\n            }\n        }\n    }\n}\n\n// Home Pages Section\n.layout-section {\n    .layout-page-box {\n        padding: 0;\n        background-color: $white;\n        position: relative;\n        transition: all 0.5s ease;\n\n        @include mq-max(sm) {\n            margin-bottom: 0;\n        }\n\n        &:hover {\n            transform: translateY(-8px);\n            transition: all 0.5s ease;\n\n            .layout-image {\n                a {\n                    .link-button {\n                        transform: scale(1);\n                    }\n                }\n            }\n\n            .layout-name {\n                color: var(--theme-color);\n\n                &:before {\n                    opacity: 1;\n                }\n            }\n        }\n\n        .layout-image {\n            position: relative;\n\n            a {\n                display: block;\n                height: auto;\n                object-fit: contain;\n                position: relative;\n\n                .link-button {\n                    @include flex_common;\n                    @include pseudowh;\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    // background-color: #0da487;\n                    position: relative;\n                    transition: all 0.3s ease-in-out;\n                    transform-origin: center;\n                    transform: scale(0);\n                    z-index: 0;\n\n                    &::after {\n                        content: \"\";\n                        position: absolute;\n                        top: 0;\n                        left: 0;\n                        width: 100%;\n                        height: 100%;\n                        background-color: var(--theme-color);\n                        opacity: 0.3;\n                        z-index: -1;\n                    }\n                }\n\n                img {\n                    height: 300px;\n                    padding: 0;\n                    background-color: $white;\n                    object-fit: cover;\n                    object-position: top left;\n                    box-shadow: 0 15px 70px rgba($title-color, 0.07);\n\n                    @media (max-width: 1367px) {\n                        height: 250px;\n                    }\n\n                    @media (max-width: 768px) {\n                        height: 220px;\n                    }\n\n                    @media (max-width: 576px) {\n                        height: 310px;\n                    }\n\n                    @media (max-width: 380px) {\n                        height: 190px;\n                    }\n                }\n            }\n        }\n\n        .layout-name {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin-top: calc(10px + (20 - 10) * ((100vw - 320px) / (1920 - 320)));\n            color: #222;\n\n            h4 {\n                font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n                font-weight: 600;\n            }\n        }\n    }\n\n    .custome-nav-tabs {\n        gap: 15px;\n        justify-content: center;\n        border-bottom: none;\n        margin-bottom: 27px;\n\n        .nav-item {\n            &.show {\n                .nav-link {\n                    color: $white;\n                    background-color: var(--theme-color);\n                }\n            }\n\n            .nav-link {\n                margin: 0;\n                background-color: #f8f8f8;\n                border: none;\n                border-radius: 4px;\n                font-size: calc(16px + (20 - 16) * ((100vw - 320px) / (1920 - 320)));\n                color: $content-color;\n                padding: 10px 20px;\n\n                &.active {\n                    color: $white;\n                    background-color: var(--theme-color);\n                }\n            }\n        }\n    }\n}\n\n// Feature Scss\n.feature-section {\n    background-image: url(../images/landing-image/feature-bg.jpg);\n    background-position: top;\n    background-repeat: no-repeat;\n    background-size: cover;\n    position: relative;\n    overflow: hidden;\n\n    .feature-icon {\n        @include mq-max(lg) {\n            display: none;\n        }\n\n        img {\n            position: absolute;\n            animation: mover 2s infinite alternate;\n\n            &.img-1 {\n                bottom: 73px;\n                right: 31px;\n                animation-delay: 8s;\n            }\n\n            &.img-2 {\n                top: 272px;\n                left: 73px;\n                animation-delay: 12s;\n            }\n\n            &.img-3 {\n                bottom: 516px;\n                left: 82px;\n                animation-delay: 16s;\n            }\n        }\n    }\n\n    .feature-grid {\n        >div {\n            &:first-child {\n                padding-top: 100px;\n\n                @include mq-max(xl) {\n                    padding-top: 0;\n                }\n            }\n\n            &:last-child {\n                padding-top: 150px;\n\n                @include mq-max(xl) {\n                    padding-top: 0;\n                }\n            }\n        }\n    }\n\n    .feature-bg {\n        position: relative;\n        z-index: 0;\n\n        +.feature-bg {\n            margin-top: 50px;\n        }\n\n        &::before {\n            @include pos;\n            bottom: -4%;\n            left: -4%;\n            @include pseudowh($width: 70%, $height: 70%);\n            border: 1px dashed rgba($title-color, 0.4);\n            z-index: -1;\n        }\n\n        &-2 {\n            &::before {\n                @include pseudowh($width: 70%, $height: 70%);\n                @include pos;\n                @include center(horizontal);\n                bottom: -4%;\n                border: 1px dashed rgba($title-color, 0.4);\n                z-index: -1;\n            }\n        }\n\n        &-3 {\n            &::before {\n                @include pos;\n                bottom: -4%;\n                right: -4%;\n                left: unset;\n                @include pseudowh($width: 70%, $height: 70%);\n                border: 1px dashed rgba($title-color, 0.4);\n                z-index: -1;\n            }\n        }\n\n        .feature-box {\n            padding: calc(17px + (28 - 17) * ((100vw - 320px) / (1920 - 320))) calc(17px + (25 - 17) * ((100vw - 320px) / (1920 - 320)));\n            background: $white;\n            position: relative;\n            z-index: 0;\n            box-shadow: -4.31566px 6.47349px 21.5783px rgba(14, 109, 91, 0.06);\n\n            .feature-image {\n                position: relative;\n\n                .main-img {\n                    @include pseudowh($width: 100%,\n                        $height: calc(180px + (263 - 180) * ((100vw - 320px) / (1920 - 320))));\n                    object-fit: contain;\n                }\n\n                .header-footer {\n                    @include mq-max(md) {\n                        display: none;\n                    }\n\n                    img {\n                        position: absolute;\n                        animation: mover 2s infinite alternate;\n\n                        &.img-header {\n                            top: 12px;\n                            left: -96px;\n                            animation-delay: 5s;\n                        }\n\n                        &.img-footer {\n                            bottom: -13px;\n                            right: -70px;\n                            animation-delay: 9s;\n                        }\n                    }\n                }\n            }\n\n            .feature-contain {\n                margin-top: 15px;\n\n                h6 {\n                    font-weight: 500;\n                    margin-bottom: 10px;\n                }\n\n                h3 {\n                    font-size: calc(19px + (22 - 19) * ((100vw - 320px) / (1920 - 320)));\n                    font-weight: 600;\n                    margin-bottom: 6px;\n                }\n\n                p {\n                    line-height: 1.4;\n                    margin: 0;\n                }\n            }\n        }\n    }\n}\n\n// Shop & Product Page\n.spacing-bottom {\n    .shop-product-section {\n        background-image: url(../images/landing-image/shop-bg.jpg);\n        background-position: top center;\n        background-repeat: no-repeat;\n        background-size: cover;\n        padding-bottom: calc(37px + (100 - 37) * ((100vw - 320px) / (1920 - 320)));\n\n        .shop-nav-tabs {\n            border: none;\n            margin-bottom: calc(17px + (40 - 17) * ((100vw - 320px) / (1920 - 320)));\n            flex-wrap: wrap;\n\n            .nav-item {\n                .nav-link {\n                    font-size: calc(17px + (22 - 17) * ((100vw - 320px) / (1920 - 320)));\n                    font-weight: 700;\n                    color: $white;\n                    background-color: transparent;\n                    border: none;\n                    white-space: nowrap;\n                    border-bottom: 4px solid transparent;\n                    padding-bottom: calc(8px + (13 - 8) * ((100vw - 320px) / (1920 - 320)));\n\n                    &.active {\n                        border-bottom: 4px solid $white;\n                        border-top: transparent;\n                        position: relative;\n                        background: transparent;\n                    }\n                }\n            }\n        }\n    }\n\n    .nav-tabs-section {\n        margin-top: calc(-55px + (-115 - -55) * ((100vw - 320px) / (1920 - 320)));\n\n        .slider-image {\n            padding-bottom: 16px;\n            transition: all 0.3s ease-in-out;\n            padding-top: 15px;\n\n            &:hover {\n                transform: translateY(-8px);\n\n                >div {\n                    .visit-button {\n                        transform: scale(1);\n                    }\n                }\n            }\n\n            >div {\n                position: relative;\n\n                a {\n                    display: block;\n\n                    img {\n                        box-shadow: -4.31566px 6.47349px 21.5783px rgba($title-color, 0.06);\n                    }\n                }\n\n                .visit-button {\n                    @include flex_common;\n                    @include pseudowh;\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    background-color: rgba($title-color, 0.161);\n                    transform-origin: center;\n                    transform: scale(0);\n                    transition: all 0.3s ease-in-out;\n                }\n            }\n        }\n    }\n}\n\n// inner Page scss\n.inner-page-section {\n    .inner-page-image {\n        background-image: url(../images/landing-image/pages/inner-page/inner-page.png);\n        background-position: center;\n        background-repeat: no-repeat;\n        background-size: cover;\n        animation: floating 110s linear infinite reverse;\n        background-repeat: repeat-x !important;\n        height: calc(57vh + (100 - 57) * ((100vw - 320px) / (1920 - 320)));\n    }\n}\n\n// Blog Scss\n.blog-section {\n    .swiper {\n        width: 100%;\n\n        .swiper-slide {\n            @include flex_common;\n            width: 100%;\n            border-radius: 8px;\n            cursor: col-resize;\n        }\n    }\n}\n\n// Core Feature Scss\n.core-feature-section {\n    .core-grid {\n        >.row {\n            >div {\n\n                &:first-child,\n                &:nth-child(3) {\n                    padding-top: calc(0px + (80 - 0) * ((100vw - 320px) / (1920 - 320)));\n                }\n\n                &:nth-child(4) {\n                    padding-top: calc(0px + (148 - 0) * ((100vw - 320px) / (1920 - 320)));\n                }\n\n                &:nth-child(5) {\n                    padding-top: calc(0px + (70 - 0) * ((100vw - 320px) / (1920 - 320)));\n                }\n\n                &:last-child {\n                    padding-top: calc(0px + (12 - 0) * ((100vw - 320px) / (1920 - 320)));\n                }\n\n                &:first-child,\n                &:last-child {\n                    .core-feature-image {\n                        @include pseudowh($width: calc(118px + (178 - 118) * ((100vw - 320px) / (1920 - 320))),\n                            $height: calc(118px + (178 - 118) * ((100vw - 320px) / (1920 - 320))));\n                    }\n                }\n\n                &:nth-child(2),\n                &:nth-child(4) {\n                    .core-feature-image {\n                        @include pseudowh($width: calc(118px + (142 - 118) * ((100vw - 320px) / (1920 - 320))),\n                            $height: calc(118px + (142 - 118) * ((100vw - 320px) / (1920 - 320))));\n                    }\n                }\n\n                &:nth-child(3),\n                &:nth-child(5) {\n                    .core-feature-image {\n                        @include pseudowh($width: calc(118px + (155 - 118) * ((100vw - 320px) / (1920 - 320))),\n                            $height: calc(118px + (155 - 118) * ((100vw - 320px) / (1920 - 320))));\n                    }\n                }\n            }\n        }\n    }\n\n    .core-feature-box {\n        +.core-feature-box {\n            margin-top: calc(16px + (65 - 16) * ((100vw - 320px) / (1920 - 320)));\n        }\n\n        &:hover {\n            .core-feature-image {\n                box-shadow: 0px 7px 30px rgba($title-color, 0.08);\n                transform: translateY(-5px);\n            }\n        }\n\n        .core-feature-image {\n            @include flex_common;\n            box-shadow: 0px 39px 30px rgba($title-color, 0.05);\n            background-color: $white;\n            border-radius: 100%;\n            margin: 0 auto;\n            transition: 0.4s ease-in;\n\n            img {\n                @include pseudowh($width: calc(50px + (85 - 50) * ((100vw - 320px) / (1920 - 320))),\n                    $height: calc(50px + (85 - 50) * ((100vw - 320px) / (1920 - 320))));\n                transition: 0.4s ease-in;\n                object-fit: contain;\n            }\n        }\n\n        .core-feature-name {\n            margin-top: calc(11px + (22 - 11) * ((100vw - 320px) / (1920 - 320)));\n            text-align: center;\n\n            h4 {\n                text-transform: capitalize;\n                font-size: calc(15px + (18 - 15) * ((100vw - 320px) / (1920 - 320)));\n            }\n        }\n    }\n}\n\n// Footer scss\nfooter {\n    background-image: url(../images/landing-image/footer.jpg);\n    background-position: center;\n    background-repeat: no-repeat;\n    background-size: cover;\n    color: $white;\n    position: relative;\n    z-index: 0;\n\n    .main-footer {\n        padding: calc(25px + (70 - 25) * ((100vw - 320px) / (1920 - 320))) 0;\n        text-align: center;\n\n        h2 {\n            font-size: calc(25px + (45 - 25) * ((100vw - 320px) / (1920 - 320)));\n            margin-bottom: calc(14px + (33 - 14) * ((100vw - 320px) / (1920 - 320)));\n        }\n\n        .theme-rating {\n            @include flex_common;\n            flex-wrap: wrap;\n            gap: calc(2px + (18 - 2) * ((100vw - 320px) / (1920 - 320)));\n            margin-bottom: calc(19px + (42 - 19) * ((100vw - 320px) / (1920 - 320)));\n\n            h3 {\n                font-size: calc(16px + (26 - 16) * ((100vw - 320px) / (1920 - 320)));\n            }\n\n            .rating {\n                li {\n                    .feather {\n                        @include pseudowh($width: calc(19px + (25 - 19) * ((100vw - 320px) / (1920 - 320))),\n                            $height: calc(19px + (25 - 19) * ((100vw - 320px) / (1920 - 320))));\n                        fill: #ffb016;\n                        stroke: #ffb016;\n                    }\n                }\n            }\n        }\n    }\n\n    .sub-footer {\n        padding: calc(16px + (24 - 16) * ((100vw - 320px) / (1920 - 320))) 0;\n        border-top: 1px solid rgba($white, 0.1);\n        text-align: center;\n\n        h4 {\n            font-size: calc(14px + (18 - 14) * ((100vw - 320px) / (1920 - 320)));\n        }\n    }\n}\n\n.mySwiper {\n    .swiper-pagination {\n        bottom: -29px;\n\n        .swiper-pagination-bullet-active {\n            background-color: var(--theme-color);\n        }\n    }\n}", "/**=====================\n     Common scss\n==========================**/\n/* ======= Display Flex Css Start ======= */\n@mixin flex_common ($dis: flex, $align: center, $justify: center) {\n    display: $dis;\n    align-items: $align;\n    justify-content: $justify;\n}\n\n/* ======= Display Flex Css End ======= */\n\n/* ======= Gap Flex Css Start ======= */\n@mixin flex_wrap ($dis: flex, $wrap: wrap, $gap: 15px) {\n    display: $dis;\n    flex-wrap: $wrap;\n    gap: $gap;\n}\n\n/* ======= Gap Flex Css End ======= */\n\n/*======= position css starts  ======= */\n@mixin pos($pos: absolute, $content: \"\") {\n    content: $content;\n    position: $pos;\n}\n\n@mixin pseudowh($width: 100%, $height: 100%) {\n    width: $width;\n    height: $height;\n}\n\n@mixin center($position) {\n    @if $position==\"vertical\" {\n        top: 50%;\n        transform: translateY(-50%);\n    }\n\n    @else if $position==\"horizontal\" {\n        left: 50%;\n        transform: translateX(-50%);\n    }\n\n    @else if $position==\"both\" {\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n    }\n}\n\n/*======= position css ends  ======= */\n\n/*======= font awesome css start  ======= */\n@mixin font($weight: 900, $family: $fontawesome) {\n    font-family: $family;\n    font-weight: $weight;\n}\n\n/*======= font awesome css ends  ======= */\n\n/*======= align css starts  ======= */\n@mixin rtl($property, $ltr-value, $rtl-value) {\n    #{$property}: $ltr-value;\n\n    [dir=\"rtl\"] & {\n        #{$property}: $rtl-value;\n    }\n}\n\n// @include rtl(float, left, right);\n/*======= align css ends  ======= */", "/**=====================\n    breakpoint mixins scss\n==========================**/\n// min width\n@mixin mq-min($breakpoint) {\n    @if map-has-key($min-breakpoints, $breakpoint) {\n        $breakpoint-value: map-get($min-breakpoints, $breakpoint);\n\n        @media (min-width: $breakpoint-value) {\n            @content;\n        }\n    }\n\n    @else {\n        @warn 'Invalid breakpoint: #{$breakpoint}.';\n    }\n}\n\n// max width\n@mixin mq-max($breakpoint) {\n    @if map-has-key($max-breakpoints, $breakpoint) {\n        $breakpoint-value: map-get($max-breakpoints, $breakpoint);\n\n        @media (max-width: ($breakpoint-value)) {\n            @content;\n        }\n    }\n\n    @else {\n        @warn 'Invalid breakpoint: #{$breakpoint}.';\n    }\n}\n\n// min and max\n@mixin mq-between($lower, $upper) {\n    @if map-has-key($max-breakpoints, $lower) and map-has-key($min-breakpoints, $upper) {\n        $lower-breakpoint: map-get($max-breakpoints, $lower);\n        $upper-breakpoint: map-get($min-breakpoints, $upper);\n\n        @media (min-width: $lower-breakpoint) and (max-width: ($upper-breakpoint - 1)) {\n            @content;\n        }\n    }\n\n    @else {\n        @if (map-has-key($max-breakpoints, $lower)==false) {\n            @warn 'Your lower breakpoint was invalid: #{$lower}.';\n        }\n\n        @if (map-has-key($min-breakpoints, $upper)==false) {\n            @warn 'Your upper breakpoint was invalid: #{$upper}.';\n        }\n    }\n}"]}