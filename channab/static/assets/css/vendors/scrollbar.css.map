{"version": 3, "sources": ["vendors/scrollbar.css", "vendors/scrollbar/_simplebar.scss"], "names": [], "mappings": "AAAA,iBCCE,iBAAkB,CAClB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,kBAAe,CAAf,cAAe,CACf,sBAA2B,CAA3B,mBAA2B,CAA3B,0BAA2B,CAC3B,wBAAyB,CAAzB,wBAAyB,CACzB,uBAAa,CAAb,oBAAa,CAAb,sBAAuB,CACxB,mBAGC,eAAgB,CAChB,aAAc,CACd,cAAe,CACf,iBAAkB,CAClB,kBAAmB,CACpB,gBAGC,iBAAkB,CAClB,iBAAkB,CAClB,eAAgB,CAChB,SAAU,CACV,QAAS,CACT,MAAO,CACP,KAAM,CACN,QAAS,CACT,OAAQ,CACR,qBAAsB,CACtB,sBAAuB,CACvB,SAAU,CACX,kBAGC,4BAA6B,CAC7B,qCAA8B,CAA9B,6BAA8B,CAC9B,sBAAuB,CACvB,iBAAkB,CAClB,QAAS,CACT,MAAO,CACP,QAAS,CACT,OAAQ,CACR,SAAU,CACV,QAAS,CACT,gCAAiC,CAClC,2BAGC,iBAAkB,CAClB,wCAAiC,CAAjC,gCAAiC,CACjC,iBAAkB,CAClB,aAAc,CACd,WAAY,CAEZ,UAAW,CACX,cAAe,CAEf,eAAgB,CAEhB,oBAAqB,CACrB,uBAAwB,CACxB,mBAAoB,CACrB,2FAIC,OAAQ,CACR,QAAS,CACV,mDAIC,WAAY,CACZ,aAAc,CACf,uBAGC,eAAgB,CAChB,cAAe,CACf,UAAW,CACX,mBAAoB,CACrB,wCAGC,qCAA8B,CAA9B,6BAA8B,CAC9B,WAAY,CACZ,UAAW,CACX,aAAc,CACd,iBAAkB,CAClB,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,UAAW,CACX,SAAU,CACV,QAAS,CACT,mBAAoB,CACpB,wBAAkB,CAAlB,yBAAkB,CAAlB,iBAAkB,CAClB,mBAAc,CAAd,aAAc,CACd,yBAAY,CAAZ,YAAa,CACd,gCAGC,0BAAmB,CAAnB,kBAAmB,CACnB,aAAc,CACd,SAAU,CACV,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,YAAa,CACb,WAAY,CACZ,cAAe,CACf,aAAc,CACd,eAAgB,CAChB,mBAAoB,CACpB,UAAW,CACZ,iBAGC,SAAU,CACV,iBAAkB,CAClB,OAAQ,CACR,QAAS,CACT,mBAAoB,CACpB,eAAgB,CACjB,uDAGC,mBAAoB,CACpB,qBAAiB,CAAjB,oBAAiB,CAAjB,gBAAiB,CACjB,wBAAyB,CAC1B,qDAGC,kBAAmB,CACpB,qBAGC,iBAAkB,CAClB,MAAO,CACP,OAAQ,CACR,eAAgB,CACjB,4BAGC,iBAAkB,CAClB,UAAW,CACX,gBAAiB,CACjB,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,SAAU,CACV,sCAAY,CAAZ,8BAA+B,CAChC,8CAIC,WAAY,CACZ,oCAAY,CAAZ,4BAA6B,CAC9B,oCAGC,KAAM,CACN,UAAW,CACZ,gEAGC,OAAQ,CACR,UAAW,CACZ,sCAGC,MAAO,CACP,WAAY,CACb,kEAGC,WAAY,CACZ,QAAS,CACT,SAAU,CACX,2DAGC,UAAW,CACX,MAAO,CACP,OAAQ,CACR,UAAW,CACX,YAAa,CACb,cAAe,CACf,UAAW,CACZ,qEAIC,UAAW,CACX,MAAO,CACR,yBAGC,aAAc,CACd,cAAe,CACf,SAAU,CACV,iBAAkB,CAClB,YAAa,CACb,WAAY,CACZ,iBAAkB,CAClB,iBAAkB,CACnB,0BAGC,cAAe,CACf,MAAO,CACP,iBAAkB,CAClB,iBAAkB,CAClB,oBAAqB,CACrB,uBAAwB", "file": "vendors/scrollbar.css", "sourcesContent": ["[data-simplebar]{position:relative;flex-direction:column;flex-wrap:wrap;justify-content:flex-start;align-content:flex-start;align-items:flex-start}.simplebar-wrapper{overflow:hidden;width:inherit;height:inherit;max-width:inherit;max-height:inherit}.simplebar-mask{direction:inherit;position:absolute;overflow:hidden;padding:0;margin:0;left:0;top:0;bottom:0;right:0;width:auto !important;height:auto !important;z-index:0}.simplebar-offset{direction:inherit !important;box-sizing:inherit !important;resize:none !important;position:absolute;top:18px;left:0;bottom:0;right:0;padding:0;margin:0;-webkit-overflow-scrolling:touch}.simplebar-content-wrapper{direction:inherit;box-sizing:border-box !important;position:relative;display:block;height:100%;width:auto;max-width:100%;max-height:100%;scrollbar-width:none;-ms-overflow-style:none;padding-bottom:30px}.simplebar-content-wrapper::-webkit-scrollbar,.simplebar-hide-scrollbar::-webkit-scrollbar{width:0;height:0}.simplebar-content:before,.simplebar-content:after{content:\" \";display:table}.simplebar-placeholder{max-height:100%;max-width:100%;width:100%;pointer-events:none}.simplebar-height-auto-observer-wrapper{box-sizing:inherit !important;height:100%;width:100%;max-width:1px;position:relative;float:left;max-height:1px;overflow:hidden;z-index:-1;padding:0;margin:0;pointer-events:none;flex-grow:inherit;flex-shrink:0;flex-basis:0}.simplebar-height-auto-observer{box-sizing:inherit;display:block;opacity:0;position:absolute;top:0;left:0;height:1000%;width:1000%;min-height:1px;min-width:1px;overflow:hidden;pointer-events:none;z-index:-1}.simplebar-track{z-index:1;position:absolute;right:0;bottom:0;pointer-events:none;overflow:hidden}[data-simplebar].simplebar-dragging .simplebar-content{pointer-events:none;user-select:none;-webkit-user-select:none}[data-simplebar].simplebar-dragging .simplebar-track{pointer-events:all}.simplebar-scrollbar{position:absolute;left:0;right:0;min-height:10px}.simplebar-scrollbar:before{position:absolute;content:\"\";background:black;border-radius:7px;left:2px;right:2px;opacity:0;transition:opacity 0.2s linear}.simplebar-scrollbar.simplebar-visible:before{opacity:0.5;transition:opacity 0s linear}.simplebar-track.simplebar-vertical{top:0;width:11px}.simplebar-track.simplebar-vertical .simplebar-scrollbar:before{top:2px;bottom:2px}.simplebar-track.simplebar-horizontal{left:0;height:11px}.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before{height:100%;left:2px;right:2px}.simplebar-track.simplebar-horizontal .simplebar-scrollbar{right:auto;left:0;top:2px;height:7px;min-height:0;min-width:10px;width:auto}[data-simplebar-direction=\"rtl\"] .simplebar-track.simplebar-vertical{right:auto;left:0}.hs-dummy-scrollbar-size{direction:rtl;position:fixed;opacity:0;visibility:hidden;height:500px;width:500px;overflow-y:hidden;overflow-x:scroll}.simplebar-hide-scrollbar{position:fixed;left:0;visibility:hidden;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none}\n", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 18px;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  padding-bottom: 30px;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  width: 0;\n  height: 0;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  left: 0;\n  right: 0;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: black;\n  border-radius: 7px;\n  left: 2px;\n  right: 2px;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=\"rtl\"] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}"]}