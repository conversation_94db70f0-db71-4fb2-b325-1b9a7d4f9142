{"version": 3, "sources": ["vendors/bootstrap/bootstrap-utilities.scss", "vendors/bootstrap/bootstrap-utilities.css", "vendors/bootstrap/mixins/_clearfix.scss", "vendors/bootstrap/helpers/_colored-links.scss", "vendors/bootstrap/_variables.scss", "vendors/bootstrap/_functions.scss", "vendors/bootstrap/helpers/_ratio.scss", "vendors/bootstrap/helpers/_position.scss", "vendors/bootstrap/mixins/_breakpoints.scss", "vendors/bootstrap/helpers/_stacks.scss", "vendors/bootstrap/mixins/_visually-hidden.scss", "vendors/bootstrap/helpers/_visually-hidden.scss", "vendors/bootstrap/helpers/_stretched-link.scss", "vendors/bootstrap/mixins/_text-truncate.scss", "vendors/bootstrap/helpers/_text-truncation.scss", "vendors/bootstrap/helpers/_vr.scss", "vendors/bootstrap/mixins/_utilities.scss", "vendors/bootstrap/utilities/_api.scss"], "names": [], "mappings": "AAAA;;;;;ECKE,CDAC,iBEFC,aAAc,CACd,UAAW,CACX,UAAW,CACZ,cCJC,aCkCU,CDnCZ,wCAMM,aE4M6B,CFlNnC,gBACE,aCYc,CDbhB,4CAMM,aE4M6B,CFlNnC,cACE,aCyCW,CD1Cb,wCAMM,aE4M6B,CFlNnC,WACE,aC2CU,CD5CZ,kCAMM,aEuM6B,CF7MnC,cACE,aCwCY,CDzCd,wCAMM,aEuM6B,CF7MnC,aACE,aCsCS,CDvCX,sCAMM,aE4M6B,CFlNnC,YACE,aCOc,CDRhB,oCAMM,aEuM6B,CF7MnC,WACE,aCec,CDhBhB,kCAMM,aE4M6B,CF3M9B,OGLL,iBAAkB,CAClB,UAAW,CAFb,eAKI,aAAc,CACd,kCAAiE,CACjE,UAAW,CAPf,SAWI,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,UAAW,CACX,WAAY,CACb,WAKC,uBAAgD,CADlD,WACE,qCAAgD,CADlD,YACE,sCAAgD,CADlD,YACE,sCAAgD,CACjD,WCrBD,cAAe,CACf,KAAM,CACN,OAAQ,CACR,MAAO,CACP,YHwgCiB,CGvgClB,cAGC,cAAe,CACf,OAAQ,CACR,QAAS,CACT,MAAO,CACP,YHggCiB,CG//BlB,YAQK,eAAgB,CAChB,KAAM,CACN,YHo/Bc,CI/8BhB,0BDxCA,eACE,eAAgB,CAChB,KAAM,CACN,YHo/Bc,CGn/Bf,CCoCD,0BDxCA,eACE,eAAgB,CAChB,KAAM,CACN,YHo/Bc,CGn/Bf,CCoCD,0BDxCA,eACE,eAAgB,CAChB,KAAM,CACN,YHo/Bc,CGn/Bf,CCoCD,2BDxCA,eACE,eAAgB,CAChB,KAAM,CACN,YHo/Bc,CGn/Bf,CCoCD,2BDxCA,gBACE,eAAgB,CAChB,KAAM,CACN,YHo/Bc,CGn/Bf,CE1BL,QACE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,2BAAY,CAAZ,kBAAmB,CACpB,QAGC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,2BAAY,CAAZ,kBAAmB,CACpB,2ECLC,4BAA6B,CAC7B,oBAAqB,CACrB,qBAAsB,CACtB,oBAAqB,CACrB,sBAAuB,CACvB,0BAA2B,CAC3B,gCAAiC,CACjC,6BAA8B,CAC9B,mBAAoB,CCTrB,uBCDG,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,QAAS,CACT,MAAO,CACP,SRkZsB,CQjZtB,UAAW,CACZ,eCTD,eAAgB,CAChB,sBAAuB,CACvB,kBAAmB,CCApB,ICLC,oBAAqB,CACrB,2BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CACV,cAAe,CACf,6BAA8B,CAC9B,WXinBc,CYxjBR,gBAOI,kCAA+D,CAPnE,WAOI,6BAA+D,CAPnE,cAOI,gCAA+D,CAPnE,cAOI,gCAA+D,CAPnE,mBAOI,qCAA+D,CAPnE,gBAOI,kCAA+D,CAPnE,aAOI,qBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,YAOI,qBAA+D,CAPnE,WAOI,oBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,YAOI,qBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,aAOI,oBAA+D,CAPnE,eAOI,wBAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,kBAOI,2BAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,aAOI,4BAA+D,CAPnE,cAOI,6BAA+D,CAPnE,QAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,eAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,4DAAqD,CAArD,oDAA+D,CAPnE,WAOI,kEAAqD,CAArD,0DAA+D,CAPnE,WAOI,2DAAqD,CAArD,mDAA+D,CAPnE,aAOI,kCAAqD,CAArD,0BAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,gBAOI,yBAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,OAOI,gBAA+D,CAPnE,QAOI,kBAA+D,CAPnE,SAOI,mBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,WAOI,qBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,SAOI,iBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,WAOI,oBAA+D,CAPnE,OAOI,kBAA+D,CAPnE,QAOI,oBAA+D,CAPnE,SAOI,qBAA+D,CAPnE,kBAOI,kDAAqD,CAArD,0CAA+D,CAPnE,oBAOI,6CAAqD,CAArD,qCAA+D,CAPnE,oBAOI,6CAAqD,CAArD,qCAA+D,CAPnE,QAOI,mCAA+D,CAPnE,UAOI,mBAA+D,CAPnE,YAOI,uCAA+D,CAPnE,cAOI,uBAA+D,CAPnE,YAOI,yCAA+D,CAPnE,cAOI,yBAA+D,CAPnE,eAOI,0CAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,cAOI,wCAA+D,CAPnE,gBAOI,wBAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,kBAOI,+BAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,aAOI,+BAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,eAOI,+BAA+D,CAPnE,cAOI,+BAA+D,CAPnE,aAOI,+BAA+D,CAPnE,cAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,MAOI,oBAA+D,CAPnE,MAOI,oBAA+D,CAPnE,MAOI,oBAA+D,CAPnE,OAOI,qBAA+D,CAPnE,QAOI,qBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,YAOI,0BAA+D,CAPnE,MAOI,qBAA+D,CAPnE,MAOI,qBAA+D,CAPnE,MAOI,qBAA+D,CAPnE,OAOI,sBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,0BAA+D,CAPnE,QAOI,uBAA+D,CAPnE,YAOI,2BAA+D,CAPnE,WAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,UAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,aAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,kBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,qBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,aAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,aAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,eAOI,8BAAqD,CAArD,wBAA+D,CAPnE,eAOI,8BAAqD,CAArD,wBAA+D,CAPnE,WAOI,6BAAqD,CAArD,yBAA+D,CAPnE,aAOI,+BAAqD,CAArD,2BAA+D,CAPnE,mBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,OAOI,gBAA+D,CAPnE,OAOI,qBAA+D,CAPnE,OAOI,oBAA+D,CAPnE,OAOI,mBAA+D,CAPnE,OAOI,qBAA+D,CAPnE,OAOI,mBAA+D,CAPnE,uBAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,qBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,wBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,yBAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,wBAOI,mCAAqD,CAArD,uCAA+D,CAPnE,wBAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,iBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,oBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,qBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,mBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,sBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,sCAA+D,CAPnE,sBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,uBAOI,qCAAqD,CAArD,gCAA+D,CAPnE,iBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,kBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,gBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,mBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,qBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,oBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,aAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,KAOI,mBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,KAOI,sBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,sBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,MAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,MAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,uBAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,KAOI,oBAA+D,CAPnE,KAOI,yBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,KAOI,yBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,MAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,MAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,gCAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,gCAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,gBAOI,+CAA+D,CAPnE,MAOI,2CAA+D,CAPnE,MAOI,0CAA+D,CAPnE,MAOI,wCAA+D,CAPnE,MAOI,0CAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,YAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,YAOI,8BAA+D,CAPnE,WAOI,0BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,WAOI,6BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,OAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,OAOI,wBAA+D,CAPnE,YAOI,0BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,aAOI,4BAA+D,CAPnE,sBAOI,+BAA+D,CAPnE,2BAOI,oCAA+D,CAPnE,8BAOI,uCAA+D,CAPnE,gBAOI,mCAA+D,CAPnE,gBAOI,mCAA+D,CAPnE,iBAOI,oCAA+D,CAPnE,WAOI,6BAA+D,CAPnE,aAOI,6BAA+D,CAPnE,YAOI,+BAA+D,CAA/D,gCAA+D,CAPnE,cAIQ,oBAAqC,CAGzC,oEAA+D,CAPnE,gBAIQ,oBAAqC,CAGzC,sEAA+D,CAPnE,cAIQ,oBAAqC,CAGzC,oEAA+D,CAPnE,WAIQ,oBAAqC,CAGzC,iEAA+D,CAPnE,cAIQ,oBAAqC,CAGzC,oEAA+D,CAPnE,aAIQ,oBAAqC,CAGzC,mEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,kEAA+D,CAPnE,WAIQ,oBAAqC,CAGzC,iEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,kEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,kEAA+D,CAPnE,WAIQ,oBAAqC,CAGzC,uEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,wBAA+D,CAPnE,eAIQ,oBAAqC,CAGzC,gCAA+D,CAPnE,eAIQ,oBAAqC,CAGzC,sCAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,wBAA+D,CAjBnE,iBACE,sBAA0C,CAD5C,iBACE,qBAA0C,CAD5C,iBACE,sBAA0C,CAD5C,kBACE,oBAA0C,CAC3C,YAYO,kBAAqC,CAGzC,6EAA+D,CAPnE,cAIQ,kBAAqC,CAGzC,+EAA+D,CAPnE,YAIQ,kBAAqC,CAGzC,6EAA+D,CAPnE,SAIQ,kBAAqC,CAGzC,0EAA+D,CAPnE,YAIQ,kBAAqC,CAGzC,6EAA+D,CAPnE,WAIQ,kBAAqC,CAGzC,4EAA+D,CAPnE,UAIQ,kBAAqC,CAGzC,2EAA+D,CAPnE,SAIQ,kBAAqC,CAGzC,0EAA+D,CAPnE,UAIQ,kBAAqC,CAGzC,2EAA+D,CAPnE,UAIQ,kBAAqC,CAGzC,2EAA+D,CAPnE,SAIQ,kBAAqC,CAGzC,6EAA+D,CAPnE,gBAIQ,kBAAqC,CAGzC,yCAA+D,CAjBnE,eACE,mBAA0C,CAD5C,eACE,oBAA0C,CAD5C,eACE,mBAA0C,CAD5C,eACE,oBAA0C,CAD5C,gBACE,kBAA0C,CAC3C,aAeG,8CAA+D,CAPnE,iBAOI,kCAAqD,CAArD,+BAAqD,CAArD,8BAAqD,CAArD,0BAA+D,CAPnE,kBAOI,mCAAqD,CAArD,gCAAqD,CAArD,+BAAqD,CAArD,2BAA+D,CAPnE,kBAOI,mCAAqD,CAArD,gCAAqD,CAArD,+BAAqD,CAArD,2BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,WAOI,0BAA+D,CAPnE,WAOI,8BAA+D,CAPnE,WAOI,+BAA+D,CAPnE,WAOI,8BAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,cAOI,8BAA+D,CAPnE,aAOI,wCAA+D,CAA/D,yCAA+D,CAPnE,aAOI,yCAA+D,CAA/D,4CAA+D,CAPnE,gBAOI,4CAA+D,CAA/D,2CAA+D,CAPnE,eAOI,2CAA+D,CAA/D,wCAA+D,CAPnE,SAOI,6BAA+D,CAPnE,WAOI,4BAA+D,CRPvE,0BQAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,CRTL,0BQAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,CRTL,0BQAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,CRTL,2BQAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,CRTL,2BQAI,iBAOI,qBAA+D,CAPnE,eAOI,sBAA+D,CAPnE,gBAOI,qBAA+D,CAPnE,cAOI,yBAA+D,CAPnE,oBAOI,+BAA+D,CAPnE,aAOI,wBAA+D,CAPnE,YAOI,uBAA+D,CAPnE,aAOI,wBAA+D,CAPnE,iBAOI,4BAA+D,CAPnE,kBAOI,6BAA+D,CAPnE,YAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,mBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,YAOI,uBAA+D,CAPnE,eAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,cAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,iBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,sBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,yBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,iBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,iBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,mBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,mBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,eAOI,6BAAqD,CAArD,yBAA+D,CAPnE,iBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,WAOI,gBAA+D,CAPnE,WAOI,qBAA+D,CAPnE,WAOI,oBAA+D,CAPnE,WAOI,mBAA+D,CAPnE,WAOI,qBAA+D,CAPnE,WAOI,mBAA+D,CAPnE,2BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,yBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,4BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,6BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,4BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,uBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,qBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,yBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,yBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,uBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,oCAAqD,CAArD,+BAA+D,CAPnE,2BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,0BAOI,wCAAqD,CAArD,qCAA+D,CAPnE,2BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,sBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,yBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,iBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,gBAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,mBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,sBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,sBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,UAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,aAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,UAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,aAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,uBAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,aAOI,0BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,aAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,aAOI,6BAA+D,CAPnE,UAOI,wBAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,SAOI,oBAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,UAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,UAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,wBAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,gCAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,gCAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,gBAOI,0BAA+D,CAPnE,cAOI,2BAA+D,CAPnE,iBAOI,4BAA+D,CAElE,CCrDT,2BD4CQ,MAOI,2BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAElE,CClCT,aDyBQ,gBAOI,yBAA+D,CAPnE,sBAOI,+BAA+D,CAPnE,eAOI,wBAA+D,CAPnE,cAOI,uBAA+D,CAPnE,eAOI,wBAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,oBAOI,6BAA+D,CAPnE,cAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,qBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,cAOI,uBAA+D,CAElE", "file": "vendors/bootstrap/bootstrap-utilities.css", "sourcesContent": ["/*!\n * Bootstrap Utilities v5.1.3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n// Configuration\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"utilities\";\n\n// Helpers\n@import \"helpers\";\n\n// Utilities\n@import \"utilities/api\";", "/*!\n * Bootstrap Utilities v5.1.3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */.clearfix::after{display:block;clear:both;content:\"\"}.link-primary{color:#0d6efd}.link-primary:hover,.link-primary:focus{color:#0a58ca}.link-secondary{color:#6c757d}.link-secondary:hover,.link-secondary:focus{color:#565e64}.link-success{color:#198754}.link-success:hover,.link-success:focus{color:#146c43}.link-info{color:#0dcaf0}.link-info:hover,.link-info:focus{color:#3dd5f3}.link-warning{color:#ffc107}.link-warning:hover,.link-warning:focus{color:#ffcd39}.link-danger{color:#dc3545}.link-danger:hover,.link-danger:focus{color:#b02a37}.link-light{color:#f8f9fa}.link-light:hover,.link-light:focus{color:#f9fafb}.link-dark{color:#212529}.link-dark:hover,.link-dark:focus{color:#1a1e21}.ratio{position:relative;width:100%}.ratio::before{display:block;padding-top:var(--bs-aspect-ratio);content:\"\"}.ratio>*{position:absolute;top:0;left:0;width:100%;height:100%}.ratio-1x1{--bs-aspect-ratio: 100%}.ratio-4x3{--bs-aspect-ratio: calc(3 / 4 * 100%)}.ratio-16x9{--bs-aspect-ratio: calc(9 / 16 * 100%)}.ratio-21x9{--bs-aspect-ratio: calc(9 / 21 * 100%)}.fixed-top{position:fixed;top:0;right:0;left:0;z-index:1030}.fixed-bottom{position:fixed;right:0;bottom:0;left:0;z-index:1030}.sticky-top{position:sticky;top:0;z-index:1020}@media (min-width: 576px){.sticky-sm-top{position:sticky;top:0;z-index:1020}}@media (min-width: 768px){.sticky-md-top{position:sticky;top:0;z-index:1020}}@media (min-width: 992px){.sticky-lg-top{position:sticky;top:0;z-index:1020}}@media (min-width: 1200px){.sticky-xl-top{position:sticky;top:0;z-index:1020}}@media (min-width: 1400px){.sticky-xxl-top{position:sticky;top:0;z-index:1020}}.hstack{display:flex;flex-direction:row;align-items:center;align-self:stretch}.vstack{display:flex;flex:1 1 auto;flex-direction:column;align-self:stretch}.visually-hidden,.visually-hidden-focusable:not(:focus):not(:focus-within){position:absolute !important;width:1px !important;height:1px !important;padding:0 !important;margin:-1px !important;overflow:hidden !important;clip:rect(0, 0, 0, 0) !important;white-space:nowrap !important;border:0 !important}.stretched-link::after{position:absolute;top:0;right:0;bottom:0;left:0;z-index:1;content:\"\"}.text-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.vr{display:inline-block;align-self:stretch;width:1px;min-height:1em;background-color:currentColor;opacity:.25}.align-baseline{vertical-align:baseline !important}.align-top{vertical-align:top !important}.align-middle{vertical-align:middle !important}.align-bottom{vertical-align:bottom !important}.align-text-bottom{vertical-align:text-bottom !important}.align-text-top{vertical-align:text-top !important}.float-start{float:left !important}.float-end{float:right !important}.float-none{float:none !important}.opacity-0{opacity:0 !important}.opacity-25{opacity:.25 !important}.opacity-50{opacity:.5 !important}.opacity-75{opacity:.75 !important}.opacity-100{opacity:1 !important}.overflow-auto{overflow:auto !important}.overflow-hidden{overflow:hidden !important}.overflow-visible{overflow:visible !important}.overflow-scroll{overflow:scroll !important}.d-inline{display:inline !important}.d-inline-block{display:inline-block !important}.d-block{display:block !important}.d-grid{display:grid !important}.d-table{display:table !important}.d-table-row{display:table-row !important}.d-table-cell{display:table-cell !important}.d-flex{display:flex !important}.d-inline-flex{display:inline-flex !important}.d-none{display:none !important}.shadow{box-shadow:0 0.5rem 1rem rgba(0,0,0,0.15) !important}.shadow-sm{box-shadow:0 0.125rem 0.25rem rgba(0,0,0,0.075) !important}.shadow-lg{box-shadow:0 1rem 3rem rgba(0,0,0,0.175) !important}.shadow-none{box-shadow:none !important}.position-static{position:static !important}.position-relative{position:relative !important}.position-absolute{position:absolute !important}.position-fixed{position:fixed !important}.position-sticky{position:sticky !important}.top-0{top:0 !important}.top-50{top:50% !important}.top-100{top:100% !important}.bottom-0{bottom:0 !important}.bottom-50{bottom:50% !important}.bottom-100{bottom:100% !important}.start-0{left:0 !important}.start-50{left:50% !important}.start-100{left:100% !important}.end-0{right:0 !important}.end-50{right:50% !important}.end-100{right:100% !important}.translate-middle{transform:translate(-50%, -50%) !important}.translate-middle-x{transform:translateX(-50%) !important}.translate-middle-y{transform:translateY(-50%) !important}.border{border:1px solid #dee2e6 !important}.border-0{border:0 !important}.border-top{border-top:1px solid #dee2e6 !important}.border-top-0{border-top:0 !important}.border-end{border-right:1px solid #dee2e6 !important}.border-end-0{border-right:0 !important}.border-bottom{border-bottom:1px solid #dee2e6 !important}.border-bottom-0{border-bottom:0 !important}.border-start{border-left:1px solid #dee2e6 !important}.border-start-0{border-left:0 !important}.border-primary{border-color:#0d6efd !important}.border-secondary{border-color:#6c757d !important}.border-success{border-color:#198754 !important}.border-info{border-color:#0dcaf0 !important}.border-warning{border-color:#ffc107 !important}.border-danger{border-color:#dc3545 !important}.border-light{border-color:#f8f9fa !important}.border-dark{border-color:#212529 !important}.border-white{border-color:#fff !important}.border-1{border-width:1px !important}.border-2{border-width:2px !important}.border-3{border-width:3px !important}.border-4{border-width:4px !important}.border-5{border-width:5px !important}.w-25{width:25% !important}.w-50{width:50% !important}.w-75{width:75% !important}.w-100{width:100% !important}.w-auto{width:auto !important}.mw-100{max-width:100% !important}.vw-100{width:100vw !important}.min-vw-100{min-width:100vw !important}.h-25{height:25% !important}.h-50{height:50% !important}.h-75{height:75% !important}.h-100{height:100% !important}.h-auto{height:auto !important}.mh-100{max-height:100% !important}.vh-100{height:100vh !important}.min-vh-100{min-height:100vh !important}.flex-fill{flex:1 1 auto !important}.flex-row{flex-direction:row !important}.flex-column{flex-direction:column !important}.flex-row-reverse{flex-direction:row-reverse !important}.flex-column-reverse{flex-direction:column-reverse !important}.flex-grow-0{flex-grow:0 !important}.flex-grow-1{flex-grow:1 !important}.flex-shrink-0{flex-shrink:0 !important}.flex-shrink-1{flex-shrink:1 !important}.flex-wrap{flex-wrap:wrap !important}.flex-nowrap{flex-wrap:nowrap !important}.flex-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-0{gap:0 !important}.gap-1{gap:.25rem !important}.gap-2{gap:.5rem !important}.gap-3{gap:1rem !important}.gap-4{gap:1.5rem !important}.gap-5{gap:3rem !important}.justify-content-start{justify-content:flex-start !important}.justify-content-end{justify-content:flex-end !important}.justify-content-center{justify-content:center !important}.justify-content-between{justify-content:space-between !important}.justify-content-around{justify-content:space-around !important}.justify-content-evenly{justify-content:space-evenly !important}.align-items-start{align-items:flex-start !important}.align-items-end{align-items:flex-end !important}.align-items-center{align-items:center !important}.align-items-baseline{align-items:baseline !important}.align-items-stretch{align-items:stretch !important}.align-content-start{align-content:flex-start !important}.align-content-end{align-content:flex-end !important}.align-content-center{align-content:center !important}.align-content-between{align-content:space-between !important}.align-content-around{align-content:space-around !important}.align-content-stretch{align-content:stretch !important}.align-self-auto{align-self:auto !important}.align-self-start{align-self:flex-start !important}.align-self-end{align-self:flex-end !important}.align-self-center{align-self:center !important}.align-self-baseline{align-self:baseline !important}.align-self-stretch{align-self:stretch !important}.order-first{order:-1 !important}.order-0{order:0 !important}.order-1{order:1 !important}.order-2{order:2 !important}.order-3{order:3 !important}.order-4{order:4 !important}.order-5{order:5 !important}.order-last{order:6 !important}.m-0{margin:0 !important}.m-1{margin:.25rem !important}.m-2{margin:.5rem !important}.m-3{margin:1rem !important}.m-4{margin:1.5rem !important}.m-5{margin:3rem !important}.m-auto{margin:auto !important}.mx-0{margin-right:0 !important;margin-left:0 !important}.mx-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-3{margin-right:1rem !important;margin-left:1rem !important}.mx-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-5{margin-right:3rem !important;margin-left:3rem !important}.mx-auto{margin-right:auto !important;margin-left:auto !important}.my-0{margin-top:0 !important;margin-bottom:0 !important}.my-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-0{margin-top:0 !important}.mt-1{margin-top:.25rem !important}.mt-2{margin-top:.5rem !important}.mt-3{margin-top:1rem !important}.mt-4{margin-top:1.5rem !important}.mt-5{margin-top:3rem !important}.mt-auto{margin-top:auto !important}.me-0{margin-right:0 !important}.me-1{margin-right:.25rem !important}.me-2{margin-right:.5rem !important}.me-3{margin-right:1rem !important}.me-4{margin-right:1.5rem !important}.me-5{margin-right:3rem !important}.me-auto{margin-right:auto !important}.mb-0{margin-bottom:0 !important}.mb-1{margin-bottom:.25rem !important}.mb-2{margin-bottom:.5rem !important}.mb-3{margin-bottom:1rem !important}.mb-4{margin-bottom:1.5rem !important}.mb-5{margin-bottom:3rem !important}.mb-auto{margin-bottom:auto !important}.ms-0{margin-left:0 !important}.ms-1{margin-left:.25rem !important}.ms-2{margin-left:.5rem !important}.ms-3{margin-left:1rem !important}.ms-4{margin-left:1.5rem !important}.ms-5{margin-left:3rem !important}.ms-auto{margin-left:auto !important}.p-0{padding:0 !important}.p-1{padding:.25rem !important}.p-2{padding:.5rem !important}.p-3{padding:1rem !important}.p-4{padding:1.5rem !important}.p-5{padding:3rem !important}.px-0{padding-right:0 !important;padding-left:0 !important}.px-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-3{padding-right:1rem !important;padding-left:1rem !important}.px-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-5{padding-right:3rem !important;padding-left:3rem !important}.py-0{padding-top:0 !important;padding-bottom:0 !important}.py-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-0{padding-top:0 !important}.pt-1{padding-top:.25rem !important}.pt-2{padding-top:.5rem !important}.pt-3{padding-top:1rem !important}.pt-4{padding-top:1.5rem !important}.pt-5{padding-top:3rem !important}.pe-0{padding-right:0 !important}.pe-1{padding-right:.25rem !important}.pe-2{padding-right:.5rem !important}.pe-3{padding-right:1rem !important}.pe-4{padding-right:1.5rem !important}.pe-5{padding-right:3rem !important}.pb-0{padding-bottom:0 !important}.pb-1{padding-bottom:.25rem !important}.pb-2{padding-bottom:.5rem !important}.pb-3{padding-bottom:1rem !important}.pb-4{padding-bottom:1.5rem !important}.pb-5{padding-bottom:3rem !important}.ps-0{padding-left:0 !important}.ps-1{padding-left:.25rem !important}.ps-2{padding-left:.5rem !important}.ps-3{padding-left:1rem !important}.ps-4{padding-left:1.5rem !important}.ps-5{padding-left:3rem !important}.font-monospace{font-family:var(--bs-font-monospace) !important}.fs-1{font-size:calc(1.375rem + 1.5vw) !important}.fs-2{font-size:calc(1.325rem + .9vw) !important}.fs-3{font-size:calc(1.3rem + .6vw) !important}.fs-4{font-size:calc(1.275rem + .3vw) !important}.fs-5{font-size:1.25rem !important}.fs-6{font-size:1rem !important}.fst-italic{font-style:italic !important}.fst-normal{font-style:normal !important}.fw-light{font-weight:300 !important}.fw-lighter{font-weight:lighter !important}.fw-normal{font-weight:400 !important}.fw-bold{font-weight:700 !important}.fw-bolder{font-weight:bolder !important}.lh-1{line-height:1 !important}.lh-sm{line-height:1.25 !important}.lh-base{line-height:1.5 !important}.lh-lg{line-height:2 !important}.text-start{text-align:left !important}.text-end{text-align:right !important}.text-center{text-align:center !important}.text-decoration-none{text-decoration:none !important}.text-decoration-underline{text-decoration:underline !important}.text-decoration-line-through{text-decoration:line-through !important}.text-lowercase{text-transform:lowercase !important}.text-uppercase{text-transform:uppercase !important}.text-capitalize{text-transform:capitalize !important}.text-wrap{white-space:normal !important}.text-nowrap{white-space:nowrap !important}.text-break{word-wrap:break-word !important;word-break:break-word !important}.text-primary{--bs-text-opacity: 1;color:rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important}.text-secondary{--bs-text-opacity: 1;color:rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important}.text-success{--bs-text-opacity: 1;color:rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important}.text-info{--bs-text-opacity: 1;color:rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important}.text-warning{--bs-text-opacity: 1;color:rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important}.text-danger{--bs-text-opacity: 1;color:rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important}.text-light{--bs-text-opacity: 1;color:rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important}.text-dark{--bs-text-opacity: 1;color:rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important}.text-black{--bs-text-opacity: 1;color:rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important}.text-white{--bs-text-opacity: 1;color:rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important}.text-body{--bs-text-opacity: 1;color:rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important}.text-muted{--bs-text-opacity: 1;color:#6c757d !important}.text-black-50{--bs-text-opacity: 1;color:rgba(0,0,0,0.5) !important}.text-white-50{--bs-text-opacity: 1;color:rgba(255,255,255,0.5) !important}.text-reset{--bs-text-opacity: 1;color:inherit !important}.text-opacity-25{--bs-text-opacity: .25}.text-opacity-50{--bs-text-opacity: .5}.text-opacity-75{--bs-text-opacity: .75}.text-opacity-100{--bs-text-opacity: 1}.bg-primary{--bs-bg-opacity: 1;background-color:rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important}.bg-secondary{--bs-bg-opacity: 1;background-color:rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important}.bg-success{--bs-bg-opacity: 1;background-color:rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important}.bg-info{--bs-bg-opacity: 1;background-color:rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important}.bg-warning{--bs-bg-opacity: 1;background-color:rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important}.bg-danger{--bs-bg-opacity: 1;background-color:rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important}.bg-light{--bs-bg-opacity: 1;background-color:rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important}.bg-dark{--bs-bg-opacity: 1;background-color:rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important}.bg-black{--bs-bg-opacity: 1;background-color:rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important}.bg-white{--bs-bg-opacity: 1;background-color:rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important}.bg-body{--bs-bg-opacity: 1;background-color:rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important}.bg-transparent{--bs-bg-opacity: 1;background-color:rgba(0,0,0,0) !important}.bg-opacity-10{--bs-bg-opacity: .1}.bg-opacity-25{--bs-bg-opacity: .25}.bg-opacity-50{--bs-bg-opacity: .5}.bg-opacity-75{--bs-bg-opacity: .75}.bg-opacity-100{--bs-bg-opacity: 1}.bg-gradient{background-image:var(--bs-gradient) !important}.user-select-all{user-select:all !important}.user-select-auto{user-select:auto !important}.user-select-none{user-select:none !important}.pe-none{pointer-events:none !important}.pe-auto{pointer-events:auto !important}.rounded{border-radius:.25rem !important}.rounded-0{border-radius:0 !important}.rounded-1{border-radius:.2rem !important}.rounded-2{border-radius:.25rem !important}.rounded-3{border-radius:.3rem !important}.rounded-circle{border-radius:50% !important}.rounded-pill{border-radius:50rem !important}.rounded-top{border-top-left-radius:.25rem !important;border-top-right-radius:.25rem !important}.rounded-end{border-top-right-radius:.25rem !important;border-bottom-right-radius:.25rem !important}.rounded-bottom{border-bottom-right-radius:.25rem !important;border-bottom-left-radius:.25rem !important}.rounded-start{border-bottom-left-radius:.25rem !important;border-top-left-radius:.25rem !important}.visible{visibility:visible !important}.invisible{visibility:hidden !important}@media (min-width: 576px){.float-sm-start{float:left !important}.float-sm-end{float:right !important}.float-sm-none{float:none !important}.d-sm-inline{display:inline !important}.d-sm-inline-block{display:inline-block !important}.d-sm-block{display:block !important}.d-sm-grid{display:grid !important}.d-sm-table{display:table !important}.d-sm-table-row{display:table-row !important}.d-sm-table-cell{display:table-cell !important}.d-sm-flex{display:flex !important}.d-sm-inline-flex{display:inline-flex !important}.d-sm-none{display:none !important}.flex-sm-fill{flex:1 1 auto !important}.flex-sm-row{flex-direction:row !important}.flex-sm-column{flex-direction:column !important}.flex-sm-row-reverse{flex-direction:row-reverse !important}.flex-sm-column-reverse{flex-direction:column-reverse !important}.flex-sm-grow-0{flex-grow:0 !important}.flex-sm-grow-1{flex-grow:1 !important}.flex-sm-shrink-0{flex-shrink:0 !important}.flex-sm-shrink-1{flex-shrink:1 !important}.flex-sm-wrap{flex-wrap:wrap !important}.flex-sm-nowrap{flex-wrap:nowrap !important}.flex-sm-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-sm-0{gap:0 !important}.gap-sm-1{gap:.25rem !important}.gap-sm-2{gap:.5rem !important}.gap-sm-3{gap:1rem !important}.gap-sm-4{gap:1.5rem !important}.gap-sm-5{gap:3rem !important}.justify-content-sm-start{justify-content:flex-start !important}.justify-content-sm-end{justify-content:flex-end !important}.justify-content-sm-center{justify-content:center !important}.justify-content-sm-between{justify-content:space-between !important}.justify-content-sm-around{justify-content:space-around !important}.justify-content-sm-evenly{justify-content:space-evenly !important}.align-items-sm-start{align-items:flex-start !important}.align-items-sm-end{align-items:flex-end !important}.align-items-sm-center{align-items:center !important}.align-items-sm-baseline{align-items:baseline !important}.align-items-sm-stretch{align-items:stretch !important}.align-content-sm-start{align-content:flex-start !important}.align-content-sm-end{align-content:flex-end !important}.align-content-sm-center{align-content:center !important}.align-content-sm-between{align-content:space-between !important}.align-content-sm-around{align-content:space-around !important}.align-content-sm-stretch{align-content:stretch !important}.align-self-sm-auto{align-self:auto !important}.align-self-sm-start{align-self:flex-start !important}.align-self-sm-end{align-self:flex-end !important}.align-self-sm-center{align-self:center !important}.align-self-sm-baseline{align-self:baseline !important}.align-self-sm-stretch{align-self:stretch !important}.order-sm-first{order:-1 !important}.order-sm-0{order:0 !important}.order-sm-1{order:1 !important}.order-sm-2{order:2 !important}.order-sm-3{order:3 !important}.order-sm-4{order:4 !important}.order-sm-5{order:5 !important}.order-sm-last{order:6 !important}.m-sm-0{margin:0 !important}.m-sm-1{margin:.25rem !important}.m-sm-2{margin:.5rem !important}.m-sm-3{margin:1rem !important}.m-sm-4{margin:1.5rem !important}.m-sm-5{margin:3rem !important}.m-sm-auto{margin:auto !important}.mx-sm-0{margin-right:0 !important;margin-left:0 !important}.mx-sm-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-sm-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-sm-3{margin-right:1rem !important;margin-left:1rem !important}.mx-sm-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-sm-5{margin-right:3rem !important;margin-left:3rem !important}.mx-sm-auto{margin-right:auto !important;margin-left:auto !important}.my-sm-0{margin-top:0 !important;margin-bottom:0 !important}.my-sm-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-sm-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-sm-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-sm-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-sm-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-sm-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-sm-0{margin-top:0 !important}.mt-sm-1{margin-top:.25rem !important}.mt-sm-2{margin-top:.5rem !important}.mt-sm-3{margin-top:1rem !important}.mt-sm-4{margin-top:1.5rem !important}.mt-sm-5{margin-top:3rem !important}.mt-sm-auto{margin-top:auto !important}.me-sm-0{margin-right:0 !important}.me-sm-1{margin-right:.25rem !important}.me-sm-2{margin-right:.5rem !important}.me-sm-3{margin-right:1rem !important}.me-sm-4{margin-right:1.5rem !important}.me-sm-5{margin-right:3rem !important}.me-sm-auto{margin-right:auto !important}.mb-sm-0{margin-bottom:0 !important}.mb-sm-1{margin-bottom:.25rem !important}.mb-sm-2{margin-bottom:.5rem !important}.mb-sm-3{margin-bottom:1rem !important}.mb-sm-4{margin-bottom:1.5rem !important}.mb-sm-5{margin-bottom:3rem !important}.mb-sm-auto{margin-bottom:auto !important}.ms-sm-0{margin-left:0 !important}.ms-sm-1{margin-left:.25rem !important}.ms-sm-2{margin-left:.5rem !important}.ms-sm-3{margin-left:1rem !important}.ms-sm-4{margin-left:1.5rem !important}.ms-sm-5{margin-left:3rem !important}.ms-sm-auto{margin-left:auto !important}.p-sm-0{padding:0 !important}.p-sm-1{padding:.25rem !important}.p-sm-2{padding:.5rem !important}.p-sm-3{padding:1rem !important}.p-sm-4{padding:1.5rem !important}.p-sm-5{padding:3rem !important}.px-sm-0{padding-right:0 !important;padding-left:0 !important}.px-sm-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-sm-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-sm-3{padding-right:1rem !important;padding-left:1rem !important}.px-sm-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-sm-5{padding-right:3rem !important;padding-left:3rem !important}.py-sm-0{padding-top:0 !important;padding-bottom:0 !important}.py-sm-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-sm-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-sm-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-sm-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-sm-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-sm-0{padding-top:0 !important}.pt-sm-1{padding-top:.25rem !important}.pt-sm-2{padding-top:.5rem !important}.pt-sm-3{padding-top:1rem !important}.pt-sm-4{padding-top:1.5rem !important}.pt-sm-5{padding-top:3rem !important}.pe-sm-0{padding-right:0 !important}.pe-sm-1{padding-right:.25rem !important}.pe-sm-2{padding-right:.5rem !important}.pe-sm-3{padding-right:1rem !important}.pe-sm-4{padding-right:1.5rem !important}.pe-sm-5{padding-right:3rem !important}.pb-sm-0{padding-bottom:0 !important}.pb-sm-1{padding-bottom:.25rem !important}.pb-sm-2{padding-bottom:.5rem !important}.pb-sm-3{padding-bottom:1rem !important}.pb-sm-4{padding-bottom:1.5rem !important}.pb-sm-5{padding-bottom:3rem !important}.ps-sm-0{padding-left:0 !important}.ps-sm-1{padding-left:.25rem !important}.ps-sm-2{padding-left:.5rem !important}.ps-sm-3{padding-left:1rem !important}.ps-sm-4{padding-left:1.5rem !important}.ps-sm-5{padding-left:3rem !important}.text-sm-start{text-align:left !important}.text-sm-end{text-align:right !important}.text-sm-center{text-align:center !important}}@media (min-width: 768px){.float-md-start{float:left !important}.float-md-end{float:right !important}.float-md-none{float:none !important}.d-md-inline{display:inline !important}.d-md-inline-block{display:inline-block !important}.d-md-block{display:block !important}.d-md-grid{display:grid !important}.d-md-table{display:table !important}.d-md-table-row{display:table-row !important}.d-md-table-cell{display:table-cell !important}.d-md-flex{display:flex !important}.d-md-inline-flex{display:inline-flex !important}.d-md-none{display:none !important}.flex-md-fill{flex:1 1 auto !important}.flex-md-row{flex-direction:row !important}.flex-md-column{flex-direction:column !important}.flex-md-row-reverse{flex-direction:row-reverse !important}.flex-md-column-reverse{flex-direction:column-reverse !important}.flex-md-grow-0{flex-grow:0 !important}.flex-md-grow-1{flex-grow:1 !important}.flex-md-shrink-0{flex-shrink:0 !important}.flex-md-shrink-1{flex-shrink:1 !important}.flex-md-wrap{flex-wrap:wrap !important}.flex-md-nowrap{flex-wrap:nowrap !important}.flex-md-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-md-0{gap:0 !important}.gap-md-1{gap:.25rem !important}.gap-md-2{gap:.5rem !important}.gap-md-3{gap:1rem !important}.gap-md-4{gap:1.5rem !important}.gap-md-5{gap:3rem !important}.justify-content-md-start{justify-content:flex-start !important}.justify-content-md-end{justify-content:flex-end !important}.justify-content-md-center{justify-content:center !important}.justify-content-md-between{justify-content:space-between !important}.justify-content-md-around{justify-content:space-around !important}.justify-content-md-evenly{justify-content:space-evenly !important}.align-items-md-start{align-items:flex-start !important}.align-items-md-end{align-items:flex-end !important}.align-items-md-center{align-items:center !important}.align-items-md-baseline{align-items:baseline !important}.align-items-md-stretch{align-items:stretch !important}.align-content-md-start{align-content:flex-start !important}.align-content-md-end{align-content:flex-end !important}.align-content-md-center{align-content:center !important}.align-content-md-between{align-content:space-between !important}.align-content-md-around{align-content:space-around !important}.align-content-md-stretch{align-content:stretch !important}.align-self-md-auto{align-self:auto !important}.align-self-md-start{align-self:flex-start !important}.align-self-md-end{align-self:flex-end !important}.align-self-md-center{align-self:center !important}.align-self-md-baseline{align-self:baseline !important}.align-self-md-stretch{align-self:stretch !important}.order-md-first{order:-1 !important}.order-md-0{order:0 !important}.order-md-1{order:1 !important}.order-md-2{order:2 !important}.order-md-3{order:3 !important}.order-md-4{order:4 !important}.order-md-5{order:5 !important}.order-md-last{order:6 !important}.m-md-0{margin:0 !important}.m-md-1{margin:.25rem !important}.m-md-2{margin:.5rem !important}.m-md-3{margin:1rem !important}.m-md-4{margin:1.5rem !important}.m-md-5{margin:3rem !important}.m-md-auto{margin:auto !important}.mx-md-0{margin-right:0 !important;margin-left:0 !important}.mx-md-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-md-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-md-3{margin-right:1rem !important;margin-left:1rem !important}.mx-md-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-md-5{margin-right:3rem !important;margin-left:3rem !important}.mx-md-auto{margin-right:auto !important;margin-left:auto !important}.my-md-0{margin-top:0 !important;margin-bottom:0 !important}.my-md-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-md-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-md-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-md-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-md-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-md-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-md-0{margin-top:0 !important}.mt-md-1{margin-top:.25rem !important}.mt-md-2{margin-top:.5rem !important}.mt-md-3{margin-top:1rem !important}.mt-md-4{margin-top:1.5rem !important}.mt-md-5{margin-top:3rem !important}.mt-md-auto{margin-top:auto !important}.me-md-0{margin-right:0 !important}.me-md-1{margin-right:.25rem !important}.me-md-2{margin-right:.5rem !important}.me-md-3{margin-right:1rem !important}.me-md-4{margin-right:1.5rem !important}.me-md-5{margin-right:3rem !important}.me-md-auto{margin-right:auto !important}.mb-md-0{margin-bottom:0 !important}.mb-md-1{margin-bottom:.25rem !important}.mb-md-2{margin-bottom:.5rem !important}.mb-md-3{margin-bottom:1rem !important}.mb-md-4{margin-bottom:1.5rem !important}.mb-md-5{margin-bottom:3rem !important}.mb-md-auto{margin-bottom:auto !important}.ms-md-0{margin-left:0 !important}.ms-md-1{margin-left:.25rem !important}.ms-md-2{margin-left:.5rem !important}.ms-md-3{margin-left:1rem !important}.ms-md-4{margin-left:1.5rem !important}.ms-md-5{margin-left:3rem !important}.ms-md-auto{margin-left:auto !important}.p-md-0{padding:0 !important}.p-md-1{padding:.25rem !important}.p-md-2{padding:.5rem !important}.p-md-3{padding:1rem !important}.p-md-4{padding:1.5rem !important}.p-md-5{padding:3rem !important}.px-md-0{padding-right:0 !important;padding-left:0 !important}.px-md-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-md-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-md-3{padding-right:1rem !important;padding-left:1rem !important}.px-md-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-md-5{padding-right:3rem !important;padding-left:3rem !important}.py-md-0{padding-top:0 !important;padding-bottom:0 !important}.py-md-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-md-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-md-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-md-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-md-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-md-0{padding-top:0 !important}.pt-md-1{padding-top:.25rem !important}.pt-md-2{padding-top:.5rem !important}.pt-md-3{padding-top:1rem !important}.pt-md-4{padding-top:1.5rem !important}.pt-md-5{padding-top:3rem !important}.pe-md-0{padding-right:0 !important}.pe-md-1{padding-right:.25rem !important}.pe-md-2{padding-right:.5rem !important}.pe-md-3{padding-right:1rem !important}.pe-md-4{padding-right:1.5rem !important}.pe-md-5{padding-right:3rem !important}.pb-md-0{padding-bottom:0 !important}.pb-md-1{padding-bottom:.25rem !important}.pb-md-2{padding-bottom:.5rem !important}.pb-md-3{padding-bottom:1rem !important}.pb-md-4{padding-bottom:1.5rem !important}.pb-md-5{padding-bottom:3rem !important}.ps-md-0{padding-left:0 !important}.ps-md-1{padding-left:.25rem !important}.ps-md-2{padding-left:.5rem !important}.ps-md-3{padding-left:1rem !important}.ps-md-4{padding-left:1.5rem !important}.ps-md-5{padding-left:3rem !important}.text-md-start{text-align:left !important}.text-md-end{text-align:right !important}.text-md-center{text-align:center !important}}@media (min-width: 992px){.float-lg-start{float:left !important}.float-lg-end{float:right !important}.float-lg-none{float:none !important}.d-lg-inline{display:inline !important}.d-lg-inline-block{display:inline-block !important}.d-lg-block{display:block !important}.d-lg-grid{display:grid !important}.d-lg-table{display:table !important}.d-lg-table-row{display:table-row !important}.d-lg-table-cell{display:table-cell !important}.d-lg-flex{display:flex !important}.d-lg-inline-flex{display:inline-flex !important}.d-lg-none{display:none !important}.flex-lg-fill{flex:1 1 auto !important}.flex-lg-row{flex-direction:row !important}.flex-lg-column{flex-direction:column !important}.flex-lg-row-reverse{flex-direction:row-reverse !important}.flex-lg-column-reverse{flex-direction:column-reverse !important}.flex-lg-grow-0{flex-grow:0 !important}.flex-lg-grow-1{flex-grow:1 !important}.flex-lg-shrink-0{flex-shrink:0 !important}.flex-lg-shrink-1{flex-shrink:1 !important}.flex-lg-wrap{flex-wrap:wrap !important}.flex-lg-nowrap{flex-wrap:nowrap !important}.flex-lg-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-lg-0{gap:0 !important}.gap-lg-1{gap:.25rem !important}.gap-lg-2{gap:.5rem !important}.gap-lg-3{gap:1rem !important}.gap-lg-4{gap:1.5rem !important}.gap-lg-5{gap:3rem !important}.justify-content-lg-start{justify-content:flex-start !important}.justify-content-lg-end{justify-content:flex-end !important}.justify-content-lg-center{justify-content:center !important}.justify-content-lg-between{justify-content:space-between !important}.justify-content-lg-around{justify-content:space-around !important}.justify-content-lg-evenly{justify-content:space-evenly !important}.align-items-lg-start{align-items:flex-start !important}.align-items-lg-end{align-items:flex-end !important}.align-items-lg-center{align-items:center !important}.align-items-lg-baseline{align-items:baseline !important}.align-items-lg-stretch{align-items:stretch !important}.align-content-lg-start{align-content:flex-start !important}.align-content-lg-end{align-content:flex-end !important}.align-content-lg-center{align-content:center !important}.align-content-lg-between{align-content:space-between !important}.align-content-lg-around{align-content:space-around !important}.align-content-lg-stretch{align-content:stretch !important}.align-self-lg-auto{align-self:auto !important}.align-self-lg-start{align-self:flex-start !important}.align-self-lg-end{align-self:flex-end !important}.align-self-lg-center{align-self:center !important}.align-self-lg-baseline{align-self:baseline !important}.align-self-lg-stretch{align-self:stretch !important}.order-lg-first{order:-1 !important}.order-lg-0{order:0 !important}.order-lg-1{order:1 !important}.order-lg-2{order:2 !important}.order-lg-3{order:3 !important}.order-lg-4{order:4 !important}.order-lg-5{order:5 !important}.order-lg-last{order:6 !important}.m-lg-0{margin:0 !important}.m-lg-1{margin:.25rem !important}.m-lg-2{margin:.5rem !important}.m-lg-3{margin:1rem !important}.m-lg-4{margin:1.5rem !important}.m-lg-5{margin:3rem !important}.m-lg-auto{margin:auto !important}.mx-lg-0{margin-right:0 !important;margin-left:0 !important}.mx-lg-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-lg-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-lg-3{margin-right:1rem !important;margin-left:1rem !important}.mx-lg-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-lg-5{margin-right:3rem !important;margin-left:3rem !important}.mx-lg-auto{margin-right:auto !important;margin-left:auto !important}.my-lg-0{margin-top:0 !important;margin-bottom:0 !important}.my-lg-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-lg-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-lg-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-lg-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-lg-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-lg-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-lg-0{margin-top:0 !important}.mt-lg-1{margin-top:.25rem !important}.mt-lg-2{margin-top:.5rem !important}.mt-lg-3{margin-top:1rem !important}.mt-lg-4{margin-top:1.5rem !important}.mt-lg-5{margin-top:3rem !important}.mt-lg-auto{margin-top:auto !important}.me-lg-0{margin-right:0 !important}.me-lg-1{margin-right:.25rem !important}.me-lg-2{margin-right:.5rem !important}.me-lg-3{margin-right:1rem !important}.me-lg-4{margin-right:1.5rem !important}.me-lg-5{margin-right:3rem !important}.me-lg-auto{margin-right:auto !important}.mb-lg-0{margin-bottom:0 !important}.mb-lg-1{margin-bottom:.25rem !important}.mb-lg-2{margin-bottom:.5rem !important}.mb-lg-3{margin-bottom:1rem !important}.mb-lg-4{margin-bottom:1.5rem !important}.mb-lg-5{margin-bottom:3rem !important}.mb-lg-auto{margin-bottom:auto !important}.ms-lg-0{margin-left:0 !important}.ms-lg-1{margin-left:.25rem !important}.ms-lg-2{margin-left:.5rem !important}.ms-lg-3{margin-left:1rem !important}.ms-lg-4{margin-left:1.5rem !important}.ms-lg-5{margin-left:3rem !important}.ms-lg-auto{margin-left:auto !important}.p-lg-0{padding:0 !important}.p-lg-1{padding:.25rem !important}.p-lg-2{padding:.5rem !important}.p-lg-3{padding:1rem !important}.p-lg-4{padding:1.5rem !important}.p-lg-5{padding:3rem !important}.px-lg-0{padding-right:0 !important;padding-left:0 !important}.px-lg-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-lg-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-lg-3{padding-right:1rem !important;padding-left:1rem !important}.px-lg-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-lg-5{padding-right:3rem !important;padding-left:3rem !important}.py-lg-0{padding-top:0 !important;padding-bottom:0 !important}.py-lg-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-lg-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-lg-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-lg-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-lg-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-lg-0{padding-top:0 !important}.pt-lg-1{padding-top:.25rem !important}.pt-lg-2{padding-top:.5rem !important}.pt-lg-3{padding-top:1rem !important}.pt-lg-4{padding-top:1.5rem !important}.pt-lg-5{padding-top:3rem !important}.pe-lg-0{padding-right:0 !important}.pe-lg-1{padding-right:.25rem !important}.pe-lg-2{padding-right:.5rem !important}.pe-lg-3{padding-right:1rem !important}.pe-lg-4{padding-right:1.5rem !important}.pe-lg-5{padding-right:3rem !important}.pb-lg-0{padding-bottom:0 !important}.pb-lg-1{padding-bottom:.25rem !important}.pb-lg-2{padding-bottom:.5rem !important}.pb-lg-3{padding-bottom:1rem !important}.pb-lg-4{padding-bottom:1.5rem !important}.pb-lg-5{padding-bottom:3rem !important}.ps-lg-0{padding-left:0 !important}.ps-lg-1{padding-left:.25rem !important}.ps-lg-2{padding-left:.5rem !important}.ps-lg-3{padding-left:1rem !important}.ps-lg-4{padding-left:1.5rem !important}.ps-lg-5{padding-left:3rem !important}.text-lg-start{text-align:left !important}.text-lg-end{text-align:right !important}.text-lg-center{text-align:center !important}}@media (min-width: 1200px){.float-xl-start{float:left !important}.float-xl-end{float:right !important}.float-xl-none{float:none !important}.d-xl-inline{display:inline !important}.d-xl-inline-block{display:inline-block !important}.d-xl-block{display:block !important}.d-xl-grid{display:grid !important}.d-xl-table{display:table !important}.d-xl-table-row{display:table-row !important}.d-xl-table-cell{display:table-cell !important}.d-xl-flex{display:flex !important}.d-xl-inline-flex{display:inline-flex !important}.d-xl-none{display:none !important}.flex-xl-fill{flex:1 1 auto !important}.flex-xl-row{flex-direction:row !important}.flex-xl-column{flex-direction:column !important}.flex-xl-row-reverse{flex-direction:row-reverse !important}.flex-xl-column-reverse{flex-direction:column-reverse !important}.flex-xl-grow-0{flex-grow:0 !important}.flex-xl-grow-1{flex-grow:1 !important}.flex-xl-shrink-0{flex-shrink:0 !important}.flex-xl-shrink-1{flex-shrink:1 !important}.flex-xl-wrap{flex-wrap:wrap !important}.flex-xl-nowrap{flex-wrap:nowrap !important}.flex-xl-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-xl-0{gap:0 !important}.gap-xl-1{gap:.25rem !important}.gap-xl-2{gap:.5rem !important}.gap-xl-3{gap:1rem !important}.gap-xl-4{gap:1.5rem !important}.gap-xl-5{gap:3rem !important}.justify-content-xl-start{justify-content:flex-start !important}.justify-content-xl-end{justify-content:flex-end !important}.justify-content-xl-center{justify-content:center !important}.justify-content-xl-between{justify-content:space-between !important}.justify-content-xl-around{justify-content:space-around !important}.justify-content-xl-evenly{justify-content:space-evenly !important}.align-items-xl-start{align-items:flex-start !important}.align-items-xl-end{align-items:flex-end !important}.align-items-xl-center{align-items:center !important}.align-items-xl-baseline{align-items:baseline !important}.align-items-xl-stretch{align-items:stretch !important}.align-content-xl-start{align-content:flex-start !important}.align-content-xl-end{align-content:flex-end !important}.align-content-xl-center{align-content:center !important}.align-content-xl-between{align-content:space-between !important}.align-content-xl-around{align-content:space-around !important}.align-content-xl-stretch{align-content:stretch !important}.align-self-xl-auto{align-self:auto !important}.align-self-xl-start{align-self:flex-start !important}.align-self-xl-end{align-self:flex-end !important}.align-self-xl-center{align-self:center !important}.align-self-xl-baseline{align-self:baseline !important}.align-self-xl-stretch{align-self:stretch !important}.order-xl-first{order:-1 !important}.order-xl-0{order:0 !important}.order-xl-1{order:1 !important}.order-xl-2{order:2 !important}.order-xl-3{order:3 !important}.order-xl-4{order:4 !important}.order-xl-5{order:5 !important}.order-xl-last{order:6 !important}.m-xl-0{margin:0 !important}.m-xl-1{margin:.25rem !important}.m-xl-2{margin:.5rem !important}.m-xl-3{margin:1rem !important}.m-xl-4{margin:1.5rem !important}.m-xl-5{margin:3rem !important}.m-xl-auto{margin:auto !important}.mx-xl-0{margin-right:0 !important;margin-left:0 !important}.mx-xl-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-xl-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-xl-3{margin-right:1rem !important;margin-left:1rem !important}.mx-xl-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-xl-5{margin-right:3rem !important;margin-left:3rem !important}.mx-xl-auto{margin-right:auto !important;margin-left:auto !important}.my-xl-0{margin-top:0 !important;margin-bottom:0 !important}.my-xl-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-xl-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-xl-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-xl-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-xl-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-xl-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-xl-0{margin-top:0 !important}.mt-xl-1{margin-top:.25rem !important}.mt-xl-2{margin-top:.5rem !important}.mt-xl-3{margin-top:1rem !important}.mt-xl-4{margin-top:1.5rem !important}.mt-xl-5{margin-top:3rem !important}.mt-xl-auto{margin-top:auto !important}.me-xl-0{margin-right:0 !important}.me-xl-1{margin-right:.25rem !important}.me-xl-2{margin-right:.5rem !important}.me-xl-3{margin-right:1rem !important}.me-xl-4{margin-right:1.5rem !important}.me-xl-5{margin-right:3rem !important}.me-xl-auto{margin-right:auto !important}.mb-xl-0{margin-bottom:0 !important}.mb-xl-1{margin-bottom:.25rem !important}.mb-xl-2{margin-bottom:.5rem !important}.mb-xl-3{margin-bottom:1rem !important}.mb-xl-4{margin-bottom:1.5rem !important}.mb-xl-5{margin-bottom:3rem !important}.mb-xl-auto{margin-bottom:auto !important}.ms-xl-0{margin-left:0 !important}.ms-xl-1{margin-left:.25rem !important}.ms-xl-2{margin-left:.5rem !important}.ms-xl-3{margin-left:1rem !important}.ms-xl-4{margin-left:1.5rem !important}.ms-xl-5{margin-left:3rem !important}.ms-xl-auto{margin-left:auto !important}.p-xl-0{padding:0 !important}.p-xl-1{padding:.25rem !important}.p-xl-2{padding:.5rem !important}.p-xl-3{padding:1rem !important}.p-xl-4{padding:1.5rem !important}.p-xl-5{padding:3rem !important}.px-xl-0{padding-right:0 !important;padding-left:0 !important}.px-xl-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-xl-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-xl-3{padding-right:1rem !important;padding-left:1rem !important}.px-xl-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-xl-5{padding-right:3rem !important;padding-left:3rem !important}.py-xl-0{padding-top:0 !important;padding-bottom:0 !important}.py-xl-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-xl-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-xl-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-xl-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-xl-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-xl-0{padding-top:0 !important}.pt-xl-1{padding-top:.25rem !important}.pt-xl-2{padding-top:.5rem !important}.pt-xl-3{padding-top:1rem !important}.pt-xl-4{padding-top:1.5rem !important}.pt-xl-5{padding-top:3rem !important}.pe-xl-0{padding-right:0 !important}.pe-xl-1{padding-right:.25rem !important}.pe-xl-2{padding-right:.5rem !important}.pe-xl-3{padding-right:1rem !important}.pe-xl-4{padding-right:1.5rem !important}.pe-xl-5{padding-right:3rem !important}.pb-xl-0{padding-bottom:0 !important}.pb-xl-1{padding-bottom:.25rem !important}.pb-xl-2{padding-bottom:.5rem !important}.pb-xl-3{padding-bottom:1rem !important}.pb-xl-4{padding-bottom:1.5rem !important}.pb-xl-5{padding-bottom:3rem !important}.ps-xl-0{padding-left:0 !important}.ps-xl-1{padding-left:.25rem !important}.ps-xl-2{padding-left:.5rem !important}.ps-xl-3{padding-left:1rem !important}.ps-xl-4{padding-left:1.5rem !important}.ps-xl-5{padding-left:3rem !important}.text-xl-start{text-align:left !important}.text-xl-end{text-align:right !important}.text-xl-center{text-align:center !important}}@media (min-width: 1400px){.float-xxl-start{float:left !important}.float-xxl-end{float:right !important}.float-xxl-none{float:none !important}.d-xxl-inline{display:inline !important}.d-xxl-inline-block{display:inline-block !important}.d-xxl-block{display:block !important}.d-xxl-grid{display:grid !important}.d-xxl-table{display:table !important}.d-xxl-table-row{display:table-row !important}.d-xxl-table-cell{display:table-cell !important}.d-xxl-flex{display:flex !important}.d-xxl-inline-flex{display:inline-flex !important}.d-xxl-none{display:none !important}.flex-xxl-fill{flex:1 1 auto !important}.flex-xxl-row{flex-direction:row !important}.flex-xxl-column{flex-direction:column !important}.flex-xxl-row-reverse{flex-direction:row-reverse !important}.flex-xxl-column-reverse{flex-direction:column-reverse !important}.flex-xxl-grow-0{flex-grow:0 !important}.flex-xxl-grow-1{flex-grow:1 !important}.flex-xxl-shrink-0{flex-shrink:0 !important}.flex-xxl-shrink-1{flex-shrink:1 !important}.flex-xxl-wrap{flex-wrap:wrap !important}.flex-xxl-nowrap{flex-wrap:nowrap !important}.flex-xxl-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-xxl-0{gap:0 !important}.gap-xxl-1{gap:.25rem !important}.gap-xxl-2{gap:.5rem !important}.gap-xxl-3{gap:1rem !important}.gap-xxl-4{gap:1.5rem !important}.gap-xxl-5{gap:3rem !important}.justify-content-xxl-start{justify-content:flex-start !important}.justify-content-xxl-end{justify-content:flex-end !important}.justify-content-xxl-center{justify-content:center !important}.justify-content-xxl-between{justify-content:space-between !important}.justify-content-xxl-around{justify-content:space-around !important}.justify-content-xxl-evenly{justify-content:space-evenly !important}.align-items-xxl-start{align-items:flex-start !important}.align-items-xxl-end{align-items:flex-end !important}.align-items-xxl-center{align-items:center !important}.align-items-xxl-baseline{align-items:baseline !important}.align-items-xxl-stretch{align-items:stretch !important}.align-content-xxl-start{align-content:flex-start !important}.align-content-xxl-end{align-content:flex-end !important}.align-content-xxl-center{align-content:center !important}.align-content-xxl-between{align-content:space-between !important}.align-content-xxl-around{align-content:space-around !important}.align-content-xxl-stretch{align-content:stretch !important}.align-self-xxl-auto{align-self:auto !important}.align-self-xxl-start{align-self:flex-start !important}.align-self-xxl-end{align-self:flex-end !important}.align-self-xxl-center{align-self:center !important}.align-self-xxl-baseline{align-self:baseline !important}.align-self-xxl-stretch{align-self:stretch !important}.order-xxl-first{order:-1 !important}.order-xxl-0{order:0 !important}.order-xxl-1{order:1 !important}.order-xxl-2{order:2 !important}.order-xxl-3{order:3 !important}.order-xxl-4{order:4 !important}.order-xxl-5{order:5 !important}.order-xxl-last{order:6 !important}.m-xxl-0{margin:0 !important}.m-xxl-1{margin:.25rem !important}.m-xxl-2{margin:.5rem !important}.m-xxl-3{margin:1rem !important}.m-xxl-4{margin:1.5rem !important}.m-xxl-5{margin:3rem !important}.m-xxl-auto{margin:auto !important}.mx-xxl-0{margin-right:0 !important;margin-left:0 !important}.mx-xxl-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-xxl-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-xxl-3{margin-right:1rem !important;margin-left:1rem !important}.mx-xxl-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-xxl-5{margin-right:3rem !important;margin-left:3rem !important}.mx-xxl-auto{margin-right:auto !important;margin-left:auto !important}.my-xxl-0{margin-top:0 !important;margin-bottom:0 !important}.my-xxl-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-xxl-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-xxl-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-xxl-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-xxl-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-xxl-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-xxl-0{margin-top:0 !important}.mt-xxl-1{margin-top:.25rem !important}.mt-xxl-2{margin-top:.5rem !important}.mt-xxl-3{margin-top:1rem !important}.mt-xxl-4{margin-top:1.5rem !important}.mt-xxl-5{margin-top:3rem !important}.mt-xxl-auto{margin-top:auto !important}.me-xxl-0{margin-right:0 !important}.me-xxl-1{margin-right:.25rem !important}.me-xxl-2{margin-right:.5rem !important}.me-xxl-3{margin-right:1rem !important}.me-xxl-4{margin-right:1.5rem !important}.me-xxl-5{margin-right:3rem !important}.me-xxl-auto{margin-right:auto !important}.mb-xxl-0{margin-bottom:0 !important}.mb-xxl-1{margin-bottom:.25rem !important}.mb-xxl-2{margin-bottom:.5rem !important}.mb-xxl-3{margin-bottom:1rem !important}.mb-xxl-4{margin-bottom:1.5rem !important}.mb-xxl-5{margin-bottom:3rem !important}.mb-xxl-auto{margin-bottom:auto !important}.ms-xxl-0{margin-left:0 !important}.ms-xxl-1{margin-left:.25rem !important}.ms-xxl-2{margin-left:.5rem !important}.ms-xxl-3{margin-left:1rem !important}.ms-xxl-4{margin-left:1.5rem !important}.ms-xxl-5{margin-left:3rem !important}.ms-xxl-auto{margin-left:auto !important}.p-xxl-0{padding:0 !important}.p-xxl-1{padding:.25rem !important}.p-xxl-2{padding:.5rem !important}.p-xxl-3{padding:1rem !important}.p-xxl-4{padding:1.5rem !important}.p-xxl-5{padding:3rem !important}.px-xxl-0{padding-right:0 !important;padding-left:0 !important}.px-xxl-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-xxl-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-xxl-3{padding-right:1rem !important;padding-left:1rem !important}.px-xxl-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-xxl-5{padding-right:3rem !important;padding-left:3rem !important}.py-xxl-0{padding-top:0 !important;padding-bottom:0 !important}.py-xxl-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-xxl-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-xxl-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-xxl-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-xxl-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-xxl-0{padding-top:0 !important}.pt-xxl-1{padding-top:.25rem !important}.pt-xxl-2{padding-top:.5rem !important}.pt-xxl-3{padding-top:1rem !important}.pt-xxl-4{padding-top:1.5rem !important}.pt-xxl-5{padding-top:3rem !important}.pe-xxl-0{padding-right:0 !important}.pe-xxl-1{padding-right:.25rem !important}.pe-xxl-2{padding-right:.5rem !important}.pe-xxl-3{padding-right:1rem !important}.pe-xxl-4{padding-right:1.5rem !important}.pe-xxl-5{padding-right:3rem !important}.pb-xxl-0{padding-bottom:0 !important}.pb-xxl-1{padding-bottom:.25rem !important}.pb-xxl-2{padding-bottom:.5rem !important}.pb-xxl-3{padding-bottom:1rem !important}.pb-xxl-4{padding-bottom:1.5rem !important}.pb-xxl-5{padding-bottom:3rem !important}.ps-xxl-0{padding-left:0 !important}.ps-xxl-1{padding-left:.25rem !important}.ps-xxl-2{padding-left:.5rem !important}.ps-xxl-3{padding-left:1rem !important}.ps-xxl-4{padding-left:1.5rem !important}.ps-xxl-5{padding-left:3rem !important}.text-xxl-start{text-align:left !important}.text-xxl-end{text-align:right !important}.text-xxl-center{text-align:center !important}}@media (min-width: 1200px){.fs-1{font-size:2.5rem !important}.fs-2{font-size:2rem !important}.fs-3{font-size:1.75rem !important}.fs-4{font-size:1.5rem !important}}@media print{.d-print-inline{display:inline !important}.d-print-inline-block{display:inline-block !important}.d-print-block{display:block !important}.d-print-grid{display:grid !important}.d-print-table{display:table !important}.d-print-table-row{display:table-row !important}.d-print-table-cell{display:table-cell !important}.d-print-flex{display:flex !important}.d-print-inline-flex{display:inline-flex !important}.d-print-none{display:none !important}}\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: $value;\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n      }\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white: #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black: #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue: #0d6efd !default;\n$indigo: #6610f2 !default;\n$purple: #6f42c1 !default;\n$pink: #d63384 !default;\n$red: #dc3545 !default;\n$orange: #fd7e14 !default;\n$yellow: #ffc107 !default;\n$green: #198754 !default;\n$teal: #20c997 !default;\n$cyan: #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\"blue\": $blue,\n  \"indigo\": $indigo,\n  \"purple\": $purple,\n  \"pink\": $pink,\n  \"red\": $red,\n  \"orange\": $orange,\n  \"yellow\": $yellow,\n  \"green\": $green,\n  \"teal\": $teal,\n  \"cyan\": $cyan,\n  \"white\": $white,\n  \"gray\": $gray-600,\n  \"gray-dark\": $gray-800) !default;\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary: $blue !default;\n$secondary: $gray-600 !default;\n$success: $green !default;\n$info: $cyan !default;\n$warning: $yellow !default;\n$danger: $red !default;\n$light: $gray-100 !default;\n$dark: $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\"primary\": $primary,\n  \"secondary\": $secondary,\n  \"success\": $success,\n  \"info\": $info,\n  \"warning\": $warning,\n  \"danger\": $danger,\n  \"light\": $light,\n  \"dark\": $dark) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\") !default;\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio: 4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark: $black !default;\n$color-contrast-light: $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900) !default;\n\n$indigos: (\"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900) !default;\n\n$purples: (\"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900) !default;\n\n$pinks: (\"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900) !default;\n\n$reds: (\"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900) !default;\n\n$oranges: (\"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900) !default;\n\n$yellows: (\"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900) !default;\n\n$greens: (\"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900) !default;\n\n$teals: (\"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900) !default;\n\n$cyans: (\"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: ((\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret: true !default;\n$enable-rounded: true !default;\n$enable-shadows: false !default;\n$enable-gradients: false !default;\n$enable-transitions: true !default;\n$enable-reduced-motion: true !default;\n$enable-smooth-scroll: true !default;\n$enable-grid-classes: true !default;\n$enable-cssgrid: false !default;\n$enable-button-pointers: true !default;\n$enable-rfs: true !default;\n$enable-validation-icons: true !default;\n$enable-negative-margins: false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities: true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix: bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (0: 0,\n  50: 50%,\n  100: 100%) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg: $white !default;\n$body-color: $gray-900 !default;\n$body-text-align: null !default;\n\n// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// Come v6, we'll de-dupe these variables. Until then, for backward compatibility, we keep them to reassign.\n// scss-docs-start utilities-colors\n$utilities-colors: $theme-colors-rgb !default;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text: map-merge($utilities-colors,\n  (\"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-color))) !default;\n$utilities-text-colors: map-loop($utilities-text, rgba-css-var, \"$key\", \"text\") !default;\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg: map-merge($utilities-colors,\n  (\"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-bg))) !default;\n$utilities-bg-colors: map-loop($utilities-bg, rgba-css-var, \"$key\", \"bg\") !default;\n// scss-docs-end utilities-bg-colors\n\n// Links\n//\n// Style anchor elements.\n\n$link-color: $primary !default;\n$link-decoration: underline !default;\n$link-shade-percentage: 20% !default;\n$link-hover-color: shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration: null !default;\n\n$stretched-link-pseudo-element: after !default;\n$stretched-link-z-index: 1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom: 1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns: 12 !default;\n$grid-gutter-width: 1.5rem !default;\n$grid-row-columns: 6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * .5 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width: 1px !default;\n$border-widths: (1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px) !default;\n\n$border-color: $gray-300 !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius: .25rem !default;\n$border-radius-sm: .2rem !default;\n$border-radius-lg: .3rem !default;\n$border-radius-pill: 50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow: 0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm: 0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg: 0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset: inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color: $white !default;\n$component-active-bg: $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width: .3em !default;\n$caret-vertical-align: $caret-width * .85 !default;\n$caret-spacing: $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base: all .2s ease-in-out !default;\n$transition-fade: opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse: height .35s ease !default;\n$transition-collapse-width: width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif: system-ui,\n-apple-system,\n\"Segoe UI\",\nRoboto,\n\"Helvetica Neue\",\nArial,\n\"Noto Sans\",\n\"Liberation Sans\",\nsans-serif,\n\"Apple Color Emoji\",\n\"Segoe UI Emoji\",\n\"Segoe UI Symbol\",\n\"Noto Color Emoji\" !default;\n$font-family-monospace: SFMono-Regular,\nMenlo,\nMonaco,\nConsolas,\n\"Liberation Mono\",\n\"Courier New\",\nmonospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base: var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code: var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root: null !default;\n$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm: $font-size-base * .875 !default;\n$font-size-lg: $font-size-base * 1.25 !default;\n\n$font-weight-lighter: lighter !default;\n$font-weight-light: 300 !default;\n$font-weight-normal: 400 !default;\n$font-weight-bold: 700 !default;\n$font-weight-bolder: bolder !default;\n\n$font-weight-base: $font-weight-normal !default;\n\n$line-height-base: 1.5 !default;\n$line-height-sm: 1.25 !default;\n$line-height-lg: 2 !default;\n\n$h1-font-size: $font-size-base * 2.5 !default;\n$h2-font-size: $font-size-base * 2 !default;\n$h3-font-size: $font-size-base * 1.75 !default;\n$h4-font-size: $font-size-base * 1.5 !default;\n$h5-font-size: $font-size-base * 1.25 !default;\n$h6-font-size: $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom: $spacer * .5 !default;\n$headings-font-family: null !default;\n$headings-font-style: null !default;\n$headings-font-weight: 500 !default;\n$headings-line-height: 1.2 !default;\n$headings-color: null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size: $font-size-base * 1.25 !default;\n$lead-font-weight: 300 !default;\n\n$small-font-size: .875em !default;\n\n$sub-sup-font-size: .75em !default;\n\n$text-muted: $gray-600 !default;\n\n$initialism-font-size: $small-font-size !default;\n\n$blockquote-margin-y: $spacer !default;\n$blockquote-font-size: $font-size-base * 1.25 !default;\n$blockquote-footer-color: $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y: $spacer !default;\n$hr-color: inherit !default;\n$hr-height: $border-width !default;\n$hr-opacity: .25 !default;\n\n$legend-margin-bottom: .5rem !default;\n$legend-font-size: 1.5rem !default;\n$legend-font-weight: null !default;\n\n$mark-padding: .2em !default;\n\n$dt-font-weight: $font-weight-bold !default;\n\n$nested-kbd-font-weight: $font-weight-bold !default;\n\n$list-inline-padding: .5rem !default;\n\n$mark-bg: #fcf8e3 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y: .5rem !default;\n$table-cell-padding-x: .5rem !default;\n$table-cell-padding-y-sm: .25rem !default;\n$table-cell-padding-x-sm: .25rem !default;\n\n$table-cell-vertical-align: top !default;\n\n$table-color: $body-color !default;\n$table-bg: transparent !default;\n$table-accent-bg: transparent !default;\n\n$table-th-font-weight: null !default;\n\n$table-striped-color: $table-color !default;\n$table-striped-bg-factor: .05 !default;\n$table-striped-bg: rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color: $table-color !default;\n$table-active-bg-factor: .1 !default;\n$table-active-bg: rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color: $table-color !default;\n$table-hover-bg-factor: .075 !default;\n$table-hover-bg: rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor: .1 !default;\n$table-border-width: $border-width !default;\n$table-border-color: $border-color !default;\n\n$table-striped-order: odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color: $text-muted !default;\n\n$table-bg-scale: -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\"primary\": shift-color($primary, $table-bg-scale),\n  \"secondary\": shift-color($secondary, $table-bg-scale),\n  \"success\": shift-color($success, $table-bg-scale),\n  \"info\": shift-color($info, $table-bg-scale),\n  \"warning\": shift-color($warning, $table-bg-scale),\n  \"danger\": shift-color($danger, $table-bg-scale),\n  \"light\": $light,\n  \"dark\": $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y: .375rem !default;\n$input-btn-padding-x: .75rem !default;\n$input-btn-font-family: null !default;\n$input-btn-font-size: $font-size-base !default;\n$input-btn-line-height: $line-height-base !default;\n\n$input-btn-focus-width: .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color: rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur: 0 !default;\n$input-btn-focus-box-shadow: 0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm: .25rem !default;\n$input-btn-padding-x-sm: .5rem !default;\n$input-btn-font-size-sm: $font-size-sm !default;\n\n$input-btn-padding-y-lg: .5rem !default;\n$input-btn-padding-x-lg: 1rem !default;\n$input-btn-font-size-lg: $font-size-lg !default;\n\n$input-btn-border-width: $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y: $input-btn-padding-y !default;\n$btn-padding-x: $input-btn-padding-x !default;\n$btn-font-family: $input-btn-font-family !default;\n$btn-font-size: $input-btn-font-size !default;\n$btn-line-height: $input-btn-line-height !default;\n$btn-white-space: null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm: $input-btn-padding-y-sm !default;\n$btn-padding-x-sm: $input-btn-padding-x-sm !default;\n$btn-font-size-sm: $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg: $input-btn-padding-y-lg !default;\n$btn-padding-x-lg: $input-btn-padding-x-lg !default;\n$btn-font-size-lg: $input-btn-font-size-lg !default;\n\n$btn-border-width: $input-btn-border-width !default;\n\n$btn-font-weight: $font-weight-normal !default;\n$btn-box-shadow: inset 0 1px 0 rgba($white, .15),\n0 1px 1px rgba($black, .075) !default;\n$btn-focus-width: $input-btn-focus-width !default;\n$btn-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity: .65 !default;\n$btn-active-box-shadow: inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color: $link-color !default;\n$btn-link-hover-color: $link-hover-color !default;\n$btn-link-disabled-color: $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius: $border-radius !default;\n$btn-border-radius-sm: $border-radius-sm !default;\n$btn-border-radius-lg: $border-radius-lg !default;\n\n$btn-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount: 15% !default;\n$btn-hover-bg-tint-amount: 15% !default;\n$btn-hover-border-shade-amount: 20% !default;\n$btn-hover-border-tint-amount: 10% !default;\n$btn-active-bg-shade-amount: 20% !default;\n$btn-active-bg-tint-amount: 20% !default;\n$btn-active-border-shade-amount: 25% !default;\n$btn-active-border-tint-amount: 10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top: .25rem !default;\n$form-text-font-size: $small-font-size !default;\n$form-text-font-style: null !default;\n$form-text-font-weight: null !default;\n$form-text-color: $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom: .5rem !default;\n$form-label-font-size: null !default;\n$form-label-font-style: null !default;\n$form-label-font-weight: null !default;\n$form-label-color: null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y: $input-btn-padding-y !default;\n$input-padding-x: $input-btn-padding-x !default;\n$input-font-family: $input-btn-font-family !default;\n$input-font-size: $input-btn-font-size !default;\n$input-font-weight: $font-weight-base !default;\n$input-line-height: $input-btn-line-height !default;\n\n$input-padding-y-sm: $input-btn-padding-y-sm !default;\n$input-padding-x-sm: $input-btn-padding-x-sm !default;\n$input-font-size-sm: $input-btn-font-size-sm !default;\n\n$input-padding-y-lg: $input-btn-padding-y-lg !default;\n$input-padding-x-lg: $input-btn-padding-x-lg !default;\n$input-font-size-lg: $input-btn-font-size-lg !default;\n\n$input-bg: $body-bg !default;\n$input-disabled-bg: $gray-200 !default;\n$input-disabled-border-color: null !default;\n\n$input-color: $body-color !default;\n$input-border-color: $gray-400 !default;\n$input-border-width: $input-btn-border-width !default;\n$input-box-shadow: $box-shadow-inset !default;\n\n$input-border-radius: $border-radius !default;\n$input-border-radius-sm: $border-radius-sm !default;\n$input-border-radius-lg: $border-radius-lg !default;\n\n$input-focus-bg: $input-bg !default;\n$input-focus-border-color: tint-color($component-active-bg, 50%) !default;\n$input-focus-color: $input-color !default;\n$input-focus-width: $input-btn-focus-width !default;\n$input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color: $gray-600 !default;\n$input-plaintext-color: $body-color !default;\n\n$input-height-border: $input-border-width * 2 !default;\n\n$input-height-inner: add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half: add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter: add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height: add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm: add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg: add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition: border-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$form-color-width: 3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width: 1em !default;\n$form-check-min-height: $font-size-base * $line-height-base !default;\n$form-check-padding-start: $form-check-input-width+.5em !default;\n$form-check-margin-bottom: .125rem !default;\n$form-check-label-color: null !default;\n$form-check-label-cursor: null !default;\n$form-check-transition: null !default;\n\n$form-check-input-active-filter: brightness(90%) !default;\n\n$form-check-input-bg: $input-bg !default;\n$form-check-input-border: 1px solid rgba($black, .25) !default;\n$form-check-input-border-radius: .25em !default;\n$form-check-radio-border-radius: 50% !default;\n$form-check-input-focus-border: $input-focus-border-color !default;\n$form-check-input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color: $component-active-color !default;\n$form-check-input-checked-bg-color: $component-active-bg !default;\n$form-check-input-checked-border-color: $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color: $component-active-color !default;\n$form-check-input-indeterminate-bg-color: $component-active-bg !default;\n$form-check-input-indeterminate-border-color: $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity: .5 !default;\n$form-check-label-disabled-opacity: $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity: $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end: 1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color: rgba($black, .25) !default;\n$form-switch-width: 2em !default;\n$form-switch-padding-start: $form-switch-width+.5em !default;\n$form-switch-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius: $form-switch-width !default;\n$form-switch-transition: background-position .15s ease-in-out !default;\n\n$form-switch-focus-color: $input-focus-border-color !default;\n$form-switch-focus-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color: $component-active-color !default;\n$form-switch-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y: $input-padding-y !default;\n$input-group-addon-padding-x: $input-padding-x !default;\n$input-group-addon-font-weight: $input-font-weight !default;\n$input-group-addon-color: $input-color !default;\n$input-group-addon-bg: $gray-200 !default;\n$input-group-addon-border-color: $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y: $input-padding-y !default;\n$form-select-padding-x: $input-padding-x !default;\n$form-select-font-family: $input-font-family !default;\n$form-select-font-size: $input-font-size !default;\n$form-select-indicator-padding: $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight: $input-font-weight !default;\n$form-select-line-height: $input-line-height !default;\n$form-select-color: $input-color !default;\n$form-select-bg: $input-bg !default;\n$form-select-disabled-color: null !default;\n$form-select-disabled-bg: $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position: right $form-select-padding-x center !default;\n$form-select-bg-size: 16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color: $gray-800 !default;\n$form-select-indicator: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5+$form-select-indicator-padding !default;\n$form-select-feedback-icon-position: center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size: $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width: $input-border-width !default;\n$form-select-border-color: $input-border-color !default;\n$form-select-border-radius: $input-border-radius !default;\n$form-select-box-shadow: $box-shadow-inset !default;\n\n$form-select-focus-border-color: $input-focus-border-color !default;\n$form-select-focus-width: $input-focus-width !default;\n$form-select-focus-box-shadow: 0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm: $input-padding-y-sm !default;\n$form-select-padding-x-sm: $input-padding-x-sm !default;\n$form-select-font-size-sm: $input-font-size-sm !default;\n$form-select-border-radius-sm: $input-border-radius-sm !default;\n\n$form-select-padding-y-lg: $input-padding-y-lg !default;\n$form-select-padding-x-lg: $input-padding-x-lg !default;\n$form-select-font-size-lg: $input-font-size-lg !default;\n$form-select-border-radius-lg: $input-border-radius-lg !default;\n\n$form-select-transition: $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width: 100% !default;\n$form-range-track-height: .5rem !default;\n$form-range-track-cursor: pointer !default;\n$form-range-track-bg: $gray-300 !default;\n$form-range-track-border-radius: 1rem !default;\n$form-range-track-box-shadow: $box-shadow-inset !default;\n\n$form-range-thumb-width: 1rem !default;\n$form-range-thumb-height: $form-range-thumb-width !default;\n$form-range-thumb-bg: $component-active-bg !default;\n$form-range-thumb-border: 0 !default;\n$form-range-thumb-border-radius: 1rem !default;\n$form-range-thumb-box-shadow: 0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow: 0 0 0 1px $body-bg,\n$input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width: $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg: tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg: $gray-500 !default;\n$form-range-thumb-transition: background-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color: $input-color !default;\n$form-file-button-bg: $input-group-addon-bg !default;\n$form-file-button-hover-bg: shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height: add(3.5rem, $input-height-border) !default;\n$form-floating-line-height: 1.25 !default;\n$form-floating-padding-x: $input-padding-x !default;\n$form-floating-padding-y: 1rem !default;\n$form-floating-input-padding-t: 1.625rem !default;\n$form-floating-input-padding-b: .625rem !default;\n$form-floating-label-opacity: .65 !default;\n$form-floating-label-transform: scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition: opacity .1s ease-in-out,\ntransform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top: $form-text-margin-top !default;\n$form-feedback-font-size: $form-text-font-size !default;\n$form-feedback-font-style: $form-text-font-style !default;\n$form-feedback-valid-color: $success !default;\n$form-feedback-invalid-color: $danger !default;\n\n$form-feedback-icon-valid-color: $form-feedback-valid-color !default;\n$form-feedback-icon-valid: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color: $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\"valid\": (\"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid),\n  \"invalid\": (\"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid)) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown: 1000 !default;\n$zindex-sticky: 1020 !default;\n$zindex-fixed: 1030 !default;\n$zindex-offcanvas-backdrop: 1040 !default;\n$zindex-offcanvas: 1045 !default;\n$zindex-modal-backdrop: 1050 !default;\n$zindex-modal: 1055 !default;\n$zindex-popover: 1070 !default;\n$zindex-tooltip: 1080 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y: .5rem !default;\n$nav-link-padding-x: 1rem !default;\n$nav-link-font-size: null !default;\n$nav-link-font-weight: null !default;\n$nav-link-color: $link-color !default;\n$nav-link-hover-color: $link-hover-color !default;\n$nav-link-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out !default;\n$nav-link-disabled-color: $gray-600 !default;\n\n$nav-tabs-border-color: $gray-300 !default;\n$nav-tabs-border-width: $border-width !default;\n$nav-tabs-border-radius: $border-radius !default;\n$nav-tabs-link-hover-border-color: $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color: $gray-700 !default;\n$nav-tabs-link-active-bg: $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius: $border-radius !default;\n$nav-pills-link-active-color: $component-active-color !default;\n$nav-pills-link-active-bg: $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y: $spacer * .5 !default;\n$navbar-padding-x: null !default;\n\n$navbar-nav-link-padding-x: .5rem !default;\n\n$navbar-brand-font-size: $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height: $font-size-base * $line-height-base+$nav-link-padding-y * 2 !default;\n$navbar-brand-height: $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y: ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end: 1rem !default;\n\n$navbar-toggler-padding-y: .25rem !default;\n$navbar-toggler-padding-x: .75rem !default;\n$navbar-toggler-font-size: $font-size-lg !default;\n$navbar-toggler-border-radius: $btn-border-radius !default;\n$navbar-toggler-focus-width: $btn-focus-width !default;\n$navbar-toggler-transition: box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color: rgba($white, .55) !default;\n$navbar-dark-hover-color: rgba($white, .75) !default;\n$navbar-dark-active-color: $white !default;\n$navbar-dark-disabled-color: rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color: rgba($white, .1) !default;\n\n$navbar-light-color: rgba($black, .55) !default;\n$navbar-light-hover-color: rgba($black, .7) !default;\n$navbar-light-active-color: rgba($black, .9) !default;\n$navbar-light-disabled-color: rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color: $navbar-light-active-color !default;\n$navbar-light-brand-hover-color: $navbar-light-active-color !default;\n$navbar-dark-brand-color: $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color: $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width: 10rem !default;\n$dropdown-padding-x: 0 !default;\n$dropdown-padding-y: .5rem !default;\n$dropdown-spacer: .125rem !default;\n$dropdown-font-size: $font-size-base !default;\n$dropdown-color: $body-color !default;\n$dropdown-bg: $white !default;\n$dropdown-border-color: rgba($black, .15) !default;\n$dropdown-border-radius: $border-radius !default;\n$dropdown-border-width: $border-width !default;\n$dropdown-inner-border-radius: subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg: $dropdown-border-color !default;\n$dropdown-divider-margin-y: $spacer * .5 !default;\n$dropdown-box-shadow: $box-shadow !default;\n\n$dropdown-link-color: $gray-900 !default;\n$dropdown-link-hover-color: shade-color($dropdown-link-color, 10%) !default;\n$dropdown-link-hover-bg: $gray-200 !default;\n\n$dropdown-link-active-color: $component-active-color !default;\n$dropdown-link-active-bg: $component-active-bg !default;\n\n$dropdown-link-disabled-color: $gray-500 !default;\n\n$dropdown-item-padding-y: $spacer * .25 !default;\n$dropdown-item-padding-x: $spacer !default;\n\n$dropdown-header-color: $gray-600 !default;\n$dropdown-header-padding: $dropdown-padding-y $dropdown-item-padding-x !default;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color: $gray-300 !default;\n$dropdown-dark-bg: $gray-800 !default;\n$dropdown-dark-border-color: $dropdown-border-color !default;\n$dropdown-dark-divider-bg: $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow: null !default;\n$dropdown-dark-link-color: $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color: $white !default;\n$dropdown-dark-link-hover-bg: rgba($white, .15) !default;\n$dropdown-dark-link-active-color: $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg: $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color: $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y: .375rem !default;\n$pagination-padding-x: .75rem !default;\n$pagination-padding-y-sm: .25rem !default;\n$pagination-padding-x-sm: .5rem !default;\n$pagination-padding-y-lg: .75rem !default;\n$pagination-padding-x-lg: 1.5rem !default;\n\n$pagination-color: $link-color !default;\n$pagination-bg: $white !default;\n$pagination-border-width: $border-width !default;\n$pagination-border-radius: $border-radius !default;\n$pagination-margin-start: -$pagination-border-width !default;\n$pagination-border-color: $gray-300 !default;\n\n$pagination-focus-color: $link-hover-color !default;\n$pagination-focus-bg: $gray-200 !default;\n$pagination-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$pagination-focus-outline: 0 !default;\n\n$pagination-hover-color: $link-hover-color !default;\n$pagination-hover-bg: $gray-200 !default;\n$pagination-hover-border-color: $gray-300 !default;\n\n$pagination-active-color: $component-active-color !default;\n$pagination-active-bg: $component-active-bg !default;\n$pagination-active-border-color: $pagination-active-bg !default;\n\n$pagination-disabled-color: $gray-600 !default;\n$pagination-disabled-bg: $white !default;\n$pagination-disabled-border-color: $gray-300 !default;\n\n$pagination-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm: $border-radius-sm !default;\n$pagination-border-radius-lg: $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max: .5 !default;\n$placeholder-opacity-min: .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y: $spacer !default;\n$card-spacer-x: $spacer !default;\n$card-title-spacer-y: $spacer * .5 !default;\n$card-border-width: $border-width !default;\n$card-border-color: rgba($black, .125) !default;\n$card-border-radius: $border-radius !default;\n$card-box-shadow: null !default;\n$card-inner-border-radius: subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y: $card-spacer-y * .5 !default;\n$card-cap-padding-x: $card-spacer-x !default;\n$card-cap-bg: rgba($black, .03) !default;\n$card-cap-color: null !default;\n$card-height: null !default;\n$card-color: null !default;\n$card-bg: $white !default;\n$card-img-overlay-padding: $spacer !default;\n$card-group-margin: $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y: 1rem !default;\n$accordion-padding-x: 1.25rem !default;\n$accordion-color: $body-color !default;\n$accordion-bg: $body-bg !default;\n$accordion-border-width: $border-width !default;\n$accordion-border-color: rgba($black, .125) !default;\n$accordion-border-radius: $border-radius !default;\n$accordion-inner-border-radius: subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y: $accordion-padding-y !default;\n$accordion-body-padding-x: $accordion-padding-x !default;\n\n$accordion-button-padding-y: $accordion-padding-y !default;\n$accordion-button-padding-x: $accordion-padding-x !default;\n$accordion-button-color: $accordion-color !default;\n$accordion-button-bg: $accordion-bg !default;\n$accordion-transition: $btn-transition,\nborder-radius .15s ease !default;\n$accordion-button-active-bg: tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color: shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color: $input-focus-border-color !default;\n$accordion-button-focus-box-shadow: $btn-focus-box-shadow !default;\n\n$accordion-icon-width: 1.25rem !default;\n$accordion-icon-color: $accordion-button-color !default;\n$accordion-icon-active-color: $accordion-button-active-color !default;\n$accordion-icon-transition: transform .2s ease-in-out !default;\n$accordion-icon-transform: rotate(-180deg) !default;\n\n$accordion-button-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size: $font-size-sm !default;\n$tooltip-max-width: 200px !default;\n$tooltip-color: $white !default;\n$tooltip-bg: $black !default;\n$tooltip-border-radius: $border-radius !default;\n$tooltip-opacity: .9 !default;\n$tooltip-padding-y: $spacer * .25 !default;\n$tooltip-padding-x: $spacer * .5 !default;\n$tooltip-margin: 0 !default;\n\n$tooltip-arrow-width: .8rem !default;\n$tooltip-arrow-height: .4rem !default;\n$tooltip-arrow-color: $tooltip-bg !default;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y: $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x: $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size: $tooltip-font-size !default;\n$form-feedback-tooltip-line-height: null !default;\n$form-feedback-tooltip-opacity: $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size: $font-size-sm !default;\n$popover-bg: $white !default;\n$popover-max-width: 276px !default;\n$popover-border-width: $border-width !default;\n$popover-border-color: rgba($black, .2) !default;\n$popover-border-radius: $border-radius-lg !default;\n$popover-inner-border-radius: subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow: $box-shadow !default;\n\n$popover-header-bg: shade-color($popover-bg, 6%) !default;\n$popover-header-color: $headings-color !default;\n$popover-header-padding-y: .5rem !default;\n$popover-header-padding-x: $spacer !default;\n\n$popover-body-color: $body-color !default;\n$popover-body-padding-y: $spacer !default;\n$popover-body-padding-x: $spacer !default;\n\n$popover-arrow-width: 1rem !default;\n$popover-arrow-height: .5rem !default;\n$popover-arrow-color: $popover-bg !default;\n\n$popover-arrow-outer-color: fade-in($popover-border-color, .05) !default;\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width: 350px !default;\n$toast-padding-x: .75rem !default;\n$toast-padding-y: .5rem !default;\n$toast-font-size: .875rem !default;\n$toast-color: null !default;\n$toast-background-color: rgba($white, .85) !default;\n$toast-border-width: 1px !default;\n$toast-border-color: rgba($black, .1) !default;\n$toast-border-radius: $border-radius !default;\n$toast-box-shadow: $box-shadow !default;\n$toast-spacing: $container-padding-x !default;\n\n$toast-header-color: $gray-600 !default;\n$toast-header-background-color: rgba($white, .85) !default;\n$toast-header-border-color: rgba($black, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size: .75em !default;\n$badge-font-weight: $font-weight-bold !default;\n$badge-color: $white !default;\n$badge-padding-y: .35em !default;\n$badge-padding-x: .65em !default;\n$badge-border-radius: $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding: $spacer !default;\n\n$modal-footer-margin-between: .5rem !default;\n\n$modal-dialog-margin: .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height: $line-height-base !default;\n\n$modal-content-color: null !default;\n$modal-content-bg: $white !default;\n$modal-content-border-color: rgba($black, .2) !default;\n$modal-content-border-width: $border-width !default;\n$modal-content-border-radius: $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs: $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up: $box-shadow !default;\n\n$modal-backdrop-bg: $black !default;\n$modal-backdrop-opacity: .5 !default;\n$modal-header-border-color: $border-color !default;\n$modal-footer-border-color: $modal-header-border-color !default;\n$modal-header-border-width: $modal-content-border-width !default;\n$modal-footer-border-width: $modal-header-border-width !default;\n$modal-header-padding-y: $modal-inner-padding !default;\n$modal-header-padding-x: $modal-inner-padding !default;\n$modal-header-padding: $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm: 300px !default;\n$modal-md: 500px !default;\n$modal-lg: 800px !default;\n$modal-xl: 1140px !default;\n\n$modal-fade-transform: translate(0, -50px) !default;\n$modal-show-transform: none !default;\n$modal-transition: transform .3s ease-out !default;\n$modal-scale-transform: scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y: $spacer !default;\n$alert-padding-x: $spacer !default;\n$alert-margin-bottom: 1rem !default;\n$alert-border-radius: $border-radius !default;\n$alert-link-font-weight: $font-weight-bold !default;\n$alert-border-width: $border-width !default;\n$alert-bg-scale: -80% !default;\n$alert-border-scale: -70% !default;\n$alert-color-scale: 40% !default;\n$alert-dismissible-padding-r: $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height: 1rem !default;\n$progress-font-size: $font-size-base * .75 !default;\n$progress-bg: $gray-200 !default;\n$progress-border-radius: $border-radius !default;\n$progress-box-shadow: $box-shadow-inset !default;\n$progress-bar-color: $white !default;\n$progress-bar-bg: $primary !default;\n$progress-bar-animation-timing: 1s linear infinite !default;\n$progress-bar-transition: width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color: $gray-900 !default;\n$list-group-bg: $white !default;\n$list-group-border-color: rgba($black, .125) !default;\n$list-group-border-width: $border-width !default;\n$list-group-border-radius: $border-radius !default;\n\n$list-group-item-padding-y: $spacer * .5 !default;\n$list-group-item-padding-x: $spacer !default;\n$list-group-item-bg-scale: -80% !default;\n$list-group-item-color-scale: 40% !default;\n\n$list-group-hover-bg: $gray-100 !default;\n$list-group-active-color: $component-active-color !default;\n$list-group-active-bg: $component-active-bg !default;\n$list-group-active-border-color: $list-group-active-bg !default;\n\n$list-group-disabled-color: $gray-600 !default;\n$list-group-disabled-bg: $list-group-bg !default;\n\n$list-group-action-color: $gray-700 !default;\n$list-group-action-hover-color: $list-group-action-color !default;\n\n$list-group-action-active-color: $body-color !default;\n$list-group-action-active-bg: $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding: .25rem !default;\n$thumbnail-bg: $body-bg !default;\n$thumbnail-border-width: $border-width !default;\n$thumbnail-border-color: $gray-300 !default;\n$thumbnail-border-radius: $border-radius !default;\n$thumbnail-box-shadow: $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size: $small-font-size !default;\n$figure-caption-color: $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size: null !default;\n$breadcrumb-padding-y: 0 !default;\n$breadcrumb-padding-x: 0 !default;\n$breadcrumb-item-padding-x: .5rem !default;\n$breadcrumb-margin-bottom: 1rem !default;\n$breadcrumb-bg: null !default;\n$breadcrumb-divider-color: $gray-600 !default;\n$breadcrumb-active-color: $gray-600 !default;\n$breadcrumb-divider: quote(\"/\") !default;\n$breadcrumb-divider-flipped: $breadcrumb-divider !default;\n$breadcrumb-border-radius: null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color: $white !default;\n$carousel-control-width: 15% !default;\n$carousel-control-opacity: .5 !default;\n$carousel-control-hover-opacity: .9 !default;\n$carousel-control-transition: opacity .15s ease !default;\n\n$carousel-indicator-width: 30px !default;\n$carousel-indicator-height: 3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer: 3px !default;\n$carousel-indicator-opacity: .5 !default;\n$carousel-indicator-active-bg: $white !default;\n$carousel-indicator-active-opacity: 1 !default;\n$carousel-indicator-transition: opacity .6s ease !default;\n\n$carousel-caption-width: 70% !default;\n$carousel-caption-color: $white !default;\n$carousel-caption-padding-y: 1.25rem !default;\n$carousel-caption-spacer: 1.25rem !default;\n\n$carousel-control-icon-width: 2rem !default;\n\n$carousel-control-prev-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration: .6s !default;\n$carousel-transition: transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg: $black !default;\n$carousel-dark-caption-color: $black !default;\n$carousel-dark-control-icon-filter: invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width: 2rem !default;\n$spinner-height: $spinner-width !default;\n$spinner-vertical-align: -.125em !default;\n$spinner-border-width: .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm: 1rem !default;\n$spinner-height-sm: $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width: 1em !default;\n$btn-close-height: $btn-close-width !default;\n$btn-close-padding-x: .25em !default;\n$btn-close-padding-y: $btn-close-padding-x !default;\n$btn-close-color: $black !default;\n$btn-close-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow: $input-btn-focus-box-shadow !default;\n$btn-close-opacity: .5 !default;\n$btn-close-hover-opacity: .75 !default;\n$btn-close-focus-opacity: 1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter: invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y: $modal-inner-padding !default;\n$offcanvas-padding-x: $modal-inner-padding !default;\n$offcanvas-horizontal-width: 400px !default;\n$offcanvas-vertical-height: 30vh !default;\n$offcanvas-transition-duration: .3s !default;\n$offcanvas-border-color: $modal-content-border-color !default;\n$offcanvas-border-width: $modal-content-border-width !default;\n$offcanvas-title-line-height: $modal-title-line-height !default;\n$offcanvas-bg-color: $modal-content-bg !default;\n$offcanvas-color: $modal-content-color !default;\n$offcanvas-box-shadow: $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg: $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity: $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size: $small-font-size !default;\n$code-color: $pink !default;\n\n$kbd-padding-y: .2rem !default;\n$kbd-padding-x: .4rem !default;\n$kbd-font-size: $code-font-size !default;\n$kbd-color: $white !default;\n$kbd-bg: $gray-900 !default;\n\n$pre-color: null !default;", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Colors\n@function to-rgb($value) {\n  @return red($value), green($value), blue($value);\n}\n\n// stylelint-disable scss/dollar-variable-pattern\n@function rgba-css-var($identifier, $target) {\n  @if $identifier == \"body\" and $target == \"bg\" {\n    @return rgba(var(--#{$variable-prefix}#{$identifier}-bg-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n  } @if $identifier == \"body\" and $target == \"text\" {\n    @return rgba(var(--#{$variable-prefix}#{$identifier}-color-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n  } @else {\n    @return rgba(var(--#{$variable-prefix}#{$identifier}-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n  }\n}\n\n@function map-loop($map, $func, $args...) {\n  $_map: ();\n\n  @each $key, $value in $map {\n    // allow to pass the $key and $value of the map as an function argument\n    $_args: ();\n    @each $arg in $args {\n      $_args: append($_args, if($arg == \"$key\", $key, if($arg == \"$value\", $value, $arg)));\n    }\n\n    $_map: map-merge($_map, ($key: call(get-function($func), $_args...)));\n  }\n\n  @return $_map;\n}\n// stylelint-enable scss/dollar-variable-pattern\n\n@function varify($list) {\n  $result: null;\n  @each $entry in $list {\n    $result: append($result, var(--#{$variable-prefix}#{$entry}), space);\n  }\n  @return $result;\n}\n\n// Internal Bootstrap function to turn maps into its negative variant.\n// It prefixes the keys with `n` and makes the value negative.\n@function negativify-map($map) {\n  $result: ();\n  @each $key, $value in $map {\n    @if $key != 0 {\n      $result: map-merge($result, (\"n\" + $key: (-$value)));\n    }\n  }\n  @return $result;\n}\n\n// Get multiple keys from a sass map\n@function map-get-multiple($map, $values) {\n  $result: ();\n  @each $key, $value in $map {\n    @if (index($values, $key) != null) {\n      $result: map-merge($result, ($key: $value));\n    }\n  }\n  @return $result;\n}\n\n// Merge multiple maps\n@function map-merge-multiple($maps...) {\n  $merged-maps: ();\n\n  @each $map in $maps {\n    $merged-maps: map-merge($merged-maps, $map);\n  }\n  @return $merged-maps;\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n//\n// Requires the use of quotes around data URIs.\n\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n// See https://github.com/twbs/bootstrap/pull/30168\n\n// A list of pre-calculated numbers of pow(divide((divide($value, 255) + .055), 1.055), 2.4). (from 0 to 255)\n// stylelint-disable-next-line scss/dollar-variable-default, scss/dollar-variable-pattern\n$_luminance-list: .0008 .001 .0011 .0013 .0015 .0017 .002 .0022 .0025 .0027 .003 .0033 .0037 .004 .0044 .0048 .0052 .0056 .006 .0065 .007 .0075 .008 .0086 .0091 .0097 .0103 .011 .0116 .0123 .013 .0137 .0144 .0152 .016 .0168 .0176 .0185 .0194 .0203 .0212 .0222 .0232 .0242 .0252 .0262 .0273 .0284 .0296 .0307 .0319 .0331 .0343 .0356 .0369 .0382 .0395 .0409 .0423 .0437 .0452 .0467 .0482 .0497 .0513 .0529 .0545 .0561 .0578 .0595 .0612 .063 .0648 .0666 .0685 .0704 .0723 .0742 .0762 .0782 .0802 .0823 .0844 .0865 .0887 .0908 .0931 .0953 .0976 .0999 .1022 .1046 .107 .1095 .1119 .1144 .117 .1195 .1221 .1248 .1274 .1301 .1329 .1356 .1384 .1413 .1441 .147 .15 .1529 .1559 .159 .162 .1651 .1683 .1714 .1746 .1779 .1812 .1845 .1878 .1912 .1946 .1981 .2016 .2051 .2086 .2122 .2159 .2195 .2232 .227 .2307 .2346 .2384 .2423 .2462 .2502 .2542 .2582 .2623 .2664 .2705 .2747 .2789 .2831 .2874 .2918 .2961 .3005 .305 .3095 .314 .3185 .3231 .3278 .3325 .3372 .3419 .3467 .3515 .3564 .3613 .3663 .3712 .3763 .3813 .3864 .3916 .3968 .402 .4072 .4125 .4179 .4233 .4287 .4342 .4397 .4452 .4508 .4564 .4621 .4678 .4735 .4793 .4851 .491 .4969 .5029 .5089 .5149 .521 .5271 .5333 .5395 .5457 .552 .5583 .5647 .5711 .5776 .5841 .5906 .5972 .6038 .6105 .6172 .624 .6308 .6376 .6445 .6514 .6584 .6654 .6724 .6795 .6867 .6939 .7011 .7084 .7157 .7231 .7305 .7379 .7454 .7529 .7605 .7682 .7758 .7835 .7913 .7991 .807 .8148 .8228 .8308 .8388 .8469 .855 .8632 .8714 .8796 .8879 .8963 .9047 .9131 .9216 .9301 .9387 .9473 .956 .9647 .9734 .9823 .9911 1;\n\n@function color-contrast($background, $color-contrast-dark: $color-contrast-dark, $color-contrast-light: $color-contrast-light, $min-contrast-ratio: $min-contrast-ratio) {\n  $foregrounds: $color-contrast-light, $color-contrast-dark, $white, $black;\n  $max-ratio: 0;\n  $max-ratio-color: null;\n\n  @each $color in $foregrounds {\n    $contrast-ratio: contrast-ratio($background, $color);\n    @if $contrast-ratio > $min-contrast-ratio {\n      @return $color;\n    } @else if $contrast-ratio > $max-ratio {\n      $max-ratio: $contrast-ratio;\n      $max-ratio-color: $color;\n    }\n  }\n\n  @warn \"Found no color leading to #{$min-contrast-ratio}:1 contrast ratio against #{$background}...\";\n\n  @return $max-ratio-color;\n}\n\n@function contrast-ratio($background, $foreground: $color-contrast-light) {\n  $l1: luminance($background);\n  $l2: luminance(opaque($background, $foreground));\n\n  @return if($l1 > $l2, divide($l1 + .05, $l2 + .05), divide($l2 + .05, $l1 + .05));\n}\n\n// Return WCAG2.0 relative luminance\n// See https://www.w3.org/WAI/GL/wiki/Relative_luminance\n// See https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n@function luminance($color) {\n  $rgb: (\n    \"r\": red($color),\n    \"g\": green($color),\n    \"b\": blue($color)\n  );\n\n  @each $name, $value in $rgb {\n    $value: if(divide($value, 255) < .03928, divide(divide($value, 255), 12.92), nth($_luminance-list, $value + 1));\n    $rgb: map-merge($rgb, ($name: $value));\n  }\n\n  @return (map-get($rgb, \"r\") * .2126) + (map-get($rgb, \"g\") * .7152) + (map-get($rgb, \"b\") * .0722);\n}\n\n// Return opaque color\n// opaque(#fff, rgba(0, 0, 0, .5)) => #808080\n@function opaque($background, $foreground) {\n  @return mix(rgba($foreground, 1), $background, opacity($foreground) * 100);\n}\n\n// scss-docs-start color-functions\n// Tint a color: mix a color with white\n@function tint-color($color, $weight) {\n  @return mix(white, $color, $weight);\n}\n\n// Shade a color: mix a color with black\n@function shade-color($color, $weight) {\n  @return mix(black, $color, $weight);\n}\n\n// Shade the color if the weight is positive, else tint it\n@function shift-color($color, $weight) {\n  @return if($weight > 0, shade-color($color, $weight), tint-color($color, -$weight));\n}\n// scss-docs-end color-functions\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @if type-of($value2) != number {\n    $value2: unquote(\"(\") + $value2 + unquote(\")\");\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$variable-prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$variable-prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentColor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$variable-prefix}#{$property-class}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$variable-prefix}#{$property-class}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $value in $is-local-vars {\n                --#{$variable-prefix}#{$local-var}: #{$value};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}