{"version": 3, "sources": ["vendors/bootstrap/bootstrap.scss", "vendors/bootstrap/bootstrap.css", "vendors/bootstrap/_root.scss", "vendors/bootstrap/_reboot.scss", "vendors/bootstrap/vendor/_rfs.scss", "vendors/bootstrap/_variables.scss", "vendors/bootstrap/_functions.scss", "vendors/bootstrap/mixins/_border-radius.scss", "vendors/bootstrap/_type.scss", "vendors/bootstrap/mixins/_lists.scss", "vendors/bootstrap/mixins/_image.scss", "vendors/bootstrap/_images.scss", "vendors/bootstrap/mixins/_container.scss", "vendors/bootstrap/mixins/_breakpoints.scss", "vendors/bootstrap/_containers.scss", "vendors/bootstrap/_grid.scss", "vendors/bootstrap/mixins/_grid.scss", "vendors/bootstrap/_tables.scss", "vendors/bootstrap/mixins/_table-variants.scss", "vendors/bootstrap/forms/_labels.scss", "vendors/bootstrap/forms/_form-text.scss", "vendors/bootstrap/forms/_form-control.scss", "vendors/bootstrap/mixins/_transition.scss", "vendors/bootstrap/mixins/_gradients.scss", "vendors/bootstrap/forms/_form-select.scss", "vendors/bootstrap/forms/_form-check.scss", "vendors/bootstrap/forms/_form-range.scss", "vendors/bootstrap/forms/_floating-labels.scss", "vendors/bootstrap/forms/_input-group.scss", "vendors/bootstrap/mixins/_forms.scss", "vendors/bootstrap/_buttons.scss", "vendors/bootstrap/mixins/_buttons.scss", "vendors/bootstrap/_transitions.scss", "vendors/bootstrap/_dropdown.scss", "vendors/bootstrap/mixins/_caret.scss", "vendors/bootstrap/_button-group.scss", "vendors/bootstrap/_nav.scss", "vendors/bootstrap/_navbar.scss", "vendors/bootstrap/_card.scss", "vendors/bootstrap/_accordion.scss", "vendors/bootstrap/_breadcrumb.scss", "vendors/bootstrap/_pagination.scss", "vendors/bootstrap/mixins/_pagination.scss", "vendors/bootstrap/_badge.scss", "vendors/bootstrap/_alert.scss", "vendors/bootstrap/mixins/_alert.scss", "vendors/bootstrap/_progress.scss", "vendors/bootstrap/_list-group.scss", "vendors/bootstrap/mixins/_list-group.scss", "vendors/bootstrap/_close.scss", "vendors/bootstrap/_toasts.scss", "vendors/bootstrap/_modal.scss", "vendors/bootstrap/mixins/_backdrop.scss", "vendors/bootstrap/_tooltip.scss", "vendors/bootstrap/mixins/_reset-text.scss", "vendors/bootstrap/_popover.scss", "vendors/bootstrap/_carousel.scss", "vendors/bootstrap/mixins/_clearfix.scss", "vendors/bootstrap/_spinners.scss", "vendors/bootstrap/_offcanvas.scss", "vendors/bootstrap/_placeholders.scss", "vendors/bootstrap/helpers/_colored-links.scss", "vendors/bootstrap/helpers/_ratio.scss", "vendors/bootstrap/helpers/_position.scss", "vendors/bootstrap/helpers/_stacks.scss", "vendors/bootstrap/mixins/_visually-hidden.scss", "vendors/bootstrap/helpers/_visually-hidden.scss", "vendors/bootstrap/helpers/_stretched-link.scss", "vendors/bootstrap/mixins/_text-truncate.scss", "vendors/bootstrap/helpers/_text-truncation.scss", "vendors/bootstrap/helpers/_vr.scss", "vendors/bootstrap/mixins/_utilities.scss", "vendors/bootstrap/utilities/_api.scss"], "names": [], "mappings": "AAAA;;;;;ECKE,CDAC,MEIC,kBAAiC,CAAjC,oBAAiC,CAAjC,oBAAiC,CAAjC,kBAAiC,CAAjC,iBAAiC,CAAjC,oBAAiC,CAAjC,oBAAiC,CAAjC,mBAAiC,CAAjC,kBAAiC,CAAjC,kBAAiC,CAAjC,gBAAiC,CAAjC,kBAAiC,CAAjC,uBAAiC,CAKjC,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAK3C,qBAAiC,CAAjC,uBAAiC,CAAjC,qBAAiC,CAAjC,kBAAiC,CAAjC,qBAAiC,CAAjC,oBAAiC,CAAjC,mBAAiC,CAAjC,kBAAiC,CAKjC,4BAAyC,CAAzC,+BAAyC,CAAzC,2BAAyC,CAAzC,yBAAyC,CAAzC,2BAAyC,CAAzC,0BAAyC,CAAzC,2BAAyC,CAAzC,uBAAyC,CAG3C,2BAA0C,CAC1C,qBAA0C,CAC1C,6BAAoD,CACpD,6BAA8C,CAM9C,qNAAsD,CACtD,yGAAoD,CACpD,mFAAwC,CASxC,gDAAwD,CACxD,yBAAoD,CACpD,0BAAwD,CACxD,0BAAwD,CACxD,wBAA4C,CAM5C,kBAAsC,CAGvC,qBC1CC,6BAAY,CAAZ,qBAAsB,CACvB,+CDnBD,MCkCM,sBAAuB,CAG5B,CAWD,KACE,QAAS,CACT,sCAAyE,CC6QrE,kCAxE+B,CDnMnC,sCAAyE,CACzE,sCAAyE,CACzE,0BAAuD,CACvD,oCAAsE,CACtE,kCAA4D,CAC5D,6BAA8B,CAC9B,yCExCU,CFyCX,GAWC,aAAsB,CACtB,aE8iBgB,CF7iBhB,6BAA8B,CAC9B,QAAS,CACT,WE6iBc,CF5iBf,eAGC,UE6YgB,CF5YjB,0CAUC,YAAa,CACb,mBEqfmC,CFlfnC,eEqfwB,CFpfxB,eEqfwB,CFnfzB,OCoOO,gCAhB6B,CA/JjC,2BDnDJ,OCyOQ,gBApF6B,CDlJpC,CAED,OC6NQ,+BAhB6B,CA/JjC,2BD9CJ,OCoOQ,cApF6B,CD7IpC,CAED,OCwNQ,6BAhB6B,CA/JjC,2BDzCJ,OC+NQ,iBApF6B,CDxIpC,CAED,OCmNQ,+BAhB6B,CA/JjC,2BDpCJ,OC0NQ,gBApF6B,CDnIpC,CAED,OCyMM,iBAxE+B,CD9HpC,OCsMK,cAxE+B,CDzHpC,EASC,YAAa,CACb,kBE2R4B,CF1R7B,yCAaC,wCAAiC,CAAjC,gCAAiC,CACjC,WAAY,CACZ,qCAA0B,CAA1B,6BAA8B,CAC/B,QAMC,kBAAmB,CACnB,iBAAkB,CAClB,mBAAoB,CACrB,MAOC,iBAAkB,CACnB,SAKC,YAAa,CACb,kBAAmB,CACpB,wBAMC,eAAgB,CACjB,GAGC,eEyXoB,CFxXrB,GAKC,mBAAoB,CACpB,aAAc,CACf,WAMC,eAAgB,CACjB,SASC,kBEkWyB,CFjW1B,aC2GK,gBAxE+B,CD1BpC,WAMC,YE0ZiB,CFzZjB,wBEiae,CFhahB,QAUC,iBAAkB,CCgFd,eAxE+B,CDNnC,aAAc,CACd,uBAAwB,CACzB,IAGC,aAAc,CACf,IAGC,SAAU,CACX,EAMC,aE7NY,CF8NZ,yBEqJyB,CFvJ3B,QAKI,aGlDiC,CHoDlC,4DAYC,aAAc,CACd,oBAAqB,CACtB,kBAUD,oCEyQyE,CDzOrE,aAxE+B,CD0CnC,8BAAoC,CACpC,0BAA2B,CAC5B,IAOC,aAAc,CACd,YAAa,CACb,kBAAmB,CACnB,aAAc,CCkBV,gBAxE+B,CDkDrC,SCsBM,iBAxE+B,CD6DjC,aAAc,CACd,iBAAkB,CACnB,KCSG,gBAxE+B,CDoEnC,aEpRY,CFqRZ,oBAAqB,CAGrB,OACE,aAAc,CACf,IAID,mBEswCmB,CD5wCf,gBAxE+B,CDgFnC,UE/TU,CFgUV,wBEvTgB,CEEd,mBFodoB,CFnKxB,QAQI,SAAU,CCbR,aAxE+B,CDuFjC,eEsOkB,CFrOnB,OASD,eAAgB,CACjB,QAOC,qBAAsB,CACvB,MAQC,mBAAoB,CACpB,wBAAyB,CAC1B,QAGC,iBEgS0B,CF/R1B,oBE+R0B,CF9R1B,aElWgB,CFmWhB,eAAgB,CACjB,GAQC,kBAAmB,CACnB,+BAAgC,CACjC,2BAQC,oBAAqB,CACrB,kBAAmB,CACnB,cAAe,CAChB,MAQC,oBAAqB,CACtB,OAOC,eAAgB,CACjB,iCAQC,SAAU,CACX,sCASC,QAAS,CACT,mBAAoB,CC5GhB,iBAxE+B,CDsLnC,mBAAoB,CACrB,cAKC,mBAAoB,CACrB,gBAMC,cAAe,CAChB,OAKC,gBAAiB,CAHnB,gBAOI,SAAU,CACX,0CAOD,YAAa,CACd,sDAYC,yBAA0B,CAL5B,kHASM,cAAe,CAChB,mBAOH,SAAU,CACV,iBAAkB,CACnB,SAKC,eAAgB,CACjB,SAUC,WAAY,CACZ,SAAU,CACV,QAAS,CACT,QAAS,CACV,OAQC,UAAW,CACX,UAAW,CACX,SAAU,CACV,mBEmH0B,CDrTpB,+BAhB6B,CDqNnC,mBAAoB,CCpXlB,2BD6WJ,OCvLQ,gBApF6B,CDuRpC,CAZD,SAUI,UAAW,CACZ,+OAaD,SAAU,CACX,4BAGC,WAAY,CACb,gBASC,mBAAoB,CACpB,4BAA6B,CAC9B,4BAmBC,uBAAwB,CACzB,+BAKC,SAAU,CACX,uBAMC,YAAa,CACd,6BAMC,YAAa,CACb,yBAA0B,CAC3B,OAKC,oBAAqB,CACtB,OAKC,QAAS,CACV,QAOC,iBAAkB,CAClB,cAAe,CAChB,SAQC,uBAAwB,CACzB,SAQC,uBAAwB,CACzB,MC3TK,iBAxE+B,CIzNnC,eHukBoB,CGtkBrB,WJqSO,gCAhB6B,CI9QjC,eHyjBqB,CGxjBrB,eH4iBsB,CD9btB,2BIjHF,WJuSM,cApF6B,CI/MlC,CAJD,WJgSM,gCAhB6B,CI9QjC,eHyjBqB,CGxjBrB,eH4iBsB,CD9btB,2BIjHF,WJuSM,gBApF6B,CI/MlC,CAJD,WJgSM,gCAhB6B,CI9QjC,eHyjBqB,CGxjBrB,eH4iBsB,CD9btB,2BIjHF,WJuSM,cApF6B,CI/MlC,CAJD,WJgSM,gCAhB6B,CI9QjC,eHyjBqB,CGxjBrB,eH4iBsB,CD9btB,2BIjHF,WJuSM,gBApF6B,CI/MlC,CAJD,WJgSM,gCAhB6B,CI9QjC,eHyjBqB,CGxjBrB,eH4iBsB,CD9btB,2BIjHF,WJuSM,cApF6B,CI/MlC,CAJD,WJgSM,gCAhB6B,CI9QjC,eHyjBqB,CGxjBrB,eH4iBsB,CD9btB,2BIjHF,WJuSM,gBApF6B,CI/MlC,CAkBH,eCtDE,cAAe,CACf,eAAgB,CDuDjB,aCxDC,cAAe,CACf,eAAgB,CD4DjB,kBAGC,oBAAqB,CADvB,mCAII,kBH4jBuB,CG3jBxB,YJuPG,gBAxE+B,CIpKnC,wBAAyB,CAC1B,YAIC,kBHmQW,CD5BP,iBAxE+B,CIhKrC,wBAKI,eAAgB,CACjB,mBAID,gBH0PW,CGzPX,kBHyPW,CD5BP,gBAxE+B,CInJnC,aHtFgB,CGkFlB,2BAOI,oBAAqB,CACtB,WE7FD,cAAe,CAGf,WAAY,CCLb,eAKC,cN87CwB,CM77CxB,qBNPU,CMQV,wBNLgB,CEQd,oBFmdkB,CK3dpB,cAAe,CAGf,WAAY,CCQb,QAQC,oBAAqB,CACtB,YAGC,mBAA2B,CAC3B,aAAc,CACf,gBP2RK,gBAxE+B,CO/MnC,aN1BgB,CM2BjB,mGCtCC,UAAW,CACX,wCAAuE,CACvE,uCAAsE,CACtE,iBAAkB,CAClB,gBAAiB,CCwDf,0BC1CE,yBACE,eTuayB,CSta1B,CDwCH,0BC1CE,uCACE,eTwaG,CSvaJ,CDwCH,0BC1CE,qDACE,eTyaG,CSxaJ,CDwCH,2BC1CE,mEACE,gBT0aI,CSzaL,CDwCH,2BC1CE,kFACE,gBT2aK,CS1aN,CClBL,KCAA,qBAAwC,CACxC,gBAAwC,CACxC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CAEf,wCAAmE,CACnE,2CAAsE,CACtE,0CAAqE,CDPrE,OCgBA,mBAAc,CAAd,aAAc,CACd,UAAW,CACX,cAAe,CACf,2CAAsE,CACtE,0CAAqE,CACrE,6BAAwD,CA+CpD,KACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,iBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UZwCqD,CY1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CY1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CYvCtD,UAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,OA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,QAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,QAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,QAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,UAlDT,oBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,WAxDV,qBAA8C,CAwDpC,WAxDV,qBAA8C,CA0DnC,WAWH,gBAAwC,CACzC,WAIC,gBAAwC,CAP1C,WAEE,qBAAwC,CACzC,WAIC,qBAAwC,CAP1C,WAEE,oBAAwC,CACzC,WAIC,oBAAwC,CAP1C,WAEE,mBAAwC,CACzC,WAIC,mBAAwC,CAP1C,WAEE,qBAAwC,CACzC,WAIC,qBAAwC,CAP1C,WAEE,mBAAwC,CACzC,WAIC,mBAAwC,CH1D9C,0BGUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CYvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CH3DL,0BGUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CYvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CH3DL,0BGUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CYvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CH3DL,2BGUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CYvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CH3DL,2BGUE,SACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,qBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UZwCqD,CY1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CY1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SZwCqD,CY1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eZwCqD,CYvCtD,cAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,WA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,YAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,YAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,YAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,cAlDT,aAA4B,CAwDlB,cAxDV,oBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,eAxDV,qBAA8C,CAwDpC,eAxDV,qBAA8C,CA0DnC,mBAWH,gBAAwC,CACzC,mBAIC,gBAAwC,CAP1C,mBAEE,qBAAwC,CACzC,mBAIC,qBAAwC,CAP1C,mBAEE,oBAAwC,CACzC,mBAIC,oBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,qBAAwC,CACzC,mBAIC,qBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CACzC,CCtHT,OACE,4BAAwC,CACxC,mCAAsD,CACtD,iCAA8D,CAC9D,uCAAwD,CACxD,gCAA4D,CAC5D,qCAAsD,CACtD,+BAA0D,CAC1D,sCAAoD,CAEpD,UAAW,CACX,kBZ4UW,CY3UX,aZCgB,CYAhB,kBZkoB6B,CYjoB7B,oBZPgB,CYPlB,yBAsBI,mBZqnBwB,CYpnBxB,mCAA8D,CAC9D,uBZ+bc,CY9bd,+DAA+B,CAA/B,uDAAyF,CAzB7F,aA6BI,sBAAuB,CA7B3B,aAiCI,qBAAsB,CAjC1B,0BAsCI,iCZmoBsC,CYloBvC,aASD,gBAAiB,CAClB,4BAWG,qBZilB4B,CYhlB7B,gCAeC,kBAAmC,CAFvC,kCAMM,kBZuYY,CYtYb,oCAQD,qBAAsB,CAJ1B,qCAQI,kBAAmB,CACpB,2CASC,gDAAsD,CACtD,mCAAyE,CAC1E,cAQD,+CAAsD,CACtD,kCAAuE,CACxE,8BAQG,8CAAsD,CACtD,iCAAqE,CC9HvE,eAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,iBAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,eAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,YAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,eAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,cAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,aAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbEQ,CaDR,oBAAwE,CAf1E,YAME,sBAAwC,CACxC,8BAAwD,CACxD,8BAA8D,CAC9D,6BAAsD,CACtD,6BAA4D,CAC5D,4BAAoD,CACpD,4BAA0D,CAE1D,UbRQ,CaSR,oBAAwE,CACzE,kBDuIG,eAAgB,CAChB,gCAAiC,CJ9EnC,6BI4EA,qBACE,eAAgB,CAChB,gCAAiC,CAClC,CJ/ED,6BI4EA,qBACE,eAAgB,CAChB,gCAAiC,CAClC,CJ/ED,6BI4EA,qBACE,eAAgB,CAChB,gCAAiC,CAClC,CJ/ED,8BI4EA,qBACE,eAAgB,CAChB,gCAAiC,CAClC,CJ/ED,8BI4EA,sBACE,eAAgB,CAChB,gCAAiC,CAClC,CEvJL,YACE,mBd2xB8B,CctxB/B,gBAKC,+Bb2N8D,Ca1N9D,kCb0N8D,CazN9D,eAAgB,Cf8SZ,iBAxE+B,CelOnC,edoiBoB,CcliBrB,mBAGC,6BbgN8D,Ca/M9D,gCb+M8D,CFqF1D,iBAxE+B,Ce1NpC,mBAGC,8Bb0M8D,CazM9D,iCbyM8D,CFqF1D,iBAxE+B,CepNpC,WC9BC,iBfmxB2B,CDzdvB,gBAxE+B,CgB9OnC,afKgB,CeJjB,cCLC,aAAc,CACd,UAAW,CACX,sBhBgsB0B,CDxYtB,cAxE+B,CiB7OnC,ehByiBsB,CgBxiBtB,ehB8iBoB,CgB7iBpB,ahBKgB,CgBJhB,qBhBLU,CgBMV,2BAA4B,CAC5B,wBhBHgB,CgBIhB,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CdGd,oBFmdkB,CiBtdhB,uCAAY,CAAZ,+BAAgC,CAIhC,wCDhBN,cCiBQ,uBAAY,CAAZ,eAAgB,CD8FvB,CA/GD,2BAqBI,eAAgB,CArBpB,0DAwBM,cAAe,CAxBrB,oBA8BI,ahBjBc,CgBkBd,qBhB3BQ,CgB4BR,oBf0KiC,CezKjC,SAAU,CAQR,qDhBTC,CgBSD,6ChBTQ,CgBhCd,2CAkDI,YAAiE,CAlDrE,yCAuDI,ahB7Cc,CgB+Cd,SAAU,CAzDd,gCAuDI,ahB7Cc,CgB+Cd,SAAU,CAzDd,oCAuDI,ahB7Cc,CgB+Cd,SAAU,CAzDd,qCAuDI,ahB7Cc,CgB+Cd,SAAU,CAzDd,2BAuDI,ahB7Cc,CgB+Cd,SAAU,CAzDd,+CAmEI,wBhB7Dc,CgBgEd,SAAU,CAtEd,oCA2EI,sBhBwnBwB,CgBvnBxB,uBhBunBwB,CgBtnBxB,yBhBsnBwB,CgBtnBxB,wBhBsnBwB,CgBrnBxB,ahBjEc,CkBbhB,wBlBMgB,CgB0Ed,mBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,cAAe,CACf,2BhBmYc,CgBlYd,eAAgB,CCzEd,uCAAY,CAAZ,+BAAgC,CAIhC,wCDhBN,oCCiBQ,uBAAY,CAAZ,eAAgB,CDsErB,CAvFH,yEA0FI,wBfqHiC,Ce/MrC,0CA8FI,sBhBqmBwB,CgBpmBxB,uBhBomBwB,CgBnmBxB,yBhBmmBwB,CgBnmBxB,wBhBmmBwB,CgBlmBxB,ahBpFc,CkBbhB,wBlBMgB,CgB6Fd,mBAAoB,CACpB,oBAAqB,CACrB,kBAAmB,CACnB,cAAe,CACf,2BhBgXc,CgB/Wd,eAAgB,CC5Fd,uCAAY,CAAZ,+BAAgC,CAIhC,wCDhBN,0CCiBQ,uBAAY,CAAZ,eAAgB,CDyFrB,CA1GH,+EA6GI,wBfkGiC,CejGlC,wBASD,aAAc,CACd,UAAW,CACX,iBAA2B,CAC3B,eAAgB,CAChB,ehB0boB,CgBzbpB,ahB/GgB,CgBgHhB,4BAA6B,CAC7B,wBAAyB,CACzB,kBAAmC,CATrC,gFAaI,eAAgB,CAChB,cAAe,CAChB,iBAWD,oCfsF8D,CerF9D,oBhB8jB4B,CDpZxB,iBAxE+B,CGpOjC,mBFodoB,CgBpVxB,uCAOI,oBhByjB0B,CgBxjB1B,qBhBwjB0B,CgBvjB1B,wBhBujBqB,CgBvjBrB,uBhBujB0B,CgBhkB9B,6CAaI,oBhBmjB0B,CgBljB1B,qBhBkjB0B,CgBjjB1B,wBhBijBqB,CgBjjBrB,uBhBijB0B,CgBhjB3B,iBAID,mCfmE8D,CelE9D,kBhB+iB2B,CDxZvB,iBAxE+B,CGpOjC,mBFqdoB,CgBlUxB,uCAOI,kBhB0iByB,CgBziBzB,mBhByiByB,CgBxiBzB,uBhBwiBqB,CgBxiBrB,sBhBwiByB,CgBjjB7B,6CAaI,kBhBoiByB,CgBniBzB,mBhBmiByB,CgBliBzB,uBhBkiBqB,CgBliBrB,sBhBkiByB,CgBjiB1B,sBAQC,qCf4C4D,Ce9ChE,yBAMI,oCfwC4D,Ce9ChE,yBAUI,mCfoC4D,CenC7D,oBAMD,UhB6oBqB,CgB5oBrB,WAAY,CACZ,ehBuf2B,CgB1f7B,mDAMI,cAAe,CANnB,uCAUI,YAAiE,CdnMjE,oBFmdkB,CgB1RtB,0CAeI,YAAiE,CdxMjE,oBFmdkB,CgBzQnB,aGvND,aAAc,CACd,UAAW,CACX,sCnB+rB0B,CmB7rB1B,qClBwP8D,CF6D1D,cAxE+B,CoB1OnC,enBsiBsB,CmBriBtB,enB2iBoB,CmB1iBpB,anBEgB,CmBDhB,qBnBRU,CmBSV,gPlBsHgF,CkBrHhF,2BAA4B,CAC5B,uCnBo5B2D,CmBn5B3D,yBnBo5B6B,CmBn5B7B,wBnBTgB,CEOd,oBFmdkB,CiBtdhB,uCAAgC,CAAhC,+BAAgC,CESpC,uBAAY,CAAZ,oBAAY,CAAZ,eAAgB,CFLZ,wCEfN,aFgBQ,uBAAY,CAAZ,eAAgB,CEkCvB,CAlDD,mBAuBI,oBlBkLiC,CkBjLjC,SAAU,CAKR,qDnBEC,CmBFD,6CnBEQ,CmB/Bd,0DAmCI,oBnB+pBwB,CmB9pBxB,qBAAsB,CApC1B,sBAyCI,wBnBpCc,CmBLlB,4BA+CI,iBAAkB,CAClB,yBnBpCc,CmBqCf,gBAID,kBnBwpB6B,CmBvpB7B,qBnBupB6B,CmBtpB7B,kBnBupB4B,CDpZxB,iBAxE+B,CGpOjC,mBFodoB,CmBxavB,gBAGC,iBnBopB4B,CmBnpB5B,oBnBmpB4B,CmBlpB5B,iBnBmpB2B,CDxZvB,iBAxE+B,CGpOjC,mBFqdoB,CmBjavB,YClEG,aAAc,CACd,iBpBy1BuD,CoBx1BvD,kBpBy1BmD,CoBx1BnD,qBpBy1B8B,CoB71BlC,8BAOQ,UAAW,CACX,kBAA2C,CAC9C,kBAID,SpB60BwB,CoB50BxB,UpB40BwB,CoB30BxB,gBAA8D,CAC9D,kBAAmB,CACnB,qBpBbQ,CoBcR,2BAA4B,CAC5B,0BAA2B,CAC3B,uBAAwB,CACxB,iCpBPQ,CoBQR,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAChB,gCAAc,CAAd,kBAAmB,CAXvB,mClBGI,mBFu1BkC,CoB11BtC,gCAoBQ,iBpBu0B4B,CoB31BpC,yBAwBQ,8BpB8zByB,CoB9zBzB,sBpB8zBwC,CoBt1BhD,wBA4BQ,oBnBkK6B,CmBjK7B,SAAU,CACV,qDpBVD,CoBUC,6CpBVM,CoBpBd,0BAkCQ,wBpBdM,CoBeN,oBpBfM,CoBpBd,2CA4CgB,8OnB2EkE,CmBvHlF,wCAuDgB,sJnBgEkE,CmBvHlF,iDA6DQ,wBpBzCM,CoB0CN,oBpB1CM,CoBkDF,wOnBiDsE,CmBvHlF,2BA2EQ,mBAAoB,CACpB,mBAAY,CAAZ,WAAY,CACZ,UpB6xB8B,CoB12BtC,+FAqFY,UpBqxB0B,CoBpxB7B,aAcL,kBpBgxB+C,CoBjxBnD,+BAIQ,SpB4wBe,CoB3wBf,kBAA4C,CAC5C,oKnBc0E,CmBb1E,+BAAgC,ClBvGpC,iBFg3BmB,CiBn3BjB,uCAAY,CAAZ,+BAAgC,CAIhC,wCG+FN,+BH9FQ,uBAAY,CAAZ,eAAgB,CGyHnB,CA3BL,qCAYY,yJnBQsE,CmBpBlF,uCAgBY,gCpB2wBkC,CoBnwB9B,sJnBJkE,CmBMzE,mBAKL,oBAAqB,CACrB,iBpB2uB+B,CoB1uBlC,WAGG,iBAAkB,CAClB,qBAAsB,CACtB,mBAAoB,CAHxB,mDAQY,mBAAoB,CACpB,mBAAY,CAAZ,WAAY,CACZ,WpBylBc,CoBxlBjB,YC1JP,UAAW,CACX,apB+N2B,CoB9N3B,SAAU,CACV,4BAA6B,CAC7B,uBAAY,CAAZ,oBAAY,CAAZ,eAAgB,CALlB,kBAQI,SAAU,CARd,wCAaM,oErBiBC,CqBjBD,4DrBiBQ,CqB9Bd,oCAiBM,4DrBaQ,CqB9Bd,8BAsBI,QAAS,CAtBb,kCA0BI,UrB86ByB,CqB76BzB,WrB66ByB,CqB56BzB,kBAAsE,CH9BxE,wBlBgCY,CqBAV,QrB66BuB,CE97BvB,kBF+7BiC,CiBl8B/B,uCAAgC,CAAhC,+BAAgC,CIwBlC,uBAAY,CAAZ,eAAgB,CJpBd,wCIdN,kCJeQ,uBAAY,CAAZ,eAAgB,CIwBrB,CAvCH,yCHFE,wBjB0MmC,CoBxMrC,2CA0CI,UrBu5ByB,CqBt5BzB,YrBu5B2B,CqBt5B3B,iBAAkB,CAClB,crBs5B6B,CqBr5B7B,wBrBzCc,CqB0Cd,wBAAyB,CnBlCzB,kBFw7BiC,CqBr8BrC,8BAqDI,UrBm5ByB,CqBl5BzB,WrBk5ByB,CkB18B3B,wBlBgCY,CqB0BV,QrBm5BuB,CE97BvB,kBF+7BiC,CiBl8B/B,oCAAgC,CAAhC,+BAAgC,CIkDlC,oBAAY,CAAZ,eAAgB,CJ9Cd,wCIdN,8BJeQ,oBAAY,CAAZ,eAAgB,CIkDrB,CAjEH,qCHFE,wBjB0MmC,CoBxMrC,8BAoEI,UrB63ByB,CqB53BzB,YrB63B2B,CqB53B3B,iBAAkB,CAClB,crB43B6B,CqB33B7B,wBrBnEc,CqBoEd,wBAAyB,CnB5DzB,kBFw7BiC,CqBr8BrC,qBA+EI,mBAAoB,CA/ExB,2CAkFM,wBrB3EY,CqBPlB,uCAsFM,wBrB/EY,CqBgFb,eC5FD,iBAAkB,CADtB,yDAKQ,yBrBqOwD,CqBpOxD,gBtBg+BwB,CsBt+BhC,qBAUQ,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,WAAY,CACZ,mBtByrBoB,CsBxrBpB,mBAAoB,CACpB,4BAA6C,CAC7C,4BAAqB,CAArB,oBAAqB,CLDvB,uCAAY,CAAZ,+BAAgC,CAIhC,wCKpBN,qBLqBQ,uBAAY,CAAZ,eAAgB,CKFnB,CAnBL,6BAuBQ,mBtBgrBoB,CsBvsB5B,wDA0BY,iBAAkB,CA1B9B,+CA0BY,iBAAkB,CA1B9B,mDA0BY,iBAAkB,CA1B9B,oDA0BY,iBAAkB,CA1B9B,0CA0BY,iBAAkB,CA1B9B,0DA+BY,oBtB08B4B,CsBz8B5B,sBtB08B2B,CsB1+BvC,yDA+BY,oBtB08B4B,CsBz8B5B,sBtB08B2B,CsB1+BvC,wFA+BY,oBtB08B4B,CsBz8B5B,sBtB08B2B,CsB1+BvC,8CAqCY,oBtBo8B4B,CsBn8B5B,sBtBo8B2B,CsB1+BvC,4BA2CQ,oBtB87BgC,CsB77BhC,sBtB87B+B,CsB1+BvC,kEAmDY,WtBw7BqB,CsBv7BrB,6DtBw7BoE,CsB5+BhF,iEAmDY,WtBw7BqB,CsBv7BrB,6DtBw7BoE,CsB5+BhF,4IAmDY,WtBw7BqB,CsBv7BrB,qEtBw7BkD,CsBx7BlD,6DtBw7BoE,CsB5+BhF,sDA2DY,WtBg7BqB,CsB/6BrB,qEtBg7BkD,CsBh7BlD,6DtBg7BoE,CsB/6BvE,aCxDP,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,yBAAoB,CAApB,sBAAoB,CAApB,mBAAoB,CACpB,UAAW,CALb,qDASI,iBAAkB,CAClB,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,QAAS,CACT,WAAY,CAZhB,iEAkBI,SAAU,CAlBd,kBAyBI,iBAAkB,CAClB,SAAU,CA1Bd,wBA6BM,SAAU,CACX,kBAWH,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,sBvBwpB0B,CDxYtB,cAxE+B,CwBtMnC,evBkgBsB,CuBjgBtB,evBugBoB,CuBtgBpB,avBlCgB,CuBmChB,iBAAkB,CAClB,kBAAmB,CACnB,wBvB5CgB,CuB6ChB,wBvB3CgB,CEOd,oBFmdkB,CuB7arB,kHAYC,kBvBkpB2B,CDxZvB,iBAxE+B,CGpOjC,mBFqdoB,CuBhavB,kHAMC,oBvBqoB4B,CDpZxB,iBAxE+B,CGpOjC,mBFodoB,CuBtZvB,0DAIC,kBAAsE,CACvE,qKrBnDG,yBqBiE8B,CrBhE9B,4BqBgE8B,CAJlC,4JrB7DI,yBqBwE8B,CrBvE9B,4BqBuE8B,CAXlC,0IAqBI,gBvBsWc,CE1ad,wBqBqE8B,CrBpE9B,2BqBoE8B,CAF4B,gBC1F1D,YAAa,CACb,UAAW,CACX,iBxB4vByB,CDzdvB,gBAxE+B,CyBxNjC,axBYW,CwBXZ,eAGC,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,YAAa,CACb,cAAe,CACf,oBxBsuC4B,CwBruC5B,gBAAiB,CzBsRf,iBAxE+B,CyB3MjC,UxBpCQ,CwBqCR,oCxBFW,CExBX,oBFmdkB,CwBhelB,sIA8CE,aAAc,CA9ChB,0DAoDE,oBxBfS,CwBkBP,kCvB6KwD,CuB5KxD,2PvByE0E,CuBxE1E,2BAA4B,CAC5B,wDAA6D,CAC7D,2DvByKwD,CuBpO5D,sEA+DI,oBxB1BO,CwB2BP,oDxB3BA,CwB2BA,4CxB3BO,CwBrCX,0EAyEI,kCvB2JwD,CuB1JxD,6EvB0JwD,CuBpO5D,wDAiFE,oBxB5CS,CwBrCX,4NAsFM,sBxBg1ByF,CwB/0BzF,2dvB0CwE,CuBzCxE,4DxBi0BgD,CwBh0BhD,qEvB2IsD,CuBpO5D,oEA8FI,oBxBzDO,CwB0DP,oDxB1DA,CwB0DA,4CxB1DO,CwBrCX,kEAsGE,oBxBjES,CwBrCX,kFAyGI,wBxBpEO,CwBrCX,8EA6GI,oDxBxEA,CwBwEA,4CxBxEO,CwBrCX,0GAiHI,axB5EO,CwB6ER,uDAKD,gBAAiB,CAvHnB,sKA+HI,SAAU,CA/Hd,8LAoII,SAAU,CACX,kBAjHH,YAAa,CACb,UAAW,CACX,iBxB4vByB,CDzdvB,gBAxE+B,CyBxNjC,axBSS,CwBRV,iBAGC,iBAAkB,CAClB,QAAS,CACT,SAAU,CACV,YAAa,CACb,cAAe,CACf,oBxBsuC4B,CwBruC5B,gBAAiB,CzBsRf,iBAxE+B,CyB3MjC,UxBpCQ,CwBqCR,oCxBLS,CErBT,oBFmdkB,CwBhelB,sJA8CE,aAAc,CA9ChB,8DAoDE,oBxBlBO,CwBqBL,kCvB6KwD,CuB5KxD,2UvByE0E,CuBxE1E,2BAA4B,CAC5B,wDAA6D,CAC7D,2DvByKwD,CuBpO5D,0EA+DI,oBxB7BK,CwB8BL,oDxB9BF,CwB8BE,4CxB9BK,CwBlCT,8EAyEI,kCvB2JwD,CuB1JxD,6EvB0JwD,CuBpO5D,4DAiFE,oBxB/CO,CwBlCT,oOAsFM,sBxBg1ByF,CwB/0BzF,2iBvB0CwE,CuBzCxE,4DxBi0BgD,CwBh0BhD,qEvB2IsD,CuBpO5D,wEA8FI,oBxB5DK,CwB6DL,oDxB7DF,CwB6DE,4CxB7DK,CwBlCT,sEAsGE,oBxBpEO,CwBlCT,sFAyGI,wBxBvEK,CwBlCT,kFA6GI,oDxB3EF,CwB2EE,4CxB3EK,CwBlCT,8GAiHI,axB/EK,CwBgFN,yDAKD,gBAAiB,CAvHnB,8KAiII,SAAU,CAjId,sMAoII,SAAU,CACX,KCtIL,oBAAqB,CAErB,ezB4iBsB,CyB3iBtB,ezBijBoB,CyBhjBpB,azBQgB,CyBPhB,iBAAkB,CAClB,oBAAsD,CAEtD,qBAAsB,CACtB,cAA2C,CAC3C,wBAAiB,CAAjB,qBAAiB,CAAjB,oBAAiB,CAAjB,gBAAiB,CACjB,4BAA6B,CAC7B,4BAA2C,CC8G3C,sB1BwkB0B,CDxYtB,cAxE+B,CGpOjC,oBFmdkB,CiBtdhB,uCAAY,CAAZ,+BAAgC,CAIhC,wCQhBN,KRiBQ,uBAAY,CAAZ,eAAgB,CQ6BvB,CA9CD,WAkBI,azBLc,CyBOf,iCAIC,SAAU,CACV,qDzBOG,CyBPH,6CzBOU,CyBhCd,mDA0CI,mBAAoB,CACpB,WzB0sBsB,CyBxsBvB,aC7BD,U1BZU,CkBJV,wBlBgCY,C0BdZ,oB1BcY,C0BXZ,mBACE,U1BlBQ,CkBJV,wBjB+MmC,CyBvLjC,oBzBuLiC,CyBtLlC,iDAIC,U1BzBQ,CkBJV,wBjB+MmC,CyBhLjC,oBzBgLiC,CyB3K/B,oDAAwC,CAAxC,4CAAiE,CAEpE,0IAOC,U1BzCQ,C0B0CR,wBzBiKiC,CyB9JjC,oBzB8JiC,CyBvKnC,wKAgBM,oDAAwC,CAAxC,4CAAiE,CAEpE,4CAKD,U1B3DQ,C0B4DR,wB1BhCU,C0BmCV,oB1BnCU,C0BoCX,eApDD,U1BZU,CkBJV,wBlBUgB,C0BQhB,oB1BRgB,C0BWhB,qBACE,U1BlBQ,CkBJV,wBjB+MmC,CyBvLjC,oBzBuLiC,CyBtLlC,qDAIC,U1BzBQ,CkBJV,wBjB+MmC,CyBhLjC,oBzBgLiC,CyB3K/B,qDAAwC,CAAxC,6CAAiE,CAEpE,oJAOC,U1BzCQ,C0B0CR,wBzBiKiC,CyB9JjC,oBzB8JiC,CyBvKnC,kLAgBM,qDAAwC,CAAxC,6CAAiE,CAEpE,gDAKD,U1B3DQ,C0B4DR,wB1BtDc,C0ByDd,oB1BzDc,C0B0Df,aApDD,U1BZU,CkBJV,wBlBuCa,C0BrBb,oB1BqBa,C0BlBb,mBACE,U1BlBQ,CkBJV,wBjB+MmC,CyBvLjC,oBzBuLiC,CyBtLlC,iDAIC,U1BzBQ,CkBJV,wBjB+MmC,CyBhLjC,oBzBgLiC,CyB3K/B,oDAAwC,CAAxC,4CAAiE,CAEpE,0IAOC,U1BzCQ,C0B0CR,wBzBiKiC,CyB9JjC,oBzB8JiC,CyBvKnC,wKAgBM,oDAAwC,CAAxC,4CAAiE,CAEpE,4CAKD,U1B3DQ,C0B4DR,wB1BzBW,C0B4BX,oB1B5BW,C0B6BZ,UApDD,U1BFU,CkBdV,wBlByCY,C0BvBZ,oB1BuBY,C0BpBZ,gBACE,U1BRQ,CkBdV,wBjB0MmC,CyBlLjC,oBzBkLiC,CyBjLlC,2CAIC,U1BfQ,CkBdV,wBjB0MmC,CyB3KjC,oBzB2KiC,CyBtK/B,oDAAwC,CAAxC,4CAAiE,CAEpE,2HAOC,U1B/BQ,C0BgCR,wBzB4JiC,CyBzJjC,oBzByJiC,CyBlKnC,yJAgBM,oDAAwC,CAAxC,4CAAiE,CAEpE,sCAKD,U1BjDQ,C0BkDR,wB1BvBU,C0B0BV,oB1B1BU,C0B2BX,aApDD,U1BFU,CkBdV,wBlBsCc,C0BpBd,oB1BoBc,C0BjBd,mBACE,U1BRQ,CkBdV,wBjB0MmC,CyBlLjC,oBzBkLiC,CyBjLlC,iDAIC,U1BfQ,CkBdV,wBjB0MmC,CyB3KjC,oBzB2KiC,CyBtK/B,mDAAwC,CAAxC,2CAAiE,CAEpE,0IAOC,U1B/BQ,C0BgCR,wBzB4JiC,CyBzJjC,oBzByJiC,CyBlKnC,wKAgBM,mDAAwC,CAAxC,2CAAiE,CAEpE,4CAKD,U1BjDQ,C0BkDR,wB1B1BY,C0B6BZ,oB1B7BY,C0B8Bb,YApDD,U1BZU,CkBJV,wBlBoCW,C0BlBX,oB1BkBW,C0BfX,kBACE,U1BlBQ,CkBJV,wBjB+MmC,CyBvLjC,oBzBuLiC,CyBtLlC,+CAIC,U1BzBQ,CkBJV,wBjB+MmC,CyBhLjC,oBzBgLiC,CyB3K/B,mDAAwC,CAAxC,2CAAiE,CAEpE,qIAOC,U1BzCQ,C0B0CR,wBzBiKiC,CyB9JjC,oBzB8JiC,CyBvKnC,mKAgBM,mDAAwC,CAAxC,2CAAiE,CAEpE,0CAKD,U1B3DQ,C0B4DR,wB1B5BS,C0B+BT,oB1B/BS,C0BgCV,WApDD,U1BFU,CkBdV,wBlBKgB,C0BahB,oB1BbgB,C0BgBhB,iBACE,U1BRQ,CkBdV,wBjB0MmC,CyBlLjC,oBzBkLiC,CyBjLlC,6CAIC,U1BfQ,CkBdV,wBjB0MmC,CyB3KjC,oBzB2KiC,CyBtK/B,qDAAwC,CAAxC,6CAAiE,CAEpE,gIAOC,U1B/BQ,C0BgCR,wBzB4JiC,CyBzJjC,oBzByJiC,CyBlKnC,8JAgBM,qDAAwC,CAAxC,6CAAiE,CAEpE,wCAKD,U1BjDQ,C0BkDR,wB1B3Dc,C0B8Dd,oB1B9Dc,C0B+Df,UApDD,U1BZU,CkBJV,wBlBagB,C0BKhB,oB1BLgB,C0BQhB,gBACE,U1BlBQ,CkBJV,wBjB+MmC,CyBvLjC,oBzBuLiC,CyBtLlC,2CAIC,U1BzBQ,CkBJV,wBjB+MmC,CyBhLjC,oBzBgLiC,CyB3K/B,kDAAwC,CAAxC,0CAAiE,CAEpE,2HAOC,U1BzCQ,C0B0CR,wBzBiKiC,CyB9JjC,oBzB8JiC,CyBvKnC,yJAgBM,kDAAwC,CAAxC,0CAAiE,CAEpE,sCAKD,U1B3DQ,C0B4DR,wB1BnDc,C0BsDd,oB1BtDc,C0BuDf,qBAYD,a1BhDY,C0BiDZ,oB1BjDY,C0BmDZ,2BACE,U1BhFQ,C0BiFR,wB1BrDU,C0BsDV,oB1BtDU,C0BuDX,iEAIC,oD1B3DG,C0B2DH,4C1B3DU,C0B4DX,iLAOC,U1B/FQ,C0BgGR,wB1BpEU,C0BqEV,oB1BrEU,C0B8DZ,+MAcM,oD1B5ED,C0B4EC,4C1B5EM,C0B8ET,4DAKD,a1BnFU,C0BoFV,4BAA6B,CAC9B,uBArCD,a1BtEgB,C0BuEhB,oB1BvEgB,C0ByEhB,6BACE,U1BhFQ,C0BiFR,wB1B3Ec,C0B4Ed,oB1B5Ec,C0B6Ef,qEAIC,qD1BjFO,C0BiFP,6C1BjFc,C0BkFf,2LAOC,U1B/FQ,C0BgGR,wB1B1Fc,C0B2Fd,oB1B3Fc,C0BoFhB,yNAcM,qD1BlGG,C0BkGH,6C1BlGU,C0BoGb,gEAKD,a1BzGc,C0B0Gd,4BAA6B,CAC9B,qBArCD,a1BzCa,C0B0Cb,oB1B1Ca,C0B4Cb,2BACE,U1BhFQ,C0BiFR,wB1B9CW,C0B+CX,oB1B/CW,C0BgDZ,iEAIC,mD1BpDI,C0BoDJ,2C1BpDW,C0BqDZ,iLAOC,U1B/FQ,C0BgGR,wB1B7DW,C0B8DX,oB1B9DW,C0BuDb,+MAcM,mD1BrEA,C0BqEA,2C1BrEO,C0BuEV,4DAKD,a1B5EW,C0B6EX,4BAA6B,CAC9B,kBArCD,a1BvCY,C0BwCZ,oB1BxCY,C0B0CZ,wBACE,U1BtEQ,C0BuER,wB1B5CU,C0B6CV,oB1B7CU,C0B8CX,2DAIC,oD1BlDG,C0BkDH,4C1BlDU,C0BmDX,kKAOC,U1BrFQ,C0BsFR,wB1B3DU,C0B4DV,oB1B5DU,C0BqDZ,gMAcM,oD1BnED,C0BmEC,4C1BnEM,C0BqET,sDAKD,a1B1EU,C0B2EV,4BAA6B,CAC9B,qBArCD,a1B1Cc,C0B2Cd,oB1B3Cc,C0B6Cd,2BACE,U1BtEQ,C0BuER,wB1B/CY,C0BgDZ,oB1BhDY,C0BiDb,iEAIC,mD1BrDK,C0BqDL,2C1BrDY,C0BsDb,iLAOC,U1BrFQ,C0BsFR,wB1B9DY,C0B+DZ,oB1B/DY,C0BwDd,+MAcM,mD1BtEC,C0BsED,2C1BtEQ,C0BwEX,4DAKD,a1B7EY,C0B8EZ,4BAA6B,CAC9B,oBArCD,a1B5CW,C0B6CX,oB1B7CW,C0B+CX,0BACE,U1BhFQ,C0BiFR,wB1BjDS,C0BkDT,oB1BlDS,C0BmDV,+DAIC,mD1BvDE,C0BuDF,2C1BvDS,C0BwDV,4KAOC,U1B/FQ,C0BgGR,wB1BhES,C0BiET,oB1BjES,C0B0DX,0MAcM,mD1BxEF,C0BwEE,2C1BxEK,C0B0ER,0DAKD,a1B/ES,C0BgFT,4BAA6B,CAC9B,mBArCD,a1B3EgB,C0B4EhB,oB1B5EgB,C0B8EhB,yBACE,U1BtEQ,C0BuER,wB1BhFc,C0BiFd,oB1BjFc,C0BkFf,6DAIC,qD1BtFO,C0BsFP,6C1BtFc,C0BuFf,uKAOC,U1BrFQ,C0BsFR,wB1B/Fc,C0BgGd,oB1BhGc,C0ByFhB,qMAcM,qD1BvGG,C0BuGH,6C1BvGU,C0ByGb,wDAKD,a1B9Gc,C0B+Gd,4BAA6B,CAC9B,kBArCD,a1BnEgB,C0BoEhB,oB1BpEgB,C0BsEhB,wBACE,U1BhFQ,C0BiFR,wB1BxEc,C0ByEd,oB1BzEc,C0B0Ef,2DAIC,kD1B9EO,C0B8EP,0C1B9Ec,C0B+Ef,kKAOC,U1B/FQ,C0BgGR,wB1BvFc,C0BwFd,oB1BxFc,C0BiFhB,gMAcM,kD1B/FG,C0B+FH,0C1B/FU,C0BiGb,sDAKD,a1BtGc,C0BuGd,4BAA6B,CAC9B,UDxCD,ezBkesB,CyBjetB,azB9CY,CyB+CZ,yBzBoUyB,CyBvU3B,gBAMI,axB6HiC,CwBnIrC,sCAgBI,azBlFc,CyBmFf,2BC8BD,kB1BwlB2B,CDxZvB,iBAxE+B,CGpOjC,mBFqdoB,CyB3XvB,2BCkBC,oB1BolB4B,CDpZxB,iBAxE+B,CGpOjC,mBFodoB,CyBtXvB,MRjGK,uCAAY,CAAZ,+BAAgC,CAIhC,wCUpBN,MVqBQ,uBAAY,CAAZ,eAAgB,CUfvB,CAND,iBAII,SAAU,CACX,qBAMC,YAAa,CACd,YAID,QAAS,CACT,eAAgB,CVDZ,uCAAY,CAAZ,+BAAgC,CAIhC,wCULN,YVMQ,uBAAY,CAAZ,eAAgB,CUIvB,CAVD,gCAMI,OAAQ,CACR,WAAY,CVNV,uCAAY,CAAZ,+BAAgC,CAIhC,wCULN,gCVMQ,uBAAY,CAAZ,eAAgB,CUGrB,CCvBH,sCAIE,iBAAkB,CL6FG,iBKzFrB,kBAAmB,CCqBjB,wBACE,oBAAqB,CACrB,kB7Byd4B,C6Bxd5B,qB7BudmC,C6BtdnC,UAAW,CAhCf,qBAA8B,CAC9B,mCAA4C,CAC5C,eAAgB,CAChB,kCAA2C,CAqCxC,8BAiBC,aAAc,CACf,eD3CH,iBAAkB,CAClB,Y5B2/BoB,C4B1/BpB,YAAa,CACb,e5BmlCwB,C4BllCxB,e5BmlCoB,C4BllCpB,QAAS,C7BySL,cAxE+B,C6B/NnC,a5BPgB,C4BQhB,eAAgB,CAChB,eAAgB,CAChB,qB5BnBU,C4BoBV,2BAA4B,CAC5B,iC5BXU,CECR,oBFmdkB,C4BtdtB,+BAkBI,QAAS,CACT,MAAO,CACP,kB5BskCqB,C4BrkCtB,qBAYG,oBAAc,CADhB,qCAII,UAAW,CACX,MAAO,CACR,mBAID,kBAAc,CADhB,mCAII,OAAQ,CACR,SAAU,CpBCd,0BoBfA,wBACE,oBAAc,CADhB,wCAII,UAAW,CACX,MAAO,CACR,sBAID,kBAAc,CADhB,sCAII,OAAQ,CACR,SAAU,CACX,CpBAH,0BoBfA,wBACE,oBAAc,CADhB,wCAII,UAAW,CACX,MAAO,CACR,sBAID,kBAAc,CADhB,sCAII,OAAQ,CACR,SAAU,CACX,CpBAH,0BoBfA,wBACE,oBAAc,CADhB,wCAII,UAAW,CACX,MAAO,CACR,sBAID,kBAAc,CADhB,sCAII,OAAQ,CACR,SAAU,CACX,CpBAH,2BoBfA,wBACE,oBAAc,CADhB,wCAII,UAAW,CACX,MAAO,CACR,sBAID,kBAAc,CADhB,sCAII,OAAQ,CACR,SAAU,CACX,CpBAH,2BoBfA,yBACE,oBAAc,CADhB,yCAII,UAAW,CACX,MAAO,CACR,uBAID,kBAAc,CADhB,uCAII,OAAQ,CACR,SAAU,CACX,CASP,uCAEI,QAAS,CACT,WAAY,CACZ,YAAa,CACb,qB5B6hCqB,C6B5kCrB,gCACE,oBAAqB,CACrB,kB7Byd4B,C6Bxd5B,qB7BudmC,C6BtdnC,UAAW,CAzBf,YAAa,CACb,mCAA4C,CAC5C,wBAAiC,CACjC,kCAA2C,CA8BxC,sCAiBC,aAAc,CACf,wCD2BD,KAAM,CACN,UAAW,CACX,SAAU,CACV,YAAa,CACb,mB5B+gCqB,C6B5kCrB,iCACE,oBAAqB,CACrB,kB7Byd4B,C6Bxd5B,qB7BudmC,C6BtdnC,UAAW,CAlBf,iCAA0C,CAC1C,cAAe,CACf,oCAA6C,CAC7C,sBAA+B,CAuB5B,uCAiBC,aAAc,CA7BhB,iCDoEE,gBAAiB,CAClB,0CAMD,KAAM,CACN,UAAW,CACX,SAAU,CACV,YAAa,CACb,oB5B6/BqB,C6B5kCrB,mCACE,oBAAqB,CACrB,kB7Byd4B,C6Bxd5B,qB7BudmC,C6BtdnC,UAAW,CAJb,mCAgBI,YAAa,CACd,oCAGC,oBAAqB,CACrB,mB7Bsc0B,C6Brc1B,qB7BociC,C6BncjC,UAAW,CA9BjB,iCAA0C,CAC1C,uBAAgC,CAChC,oCAA6C,CA8BxC,yCAID,aAAc,CAVd,oCDmEA,gBAAiB,CAClB,kBAOH,QAAS,CACT,cAAoC,CACpC,eAAgB,CAChB,qC5B7GU,C4B8GX,eAMC,aAAc,CACd,UAAW,CACX,mB5BmNW,C4BlNX,UAAW,CACX,e5ByasB,C4BxatB,a5B1HgB,C4B2HhB,kBAAmB,CACnB,oBAAsD,CACtD,kBAAmB,CACnB,4BAA6B,CAC7B,QAAS,CAXX,0CA2BI,a3BmDiC,CiB/MnC,wBlBMgB,C4B2HlB,4CAkCI,U5B/JQ,C4BgKR,oBAAqB,CVpKvB,wBlBgCY,C4BiGd,gDAyCI,a5BjKc,C4BkKd,mBAAoB,CACpB,4BAA6B,CAG9B,oBAID,aAAc,CACf,iBAIC,aAAc,CACd,kB5B8JW,C4B7JX,eAAgB,C7BiIZ,iBAxE+B,C6BvDnC,a5BlLgB,C4BmLhB,kBAAmB,CACpB,oBAIC,aAAc,CACd,mB5BoJW,C4BnJX,a5BvLgB,C4BwLjB,oBAIC,a5BlMgB,C4BmMhB,wB5B9LgB,C4B+LhB,6B5B7LU,C4B0LZ,mCAOI,a5BxMc,C4BiMlB,kFAWM,U5B/MM,CkBJV,uClBIU,C4BoMZ,oFAiBM,U5BrNM,CkBJV,wBlBgCY,C4BwKd,wFAuBM,a5BtNY,C4B+LlB,sCA4BI,6B5BtNQ,C4B0LZ,wCAgCI,a5BjOc,C4BiMlB,qCAoCI,a5BnOc,C4BoOf,+BE9OD,iBAAkB,CAClB,0BAAoB,CAApB,0BAAoB,CAApB,mBAAoB,CACpB,qBAAsB,CAJxB,yCAOI,iBAAkB,CAClB,kBAAM,CAAN,iBAAM,CAAN,aAAc,CARlB,kXAmBI,SAAU,CACX,aAKD,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CAH7B,0BAMI,UAAW,CACZ,0EAQC,gB9Bmbc,C8BxblB,mG5BAI,yB4BW4B,C5BV5B,4B4BU4B,CAXhC,6G5BcI,wB4BO8B,C5BN9B,2B4BM8B,CAC/B,uBAqBD,sBAAmC,CACnC,qBAAkC,CAFpC,2GAOI,aAAc,CACf,0CAGC,cAAe,CAChB,yEAID,qBAAsC,CACtC,oBAAqC,CACtC,yEAGC,oBAAsC,CACtC,mBAAqC,CACtC,oBAoBC,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CAHzB,wDAOI,UAAW,CAPf,4FAYI,e9BwVc,C8BpWlB,qH5B7EI,4B4B+F+B,C5B9F/B,2B4B8F+B,CAlBnC,sF5B3FI,wB4BkH4B,C5BjH5B,yB4BiH4B,CAC7B,KCzID,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,eAAgB,CACjB,UAGC,aAAc,CACd,kB/B8gCuB,C+B3gCvB,a/BkBY,C+BjBZ,oBAAsD,CdHlD,uCAAY,CAAZ,+BAAgC,CAIhC,wCcPN,UdQQ,uBAAY,CAAZ,eAAgB,CcavB,CArBD,gCAWI,a9B2LiC,C8BtMrC,mBAiBI,a/BhBc,C+BiBd,mBAAoB,CACpB,cAAe,CAChB,UAQD,+B/B9BgB,C+B6BlB,oBAII,kB/B+ac,C+B9ad,eAAgB,CAChB,4BAAgD,C7BlBhD,6BF0ckB,CEzclB,8BFyckB,C+B9btB,oDAWM,oC/BxCY,C+B0CZ,iBAAkB,CAbxB,6BAiBM,a/B3CY,C+B4CZ,4BAA6B,CAC7B,wBAAyB,CAnB/B,8DAyBI,a/BlDc,C+BmDd,qB/B1DQ,C+B2DR,iC/B3DQ,C+BgCZ,yBAgCI,e/BmZc,CE/bd,wB6B8C4B,C7B7C5B,yB6B6C4B,CAC7B,qBAUC,eAAgB,CAChB,QAAS,C7BnET,oBFmdkB,C+BnZtB,uDASI,U/BpFQ,CkBJV,wBlBgCY,C+B0DX,wCAYC,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,iBAAkB,CACnB,kDAOC,yBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,iBAAkB,CACnB,iEAMC,UAAW,CACZ,uBAUC,YAAa,CAFjB,qBAMI,aAAc,CACf,QC3HD,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,iBhCkiC6B,CgChiC7B,oBhCgiC6B,CgCxiC/B,2JAgBI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,qBAAkB,CAAlB,iBAAkB,CAClB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,wBAAiB,CAAjB,qBAAiB,CAAjB,6BAA8B,CAC/B,cAqBD,oBhCwgCqE,CgCvgCrE,uBhCugCqE,CgCtgCrE,iBhCugC4B,CDnwBxB,iBAxE+B,CiC1LnC,oBAAsD,CACtD,kBAAmB,CAMpB,YAQC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,cAAe,CACf,eAAgB,CAChB,eAAgB,CALlB,sBAQI,eAAgB,CAChB,cAAe,CATnB,2BAaI,eAAgB,CACjB,aASD,iBhC07BwB,CgCz7BxB,oBhCy7BwB,CgCx7BzB,iBAYC,4BAAgB,CAAhB,eAAgB,CAChB,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CAGZ,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CACpB,gBAIC,qBhC48B+B,CDtwB3B,iBAxE+B,CiC5HnC,aAAc,CACd,4BAA6B,CAC7B,4BAAuC,C9B1GrC,oBFmdkB,CiBtdhB,uCAAY,CAAZ,+BAAgC,CAIhC,wCeoGN,gBfnGQ,uBAAY,CAAZ,eAAgB,CeqHvB,CAlBD,sBAUI,oBAAqB,CAVzB,sBAcI,oBAAqB,CACrB,SAAU,CACV,+BhCokBoB,CgCpkBpB,uBhCokB0B,CgCnkB3B,qBAMD,oBAAqB,CACrB,WAAY,CACZ,YAAa,CACb,qBAAsB,CACtB,2BAA4B,CAC5B,0BAA2B,CAC3B,oBAAqB,CACtB,mBAGC,wCAAwE,CACxE,eAAgB,CxB3Fd,0BwBuGA,kBAEI,oBAAiB,CAAjB,gBAAiB,CACjB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CAH9B,8BAMK,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAaO,mBhCw4BqB,CgCv4BrB,kBhCu4BqB,CgCr5B5B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,4BAAY,CAAZ,eAAgB,CAxBrB,kCA4BK,YAAa,CA5BlB,oCAgCK,YAAa,CAhClB,6BAoCK,gBAAiB,CACjB,QAAS,CACT,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,6BAA8B,CAC9B,4BAA6B,CAC7B,cAAe,CACf,aAAc,CfjMlB,uCAAgC,CAAhC,+BAAgC,CemM5B,sBAAW,CAAX,cAAe,CA7CpB,qEAkDK,WAAY,CACZ,YAAa,CACb,eAAgB,CApDrB,kCAwDK,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,SAAU,CACV,kBAAmB,CACpB,CxBnKL,0BwBuGA,kBAEI,oBAAiB,CAAjB,gBAAiB,CACjB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CAH9B,8BAMK,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAaO,mBhCw4BqB,CgCv4BrB,kBhCu4BqB,CgCr5B5B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,4BAAY,CAAZ,eAAgB,CAxBrB,kCA4BK,YAAa,CA5BlB,oCAgCK,YAAa,CAhClB,6BAoCK,gBAAiB,CACjB,QAAS,CACT,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,6BAA8B,CAC9B,4BAA6B,CAC7B,cAAe,CACf,aAAc,CfjMlB,uCAAgC,CAAhC,+BAAgC,CemM5B,sBAAW,CAAX,cAAe,CA7CpB,qEAkDK,WAAY,CACZ,YAAa,CACb,eAAgB,CApDrB,kCAwDK,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,SAAU,CACV,kBAAmB,CACpB,CxBnKL,0BwBuGA,kBAEI,oBAAiB,CAAjB,gBAAiB,CACjB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CAH9B,8BAMK,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAaO,mBhCw4BqB,CgCv4BrB,kBhCu4BqB,CgCr5B5B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,4BAAY,CAAZ,eAAgB,CAxBrB,kCA4BK,YAAa,CA5BlB,oCAgCK,YAAa,CAhClB,6BAoCK,gBAAiB,CACjB,QAAS,CACT,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,6BAA8B,CAC9B,4BAA6B,CAC7B,cAAe,CACf,aAAc,CfjMlB,uCAAgC,CAAhC,+BAAgC,CemM5B,sBAAW,CAAX,cAAe,CA7CpB,qEAkDK,WAAY,CACZ,YAAa,CACb,eAAgB,CApDrB,kCAwDK,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,SAAU,CACV,kBAAmB,CACpB,CxBnKL,2BwBuGA,kBAEI,oBAAiB,CAAjB,gBAAiB,CACjB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CAH9B,8BAMK,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CANxB,6CASO,iBAAkB,CATzB,wCAaO,mBhCw4BqB,CgCv4BrB,kBhCu4BqB,CgCr5B5B,qCAmBK,gBAAiB,CAnBtB,mCAuBK,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,4BAAY,CAAZ,eAAgB,CAxBrB,kCA4BK,YAAa,CA5BlB,oCAgCK,YAAa,CAhClB,6BAoCK,gBAAiB,CACjB,QAAS,CACT,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,6BAA8B,CAC9B,4BAA6B,CAC7B,cAAe,CACf,aAAc,CfjMlB,uCAAgC,CAAhC,+BAAgC,CemM5B,sBAAW,CAAX,cAAe,CA7CpB,qEAkDK,WAAY,CACZ,YAAa,CACb,eAAgB,CApDrB,kCAwDK,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,SAAU,CACV,kBAAmB,CACpB,CxBnKL,2BwBuGA,mBAEI,oBAAiB,CAAjB,gBAAiB,CACjB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CAH9B,+BAMK,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CANxB,8CASO,iBAAkB,CATzB,yCAaO,mBhCw4BqB,CgCv4BrB,kBhCu4BqB,CgCr5B5B,sCAmBK,gBAAiB,CAnBtB,oCAuBK,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,4BAAY,CAAZ,eAAgB,CAxBrB,mCA4BK,YAAa,CA5BlB,qCAgCK,YAAa,CAhClB,8BAoCK,gBAAiB,CACjB,QAAS,CACT,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,6BAA8B,CAC9B,4BAA6B,CAC7B,cAAe,CACf,aAAc,CfjMlB,uCAAgC,CAAhC,+BAAgC,CemM5B,sBAAW,CAAX,cAAe,CA7CpB,uEAkDK,WAAY,CACZ,YAAa,CACb,eAAgB,CApDrB,mCAwDK,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,SAAU,CACV,kBAAmB,CACpB,CAlET,eAQQ,oBAAiB,CAAjB,gBAAiB,CACjB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CATnC,2BAYU,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CAZ7B,0CAeY,iBAAkB,CAf9B,qCAmBY,mBhCw4BqB,CgCv4BrB,kBhCu4BqB,CgC35BjC,kCAyBU,gBAAiB,CAzB3B,gCA6BU,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,4BAAY,CAAZ,eAAgB,CA9B1B,+BAkCU,YAAa,CAlCvB,iCAsCU,YAAa,CAtCvB,0BA0CU,gBAAiB,CACjB,QAAS,CACT,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,6BAA8B,CAC9B,4BAA6B,CAC7B,cAAe,CACf,aAAc,CfjMlB,uCAAgC,CAAhC,+BAAgC,CemM5B,sBAAW,CAAX,cAAe,CAnDzB,+DAwDU,WAAY,CACZ,YAAa,CACb,eAAgB,CA1D1B,+BA8DU,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,SAAU,CACV,kBAAmB,CACpB,4BAeL,qBhC/NQ,CgC6NZ,oEAMM,qBhCnOM,CgC6NZ,oCAYM,sBhCzOM,CgC6NZ,oFAgBQ,qBhC7OI,CgC6NZ,6CAoBQ,qBhCjPI,CgC6NZ,qFA0BM,qBhCvPM,CgC6NZ,8BA+BI,sBhC5PQ,CgC6PR,4BhC7PQ,CgC6NZ,mCAoCI,yP/B5I8E,C+BwGlF,2BAwCI,sBhCrQQ,CgC6NZ,mGA6CM,qBhC1QM,CgC2QP,2BAOD,UhC5RQ,CgC0RZ,kEAMM,UhChSM,CgC0RZ,mCAYM,4BhCtSM,CgC0RZ,kFAgBQ,4BhC1SI,CgC0RZ,4CAoBQ,4BhC9SI,CgC0RZ,mFA0BM,UhCpTM,CgC0RZ,6BA+BI,4BhCzTQ,CgC0TR,kChC1TQ,CgC0RZ,kCAoCI,+P/B/L8E,C+B2JlF,0BAwCI,4BhClUQ,CgC0RZ,gGA6CM,UhCvUM,CgCwUP,MC3UH,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,WAAY,CAEZ,oBAAqB,CACrB,qBjCHU,CiCIV,0BAA2B,CAC3B,kCjCKU,CECR,oBFmdkB,CiCletB,SAcI,cAAe,CACf,aAAc,CAflB,kBAmBI,kBAAmB,CACnB,qBAAsB,CApB1B,8BAuBM,kBAAmB,C/BCrB,yCDsO4D,CCrO5D,0CDqO4D,CgC9PhE,6BA4BM,qBAAsB,C/BUxB,6CDwN4D,CCvN5D,4CDuN4D,CgC9PhE,8DAqCI,YAAa,CACd,WAMD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,iBjC0SW,CiCxSZ,YAGC,mBjCwpCgC,CiCvpCjC,eAGC,kBAAsC,CACtC,eAAgB,CACjB,sBAGC,eAAgB,CACjB,sBAQG,gBjCmRS,CiClRV,aAQD,kBjC0QW,CiCzQX,eAAgB,CAEhB,iCjClEU,CiCmEV,yCjCnEU,CiC8DZ,yB/B7DI,uD+BqE8E,CAC/E,aAID,kBjC8PW,CiC5PX,iCjC7EU,CiC8EV,sCjC9EU,CiC0EZ,wB/BzEI,uDD+O4D,CgC9J7D,kBASD,mBAAuC,CACvC,oBjCsmCsC,CiCrmCtC,kBAAsC,CACtC,eAAgB,CAQjB,mBAGC,mBAAuC,CACvC,kBAAsC,CACvC,kBAIC,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,QAAS,CACT,MAAO,CACP,YjCqNW,CExUT,gCD+O4D,CgC1H/D,yCAKC,UAAW,CACZ,wB/BlHG,yCDsO4D,CCrO5D,0CDqO4D,CgC/G/D,2B/BzGG,6CDwN4D,CCvN5D,4CDuN4D,CgC1G/D,kBAYG,oBjCwjCuC,CQ7pCvC,0ByBgGJ,YASI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,6BAAW,CAAX,4BAAW,CAAX,sBAAW,CAAX,kBAAmB,CAVvB,kBAgBM,kBAAY,CAAZ,eAAY,CAAZ,WAAY,CACZ,eAAgB,CAjBtB,wBAoBQ,aAAc,CACd,aAAc,CArBtB,mC/B5HI,yB+BuJkC,C/BtJlC,4B+BsJkC,CA3BtC,iGAgCY,yBAA0B,CAhCtC,oGAsCY,4BAA6B,CAtCzC,oC/B9GI,wB+ByJoC,C/BxJpC,2B+BwJoC,CA3CxC,mGAgDY,wBAAyB,CAhDrC,sGAsDY,2BAA4B,CAC7B,CClNX,kBACE,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,UAAW,CACX,oBlC0tC2B,CDp6BvB,cAxE+B,CmC5OnC,alCMgB,CkCLhB,eAAgB,CAChB,qBlCLU,CkCMV,QAAS,ChCKP,egCJsB,CACxB,oBAAqB,CjBAjB,uCAAY,CAAZ,+BAAgC,CAIhC,wCiBhBN,kBjBiBQ,uBAAY,CAAZ,eAAgB,CiBgCvB,CAjDD,kCAgBI,ajC+LiC,CiC9LjC,wBjCyLiC,CiCxLjC,mDlCJI,CkCIJ,2ClCJQ,CkCdZ,yCAqBM,gSjC8G4E,CiC7G5E,iClCouCqB,CkCpuCrB,yBlCouCoC,CkC1vC1C,yBA4BI,mBAAc,CAAd,aAAc,CACd,alCytC0B,CkCxtC1B,clCwtC0B,CkCvtC1B,gBAAiB,CACjB,UAAW,CACX,gSjCkG8E,CiCjG9E,2BAA4B,CAC5B,uBlCmtC0B,CiB1uCxB,uCAAY,CAAZ,+BAAgC,CAIhC,wCiBhBN,yBjBiBQ,uBAAY,CAAZ,eAAgB,CiBoBrB,CArCH,wBAwCI,SAAU,CAxCd,wBA4CI,SAAU,CACV,oBjC6JiC,CiC5JjC,SAAU,CACV,qDlCfG,CkCeH,6ClCfU,CkCgBX,kBAID,eAAgB,CACjB,gBAGC,qBlCpDU,CkCqDV,kClC3CU,CkCyCZ,8BhC/BI,6BF0ckB,CEzclB,8BFyckB,CkC3atB,gDhC/BI,yCDsO4D,CCrO5D,0CDqO4D,CiCvMhE,oCAaI,YAAa,CAbjB,6BhCjBI,iCF4bkB,CE3blB,gCF2bkB,CkC3atB,yDhCjBI,6CDwN4D,CCvN5D,4CDuN4D,CiCvMhE,iDhCjBI,iCF4bkB,CE3blB,gCF2bkB,CkC/YjB,gBAKH,oBlCuoC2B,CkCtoC5B,qCASG,cAAe,CAFnB,iCAMI,cAAe,CACf,aAAc,ChCxFd,egCyFwB,CAR5B,6CAWM,YAAa,CAXnB,4CAeM,eAAgB,CAftB,mDhCjFI,egCoG0B,CACzB,YCvHH,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,WnC+9CsB,CmC99CtB,kBnCg+C6B,CmC99C7B,eAAgB,CAGjB,kCAMG,kBnCo9C6B,CmCx9CjC,0CAOM,UAAW,CACX,mBnCg9C2B,CmC/8C3B,anCNY,CmCOZ,wCAAS,EAAkG,2CAAuH,CAAO,CAV/O,wBAeI,anCZc,CmCaf,YC1BD,mBAAa,CAAb,mBAAa,CAAb,YAAa,ChCGb,cAAe,CACf,eAAgB,CgCFjB,WAGC,iBAAkB,CAClB,aAAc,CACd,apC4BY,CoC3BZ,oBAAsD,CACtD,qBpCFU,CoCGV,wBpCAgB,CiBKZ,uCAAY,CAAZ,+BAAgC,CAIhC,wCmBfN,WnBgBQ,uBAAY,CAAZ,eAAgB,CmBQvB,CAxBD,iBAUI,SAAU,CACV,anCmMiC,CmCjMjC,wBpCRc,CoCSd,oBpCRc,CoCNlB,iBAkBI,SAAU,CACV,anC2LiC,CmC1LjC,wBpCfc,CoCgBd,SpCgpCwB,CoC/oCxB,qDpCSG,CoCTH,6CpCSU,CoCRX,wCAKC,gBpC0bc,CoC5blB,6BAMI,SAAU,CACV,UpC9BQ,CkBJV,wBlBgCY,CoCIV,oBpCJU,CoCLd,+BAaI,apC9Bc,CoC+Bd,mBAAoB,CACpB,qBpCtCQ,CoCuCR,oBpCpCc,CoCNlB,WCAI,sBrCqpCyB,CqCnpC1B,kCnC0CC,6BFqbkB,CEpblB,gCFobkB,CqC7dpB,iCnC0BE,8BFmckB,CElclB,iCFkckB,CqClepB,0BACE,qBrCypC4B,CD/1B1B,iBAxE+B,CsChPlC,iDnC0CC,4BFuboB,CEtbpB,+BFsboB,CqC1df,gDnCqBL,6BFqcoB,CEpcpB,gCFocoB,CqCpetB,0BACE,oBrCupC2B,CD71BzB,iBAxE+B,CsChPlC,iDnC0CC,4BFsboB,CErbpB,+BFqboB,CqCzdf,gDnCqBL,6BFocoB,CEncpB,gCFmcoB,CqCndf,OCdP,oBAAqB,CACrB,mBtCm1CqB,CD3hCjB,eAxE+B,CuC9OnC,etC2iBoB,CsC1iBpB,aAAc,CACd,UtCHU,CsCIV,iBAAkB,CAClB,kBAAmB,CACnB,uBAAwB,CpCKtB,oBFmdkB,CsCjetB,aAeI,YAAa,CACd,YAKD,iBAAkB,CAClB,QAAS,CACV,OCvBC,iBAAkB,CAClB,iBvCqVW,CuCpVX,kBvC04CwB,CuCz4CxB,4BAA6C,CrCW3C,oBFmdkB,CuC5drB,eAKC,aAAc,CACf,YAIC,evCgiBoB,CuC/hBrB,mBAQC,kBvC23CgD,CuC53ClD,8BAKI,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,SvCyXsB,CuCxXtB,oBvCsTS,CuCrTV,eCpCD,avCiNmC,CiB/MnC,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,2BACE,avC4MiC,CuC3MlC,iBAND,avCiNmC,CiB/MnC,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,6BACE,avC4MiC,CuC3MlC,eAND,avCiNmC,CiB/MnC,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,2BACE,avC4MiC,CuC3MlC,YAND,aDkDuF,CrBhDvF,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,wBACE,avC4MiC,CuC3MlC,eAND,aDkDuF,CrBhDvF,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,2BACE,avC4MiC,CuC3MlC,cAND,avCiNmC,CiB/MnC,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,0BACE,avC4MiC,CuC3MlC,aAND,aDkDuF,CrBhDvF,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,yBACE,avC4MiC,CuC3MlC,YAND,avCiNmC,CiB/MnC,wBjB0MmC,CuC1MnC,oBvC0MmC,CuCxMnC,wBACE,avC4MiC,CuC3MlC,wCCHC,GACE,0BzCy5CgB,CAAA,CwCv5CnB,gCCHC,GACE,0BzCy5CgB,CAAA,CyCl5CtB,UACE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,WzCg5CoB,CyC/4CpB,eAAgB,C1C+SZ,gBAxE+B,C0CrOnC,wBzCRgB,CESd,oBFmdkB,CyCjdrB,cAGC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,eAAgB,CAChB,UzCpBU,CyCqBV,iBAAkB,CAClB,kBAAmB,CACnB,wBzCKY,CiBpBR,uCAAY,CAAZ,+BAAgC,CAIhC,wCwBGN,cxBFQ,uBAAY,CAAZ,eAAgB,CwBYvB,CAED,sBvBSE,2LAA6I,CuBP7I,yBzC03CoB,CyCz3CrB,uBAIG,yDAA0C,CAA1C,iDAA8D,CAG5D,wCAJJ,uBAKM,sBAAW,CAAX,cAAe,CAGpB,CC7CH,YACE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CAGtB,cAAe,CACf,eAAgB,CxCSd,oBFmdkB,C0C1drB,qBAGC,oBAAqB,CACrB,qBAAsB,CAFxB,gCAMI,mCAAoC,CACpC,yBAA0B,CAC3B,wBAUD,UAAW,CACX,a1ClBgB,C0CmBhB,kBAAmB,CAHrB,4DAQI,SAAU,CACV,a1CzBc,C0C0Bd,oBAAqB,CACrB,wB1CjCc,C0CsBlB,+BAeI,a1C7Bc,C0C8Bd,wB1CrCc,C0CsCf,iBASD,iBAAkB,CAClB,aAAc,CACd,kB1CgSW,C0C/RX,a1C3CgB,C0C4ChB,oBAAsD,CACtD,qB1CtDU,C0CuDV,kC1C7CU,C0CsCZ,6BxC5BI,8BwCsCkC,CxCrClC,+BwCqCkC,CAVtC,4BxCdI,kCwC4BqC,CxC3BrC,iCwC2BqC,CAdzC,oDAmBI,a1C7Dc,C0C8Dd,mBAAoB,CACpB,qB1CrEQ,C0CgDZ,wBA0BI,SAAU,CACV,U1C3EQ,C0C4ER,wB1ChDU,C0CiDV,oB1CjDU,C0CoBd,kCAiCI,kBAAmB,CAjCvB,yCAoCM,e1C+XY,C0C9XZ,oB1C8XY,C0C7Xb,uBAcC,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CADrB,oDxCjCA,gCF4ZkB,CExalB,yBwCmDsC,CANtC,mDxC7CA,8BFwakB,CE5ZlB,2BwC4C2C,CAX3C,+CAeM,YAAa,CAfnB,yDAmBM,oB1C6VQ,C0C5VR,mBAAoB,CApB1B,gEAuBQ,gB1CyVM,C0CxVN,qB1CwVM,CQ5Zd,0BkC4CA,0BACE,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CADrB,uDxCjCA,gCF4ZkB,CExalB,yBwCmDsC,CANtC,sDxC7CA,8BFwakB,CE5ZlB,2BwC4C2C,CAX3C,kDAeM,YAAa,CAfnB,4DAmBM,oB1C6VQ,C0C5VR,mBAAoB,CApB1B,mEAuBQ,gB1CyVM,C0CxVN,qB1CwVM,C0CvVP,ClCrEP,0BkC4CA,0BACE,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CADrB,uDxCjCA,gCF4ZkB,CExalB,yBwCmDsC,CANtC,sDxC7CA,8BFwakB,CE5ZlB,2BwC4C2C,CAX3C,kDAeM,YAAa,CAfnB,4DAmBM,oB1C6VQ,C0C5VR,mBAAoB,CApB1B,mEAuBQ,gB1CyVM,C0CxVN,qB1CwVM,C0CvVP,ClCrEP,0BkC4CA,0BACE,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CADrB,uDxCjCA,gCF4ZkB,CExalB,yBwCmDsC,CANtC,sDxC7CA,8BFwakB,CE5ZlB,2BwC4C2C,CAX3C,kDAeM,YAAa,CAfnB,4DAmBM,oB1C6VQ,C0C5VR,mBAAoB,CApB1B,mEAuBQ,gB1CyVM,C0CxVN,qB1CwVM,C0CvVP,ClCrEP,2BkC4CA,0BACE,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CADrB,uDxCjCA,gCF4ZkB,CExalB,yBwCmDsC,CANtC,sDxC7CA,8BFwakB,CE5ZlB,2BwC4C2C,CAX3C,kDAeM,YAAa,CAfnB,4DAmBM,oB1C6VQ,C0C5VR,mBAAoB,CApB1B,mEAuBQ,gB1CyVM,C0CxVN,qB1CwVM,C0CvVP,ClCrEP,2BkC4CA,2BACE,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CADrB,wDxCjCA,gCF4ZkB,CExalB,yBwCmDsC,CANtC,uDxC7CA,8BFwakB,CE5ZlB,2BwC4C2C,CAX3C,mDAeM,YAAa,CAfnB,6DAmBM,oB1C6VQ,C0C5VR,mBAAoB,CApB1B,oEAuBQ,gB1CyVM,C0CxVN,qB1CwVM,C0CvVP,CAaX,kBxC9HI,ewC+HsB,CAD1B,mCAII,oB1CsUc,C0C1UlB,8CAOM,qBAAsB,CCpJ1B,yBACE,a1C8MiC,C0C7MjC,wB1CwMiC,C0C1MnC,4GAOM,a1CwM6B,C0CvM7B,wB1CuM6B,C0C/MnC,uDAYM,U3CRI,C2CSJ,wB1CkM6B,C0CjM7B,oB1CiM6B,C0C/MnC,2BACE,a1C8MiC,C0C7MjC,wB1CwMiC,C0C1MnC,gHAOM,a1CwM6B,C0CvM7B,wB1CuM6B,C0C/MnC,yDAYM,U3CRI,C2CSJ,wB1CkM6B,C0CjM7B,oB1CiM6B,C0C/MnC,yBACE,a1C8MiC,C0C7MjC,wB1CwMiC,C0C1MnC,4GAOM,a1CwM6B,C0CvM7B,wB1CuM6B,C0C/MnC,uDAYM,U3CRI,C2CSJ,wB1CkM6B,C0CjM7B,oB1CiM6B,C0C/MnC,sBACE,aDqKiH,CCpKjH,wB1CwMiC,C0C1MnC,sGAOM,aD+J6G,CC9J7G,wB1CuM6B,C0C/MnC,oDAYM,U3CRI,C2CSJ,wBDyJ6G,CCxJ7G,oBDwJ6G,CCtKnH,yBACE,aDqKiH,CCpKjH,wB1CwMiC,C0C1MnC,4GAOM,aD+J6G,CC9J7G,wB1CuM6B,C0C/MnC,uDAYM,U3CRI,C2CSJ,wBDyJ6G,CCxJ7G,oBDwJ6G,CCtKnH,wBACE,a1C8MiC,C0C7MjC,wB1CwMiC,C0C1MnC,0GAOM,a1CwM6B,C0CvM7B,wB1CuM6B,C0C/MnC,sDAYM,U3CRI,C2CSJ,wB1CkM6B,C0CjM7B,oB1CiM6B,C0C/MnC,uBACE,aDqKiH,CCpKjH,wB1CwMiC,C0C1MnC,wGAOM,aD+J6G,CC9J7G,wB1CuM6B,C0C/MnC,qDAYM,U3CRI,C2CSJ,wBDyJ6G,CCxJ7G,oBDwJ6G,CCtKnH,sBACE,a1C8MiC,C0C7MjC,wB1CwMiC,C0C1MnC,sGAOM,a1CwM6B,C0CvM7B,wB1CuM6B,C0C/MnC,oDAYM,U3CRI,C2CSJ,wB1CkM6B,C0CjM7B,oB1CiM6B,C0ChM9B,WCbL,8BAAuB,CAAvB,sBAAuB,CACvB,S5C6hDmB,C4C5hDnB,U5C4hDmB,C4C3hDnB,mB5C6hDyB,C4C5hDzB,U5CQU,C4CPV,0WAA0F,CAC1F,QAAS,C1COP,oBFmdkB,C4CxdpB,U5C6hDoB,C4CtiDtB,iBAaI,U5CAQ,C4CCR,oBAAqB,CACrB,W5CwhDyB,C4CviD7B,iBAmBI,SAAU,CACV,qD5CWU,C4CXV,6C5CWU,C4CVV,S5CmhDuB,C4CxiD3B,wCA0BI,mBAAoB,CACpB,wBAAiB,CAAjB,qBAAiB,CAAjB,oBAAiB,CAAjB,gBAAiB,CACjB,W5C6gD4B,C4C5gD7B,iBAID,yD5CygDiD,C4CzgDjD,iD5CygDiE,C4CxgDlE,OCtCC,W7Cg0CqB,C6C/zCrB,cAAe,C9C6TX,iBAxE+B,C8ClPnC,mBAAoB,CACpB,uC7CEU,C6CDV,2BAA4B,CAC5B,gC7CUU,C6CTV,iD7CSU,C6CTV,yC7CSU,CECR,oBFmdkB,C6CtetB,eAaI,SAAU,CAbd,kBAiBI,YAAa,CACd,iBAID,yBAAkB,CAAlB,sBAAkB,CAAlB,iBAAkB,CAClB,cAAe,CACf,mBAAoB,CAHtB,mCAMI,oB7CwbyC,C6Cvb1C,cAID,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,oB7CgyCsB,C6C/xCtB,a7CrBgB,C6CsBhB,uC7C5BU,C6C6BV,2BAA4B,CAC5B,wC7CpBU,CEUR,yCDsO4D,CCrO5D,0CDqO4D,C4CnOhE,yBAWI,qBAAoC,CACpC,kB7CuxCoB,C6CtxCrB,YAID,c7CkxCsB,C6CjxCtB,oBAAqB,CACtB,OC1CC,cAAe,CACf,KAAM,CACN,MAAO,CACP,Y9CwgCiB,C8CvgCjB,YAAa,CACb,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,eAAgB,CAGhB,SAAU,CAIX,cAIC,iBAAkB,CAClB,UAAW,CACX,Y9Cy0CyB,C8Cv0CzB,mBAAoB,CAGpB,0B7BlBI,uCAAgC,CAAhC,+BAAgC,C6BoBlC,qC9C+1CmB,C8C/1CnB,6B9C+1CsC,CiB/2CpC,wC6BcJ,0B7BbM,uBAAY,CAAZ,eAAgB,C6BgBrB,CAED,0BACE,sB9C41CmB,C8C51CnB,c9C41CuB,C8C31CxB,kCAIC,6B9Cy1CoB,C8Cz1CpB,qB9Cy1C+B,C8Cx1ChC,yBAID,wB7CgN8D,C6CjNhE,wCAII,eAAgB,CAChB,eAAgB,CALpB,qCASI,eAAgB,CACjB,uBAID,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,4B7CiM8D,C6ChM/D,eAIC,iBAAkB,CAClB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,UAAW,CAGX,mBAAoB,CACpB,qB9CrEU,C8CsEV,2BAA4B,CAC5B,gC9C7DU,CECR,mBFqdoB,C8CrZtB,SAAU,CACX,gBClFC,cAAe,CACf,KAAM,CACN,MAAO,CACP,Y/C6gC0B,C+C5gC1B,WAAY,CACZ,YAAa,CACb,qB/CUU,C+CPV,qBAAS,SAAU,CAAI,qBACd,U/Cy2CgB,C+Cz2Cc,cDkFvC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,mBAAc,CAAd,aAAc,CACd,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,iB9CyPW,C8CxPX,+B9CxFgB,CEiBd,wCDsO4D,CCrO5D,yCDqO4D,C6CrKhE,yBAUI,mBAAsE,CACtE,gCAA4G,CAC7G,aAKD,eAAgB,CAChB,e9C0coB,C8CzcrB,YAKC,iBAAkB,CAGlB,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,Y9CkOW,C8CjOZ,cAIC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CACf,mBAAc,CAAd,aAAc,CACd,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,oBAAyB,CAAzB,iBAAyB,CAAzB,wBAAyB,CACzB,cAAiE,CACjE,4B9CzHgB,CE+Bd,4CDwN4D,CCvN5D,2CDuN4D,C6CrIhE,gBAcI,aAAyC,CtC5EzC,0BsCrCJ,cA0HI,e9C2uCY,C8C1uCZ,mBAAyC,CApG7C,yBAwGI,0B7CyG4D,C6CpMhE,uBA+FI,8B7CqG4D,C6CpG7D,UAOC,e9CytCY,C8CxtCb,CtCvGC,0BsC4GF,oBAEE,e9CmtCY,C8CltCb,CtC/GC,2BsCmHF,UACE,gB9C8sCa,C8C7sCd,CASC,kBACE,WAAY,CACZ,cAAe,CACf,WAAY,CACZ,QAAS,CAJX,iCAOI,WAAY,CACZ,QAAS,C5ClLb,e4CmL4B,CAT5B,gC5C1KA,e4CuL4B,CAb5B,8BAiBI,eAAgB,CAjBpB,gC5C1KA,e4C+L4B,CtCtI5B,6BsCiHA,0BACE,WAAY,CACZ,cAAe,CACf,WAAY,CACZ,QAAS,CAJX,yCAOI,WAAY,CACZ,QAAS,C5ClLb,e4CmL4B,CAT5B,wC5C1KA,e4CuL4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC5C1KA,e4C+L4B,CACzB,CtCvIH,6BsCiHA,0BACE,WAAY,CACZ,cAAe,CACf,WAAY,CACZ,QAAS,CAJX,yCAOI,WAAY,CACZ,QAAS,C5ClLb,e4CmL4B,CAT5B,wC5C1KA,e4CuL4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC5C1KA,e4C+L4B,CACzB,CtCvIH,6BsCiHA,0BACE,WAAY,CACZ,cAAe,CACf,WAAY,CACZ,QAAS,CAJX,yCAOI,WAAY,CACZ,QAAS,C5ClLb,e4CmL4B,CAT5B,wC5C1KA,e4CuL4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC5C1KA,e4C+L4B,CACzB,CtCvIH,8BsCiHA,0BACE,WAAY,CACZ,cAAe,CACf,WAAY,CACZ,QAAS,CAJX,yCAOI,WAAY,CACZ,QAAS,C5ClLb,e4CmL4B,CAT5B,wC5C1KA,e4CuL4B,CAb5B,sCAiBI,eAAgB,CAjBpB,wC5C1KA,e4C+L4B,CACzB,CtCvIH,8BsCiHA,2BACE,WAAY,CACZ,cAAe,CACf,WAAY,CACZ,QAAS,CAJX,0CAOI,WAAY,CACZ,QAAS,C5ClLb,e4CmL4B,CAT5B,yC5C1KA,e4CuL4B,CAb5B,uCAiBI,eAAgB,CAjBpB,yC5C1KA,e4C+L4B,CACzB,CElNP,SACE,iBAAkB,CAClB,YhDkhCmB,CgDjhCnB,aAAc,CACd,QhD0wCgB,CiD9wChB,qCjDsiB2E,CiDpiB3E,iBAAkB,CAClB,ejD+iBsB,CiD9iBtB,ejDojBoB,CiDnjBpB,eAAgB,CAChB,gBAAiB,CACjB,oBAAqB,CACrB,gBAAiB,CACjB,mBAAoB,CACpB,qBAAsB,CACtB,iBAAkB,CAClB,mBAAoB,CACpB,kBAAmB,CACnB,eAAgB,ClDgTZ,iBAxE+B,CiD5OnC,oBAAqB,CACrB,SAAU,CAXZ,cAcI,UhD6vCgB,CgD3wCpB,wBAkBI,iBAAkB,CAClB,aAAc,CACd,WhD4vCuB,CgD3vCvB,YhD4vCwB,CgDjxC5B,gCAwBM,iBAAkB,CAClB,UAAW,CACX,wBAAyB,CACzB,kBAAmB,CACpB,+DAKH,eAAgC,CADlC,6FAII,QAAS,CAJb,6GAOM,QAAS,CACT,0BAAiE,CACjE,qBhDxBM,CgDyBP,iEAKH,ehDkuC0B,CgDnuC5B,+FAII,MAAO,CACP,WhD8tCwB,CgD7tCxB,YhD4tCuB,CgDluC3B,+GASM,UAAW,CACX,gCAA6F,CAC7F,uBhDxCM,CgDyCP,qEAKH,eAAgC,CADlC,mGAII,KAAM,CAJV,mHAOM,WAAY,CACZ,0BhD2sCsB,CgD1sCtB,wBhDtDM,CgDuDP,kEAKH,ehDosC0B,CgDrsC5B,gGAII,OAAQ,CACR,WhDgsCwB,CgD/rCxB,YhD8rCuB,CgDpsC3B,gHASM,SAAU,CACV,gChD2rCsB,CgD1rCtB,sBhDtEM,CgDuEP,eAwBH,ehDupCuB,CgDtpCvB,oBhD4pC8B,CgD3pC9B,UhD3GU,CgD4GV,iBAAkB,CAClB,qBhDnGU,CECR,oBFmdkB,CgD/WrB,SEtHC,iBAAkB,CAClB,KAAM,CACN,uBAA6B,CAC7B,YlDghCmB,CkD/gCnB,aAAc,CACd,elDgyCuB,CiDryCvB,qCjDsiB2E,CiDpiB3E,iBAAkB,CAClB,ejD+iBsB,CiD9iBtB,ejDojBoB,CiDnjBpB,eAAgB,CAChB,gBAAiB,CACjB,oBAAqB,CACrB,gBAAiB,CACjB,mBAAoB,CACpB,qBAAsB,CACtB,iBAAkB,CAClB,mBAAoB,CACpB,kBAAmB,CACnB,eAAgB,ClDgTZ,iBAxE+B,CmD3OnC,oBAAqB,CACrB,qBlDLU,CkDMV,2BAA4B,CAC5B,gClDGU,CECR,mBFqdoB,CkDxexB,wBAoBI,iBAAkB,CAClB,aAAc,CACd,UlDgyCsB,CkD/xCtB,YlDgyCwB,CkDvzC5B,+DA2BM,iBAAkB,CAClB,aAAc,CACd,UAAW,CACX,wBAAyB,CACzB,kBAAmB,CACpB,6FAMD,yBjD4N4D,CiD9NhE,6GAKM,QAAS,CACT,0BAAiE,CACjE,iClD+wCyD,CkDtxC/D,2GAWM,UlD4aY,CkD3aZ,0BAAiE,CACjE,qBlDzCM,CkD0CP,+FAMD,uBjD0M4D,CiDzM5D,WlD8vCwB,CkD7vCxB,WlD4vCsB,CkDhwC1B,+GAOM,MAAO,CACP,gCAA6F,CAC7F,mClD2vCyD,CkDpwC/D,6GAaM,QlDwZY,CkDvZZ,gCAA6F,CAC7F,uBlD7DM,CkD8DP,mGAMD,sBjDsL4D,CiDxLhE,mHAKM,KAAM,CACN,gCAA6F,CAC7F,oClDyuCyD,CkDhvC/D,iHAWM,OlDsYY,CkDrYZ,gCAA6F,CAC7F,wBlD/EM,CkDkEZ,qHAmBI,iBAAkB,CAClB,KAAM,CACN,QAAS,CACT,aAAc,CACd,UlDqtCsB,CkDptCtB,kBAAuC,CACvC,UAAW,CACX,+BjD+GiC,CiD9GlC,gGAKC,wBjDwJ4D,CiDvJ5D,WlD4sCwB,CkD3sCxB,WlD0sCsB,CkD9sC1B,gHAOM,OAAQ,CACR,gClDusCsB,CkDtsCtB,kClDysCyD,CkDltC/D,8GAaM,SlDsWY,CkDrWZ,gClDisCsB,CkDhsCtB,sBlD/GM,CkDgHP,gBAwBH,kBlD2MW,CkD1MX,eAAgB,CnD8KZ,cAxE+B,CmDnGnC,wBjD+DmC,CiD9DnC,uClDnIU,CEUR,wCDsO4D,CCrO5D,yCDqO4D,CiDnHhE,sBAUI,YAAa,CACd,cAID,iBlD6LW,CkD5LX,alD9IgB,CkD+IjB,UClJC,iBAAkB,CACnB,wBAGC,sBAAc,CAAd,kBAAmB,CACpB,gBAGC,iBAAkB,CAClB,UAAW,CACX,eAAgB,CCtBhB,uBACE,aAAc,CACd,UAAW,CACX,UAAW,CACZ,eDuBD,iBAAkB,CAClB,YAAa,CACb,UAAW,CACX,UAAW,CACX,kBAAmB,CACnB,kCAA2B,CAA3B,0BAA2B,ClClBvB,uCAAY,CAAZ,+BAAgC,CAIhC,wCkCQN,elCPQ,uBAAY,CAAZ,eAAgB,CkCevB,CAED,8DAGE,aAAc,CACf,wEAKC,kCAAW,CAAX,0BAA2B,CAC5B,wEAIC,mCAAW,CAAX,2BAA4B,CAC7B,8BAWG,SAAU,CACV,mCAA4B,CAA5B,2BAA4B,CAC5B,sBAAW,CAAX,cAAe,CAJnB,iJAUI,SAAU,CACV,SAAU,CAXd,oFAgBI,SAAU,CACV,SAAU,ClC/DR,uCAAY,CAAZ,+BAAgC,CAIhC,wCkC0CN,oFlCzCQ,uBAAY,CAAZ,eAAgB,CkC4DrB,CAQH,8CAEE,iBAAkB,CAClB,KAAM,CACN,QAAS,CACT,SAAU,CAEV,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,SnD84C0B,CmD74C1B,SAAU,CACV,UnD7FU,CmD8FV,iBAAkB,CAClB,eAAgB,CAChB,QAAS,CACT,UnDy4C2B,CiBl+CvB,uCAAY,CAAZ,+BAAgC,CAIhC,wCkCqEN,8ClCpEQ,uBAAY,CAAZ,eAAgB,CkC+FvB,CA3BD,oHAsBI,UnDvGQ,CmDwGR,oBAAqB,CACrB,SAAU,CACV,UnDi4C+B,CmDh4ChC,uBAID,MAAO,CAER,uBAGC,OAAQ,CAET,wDAKC,oBAAqB,CACrB,UnDg4CgC,CmD/3ChC,WnD+3CgC,CmD93ChC,2BAA4B,CAC5B,uBAAwB,CACxB,yBAA0B,CAC3B,4BAWC,wQlDbgF,CkDcjF,4BAGC,yQlDjBgF,CkDkBjF,qBAQC,iBAAkB,CAClB,OAAQ,CACR,QAAS,CACT,MAAO,CACP,SAAU,CACV,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,SAAU,CAEV,gBnDu0C0B,CmDt0C1B,kBAAmB,CACnB,enDq0C0B,CmDp0C1B,eAAgB,CAblB,sCAgBI,8BAAuB,CAAvB,sBAAuB,CACvB,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UnDo0C2B,CmDn0C3B,UnDo0C2B,CmDn0C3B,SAAU,CACV,gBnDo0C2B,CmDn0C3B,enDm0C2B,CmDl0C3B,kBAAmB,CACnB,cAAe,CACf,qBnDjLQ,CmDkLR,2BAA4B,CAC5B,QAAS,CAET,iCAAiE,CACjE,oCAAoE,CACpE,UnD2zC2B,CiB1+CzB,uCAAY,CAAZ,+BAAgC,CAIhC,wCkC4IN,sClC3IQ,uBAAY,CAAZ,eAAgB,CkC4KrB,CAjCH,6BAoCI,SnDwzCiC,CmDvzClC,kBASD,iBAAkB,CAClB,SAA4C,CAC5C,cnDkzC+B,CmDjzC/B,QAA2C,CAC3C,mBnD+yCkC,CmD9yClC,sBnD8yCkC,CmD7yClC,UnD5MU,CmD6MV,iBAAkB,CACnB,sFAQG,uCnDgzC0C,CmDhzC1C,+BnDgzCwD,CmDpzC5D,qDAQI,qBnDhNQ,CmDwMZ,iCAYI,UnDpNQ,CmDqNT,kCEjOD,GACE,gCAAW,CAAX,uBAAW,EAAe,eAAA,CAAqB,CAAA,CFgOhD,0BEjOD,GACE,gCAAW,CAAX,uBAAW,EAAe,eAAA,CAAqB,CAAA,CAMnD,gBACE,oBAAqB,CACrB,UrDsgDkB,CqDrgDlB,WrDqgDkB,CqDpgDlB,sBrDsgD8B,CqDrgD9B,+BAAgD,CAChD,8BAA+B,CAE/B,iBAAkB,CAClB,qDAAoD,CAApD,6CAAkE,CACnE,mBAGC,UrDigDqB,CqDhgDrB,WrDggDqB,CqD//CrB,iBrDigD4B,CqDhgD7B,gCAQC,GACE,0BAAW,CAAX,kBAAmB,CAGrB,IACE,SAAU,CACV,sBAAW,CAAX,cAAe,CAAA,CAdlB,wBAQC,GACE,0BAAW,CAAX,kBAAmB,CAGrB,IACE,SAAU,CACV,sBAAW,CAAX,cAAe,CAAA,CAMnB,cACE,oBAAqB,CACrB,UrDk+CkB,CqDj+ClB,WrDi+CkB,CqDh+ClB,sBrDk+C8B,CqDj+C9B,6BAA8B,CAE9B,iBAAkB,CAClB,SAAU,CACV,mDAAoD,CAApD,2CAAgE,CACjE,iBAGC,UrD69CqB,CqD59CrB,WrD49CqB,CqD39CtB,wCAKG,8BAEE,+BAAoB,CAApB,uBAAgD,CACjD,CCxEL,WACE,cAAe,CACf,QAAS,CACT,YtD8gCqB,CsD7gCrB,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,cAAe,CAEf,iBAAkB,CAClB,qBtDDU,CsDEV,2BAA4B,CAC5B,SAAU,CrCKN,uCAAY,CAAZ,+BAAgC,CAIhC,wCqCpBN,WrCqBQ,uBAAY,CAAZ,eAAgB,CqCPvB,CAED,oBPdE,cAAe,CACf,KAAM,CACN,MAAO,CACP,Y/C2gC8B,C+C1gC9B,WAAY,CACZ,YAAa,CACb,qB/CUU,C+CPV,yBAAS,SAAU,CAAI,yBACd,U/Cy2CgB,C+Cz2Cc,kBOSvC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,wBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAC9B,iBtDmUW,CsDvUb,6BAOI,mBAAgE,CAChE,iBAAsC,CACtC,mBAAwC,CACxC,oBAAyC,CAC1C,iBAID,eAAgB,CAChB,etDqhBoB,CsDphBrB,gBAGC,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,iBtDkTW,CsDjTX,eAAgB,CACjB,iBAGC,KAAM,CACN,MAAO,CACP,WtDwgDgC,CsDvgDhC,sCtD/BU,CsDgCV,mCAAW,CAAX,2BAA4B,CAC7B,eAGC,KAAM,CACN,OAAQ,CACR,WtDggDgC,CsD//ChC,qCtDvCU,CsDwCV,kCAAW,CAAX,0BAA2B,CAC5B,eAGC,KAAM,CACN,OAAQ,CACR,MAAO,CACP,WtDw/C8B,CsDv/C9B,eAAgB,CAChB,uCtDjDU,CsDkDV,mCAAW,CAAX,2BAA4B,CAC7B,kBAGC,OAAQ,CACR,MAAO,CACP,WtD++C8B,CsD9+C9B,eAAgB,CAChB,oCtD1DU,CsD2DV,kCAAW,CAAX,0BAA2B,CAC5B,gBAGC,sBAAW,CAAX,cAAe,CAChB,aCjFC,oBAAqB,CACrB,cAAe,CACf,qBAAsB,CACtB,WAAY,CACZ,6BAA8B,CAC9B,UvD+rC0B,CuDrsC5B,yBASI,oBAAqB,CACrB,UAAW,CACZ,gBAKD,eAAgB,CACjB,gBAGC,eAAgB,CACjB,gBAGC,gBAAiB,CAClB,+BAKG,0DAAW,CAAX,kDAAmD,CACpD,oCAID,IACE,UvDkqCwB,CAAA,CuDvqCzB,4BAID,IACE,UvDkqCwB,CAAA,CuD9pC5B,kBACE,mFAA8G,CAA9G,2EAA8G,CAC9G,2BAAoB,CAApB,mBAAoB,CACpB,qDAAW,CAAX,6CAA8C,CAC/C,oCAGC,KACE,8BAAe,CAAf,sBAAuB,CAAA,CAJ1B,4BAGC,KACE,8BAAe,CAAf,sBAAuB,CAAA,CH9CzB,iBACE,aAAc,CACd,UAAW,CACX,UAAW,CACZ,cIJC,axDkCU,CwDnCZ,wCAMM,avD4M6B,CuDlNnC,gBACE,axDYc,CwDbhB,4CAMM,avD4M6B,CuDlNnC,cACE,axDyCW,CwD1Cb,wCAMM,avD4M6B,CuDlNnC,WACE,axD2CU,CwD5CZ,kCAMM,avDuM6B,CuD7MnC,cACE,axDwCY,CwDzCd,wCAMM,avDuM6B,CuD7MnC,aACE,axDsCS,CwDvCX,sCAMM,avD4M6B,CuDlNnC,YACE,axDOc,CwDRhB,oCAMM,avDuM6B,CuD7MnC,WACE,axDec,CwDhBhB,kCAMM,avD4M6B,CuD3M9B,OCLL,iBAAkB,CAClB,UAAW,CAFb,eAKI,aAAc,CACd,kCAAiE,CACjE,UAAW,CAPf,SAWI,iBAAkB,CAClB,KAAM,CACN,MAAO,CACP,UAAW,CACX,WAAY,CACb,WAKC,uBAAgD,CADlD,WACE,qCAAgD,CADlD,YACE,sCAAgD,CADlD,YACE,sCAAgD,CACjD,WCrBD,cAAe,CACf,KAAM,CACN,OAAQ,CACR,MAAO,CACP,Y1DwgCiB,C0DvgClB,cAGC,cAAe,CACf,OAAQ,CACR,QAAS,CACT,MAAO,CACP,Y1DggCiB,C0D//BlB,YAQK,eAAgB,CAChB,KAAM,CACN,Y1Do/Bc,CQ/8BhB,0BkDxCA,eACE,eAAgB,CAChB,KAAM,CACN,Y1Do/Bc,C0Dn/Bf,ClDoCD,0BkDxCA,eACE,eAAgB,CAChB,KAAM,CACN,Y1Do/Bc,C0Dn/Bf,ClDoCD,0BkDxCA,eACE,eAAgB,CAChB,KAAM,CACN,Y1Do/Bc,C0Dn/Bf,ClDoCD,2BkDxCA,eACE,eAAgB,CAChB,KAAM,CACN,Y1Do/Bc,C0Dn/Bf,ClDoCD,2BkDxCA,gBACE,eAAgB,CAChB,KAAM,CACN,Y1Do/Bc,C0Dn/Bf,CC1BL,QACE,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,2BAAY,CAAZ,kBAAmB,CACpB,QAGC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,2BAAY,CAAZ,kBAAmB,CACpB,2ECLC,4BAA6B,CAC7B,oBAAqB,CACrB,qBAAsB,CACtB,oBAAqB,CACrB,sBAAuB,CACvB,0BAA2B,CAC3B,gCAAiC,CACjC,6BAA8B,CAC9B,mBAAoB,CCTrB,uBCDG,iBAAkB,CAClB,KAAM,CACN,OAAQ,CACR,QAAS,CACT,MAAO,CACP,S9DkZsB,C8DjZtB,UAAW,CACZ,eCTD,eAAgB,CAChB,sBAAuB,CACvB,kBAAmB,CCApB,ICLC,oBAAqB,CACrB,2BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CACV,cAAe,CACf,6BAA8B,CAC9B,WjEinBc,CkExjBR,gBAOI,kCAA+D,CAPnE,WAOI,6BAA+D,CAPnE,cAOI,gCAA+D,CAPnE,cAOI,gCAA+D,CAPnE,mBAOI,qCAA+D,CAPnE,gBAOI,kCAA+D,CAPnE,aAOI,qBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,YAOI,qBAA+D,CAPnE,WAOI,oBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,YAOI,qBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,aAOI,oBAA+D,CAPnE,eAOI,wBAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,kBAOI,2BAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,aAOI,4BAA+D,CAPnE,cAOI,6BAA+D,CAPnE,QAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,eAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,4DAAqD,CAArD,oDAA+D,CAPnE,WAOI,kEAAqD,CAArD,0DAA+D,CAPnE,WAOI,2DAAqD,CAArD,mDAA+D,CAPnE,aAOI,kCAAqD,CAArD,0BAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,gBAOI,yBAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,OAOI,gBAA+D,CAPnE,QAOI,kBAA+D,CAPnE,SAOI,mBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,WAOI,qBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,SAOI,iBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,WAOI,oBAA+D,CAPnE,OAOI,kBAA+D,CAPnE,QAOI,oBAA+D,CAPnE,SAOI,qBAA+D,CAPnE,kBAOI,kDAAqD,CAArD,0CAA+D,CAPnE,oBAOI,6CAAqD,CAArD,qCAA+D,CAPnE,oBAOI,6CAAqD,CAArD,qCAA+D,CAPnE,QAOI,mCAA+D,CAPnE,UAOI,mBAA+D,CAPnE,YAOI,uCAA+D,CAPnE,cAOI,uBAA+D,CAPnE,YAOI,yCAA+D,CAPnE,cAOI,yBAA+D,CAPnE,eAOI,0CAA+D,CAPnE,iBAOI,0BAA+D,CAPnE,cAOI,wCAA+D,CAPnE,gBAOI,wBAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,kBAOI,+BAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,aAOI,+BAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,eAOI,+BAA+D,CAPnE,cAOI,+BAA+D,CAPnE,aAOI,+BAA+D,CAPnE,cAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,MAOI,oBAA+D,CAPnE,MAOI,oBAA+D,CAPnE,MAOI,oBAA+D,CAPnE,OAOI,qBAA+D,CAPnE,QAOI,qBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,YAOI,0BAA+D,CAPnE,MAOI,qBAA+D,CAPnE,MAOI,qBAA+D,CAPnE,MAOI,qBAA+D,CAPnE,OAOI,sBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,0BAA+D,CAPnE,QAOI,uBAA+D,CAPnE,YAOI,2BAA+D,CAPnE,WAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,UAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,aAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,kBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,qBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,aAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,aAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,eAOI,8BAAqD,CAArD,wBAA+D,CAPnE,eAOI,8BAAqD,CAArD,wBAA+D,CAPnE,WAOI,6BAAqD,CAArD,yBAA+D,CAPnE,aAOI,+BAAqD,CAArD,2BAA+D,CAPnE,mBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,OAOI,gBAA+D,CAPnE,OAOI,qBAA+D,CAPnE,OAOI,oBAA+D,CAPnE,OAOI,mBAA+D,CAPnE,OAOI,qBAA+D,CAPnE,OAOI,mBAA+D,CAPnE,uBAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,qBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,wBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,yBAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,wBAOI,mCAAqD,CAArD,uCAA+D,CAPnE,wBAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,iBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,oBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,qBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,mBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,sBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,sCAA+D,CAPnE,sBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,uBAOI,qCAAqD,CAArD,gCAA+D,CAPnE,iBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,kBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,gBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,mBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,qBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,oBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,aAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,KAOI,mBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,KAOI,sBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,sBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,MAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,MAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,uBAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,KAOI,oBAA+D,CAPnE,KAOI,yBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,KAOI,yBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,MAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,MAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,gCAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,gCAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,gBAOI,+CAA+D,CAPnE,MAOI,2CAA+D,CAPnE,MAOI,0CAA+D,CAPnE,MAOI,wCAA+D,CAPnE,MAOI,0CAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,YAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,YAOI,8BAA+D,CAPnE,WAOI,0BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,WAOI,6BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,OAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,OAOI,wBAA+D,CAPnE,YAOI,0BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,aAOI,4BAA+D,CAPnE,sBAOI,+BAA+D,CAPnE,2BAOI,oCAA+D,CAPnE,8BAOI,uCAA+D,CAPnE,gBAOI,mCAA+D,CAPnE,gBAOI,mCAA+D,CAPnE,iBAOI,oCAA+D,CAPnE,WAOI,6BAA+D,CAPnE,aAOI,6BAA+D,CAPnE,YAOI,+BAA+D,CAA/D,gCAA+D,CAPnE,cAIQ,oBAAqC,CAGzC,oEAA+D,CAPnE,gBAIQ,oBAAqC,CAGzC,sEAA+D,CAPnE,cAIQ,oBAAqC,CAGzC,oEAA+D,CAPnE,WAIQ,oBAAqC,CAGzC,iEAA+D,CAPnE,cAIQ,oBAAqC,CAGzC,oEAA+D,CAPnE,aAIQ,oBAAqC,CAGzC,mEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,kEAA+D,CAPnE,WAIQ,oBAAqC,CAGzC,iEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,kEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,kEAA+D,CAPnE,WAIQ,oBAAqC,CAGzC,uEAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,wBAA+D,CAPnE,eAIQ,oBAAqC,CAGzC,gCAA+D,CAPnE,eAIQ,oBAAqC,CAGzC,sCAA+D,CAPnE,YAIQ,oBAAqC,CAGzC,wBAA+D,CAjBnE,iBACE,sBAA0C,CAD5C,iBACE,qBAA0C,CAD5C,iBACE,sBAA0C,CAD5C,kBACE,oBAA0C,CAC3C,YAYO,kBAAqC,CAGzC,6EAA+D,CAPnE,cAIQ,kBAAqC,CAGzC,+EAA+D,CAPnE,YAIQ,kBAAqC,CAGzC,6EAA+D,CAPnE,SAIQ,kBAAqC,CAGzC,0EAA+D,CAPnE,YAIQ,kBAAqC,CAGzC,6EAA+D,CAPnE,WAIQ,kBAAqC,CAGzC,4EAA+D,CAPnE,UAIQ,kBAAqC,CAGzC,2EAA+D,CAPnE,SAIQ,kBAAqC,CAGzC,0EAA+D,CAPnE,UAIQ,kBAAqC,CAGzC,2EAA+D,CAPnE,UAIQ,kBAAqC,CAGzC,2EAA+D,CAPnE,SAIQ,kBAAqC,CAGzC,6EAA+D,CAPnE,gBAIQ,kBAAqC,CAGzC,yCAA+D,CAjBnE,eACE,mBAA0C,CAD5C,eACE,oBAA0C,CAD5C,eACE,mBAA0C,CAD5C,eACE,oBAA0C,CAD5C,gBACE,kBAA0C,CAC3C,aAeG,8CAA+D,CAPnE,iBAOI,kCAAqD,CAArD,+BAAqD,CAArD,8BAAqD,CAArD,0BAA+D,CAPnE,kBAOI,mCAAqD,CAArD,gCAAqD,CAArD,+BAAqD,CAArD,2BAA+D,CAPnE,kBAOI,mCAAqD,CAArD,gCAAqD,CAArD,+BAAqD,CAArD,2BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,WAOI,0BAA+D,CAPnE,WAOI,8BAA+D,CAPnE,WAOI,+BAA+D,CAPnE,WAOI,8BAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,cAOI,8BAA+D,CAPnE,aAOI,wCAA+D,CAA/D,yCAA+D,CAPnE,aAOI,yCAA+D,CAA/D,4CAA+D,CAPnE,gBAOI,4CAA+D,CAA/D,2CAA+D,CAPnE,eAOI,2CAA+D,CAA/D,wCAA+D,CAPnE,SAOI,6BAA+D,CAPnE,WAOI,4BAA+D,C1DPvE,0B0DAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,C1DTL,0B0DAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,C1DTL,0B0DAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,C1DTL,2B0DAI,gBAOI,qBAA+D,CAPnE,cAOI,sBAA+D,CAPnE,eAOI,qBAA+D,CAPnE,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,UAOI,gBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,oBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,UAOI,qBAA+D,CAPnE,UAOI,mBAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,eAOI,0BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,gBAOI,4BAA+D,CAElE,C1DTL,2B0DAI,iBAOI,qBAA+D,CAPnE,eAOI,sBAA+D,CAPnE,gBAOI,qBAA+D,CAPnE,cAOI,yBAA+D,CAPnE,oBAOI,+BAA+D,CAPnE,aAOI,wBAA+D,CAPnE,YAOI,uBAA+D,CAPnE,aAOI,wBAA+D,CAPnE,iBAOI,4BAA+D,CAPnE,kBAOI,6BAA+D,CAPnE,YAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,mBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,YAOI,uBAA+D,CAPnE,eAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,cAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,iBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,sBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,yBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,iBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,iBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,mBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,mBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,eAOI,6BAAqD,CAArD,yBAA+D,CAPnE,iBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,WAOI,gBAA+D,CAPnE,WAOI,qBAA+D,CAPnE,WAOI,oBAA+D,CAPnE,WAOI,mBAA+D,CAPnE,WAOI,qBAA+D,CAPnE,WAOI,mBAA+D,CAPnE,2BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,yBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,4BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,6BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,4BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,uBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,qBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,yBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,yBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,uBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,oCAAqD,CAArD,+BAA+D,CAPnE,2BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,0BAOI,wCAAqD,CAArD,qCAA+D,CAPnE,2BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,sBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,yBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,iBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,gBAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,mBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,sBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,sBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,UAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,aAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,UAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,aAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,uBAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,aAOI,0BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,aAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,aAOI,6BAA+D,CAPnE,UAOI,wBAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,SAOI,oBAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,UAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,UAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,wBAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,gCAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,gCAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,gBAOI,0BAA+D,CAPnE,cAOI,2BAA+D,CAPnE,iBAOI,4BAA+D,CAElE,CCrDT,2BD4CQ,MAOI,2BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAElE,CClCT,aDyBQ,gBAOI,yBAA+D,CAPnE,sBAOI,+BAA+D,CAPnE,eAOI,wBAA+D,CAPnE,cAOI,uBAA+D,CAPnE,eAOI,wBAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,oBAOI,6BAA+D,CAPnE,cAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,qBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,cAOI,uBAA+D,CAElE", "file": "vendors/bootstrap/bootstrap.css", "sourcesContent": ["/*!\n * Bootstrap v5.1.3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n// scss-docs-start import-stack\n// Configuration\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"utilities\";\n\n// Layout & components\n@import \"root\";\n@import \"reboot\";\n@import \"type\";\n@import \"images\";\n@import \"containers\";\n@import \"grid\";\n@import \"tables\";\n@import \"forms\";\n@import \"buttons\";\n@import \"transitions\";\n@import \"dropdown\";\n@import \"button-group\";\n@import \"nav\";\n@import \"navbar\";\n@import \"card\";\n@import \"accordion\";\n@import \"breadcrumb\";\n@import \"pagination\";\n@import \"badge\";\n@import \"alert\";\n@import \"progress\";\n@import \"list-group\";\n@import \"close\";\n@import \"toasts\";\n@import \"modal\";\n@import \"tooltip\";\n@import \"popover\";\n@import \"carousel\";\n@import \"spinners\";\n@import \"offcanvas\";\n@import \"placeholders\";\n\n// Helpers\n@import \"helpers\";\n\n// Utilities\n@import \"utilities/api\";\n// scss-docs-end import-stack", "/*!\n * Bootstrap v5.1.3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */:root{--bs-blue: #0d6efd;--bs-indigo: #6610f2;--bs-purple: #6f42c1;--bs-pink: #d63384;--bs-red: #dc3545;--bs-orange: #fd7e14;--bs-yellow: #ffc107;--bs-green: #198754;--bs-teal: #20c997;--bs-cyan: #0dcaf0;--bs-white: #fff;--bs-gray: #6c757d;--bs-gray-dark: #343a40;--bs-gray-100: #f8f9fa;--bs-gray-200: #e9ecef;--bs-gray-300: #dee2e6;--bs-gray-400: #ced4da;--bs-gray-500: #adb5bd;--bs-gray-600: #6c757d;--bs-gray-700: #495057;--bs-gray-800: #343a40;--bs-gray-900: #212529;--bs-primary: #0d6efd;--bs-secondary: #6c757d;--bs-success: #198754;--bs-info: #0dcaf0;--bs-warning: #ffc107;--bs-danger: #dc3545;--bs-light: #f8f9fa;--bs-dark: #212529;--bs-primary-rgb: 13,110,253;--bs-secondary-rgb: 108,117,125;--bs-success-rgb: 25,135,84;--bs-info-rgb: 13,202,240;--bs-warning-rgb: 255,193,7;--bs-danger-rgb: 220,53,69;--bs-light-rgb: 248,249,250;--bs-dark-rgb: 33,37,41;--bs-white-rgb: 255,255,255;--bs-black-rgb: 0,0,0;--bs-body-color-rgb: 33,37,41;--bs-body-bg-rgb: 255,255,255;--bs-font-sans-serif: system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";--bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;--bs-gradient: linear-gradient(180deg, rgba(255,255,255,0.15), rgba(255,255,255,0));--bs-body-font-family: var(--bs-font-sans-serif);--bs-body-font-size: 1rem;--bs-body-font-weight: 400;--bs-body-line-height: 1.5;--bs-body-color: #212529;--bs-body-bg: #fff}*,*::before,*::after{box-sizing:border-box}@media (prefers-reduced-motion: no-preference){:root{scroll-behavior:smooth}}body{margin:0;font-family:var(--bs-body-font-family);font-size:var(--bs-body-font-size);font-weight:var(--bs-body-font-weight);line-height:var(--bs-body-line-height);color:var(--bs-body-color);text-align:var(--bs-body-text-align);background-color:var(--bs-body-bg);-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:rgba(0,0,0,0)}hr{margin:1rem 0;color:inherit;background-color:currentColor;border:0;opacity:.25}hr:not([size]){height:1px}h1,.h1,h2,.h2,h3,.h3,h4,.h4,h5,.h5,h6,.h6{margin-top:0;margin-bottom:.5rem;font-weight:500;line-height:1.2}h1,.h1{font-size:calc(1.375rem + 1.5vw)}@media (min-width: 1200px){h1,.h1{font-size:2.5rem}}h2,.h2{font-size:calc(1.325rem + .9vw)}@media (min-width: 1200px){h2,.h2{font-size:2rem}}h3,.h3{font-size:calc(1.3rem + .6vw)}@media (min-width: 1200px){h3,.h3{font-size:1.75rem}}h4,.h4{font-size:calc(1.275rem + .3vw)}@media (min-width: 1200px){h4,.h4{font-size:1.5rem}}h5,.h5{font-size:1.25rem}h6,.h6{font-size:1rem}p{margin-top:0;margin-bottom:1rem}abbr[title],abbr[data-bs-original-title]{text-decoration:underline dotted;cursor:help;text-decoration-skip-ink:none}address{margin-bottom:1rem;font-style:normal;line-height:inherit}ol,ul{padding-left:2rem}ol,ul,dl{margin-top:0;margin-bottom:1rem}ol ol,ul ul,ol ul,ul ol{margin-bottom:0}dt{font-weight:700}dd{margin-bottom:.5rem;margin-left:0}blockquote{margin:0 0 1rem}b,strong{font-weight:bolder}small,.small{font-size:.875em}mark,.mark{padding:.2em;background-color:#fcf8e3}sub,sup{position:relative;font-size:.75em;line-height:0;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}a{color:#0d6efd;text-decoration:underline}a:hover{color:#0a58ca}a:not([href]):not([class]),a:not([href]):not([class]):hover{color:inherit;text-decoration:none}pre,code,kbd,samp{font-family:var(--bs-font-monospace);font-size:1em;direction:ltr /* rtl:ignore */;unicode-bidi:bidi-override}pre{display:block;margin-top:0;margin-bottom:1rem;overflow:auto;font-size:.875em}pre code{font-size:inherit;color:inherit;word-break:normal}code{font-size:.875em;color:#d63384;word-wrap:break-word}a>code{color:inherit}kbd{padding:.2rem .4rem;font-size:.875em;color:#fff;background-color:#212529;border-radius:.2rem}kbd kbd{padding:0;font-size:1em;font-weight:700}figure{margin:0 0 1rem}img,svg{vertical-align:middle}table{caption-side:bottom;border-collapse:collapse}caption{padding-top:.5rem;padding-bottom:.5rem;color:#6c757d;text-align:left}th{text-align:inherit;text-align:-webkit-match-parent}thead,tbody,tfoot,tr,td,th{border-color:inherit;border-style:solid;border-width:0}label{display:inline-block}button{border-radius:0}button:focus:not(:focus-visible){outline:0}input,button,select,optgroup,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit}button,select{text-transform:none}[role=\"button\"]{cursor:pointer}select{word-wrap:normal}select:disabled{opacity:1}[list]::-webkit-calendar-picker-indicator{display:none}button,[type=\"button\"],[type=\"reset\"],[type=\"submit\"]{-webkit-appearance:button}button:not(:disabled),[type=\"button\"]:not(:disabled),[type=\"reset\"]:not(:disabled),[type=\"submit\"]:not(:disabled){cursor:pointer}::-moz-focus-inner{padding:0;border-style:none}textarea{resize:vertical}fieldset{min-width:0;padding:0;margin:0;border:0}legend{float:left;width:100%;padding:0;margin-bottom:.5rem;font-size:calc(1.275rem + .3vw);line-height:inherit}@media (min-width: 1200px){legend{font-size:1.5rem}}legend+*{clear:left}::-webkit-datetime-edit-fields-wrapper,::-webkit-datetime-edit-text,::-webkit-datetime-edit-minute,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-year-field{padding:0}::-webkit-inner-spin-button{height:auto}[type=\"search\"]{outline-offset:-2px;-webkit-appearance:textfield}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-color-swatch-wrapper{padding:0}::file-selector-button{font:inherit}::-webkit-file-upload-button{font:inherit;-webkit-appearance:button}output{display:inline-block}iframe{border:0}summary{display:list-item;cursor:pointer}progress{vertical-align:baseline}[hidden]{display:none !important}.lead{font-size:1.25rem;font-weight:300}.display-1{font-size:calc(1.625rem + 4.5vw);font-weight:300;line-height:1.2}@media (min-width: 1200px){.display-1{font-size:5rem}}.display-2{font-size:calc(1.575rem + 3.9vw);font-weight:300;line-height:1.2}@media (min-width: 1200px){.display-2{font-size:4.5rem}}.display-3{font-size:calc(1.525rem + 3.3vw);font-weight:300;line-height:1.2}@media (min-width: 1200px){.display-3{font-size:4rem}}.display-4{font-size:calc(1.475rem + 2.7vw);font-weight:300;line-height:1.2}@media (min-width: 1200px){.display-4{font-size:3.5rem}}.display-5{font-size:calc(1.425rem + 2.1vw);font-weight:300;line-height:1.2}@media (min-width: 1200px){.display-5{font-size:3rem}}.display-6{font-size:calc(1.375rem + 1.5vw);font-weight:300;line-height:1.2}@media (min-width: 1200px){.display-6{font-size:2.5rem}}.list-unstyled{padding-left:0;list-style:none}.list-inline{padding-left:0;list-style:none}.list-inline-item{display:inline-block}.list-inline-item:not(:last-child){margin-right:.5rem}.initialism{font-size:.875em;text-transform:uppercase}.blockquote{margin-bottom:1rem;font-size:1.25rem}.blockquote>:last-child{margin-bottom:0}.blockquote-footer{margin-top:-1rem;margin-bottom:1rem;font-size:.875em;color:#6c757d}.blockquote-footer::before{content:\"\\2014\\00A0\"}.img-fluid{max-width:100%;height:auto}.img-thumbnail{padding:.25rem;background-color:#fff;border:1px solid #dee2e6;border-radius:.25rem;max-width:100%;height:auto}.figure{display:inline-block}.figure-img{margin-bottom:.5rem;line-height:1}.figure-caption{font-size:.875em;color:#6c757d}.container,.container-fluid,.container-sm,.container-md,.container-lg,.container-xl,.container-xxl{width:100%;padding-right:var(--bs-gutter-x, .75rem);padding-left:var(--bs-gutter-x, .75rem);margin-right:auto;margin-left:auto}@media (min-width: 576px){.container,.container-sm{max-width:540px}}@media (min-width: 768px){.container,.container-sm,.container-md{max-width:720px}}@media (min-width: 992px){.container,.container-sm,.container-md,.container-lg{max-width:960px}}@media (min-width: 1200px){.container,.container-sm,.container-md,.container-lg,.container-xl{max-width:1140px}}@media (min-width: 1400px){.container,.container-sm,.container-md,.container-lg,.container-xl,.container-xxl{max-width:1320px}}.row{--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display:flex;flex-wrap:wrap;margin-top:calc(-1 * var(--bs-gutter-y));margin-right:calc(-.5 * var(--bs-gutter-x));margin-left:calc(-.5 * var(--bs-gutter-x))}.row>*{flex-shrink:0;width:100%;max-width:100%;padding-right:calc(var(--bs-gutter-x) * .5);padding-left:calc(var(--bs-gutter-x) * .5);margin-top:var(--bs-gutter-y)}.col{flex:1 0 0%}.row-cols-auto>*{flex:0 0 auto;width:auto}.row-cols-1>*{flex:0 0 auto;width:100%}.row-cols-2>*{flex:0 0 auto;width:50%}.row-cols-3>*{flex:0 0 auto;width:33.33333%}.row-cols-4>*{flex:0 0 auto;width:25%}.row-cols-5>*{flex:0 0 auto;width:20%}.row-cols-6>*{flex:0 0 auto;width:16.66667%}.col-auto{flex:0 0 auto;width:auto}.col-1{flex:0 0 auto;width:8.33333%}.col-2{flex:0 0 auto;width:16.66667%}.col-3{flex:0 0 auto;width:25%}.col-4{flex:0 0 auto;width:33.33333%}.col-5{flex:0 0 auto;width:41.66667%}.col-6{flex:0 0 auto;width:50%}.col-7{flex:0 0 auto;width:58.33333%}.col-8{flex:0 0 auto;width:66.66667%}.col-9{flex:0 0 auto;width:75%}.col-10{flex:0 0 auto;width:83.33333%}.col-11{flex:0 0 auto;width:91.66667%}.col-12{flex:0 0 auto;width:100%}.offset-1{margin-left:8.33333%}.offset-2{margin-left:16.66667%}.offset-3{margin-left:25%}.offset-4{margin-left:33.33333%}.offset-5{margin-left:41.66667%}.offset-6{margin-left:50%}.offset-7{margin-left:58.33333%}.offset-8{margin-left:66.66667%}.offset-9{margin-left:75%}.offset-10{margin-left:83.33333%}.offset-11{margin-left:91.66667%}.g-0,.gx-0{--bs-gutter-x: 0}.g-0,.gy-0{--bs-gutter-y: 0}.g-1,.gx-1{--bs-gutter-x: .25rem}.g-1,.gy-1{--bs-gutter-y: .25rem}.g-2,.gx-2{--bs-gutter-x: .5rem}.g-2,.gy-2{--bs-gutter-y: .5rem}.g-3,.gx-3{--bs-gutter-x: 1rem}.g-3,.gy-3{--bs-gutter-y: 1rem}.g-4,.gx-4{--bs-gutter-x: 1.5rem}.g-4,.gy-4{--bs-gutter-y: 1.5rem}.g-5,.gx-5{--bs-gutter-x: 3rem}.g-5,.gy-5{--bs-gutter-y: 3rem}@media (min-width: 576px){.col-sm{flex:1 0 0%}.row-cols-sm-auto>*{flex:0 0 auto;width:auto}.row-cols-sm-1>*{flex:0 0 auto;width:100%}.row-cols-sm-2>*{flex:0 0 auto;width:50%}.row-cols-sm-3>*{flex:0 0 auto;width:33.33333%}.row-cols-sm-4>*{flex:0 0 auto;width:25%}.row-cols-sm-5>*{flex:0 0 auto;width:20%}.row-cols-sm-6>*{flex:0 0 auto;width:16.66667%}.col-sm-auto{flex:0 0 auto;width:auto}.col-sm-1{flex:0 0 auto;width:8.33333%}.col-sm-2{flex:0 0 auto;width:16.66667%}.col-sm-3{flex:0 0 auto;width:25%}.col-sm-4{flex:0 0 auto;width:33.33333%}.col-sm-5{flex:0 0 auto;width:41.66667%}.col-sm-6{flex:0 0 auto;width:50%}.col-sm-7{flex:0 0 auto;width:58.33333%}.col-sm-8{flex:0 0 auto;width:66.66667%}.col-sm-9{flex:0 0 auto;width:75%}.col-sm-10{flex:0 0 auto;width:83.33333%}.col-sm-11{flex:0 0 auto;width:91.66667%}.col-sm-12{flex:0 0 auto;width:100%}.offset-sm-0{margin-left:0}.offset-sm-1{margin-left:8.33333%}.offset-sm-2{margin-left:16.66667%}.offset-sm-3{margin-left:25%}.offset-sm-4{margin-left:33.33333%}.offset-sm-5{margin-left:41.66667%}.offset-sm-6{margin-left:50%}.offset-sm-7{margin-left:58.33333%}.offset-sm-8{margin-left:66.66667%}.offset-sm-9{margin-left:75%}.offset-sm-10{margin-left:83.33333%}.offset-sm-11{margin-left:91.66667%}.g-sm-0,.gx-sm-0{--bs-gutter-x: 0}.g-sm-0,.gy-sm-0{--bs-gutter-y: 0}.g-sm-1,.gx-sm-1{--bs-gutter-x: .25rem}.g-sm-1,.gy-sm-1{--bs-gutter-y: .25rem}.g-sm-2,.gx-sm-2{--bs-gutter-x: .5rem}.g-sm-2,.gy-sm-2{--bs-gutter-y: .5rem}.g-sm-3,.gx-sm-3{--bs-gutter-x: 1rem}.g-sm-3,.gy-sm-3{--bs-gutter-y: 1rem}.g-sm-4,.gx-sm-4{--bs-gutter-x: 1.5rem}.g-sm-4,.gy-sm-4{--bs-gutter-y: 1.5rem}.g-sm-5,.gx-sm-5{--bs-gutter-x: 3rem}.g-sm-5,.gy-sm-5{--bs-gutter-y: 3rem}}@media (min-width: 768px){.col-md{flex:1 0 0%}.row-cols-md-auto>*{flex:0 0 auto;width:auto}.row-cols-md-1>*{flex:0 0 auto;width:100%}.row-cols-md-2>*{flex:0 0 auto;width:50%}.row-cols-md-3>*{flex:0 0 auto;width:33.33333%}.row-cols-md-4>*{flex:0 0 auto;width:25%}.row-cols-md-5>*{flex:0 0 auto;width:20%}.row-cols-md-6>*{flex:0 0 auto;width:16.66667%}.col-md-auto{flex:0 0 auto;width:auto}.col-md-1{flex:0 0 auto;width:8.33333%}.col-md-2{flex:0 0 auto;width:16.66667%}.col-md-3{flex:0 0 auto;width:25%}.col-md-4{flex:0 0 auto;width:33.33333%}.col-md-5{flex:0 0 auto;width:41.66667%}.col-md-6{flex:0 0 auto;width:50%}.col-md-7{flex:0 0 auto;width:58.33333%}.col-md-8{flex:0 0 auto;width:66.66667%}.col-md-9{flex:0 0 auto;width:75%}.col-md-10{flex:0 0 auto;width:83.33333%}.col-md-11{flex:0 0 auto;width:91.66667%}.col-md-12{flex:0 0 auto;width:100%}.offset-md-0{margin-left:0}.offset-md-1{margin-left:8.33333%}.offset-md-2{margin-left:16.66667%}.offset-md-3{margin-left:25%}.offset-md-4{margin-left:33.33333%}.offset-md-5{margin-left:41.66667%}.offset-md-6{margin-left:50%}.offset-md-7{margin-left:58.33333%}.offset-md-8{margin-left:66.66667%}.offset-md-9{margin-left:75%}.offset-md-10{margin-left:83.33333%}.offset-md-11{margin-left:91.66667%}.g-md-0,.gx-md-0{--bs-gutter-x: 0}.g-md-0,.gy-md-0{--bs-gutter-y: 0}.g-md-1,.gx-md-1{--bs-gutter-x: .25rem}.g-md-1,.gy-md-1{--bs-gutter-y: .25rem}.g-md-2,.gx-md-2{--bs-gutter-x: .5rem}.g-md-2,.gy-md-2{--bs-gutter-y: .5rem}.g-md-3,.gx-md-3{--bs-gutter-x: 1rem}.g-md-3,.gy-md-3{--bs-gutter-y: 1rem}.g-md-4,.gx-md-4{--bs-gutter-x: 1.5rem}.g-md-4,.gy-md-4{--bs-gutter-y: 1.5rem}.g-md-5,.gx-md-5{--bs-gutter-x: 3rem}.g-md-5,.gy-md-5{--bs-gutter-y: 3rem}}@media (min-width: 992px){.col-lg{flex:1 0 0%}.row-cols-lg-auto>*{flex:0 0 auto;width:auto}.row-cols-lg-1>*{flex:0 0 auto;width:100%}.row-cols-lg-2>*{flex:0 0 auto;width:50%}.row-cols-lg-3>*{flex:0 0 auto;width:33.33333%}.row-cols-lg-4>*{flex:0 0 auto;width:25%}.row-cols-lg-5>*{flex:0 0 auto;width:20%}.row-cols-lg-6>*{flex:0 0 auto;width:16.66667%}.col-lg-auto{flex:0 0 auto;width:auto}.col-lg-1{flex:0 0 auto;width:8.33333%}.col-lg-2{flex:0 0 auto;width:16.66667%}.col-lg-3{flex:0 0 auto;width:25%}.col-lg-4{flex:0 0 auto;width:33.33333%}.col-lg-5{flex:0 0 auto;width:41.66667%}.col-lg-6{flex:0 0 auto;width:50%}.col-lg-7{flex:0 0 auto;width:58.33333%}.col-lg-8{flex:0 0 auto;width:66.66667%}.col-lg-9{flex:0 0 auto;width:75%}.col-lg-10{flex:0 0 auto;width:83.33333%}.col-lg-11{flex:0 0 auto;width:91.66667%}.col-lg-12{flex:0 0 auto;width:100%}.offset-lg-0{margin-left:0}.offset-lg-1{margin-left:8.33333%}.offset-lg-2{margin-left:16.66667%}.offset-lg-3{margin-left:25%}.offset-lg-4{margin-left:33.33333%}.offset-lg-5{margin-left:41.66667%}.offset-lg-6{margin-left:50%}.offset-lg-7{margin-left:58.33333%}.offset-lg-8{margin-left:66.66667%}.offset-lg-9{margin-left:75%}.offset-lg-10{margin-left:83.33333%}.offset-lg-11{margin-left:91.66667%}.g-lg-0,.gx-lg-0{--bs-gutter-x: 0}.g-lg-0,.gy-lg-0{--bs-gutter-y: 0}.g-lg-1,.gx-lg-1{--bs-gutter-x: .25rem}.g-lg-1,.gy-lg-1{--bs-gutter-y: .25rem}.g-lg-2,.gx-lg-2{--bs-gutter-x: .5rem}.g-lg-2,.gy-lg-2{--bs-gutter-y: .5rem}.g-lg-3,.gx-lg-3{--bs-gutter-x: 1rem}.g-lg-3,.gy-lg-3{--bs-gutter-y: 1rem}.g-lg-4,.gx-lg-4{--bs-gutter-x: 1.5rem}.g-lg-4,.gy-lg-4{--bs-gutter-y: 1.5rem}.g-lg-5,.gx-lg-5{--bs-gutter-x: 3rem}.g-lg-5,.gy-lg-5{--bs-gutter-y: 3rem}}@media (min-width: 1200px){.col-xl{flex:1 0 0%}.row-cols-xl-auto>*{flex:0 0 auto;width:auto}.row-cols-xl-1>*{flex:0 0 auto;width:100%}.row-cols-xl-2>*{flex:0 0 auto;width:50%}.row-cols-xl-3>*{flex:0 0 auto;width:33.33333%}.row-cols-xl-4>*{flex:0 0 auto;width:25%}.row-cols-xl-5>*{flex:0 0 auto;width:20%}.row-cols-xl-6>*{flex:0 0 auto;width:16.66667%}.col-xl-auto{flex:0 0 auto;width:auto}.col-xl-1{flex:0 0 auto;width:8.33333%}.col-xl-2{flex:0 0 auto;width:16.66667%}.col-xl-3{flex:0 0 auto;width:25%}.col-xl-4{flex:0 0 auto;width:33.33333%}.col-xl-5{flex:0 0 auto;width:41.66667%}.col-xl-6{flex:0 0 auto;width:50%}.col-xl-7{flex:0 0 auto;width:58.33333%}.col-xl-8{flex:0 0 auto;width:66.66667%}.col-xl-9{flex:0 0 auto;width:75%}.col-xl-10{flex:0 0 auto;width:83.33333%}.col-xl-11{flex:0 0 auto;width:91.66667%}.col-xl-12{flex:0 0 auto;width:100%}.offset-xl-0{margin-left:0}.offset-xl-1{margin-left:8.33333%}.offset-xl-2{margin-left:16.66667%}.offset-xl-3{margin-left:25%}.offset-xl-4{margin-left:33.33333%}.offset-xl-5{margin-left:41.66667%}.offset-xl-6{margin-left:50%}.offset-xl-7{margin-left:58.33333%}.offset-xl-8{margin-left:66.66667%}.offset-xl-9{margin-left:75%}.offset-xl-10{margin-left:83.33333%}.offset-xl-11{margin-left:91.66667%}.g-xl-0,.gx-xl-0{--bs-gutter-x: 0}.g-xl-0,.gy-xl-0{--bs-gutter-y: 0}.g-xl-1,.gx-xl-1{--bs-gutter-x: .25rem}.g-xl-1,.gy-xl-1{--bs-gutter-y: .25rem}.g-xl-2,.gx-xl-2{--bs-gutter-x: .5rem}.g-xl-2,.gy-xl-2{--bs-gutter-y: .5rem}.g-xl-3,.gx-xl-3{--bs-gutter-x: 1rem}.g-xl-3,.gy-xl-3{--bs-gutter-y: 1rem}.g-xl-4,.gx-xl-4{--bs-gutter-x: 1.5rem}.g-xl-4,.gy-xl-4{--bs-gutter-y: 1.5rem}.g-xl-5,.gx-xl-5{--bs-gutter-x: 3rem}.g-xl-5,.gy-xl-5{--bs-gutter-y: 3rem}}@media (min-width: 1400px){.col-xxl{flex:1 0 0%}.row-cols-xxl-auto>*{flex:0 0 auto;width:auto}.row-cols-xxl-1>*{flex:0 0 auto;width:100%}.row-cols-xxl-2>*{flex:0 0 auto;width:50%}.row-cols-xxl-3>*{flex:0 0 auto;width:33.33333%}.row-cols-xxl-4>*{flex:0 0 auto;width:25%}.row-cols-xxl-5>*{flex:0 0 auto;width:20%}.row-cols-xxl-6>*{flex:0 0 auto;width:16.66667%}.col-xxl-auto{flex:0 0 auto;width:auto}.col-xxl-1{flex:0 0 auto;width:8.33333%}.col-xxl-2{flex:0 0 auto;width:16.66667%}.col-xxl-3{flex:0 0 auto;width:25%}.col-xxl-4{flex:0 0 auto;width:33.33333%}.col-xxl-5{flex:0 0 auto;width:41.66667%}.col-xxl-6{flex:0 0 auto;width:50%}.col-xxl-7{flex:0 0 auto;width:58.33333%}.col-xxl-8{flex:0 0 auto;width:66.66667%}.col-xxl-9{flex:0 0 auto;width:75%}.col-xxl-10{flex:0 0 auto;width:83.33333%}.col-xxl-11{flex:0 0 auto;width:91.66667%}.col-xxl-12{flex:0 0 auto;width:100%}.offset-xxl-0{margin-left:0}.offset-xxl-1{margin-left:8.33333%}.offset-xxl-2{margin-left:16.66667%}.offset-xxl-3{margin-left:25%}.offset-xxl-4{margin-left:33.33333%}.offset-xxl-5{margin-left:41.66667%}.offset-xxl-6{margin-left:50%}.offset-xxl-7{margin-left:58.33333%}.offset-xxl-8{margin-left:66.66667%}.offset-xxl-9{margin-left:75%}.offset-xxl-10{margin-left:83.33333%}.offset-xxl-11{margin-left:91.66667%}.g-xxl-0,.gx-xxl-0{--bs-gutter-x: 0}.g-xxl-0,.gy-xxl-0{--bs-gutter-y: 0}.g-xxl-1,.gx-xxl-1{--bs-gutter-x: .25rem}.g-xxl-1,.gy-xxl-1{--bs-gutter-y: .25rem}.g-xxl-2,.gx-xxl-2{--bs-gutter-x: .5rem}.g-xxl-2,.gy-xxl-2{--bs-gutter-y: .5rem}.g-xxl-3,.gx-xxl-3{--bs-gutter-x: 1rem}.g-xxl-3,.gy-xxl-3{--bs-gutter-y: 1rem}.g-xxl-4,.gx-xxl-4{--bs-gutter-x: 1.5rem}.g-xxl-4,.gy-xxl-4{--bs-gutter-y: 1.5rem}.g-xxl-5,.gx-xxl-5{--bs-gutter-x: 3rem}.g-xxl-5,.gy-xxl-5{--bs-gutter-y: 3rem}}.table{--bs-table-bg: rgba(0,0,0,0);--bs-table-accent-bg: rgba(0,0,0,0);--bs-table-striped-color: #212529;--bs-table-striped-bg: rgba(0,0,0,0.05);--bs-table-active-color: #212529;--bs-table-active-bg: rgba(0,0,0,0.1);--bs-table-hover-color: #212529;--bs-table-hover-bg: rgba(0,0,0,0.075);width:100%;margin-bottom:1rem;color:#212529;vertical-align:top;border-color:#dee2e6}.table>:not(caption)>*>*{padding:.5rem .5rem;background-color:var(--bs-table-bg);border-bottom-width:1px;box-shadow:inset 0 0 0 9999px var(--bs-table-accent-bg)}.table>tbody{vertical-align:inherit}.table>thead{vertical-align:bottom}.table>:not(:first-child){border-top:2px solid currentColor}.caption-top{caption-side:top}.table-sm>:not(caption)>*>*{padding:.25rem .25rem}.table-bordered>:not(caption)>*{border-width:1px 0}.table-bordered>:not(caption)>*>*{border-width:0 1px}.table-borderless>:not(caption)>*>*{border-bottom-width:0}.table-borderless>:not(:first-child){border-top-width:0}.table-striped>tbody>tr:nth-of-type(odd)>*{--bs-table-accent-bg: var(--bs-table-striped-bg);color:var(--bs-table-striped-color)}.table-active{--bs-table-accent-bg: var(--bs-table-active-bg);color:var(--bs-table-active-color)}.table-hover>tbody>tr:hover>*{--bs-table-accent-bg: var(--bs-table-hover-bg);color:var(--bs-table-hover-color)}.table-primary{--bs-table-bg: #cfe2ff;--bs-table-striped-bg: #c5d7f2;--bs-table-striped-color: #000;--bs-table-active-bg: #bacbe6;--bs-table-active-color: #000;--bs-table-hover-bg: #bfd1ec;--bs-table-hover-color: #000;color:#000;border-color:#bacbe6}.table-secondary{--bs-table-bg: #e2e3e5;--bs-table-striped-bg: #d7d8da;--bs-table-striped-color: #000;--bs-table-active-bg: #cbccce;--bs-table-active-color: #000;--bs-table-hover-bg: #d1d2d4;--bs-table-hover-color: #000;color:#000;border-color:#cbccce}.table-success{--bs-table-bg: #d1e7dd;--bs-table-striped-bg: #c7dbd2;--bs-table-striped-color: #000;--bs-table-active-bg: #bcd0c7;--bs-table-active-color: #000;--bs-table-hover-bg: #c1d6cc;--bs-table-hover-color: #000;color:#000;border-color:#bcd0c7}.table-info{--bs-table-bg: #cff4fc;--bs-table-striped-bg: #c5e8ef;--bs-table-striped-color: #000;--bs-table-active-bg: #badce3;--bs-table-active-color: #000;--bs-table-hover-bg: #bfe2e9;--bs-table-hover-color: #000;color:#000;border-color:#badce3}.table-warning{--bs-table-bg: #fff3cd;--bs-table-striped-bg: #f2e7c3;--bs-table-striped-color: #000;--bs-table-active-bg: #e6dbb9;--bs-table-active-color: #000;--bs-table-hover-bg: #ece1be;--bs-table-hover-color: #000;color:#000;border-color:#e6dbb9}.table-danger{--bs-table-bg: #f8d7da;--bs-table-striped-bg: #eccccf;--bs-table-striped-color: #000;--bs-table-active-bg: #dfc2c4;--bs-table-active-color: #000;--bs-table-hover-bg: #e5c7ca;--bs-table-hover-color: #000;color:#000;border-color:#dfc2c4}.table-light{--bs-table-bg: #f8f9fa;--bs-table-striped-bg: #ecedee;--bs-table-striped-color: #000;--bs-table-active-bg: #dfe0e1;--bs-table-active-color: #000;--bs-table-hover-bg: #e5e6e7;--bs-table-hover-color: #000;color:#000;border-color:#dfe0e1}.table-dark{--bs-table-bg: #212529;--bs-table-striped-bg: #2c3034;--bs-table-striped-color: #fff;--bs-table-active-bg: #373b3e;--bs-table-active-color: #fff;--bs-table-hover-bg: #323539;--bs-table-hover-color: #fff;color:#fff;border-color:#373b3e}.table-responsive{overflow-x:auto;-webkit-overflow-scrolling:touch}@media (max-width: 575.98px){.table-responsive-sm{overflow-x:auto;-webkit-overflow-scrolling:touch}}@media (max-width: 767.98px){.table-responsive-md{overflow-x:auto;-webkit-overflow-scrolling:touch}}@media (max-width: 991.98px){.table-responsive-lg{overflow-x:auto;-webkit-overflow-scrolling:touch}}@media (max-width: 1199.98px){.table-responsive-xl{overflow-x:auto;-webkit-overflow-scrolling:touch}}@media (max-width: 1399.98px){.table-responsive-xxl{overflow-x:auto;-webkit-overflow-scrolling:touch}}.form-label{margin-bottom:.5rem}.col-form-label{padding-top:calc(.375rem + 1px);padding-bottom:calc(.375rem + 1px);margin-bottom:0;font-size:inherit;line-height:1.5}.col-form-label-lg{padding-top:calc(.5rem + 1px);padding-bottom:calc(.5rem + 1px);font-size:1.25rem}.col-form-label-sm{padding-top:calc(.25rem + 1px);padding-bottom:calc(.25rem + 1px);font-size:.875rem}.form-text{margin-top:.25rem;font-size:.875em;color:#6c757d}.form-control{display:block;width:100%;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;appearance:none;border-radius:.25rem;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.form-control{transition:none}}.form-control[type=\"file\"]{overflow:hidden}.form-control[type=\"file\"]:not(:disabled):not([readonly]){cursor:pointer}.form-control:focus{color:#212529;background-color:#fff;border-color:#86b7fe;outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25)}.form-control::-webkit-date-and-time-value{height:1.5em}.form-control::placeholder{color:#6c757d;opacity:1}.form-control:disabled,.form-control[readonly]{background-color:#e9ecef;opacity:1}.form-control::file-selector-button{padding:.375rem .75rem;margin:-.375rem -.75rem;margin-inline-end:.75rem;color:#212529;background-color:#e9ecef;pointer-events:none;border-color:inherit;border-style:solid;border-width:0;border-inline-end-width:1px;border-radius:0;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.form-control::file-selector-button{transition:none}}.form-control:hover:not(:disabled):not([readonly])::file-selector-button{background-color:#dde0e3}.form-control::-webkit-file-upload-button{padding:.375rem .75rem;margin:-.375rem -.75rem;margin-inline-end:.75rem;color:#212529;background-color:#e9ecef;pointer-events:none;border-color:inherit;border-style:solid;border-width:0;border-inline-end-width:1px;border-radius:0;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.form-control::-webkit-file-upload-button{transition:none}}.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button{background-color:#dde0e3}.form-control-plaintext{display:block;width:100%;padding:.375rem 0;margin-bottom:0;line-height:1.5;color:#212529;background-color:transparent;border:solid transparent;border-width:1px 0}.form-control-plaintext.form-control-sm,.form-control-plaintext.form-control-lg{padding-right:0;padding-left:0}.form-control-sm{min-height:calc(1.5em + .5rem + 2px);padding:.25rem .5rem;font-size:.875rem;border-radius:.2rem}.form-control-sm::file-selector-button{padding:.25rem .5rem;margin:-.25rem -.5rem;margin-inline-end:.5rem}.form-control-sm::-webkit-file-upload-button{padding:.25rem .5rem;margin:-.25rem -.5rem;margin-inline-end:.5rem}.form-control-lg{min-height:calc(1.5em + 1rem + 2px);padding:.5rem 1rem;font-size:1.25rem;border-radius:.3rem}.form-control-lg::file-selector-button{padding:.5rem 1rem;margin:-.5rem -1rem;margin-inline-end:1rem}.form-control-lg::-webkit-file-upload-button{padding:.5rem 1rem;margin:-.5rem -1rem;margin-inline-end:1rem}textarea.form-control{min-height:calc(1.5em + .75rem + 2px)}textarea.form-control-sm{min-height:calc(1.5em + .5rem + 2px)}textarea.form-control-lg{min-height:calc(1.5em + 1rem + 2px)}.form-control-color{width:3rem;height:auto;padding:.375rem}.form-control-color:not(:disabled):not([readonly]){cursor:pointer}.form-control-color::-moz-color-swatch{height:1.5em;border-radius:.25rem}.form-control-color::-webkit-color-swatch{height:1.5em;border-radius:.25rem}.form-select{display:block;width:100%;padding:.375rem 2.25rem .375rem .75rem;-moz-padding-start:calc(.75rem - 3px);font-size:1rem;font-weight:400;line-height:1.5;color:#212529;background-color:#fff;background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e\");background-repeat:no-repeat;background-position:right .75rem center;background-size:16px 12px;border:1px solid #ced4da;border-radius:.25rem;transition:all 0.3s ease-in-out;appearance:none}@media (prefers-reduced-motion: reduce){.form-select{transition:none}}.form-select:focus{border-color:#86b7fe;outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25)}.form-select[multiple],.form-select[size]:not([size=\"1\"]){padding-right:.75rem;background-image:none}.form-select:disabled{background-color:#e9ecef}.form-select:-moz-focusring{color:transparent;text-shadow:0 0 0 #212529}.form-select-sm{padding-top:.25rem;padding-bottom:.25rem;padding-left:.5rem;font-size:.875rem;border-radius:.2rem}.form-select-lg{padding-top:.5rem;padding-bottom:.5rem;padding-left:1rem;font-size:1.25rem;border-radius:.3rem}.form-check{display:block;min-height:1.5rem;padding-left:1.5em;margin-bottom:.125rem}.form-check .form-check-input{float:left;margin-left:-1.5em}.form-check-input{width:1em;height:1em;margin-top:.25em;vertical-align:top;background-color:#fff;background-repeat:no-repeat;background-position:center;background-size:contain;border:1px solid rgba(0,0,0,0.25);appearance:none;color-adjust:exact}.form-check-input[type=\"checkbox\"]{border-radius:.25em}.form-check-input[type=\"radio\"]{border-radius:50%}.form-check-input:active{filter:brightness(90%)}.form-check-input:focus{border-color:#86b7fe;outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25)}.form-check-input:checked{background-color:#0d6efd;border-color:#0d6efd}.form-check-input:checked[type=\"checkbox\"]{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e\")}.form-check-input:checked[type=\"radio\"]{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e\")}.form-check-input[type=\"checkbox\"]:indeterminate{background-color:#0d6efd;border-color:#0d6efd;background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e\")}.form-check-input:disabled{pointer-events:none;filter:none;opacity:.5}.form-check-input[disabled] ~ .form-check-label,.form-check-input:disabled ~ .form-check-label{opacity:.5}.form-switch{padding-left:2.5em}.form-switch .form-check-input{width:2em;margin-left:-2.5em;background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280,0,0,0.25%29'/%3e%3c/svg%3e\");background-position:left center;border-radius:2em;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.form-switch .form-check-input{transition:none}}.form-switch .form-check-input:focus{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e\")}.form-switch .form-check-input:checked{background-position:right center;background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e\")}.form-check-inline{display:inline-block;margin-right:1rem}.btn-check{position:absolute;clip:rect(0, 0, 0, 0);pointer-events:none}.btn-check[disabled]+.btn,.btn-check:disabled+.btn{pointer-events:none;filter:none;opacity:.65}.form-range{width:100%;height:1.5rem;padding:0;background-color:transparent;appearance:none}.form-range:focus{outline:0}.form-range:focus::-webkit-slider-thumb{box-shadow:0 0 0 1px #fff,0 0 0 .25rem rgba(13,110,253,0.25)}.form-range:focus::-moz-range-thumb{box-shadow:0 0 0 1px #fff,0 0 0 .25rem rgba(13,110,253,0.25)}.form-range::-moz-focus-outer{border:0}.form-range::-webkit-slider-thumb{width:1rem;height:1rem;margin-top:-.25rem;background-color:#0d6efd;border:0;border-radius:1rem;transition:all 0.3s ease-in-out;appearance:none}@media (prefers-reduced-motion: reduce){.form-range::-webkit-slider-thumb{transition:none}}.form-range::-webkit-slider-thumb:active{background-color:#b6d4fe}.form-range::-webkit-slider-runnable-track{width:100%;height:.5rem;color:transparent;cursor:pointer;background-color:#dee2e6;border-color:transparent;border-radius:1rem}.form-range::-moz-range-thumb{width:1rem;height:1rem;background-color:#0d6efd;border:0;border-radius:1rem;transition:all 0.3s ease-in-out;appearance:none}@media (prefers-reduced-motion: reduce){.form-range::-moz-range-thumb{transition:none}}.form-range::-moz-range-thumb:active{background-color:#b6d4fe}.form-range::-moz-range-track{width:100%;height:.5rem;color:transparent;cursor:pointer;background-color:#dee2e6;border-color:transparent;border-radius:1rem}.form-range:disabled{pointer-events:none}.form-range:disabled::-webkit-slider-thumb{background-color:#adb5bd}.form-range:disabled::-moz-range-thumb{background-color:#adb5bd}.form-floating{position:relative}.form-floating>.form-control,.form-floating>.form-select{height:calc(3.5rem + 2px);line-height:1.25}.form-floating>label{position:absolute;top:0;left:0;height:100%;padding:1rem .75rem;pointer-events:none;border:1px solid transparent;transform-origin:0 0;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.form-floating>label{transition:none}}.form-floating>.form-control{padding:1rem .75rem}.form-floating>.form-control::placeholder{color:transparent}.form-floating>.form-control:focus,.form-floating>.form-control:not(:placeholder-shown){padding-top:1.625rem;padding-bottom:.625rem}.form-floating>.form-control:-webkit-autofill{padding-top:1.625rem;padding-bottom:.625rem}.form-floating>.form-select{padding-top:1.625rem;padding-bottom:.625rem}.form-floating>.form-control:focus ~ label,.form-floating>.form-control:not(:placeholder-shown) ~ label,.form-floating>.form-select ~ label{opacity:.65;transform:scale(0.85) translateY(-0.5rem) translateX(0.15rem)}.form-floating>.form-control:-webkit-autofill ~ label{opacity:.65;transform:scale(0.85) translateY(-0.5rem) translateX(0.15rem)}.input-group{position:relative;display:flex;flex-wrap:wrap;align-items:stretch;width:100%}.input-group>.form-control,.input-group>.form-select{position:relative;flex:1 1 auto;width:1%;min-width:0}.input-group>.form-control:focus,.input-group>.form-select:focus{z-index:3}.input-group .btn{position:relative;z-index:2}.input-group .btn:focus{z-index:3}.input-group-text{display:flex;align-items:center;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#212529;text-align:center;white-space:nowrap;background-color:#e9ecef;border:1px solid #ced4da;border-radius:.25rem}.input-group-lg>.form-control,.input-group-lg>.form-select,.input-group-lg>.input-group-text,.input-group-lg>.btn{padding:.5rem 1rem;font-size:1.25rem;border-radius:.3rem}.input-group-sm>.form-control,.input-group-sm>.form-select,.input-group-sm>.input-group-text,.input-group-sm>.btn{padding:.25rem .5rem;font-size:.875rem;border-radius:.2rem}.input-group-lg>.form-select,.input-group-sm>.form-select{padding-right:3rem}.input-group:not(.has-validation)>:not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),.input-group:not(.has-validation)>.dropdown-toggle:nth-last-child(n+3){border-top-right-radius:0;border-bottom-right-radius:0}.input-group.has-validation>:nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),.input-group.has-validation>.dropdown-toggle:nth-last-child(n+4){border-top-right-radius:0;border-bottom-right-radius:0}.input-group>:not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback){margin-left:-1px;border-top-left-radius:0;border-bottom-left-radius:0}.valid-feedback{display:none;width:100%;margin-top:.25rem;font-size:.875em;color:#198754}.valid-tooltip{position:absolute;top:100%;z-index:5;display:none;max-width:100%;padding:.25rem .5rem;margin-top:.1rem;font-size:.875rem;color:#fff;background-color:rgba(25,135,84,0.9);border-radius:.25rem}.was-validated :valid ~ .valid-feedback,.was-validated :valid ~ .valid-tooltip,.is-valid ~ .valid-feedback,.is-valid ~ .valid-tooltip{display:block}.was-validated .form-control:valid,.form-control.is-valid{border-color:#198754;padding-right:calc(1.5em + .75rem);background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\");background-repeat:no-repeat;background-position:right calc(.375em + .1875rem) center;background-size:calc(.75em + .375rem) calc(.75em + .375rem)}.was-validated .form-control:valid:focus,.form-control.is-valid:focus{border-color:#198754;box-shadow:0 0 0 .25rem rgba(25,135,84,0.25)}.was-validated textarea.form-control:valid,textarea.form-control.is-valid{padding-right:calc(1.5em + .75rem);background-position:top calc(.375em + .1875rem) right calc(.375em + .1875rem)}.was-validated .form-select:valid,.form-select.is-valid{border-color:#198754}.was-validated .form-select:valid:not([multiple]):not([size]),.was-validated .form-select:valid:not([multiple])[size=\"1\"],.form-select.is-valid:not([multiple]):not([size]),.form-select.is-valid:not([multiple])[size=\"1\"]{padding-right:4.125rem;background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e\"),url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e\");background-position:right .75rem center,center right 2.25rem;background-size:16px 12px,calc(.75em + .375rem) calc(.75em + .375rem)}.was-validated .form-select:valid:focus,.form-select.is-valid:focus{border-color:#198754;box-shadow:0 0 0 .25rem rgba(25,135,84,0.25)}.was-validated .form-check-input:valid,.form-check-input.is-valid{border-color:#198754}.was-validated .form-check-input:valid:checked,.form-check-input.is-valid:checked{background-color:#198754}.was-validated .form-check-input:valid:focus,.form-check-input.is-valid:focus{box-shadow:0 0 0 .25rem rgba(25,135,84,0.25)}.was-validated .form-check-input:valid ~ .form-check-label,.form-check-input.is-valid ~ .form-check-label{color:#198754}.form-check-inline .form-check-input ~ .valid-feedback{margin-left:.5em}.was-validated .input-group .form-control:valid,.input-group .form-control.is-valid,.was-validated .input-group .form-select:valid,.input-group .form-select.is-valid{z-index:1}.was-validated .input-group .form-control:valid:focus,.input-group .form-control.is-valid:focus,.was-validated .input-group .form-select:valid:focus,.input-group .form-select.is-valid:focus{z-index:3}.invalid-feedback{display:none;width:100%;margin-top:.25rem;font-size:.875em;color:#dc3545}.invalid-tooltip{position:absolute;top:100%;z-index:5;display:none;max-width:100%;padding:.25rem .5rem;margin-top:.1rem;font-size:.875rem;color:#fff;background-color:rgba(220,53,69,0.9);border-radius:.25rem}.was-validated :invalid ~ .invalid-feedback,.was-validated :invalid ~ .invalid-tooltip,.is-invalid ~ .invalid-feedback,.is-invalid ~ .invalid-tooltip{display:block}.was-validated .form-control:invalid,.form-control.is-invalid{border-color:#dc3545;padding-right:calc(1.5em + .75rem);background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e\");background-repeat:no-repeat;background-position:right calc(.375em + .1875rem) center;background-size:calc(.75em + .375rem) calc(.75em + .375rem)}.was-validated .form-control:invalid:focus,.form-control.is-invalid:focus{border-color:#dc3545;box-shadow:0 0 0 .25rem rgba(220,53,69,0.25)}.was-validated textarea.form-control:invalid,textarea.form-control.is-invalid{padding-right:calc(1.5em + .75rem);background-position:top calc(.375em + .1875rem) right calc(.375em + .1875rem)}.was-validated .form-select:invalid,.form-select.is-invalid{border-color:#dc3545}.was-validated .form-select:invalid:not([multiple]):not([size]),.was-validated .form-select:invalid:not([multiple])[size=\"1\"],.form-select.is-invalid:not([multiple]):not([size]),.form-select.is-invalid:not([multiple])[size=\"1\"]{padding-right:4.125rem;background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e\"),url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e\");background-position:right .75rem center,center right 2.25rem;background-size:16px 12px,calc(.75em + .375rem) calc(.75em + .375rem)}.was-validated .form-select:invalid:focus,.form-select.is-invalid:focus{border-color:#dc3545;box-shadow:0 0 0 .25rem rgba(220,53,69,0.25)}.was-validated .form-check-input:invalid,.form-check-input.is-invalid{border-color:#dc3545}.was-validated .form-check-input:invalid:checked,.form-check-input.is-invalid:checked{background-color:#dc3545}.was-validated .form-check-input:invalid:focus,.form-check-input.is-invalid:focus{box-shadow:0 0 0 .25rem rgba(220,53,69,0.25)}.was-validated .form-check-input:invalid ~ .form-check-label,.form-check-input.is-invalid ~ .form-check-label{color:#dc3545}.form-check-inline .form-check-input ~ .invalid-feedback{margin-left:.5em}.was-validated .input-group .form-control:invalid,.input-group .form-control.is-invalid,.was-validated .input-group .form-select:invalid,.input-group .form-select.is-invalid{z-index:2}.was-validated .input-group .form-control:invalid:focus,.input-group .form-control.is-invalid:focus,.was-validated .input-group .form-select:invalid:focus,.input-group .form-select.is-invalid:focus{z-index:3}.btn{display:inline-block;font-weight:400;line-height:1.5;color:#212529;text-align:center;text-decoration:none;vertical-align:middle;cursor:pointer;user-select:none;background-color:transparent;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;border-radius:.25rem;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.btn{transition:none}}.btn:hover{color:#212529}.btn-check:focus+.btn,.btn:focus{outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25)}.btn:disabled,.btn.disabled,fieldset:disabled .btn{pointer-events:none;opacity:.65}.btn-primary{color:#fff;background-color:#0d6efd;border-color:#0d6efd}.btn-primary:hover{color:#fff;background-color:#0b5ed7;border-color:#0a58ca}.btn-check:focus+.btn-primary,.btn-primary:focus{color:#fff;background-color:#0b5ed7;border-color:#0a58ca;box-shadow:0 0 0 .25rem rgba(49,132,253,0.5)}.btn-check:checked+.btn-primary,.btn-check:active+.btn-primary,.btn-primary:active,.btn-primary.active,.show>.btn-primary.dropdown-toggle{color:#fff;background-color:#0a58ca;border-color:#0a53be}.btn-check:checked+.btn-primary:focus,.btn-check:active+.btn-primary:focus,.btn-primary:active:focus,.btn-primary.active:focus,.show>.btn-primary.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(49,132,253,0.5)}.btn-primary:disabled,.btn-primary.disabled{color:#fff;background-color:#0d6efd;border-color:#0d6efd}.btn-secondary{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-secondary:hover{color:#fff;background-color:#5c636a;border-color:#565e64}.btn-check:focus+.btn-secondary,.btn-secondary:focus{color:#fff;background-color:#5c636a;border-color:#565e64;box-shadow:0 0 0 .25rem rgba(130,138,145,0.5)}.btn-check:checked+.btn-secondary,.btn-check:active+.btn-secondary,.btn-secondary:active,.btn-secondary.active,.show>.btn-secondary.dropdown-toggle{color:#fff;background-color:#565e64;border-color:#51585e}.btn-check:checked+.btn-secondary:focus,.btn-check:active+.btn-secondary:focus,.btn-secondary:active:focus,.btn-secondary.active:focus,.show>.btn-secondary.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(130,138,145,0.5)}.btn-secondary:disabled,.btn-secondary.disabled{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-success{color:#fff;background-color:#198754;border-color:#198754}.btn-success:hover{color:#fff;background-color:#157347;border-color:#146c43}.btn-check:focus+.btn-success,.btn-success:focus{color:#fff;background-color:#157347;border-color:#146c43;box-shadow:0 0 0 .25rem rgba(60,153,110,0.5)}.btn-check:checked+.btn-success,.btn-check:active+.btn-success,.btn-success:active,.btn-success.active,.show>.btn-success.dropdown-toggle{color:#fff;background-color:#146c43;border-color:#13653f}.btn-check:checked+.btn-success:focus,.btn-check:active+.btn-success:focus,.btn-success:active:focus,.btn-success.active:focus,.show>.btn-success.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(60,153,110,0.5)}.btn-success:disabled,.btn-success.disabled{color:#fff;background-color:#198754;border-color:#198754}.btn-info{color:#000;background-color:#0dcaf0;border-color:#0dcaf0}.btn-info:hover{color:#000;background-color:#31d2f2;border-color:#25cff2}.btn-check:focus+.btn-info,.btn-info:focus{color:#000;background-color:#31d2f2;border-color:#25cff2;box-shadow:0 0 0 .25rem rgba(11,172,204,0.5)}.btn-check:checked+.btn-info,.btn-check:active+.btn-info,.btn-info:active,.btn-info.active,.show>.btn-info.dropdown-toggle{color:#000;background-color:#3dd5f3;border-color:#25cff2}.btn-check:checked+.btn-info:focus,.btn-check:active+.btn-info:focus,.btn-info:active:focus,.btn-info.active:focus,.show>.btn-info.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(11,172,204,0.5)}.btn-info:disabled,.btn-info.disabled{color:#000;background-color:#0dcaf0;border-color:#0dcaf0}.btn-warning{color:#000;background-color:#ffc107;border-color:#ffc107}.btn-warning:hover{color:#000;background-color:#ffca2c;border-color:#ffc720}.btn-check:focus+.btn-warning,.btn-warning:focus{color:#000;background-color:#ffca2c;border-color:#ffc720;box-shadow:0 0 0 .25rem rgba(217,164,6,0.5)}.btn-check:checked+.btn-warning,.btn-check:active+.btn-warning,.btn-warning:active,.btn-warning.active,.show>.btn-warning.dropdown-toggle{color:#000;background-color:#ffcd39;border-color:#ffc720}.btn-check:checked+.btn-warning:focus,.btn-check:active+.btn-warning:focus,.btn-warning:active:focus,.btn-warning.active:focus,.show>.btn-warning.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(217,164,6,0.5)}.btn-warning:disabled,.btn-warning.disabled{color:#000;background-color:#ffc107;border-color:#ffc107}.btn-danger{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-danger:hover{color:#fff;background-color:#bb2d3b;border-color:#b02a37}.btn-check:focus+.btn-danger,.btn-danger:focus{color:#fff;background-color:#bb2d3b;border-color:#b02a37;box-shadow:0 0 0 .25rem rgba(225,83,97,0.5)}.btn-check:checked+.btn-danger,.btn-check:active+.btn-danger,.btn-danger:active,.btn-danger.active,.show>.btn-danger.dropdown-toggle{color:#fff;background-color:#b02a37;border-color:#a52834}.btn-check:checked+.btn-danger:focus,.btn-check:active+.btn-danger:focus,.btn-danger:active:focus,.btn-danger.active:focus,.show>.btn-danger.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(225,83,97,0.5)}.btn-danger:disabled,.btn-danger.disabled{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-light{color:#000;background-color:#f8f9fa;border-color:#f8f9fa}.btn-light:hover{color:#000;background-color:#f9fafb;border-color:#f9fafb}.btn-check:focus+.btn-light,.btn-light:focus{color:#000;background-color:#f9fafb;border-color:#f9fafb;box-shadow:0 0 0 .25rem rgba(211,212,213,0.5)}.btn-check:checked+.btn-light,.btn-check:active+.btn-light,.btn-light:active,.btn-light.active,.show>.btn-light.dropdown-toggle{color:#000;background-color:#f9fafb;border-color:#f9fafb}.btn-check:checked+.btn-light:focus,.btn-check:active+.btn-light:focus,.btn-light:active:focus,.btn-light.active:focus,.show>.btn-light.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(211,212,213,0.5)}.btn-light:disabled,.btn-light.disabled{color:#000;background-color:#f8f9fa;border-color:#f8f9fa}.btn-dark{color:#fff;background-color:#212529;border-color:#212529}.btn-dark:hover{color:#fff;background-color:#1c1f23;border-color:#1a1e21}.btn-check:focus+.btn-dark,.btn-dark:focus{color:#fff;background-color:#1c1f23;border-color:#1a1e21;box-shadow:0 0 0 .25rem rgba(66,70,73,0.5)}.btn-check:checked+.btn-dark,.btn-check:active+.btn-dark,.btn-dark:active,.btn-dark.active,.show>.btn-dark.dropdown-toggle{color:#fff;background-color:#1a1e21;border-color:#191c1f}.btn-check:checked+.btn-dark:focus,.btn-check:active+.btn-dark:focus,.btn-dark:active:focus,.btn-dark.active:focus,.show>.btn-dark.dropdown-toggle:focus{box-shadow:0 0 0 .25rem rgba(66,70,73,0.5)}.btn-dark:disabled,.btn-dark.disabled{color:#fff;background-color:#212529;border-color:#212529}.btn-outline-primary{color:#0d6efd;border-color:#0d6efd}.btn-outline-primary:hover{color:#fff;background-color:#0d6efd;border-color:#0d6efd}.btn-check:focus+.btn-outline-primary,.btn-outline-primary:focus{box-shadow:0 0 0 .25rem rgba(13,110,253,0.5)}.btn-check:checked+.btn-outline-primary,.btn-check:active+.btn-outline-primary,.btn-outline-primary:active,.btn-outline-primary.active,.btn-outline-primary.dropdown-toggle.show{color:#fff;background-color:#0d6efd;border-color:#0d6efd}.btn-check:checked+.btn-outline-primary:focus,.btn-check:active+.btn-outline-primary:focus,.btn-outline-primary:active:focus,.btn-outline-primary.active:focus,.btn-outline-primary.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(13,110,253,0.5)}.btn-outline-primary:disabled,.btn-outline-primary.disabled{color:#0d6efd;background-color:transparent}.btn-outline-secondary{color:#6c757d;border-color:#6c757d}.btn-outline-secondary:hover{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-check:focus+.btn-outline-secondary,.btn-outline-secondary:focus{box-shadow:0 0 0 .25rem rgba(108,117,125,0.5)}.btn-check:checked+.btn-outline-secondary,.btn-check:active+.btn-outline-secondary,.btn-outline-secondary:active,.btn-outline-secondary.active,.btn-outline-secondary.dropdown-toggle.show{color:#fff;background-color:#6c757d;border-color:#6c757d}.btn-check:checked+.btn-outline-secondary:focus,.btn-check:active+.btn-outline-secondary:focus,.btn-outline-secondary:active:focus,.btn-outline-secondary.active:focus,.btn-outline-secondary.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(108,117,125,0.5)}.btn-outline-secondary:disabled,.btn-outline-secondary.disabled{color:#6c757d;background-color:transparent}.btn-outline-success{color:#198754;border-color:#198754}.btn-outline-success:hover{color:#fff;background-color:#198754;border-color:#198754}.btn-check:focus+.btn-outline-success,.btn-outline-success:focus{box-shadow:0 0 0 .25rem rgba(25,135,84,0.5)}.btn-check:checked+.btn-outline-success,.btn-check:active+.btn-outline-success,.btn-outline-success:active,.btn-outline-success.active,.btn-outline-success.dropdown-toggle.show{color:#fff;background-color:#198754;border-color:#198754}.btn-check:checked+.btn-outline-success:focus,.btn-check:active+.btn-outline-success:focus,.btn-outline-success:active:focus,.btn-outline-success.active:focus,.btn-outline-success.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(25,135,84,0.5)}.btn-outline-success:disabled,.btn-outline-success.disabled{color:#198754;background-color:transparent}.btn-outline-info{color:#0dcaf0;border-color:#0dcaf0}.btn-outline-info:hover{color:#000;background-color:#0dcaf0;border-color:#0dcaf0}.btn-check:focus+.btn-outline-info,.btn-outline-info:focus{box-shadow:0 0 0 .25rem rgba(13,202,240,0.5)}.btn-check:checked+.btn-outline-info,.btn-check:active+.btn-outline-info,.btn-outline-info:active,.btn-outline-info.active,.btn-outline-info.dropdown-toggle.show{color:#000;background-color:#0dcaf0;border-color:#0dcaf0}.btn-check:checked+.btn-outline-info:focus,.btn-check:active+.btn-outline-info:focus,.btn-outline-info:active:focus,.btn-outline-info.active:focus,.btn-outline-info.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(13,202,240,0.5)}.btn-outline-info:disabled,.btn-outline-info.disabled{color:#0dcaf0;background-color:transparent}.btn-outline-warning{color:#ffc107;border-color:#ffc107}.btn-outline-warning:hover{color:#000;background-color:#ffc107;border-color:#ffc107}.btn-check:focus+.btn-outline-warning,.btn-outline-warning:focus{box-shadow:0 0 0 .25rem rgba(255,193,7,0.5)}.btn-check:checked+.btn-outline-warning,.btn-check:active+.btn-outline-warning,.btn-outline-warning:active,.btn-outline-warning.active,.btn-outline-warning.dropdown-toggle.show{color:#000;background-color:#ffc107;border-color:#ffc107}.btn-check:checked+.btn-outline-warning:focus,.btn-check:active+.btn-outline-warning:focus,.btn-outline-warning:active:focus,.btn-outline-warning.active:focus,.btn-outline-warning.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(255,193,7,0.5)}.btn-outline-warning:disabled,.btn-outline-warning.disabled{color:#ffc107;background-color:transparent}.btn-outline-danger{color:#dc3545;border-color:#dc3545}.btn-outline-danger:hover{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-check:focus+.btn-outline-danger,.btn-outline-danger:focus{box-shadow:0 0 0 .25rem rgba(220,53,69,0.5)}.btn-check:checked+.btn-outline-danger,.btn-check:active+.btn-outline-danger,.btn-outline-danger:active,.btn-outline-danger.active,.btn-outline-danger.dropdown-toggle.show{color:#fff;background-color:#dc3545;border-color:#dc3545}.btn-check:checked+.btn-outline-danger:focus,.btn-check:active+.btn-outline-danger:focus,.btn-outline-danger:active:focus,.btn-outline-danger.active:focus,.btn-outline-danger.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(220,53,69,0.5)}.btn-outline-danger:disabled,.btn-outline-danger.disabled{color:#dc3545;background-color:transparent}.btn-outline-light{color:#f8f9fa;border-color:#f8f9fa}.btn-outline-light:hover{color:#000;background-color:#f8f9fa;border-color:#f8f9fa}.btn-check:focus+.btn-outline-light,.btn-outline-light:focus{box-shadow:0 0 0 .25rem rgba(248,249,250,0.5)}.btn-check:checked+.btn-outline-light,.btn-check:active+.btn-outline-light,.btn-outline-light:active,.btn-outline-light.active,.btn-outline-light.dropdown-toggle.show{color:#000;background-color:#f8f9fa;border-color:#f8f9fa}.btn-check:checked+.btn-outline-light:focus,.btn-check:active+.btn-outline-light:focus,.btn-outline-light:active:focus,.btn-outline-light.active:focus,.btn-outline-light.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(248,249,250,0.5)}.btn-outline-light:disabled,.btn-outline-light.disabled{color:#f8f9fa;background-color:transparent}.btn-outline-dark{color:#212529;border-color:#212529}.btn-outline-dark:hover{color:#fff;background-color:#212529;border-color:#212529}.btn-check:focus+.btn-outline-dark,.btn-outline-dark:focus{box-shadow:0 0 0 .25rem rgba(33,37,41,0.5)}.btn-check:checked+.btn-outline-dark,.btn-check:active+.btn-outline-dark,.btn-outline-dark:active,.btn-outline-dark.active,.btn-outline-dark.dropdown-toggle.show{color:#fff;background-color:#212529;border-color:#212529}.btn-check:checked+.btn-outline-dark:focus,.btn-check:active+.btn-outline-dark:focus,.btn-outline-dark:active:focus,.btn-outline-dark.active:focus,.btn-outline-dark.dropdown-toggle.show:focus{box-shadow:0 0 0 .25rem rgba(33,37,41,0.5)}.btn-outline-dark:disabled,.btn-outline-dark.disabled{color:#212529;background-color:transparent}.btn-link{font-weight:400;color:#0d6efd;text-decoration:underline}.btn-link:hover{color:#0a58ca}.btn-link:disabled,.btn-link.disabled{color:#6c757d}.btn-lg,.btn-group-lg>.btn{padding:.5rem 1rem;font-size:1.25rem;border-radius:.3rem}.btn-sm,.btn-group-sm>.btn{padding:.25rem .5rem;font-size:.875rem;border-radius:.2rem}.fade{transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.fade{transition:none}}.fade:not(.show){opacity:0}.collapse:not(.show){display:none}.collapsing{height:0;overflow:hidden;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.collapsing{transition:none}}.collapsing.collapse-horizontal{width:0;height:auto;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.collapsing.collapse-horizontal{transition:none}}.dropup,.dropend,.dropdown,.dropstart{position:relative}.dropdown-toggle{white-space:nowrap}.dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:\"\";border-top:.3em solid;border-right:.3em solid transparent;border-bottom:0;border-left:.3em solid transparent}.dropdown-toggle:empty::after{margin-left:0}.dropdown-menu{position:absolute;z-index:1000;display:none;min-width:10rem;padding:.5rem 0;margin:0;font-size:1rem;color:#212529;text-align:left;list-style:none;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,0.15);border-radius:.25rem}.dropdown-menu[data-bs-popper]{top:100%;left:0;margin-top:.125rem}.dropdown-menu-start{--bs-position: start}.dropdown-menu-start[data-bs-popper]{right:auto;left:0}.dropdown-menu-end{--bs-position: end}.dropdown-menu-end[data-bs-popper]{right:0;left:auto}@media (min-width: 576px){.dropdown-menu-sm-start{--bs-position: start}.dropdown-menu-sm-start[data-bs-popper]{right:auto;left:0}.dropdown-menu-sm-end{--bs-position: end}.dropdown-menu-sm-end[data-bs-popper]{right:0;left:auto}}@media (min-width: 768px){.dropdown-menu-md-start{--bs-position: start}.dropdown-menu-md-start[data-bs-popper]{right:auto;left:0}.dropdown-menu-md-end{--bs-position: end}.dropdown-menu-md-end[data-bs-popper]{right:0;left:auto}}@media (min-width: 992px){.dropdown-menu-lg-start{--bs-position: start}.dropdown-menu-lg-start[data-bs-popper]{right:auto;left:0}.dropdown-menu-lg-end{--bs-position: end}.dropdown-menu-lg-end[data-bs-popper]{right:0;left:auto}}@media (min-width: 1200px){.dropdown-menu-xl-start{--bs-position: start}.dropdown-menu-xl-start[data-bs-popper]{right:auto;left:0}.dropdown-menu-xl-end{--bs-position: end}.dropdown-menu-xl-end[data-bs-popper]{right:0;left:auto}}@media (min-width: 1400px){.dropdown-menu-xxl-start{--bs-position: start}.dropdown-menu-xxl-start[data-bs-popper]{right:auto;left:0}.dropdown-menu-xxl-end{--bs-position: end}.dropdown-menu-xxl-end[data-bs-popper]{right:0;left:auto}}.dropup .dropdown-menu[data-bs-popper]{top:auto;bottom:100%;margin-top:0;margin-bottom:.125rem}.dropup .dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:\"\";border-top:0;border-right:.3em solid transparent;border-bottom:.3em solid;border-left:.3em solid transparent}.dropup .dropdown-toggle:empty::after{margin-left:0}.dropend .dropdown-menu[data-bs-popper]{top:0;right:auto;left:100%;margin-top:0;margin-left:.125rem}.dropend .dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:\"\";border-top:.3em solid transparent;border-right:0;border-bottom:.3em solid transparent;border-left:.3em solid}.dropend .dropdown-toggle:empty::after{margin-left:0}.dropend .dropdown-toggle::after{vertical-align:0}.dropstart .dropdown-menu[data-bs-popper]{top:0;right:100%;left:auto;margin-top:0;margin-right:.125rem}.dropstart .dropdown-toggle::after{display:inline-block;margin-left:.255em;vertical-align:.255em;content:\"\"}.dropstart .dropdown-toggle::after{display:none}.dropstart .dropdown-toggle::before{display:inline-block;margin-right:.255em;vertical-align:.255em;content:\"\";border-top:.3em solid transparent;border-right:.3em solid;border-bottom:.3em solid transparent}.dropstart .dropdown-toggle:empty::after{margin-left:0}.dropstart .dropdown-toggle::before{vertical-align:0}.dropdown-divider{height:0;margin:.5rem 0;overflow:hidden;border-top:1px solid rgba(0,0,0,0.15)}.dropdown-item{display:block;width:100%;padding:.25rem 1rem;clear:both;font-weight:400;color:#212529;text-align:inherit;text-decoration:none;white-space:nowrap;background-color:transparent;border:0}.dropdown-item:hover,.dropdown-item:focus{color:#1e2125;background-color:#e9ecef}.dropdown-item.active,.dropdown-item:active{color:#fff;text-decoration:none;background-color:#0d6efd}.dropdown-item.disabled,.dropdown-item:disabled{color:#adb5bd;pointer-events:none;background-color:transparent}.dropdown-menu.show{display:block}.dropdown-header{display:block;padding:.5rem 1rem;margin-bottom:0;font-size:.875rem;color:#6c757d;white-space:nowrap}.dropdown-item-text{display:block;padding:.25rem 1rem;color:#212529}.dropdown-menu-dark{color:#dee2e6;background-color:#343a40;border-color:rgba(0,0,0,0.15)}.dropdown-menu-dark .dropdown-item{color:#dee2e6}.dropdown-menu-dark .dropdown-item:hover,.dropdown-menu-dark .dropdown-item:focus{color:#fff;background-color:rgba(255,255,255,0.15)}.dropdown-menu-dark .dropdown-item.active,.dropdown-menu-dark .dropdown-item:active{color:#fff;background-color:#0d6efd}.dropdown-menu-dark .dropdown-item.disabled,.dropdown-menu-dark .dropdown-item:disabled{color:#adb5bd}.dropdown-menu-dark .dropdown-divider{border-color:rgba(0,0,0,0.15)}.dropdown-menu-dark .dropdown-item-text{color:#dee2e6}.dropdown-menu-dark .dropdown-header{color:#adb5bd}.btn-group,.btn-group-vertical{position:relative;display:inline-flex;vertical-align:middle}.btn-group>.btn,.btn-group-vertical>.btn{position:relative;flex:1 1 auto}.btn-group>.btn-check:checked+.btn,.btn-group>.btn-check:focus+.btn,.btn-group>.btn:hover,.btn-group>.btn:focus,.btn-group>.btn:active,.btn-group>.btn.active,.btn-group-vertical>.btn-check:checked+.btn,.btn-group-vertical>.btn-check:focus+.btn,.btn-group-vertical>.btn:hover,.btn-group-vertical>.btn:focus,.btn-group-vertical>.btn:active,.btn-group-vertical>.btn.active{z-index:1}.btn-toolbar{display:flex;flex-wrap:wrap;justify-content:flex-start}.btn-toolbar .input-group{width:auto}.btn-group>.btn:not(:first-child),.btn-group>.btn-group:not(:first-child){margin-left:-1px}.btn-group>.btn:not(:last-child):not(.dropdown-toggle),.btn-group>.btn-group:not(:last-child)>.btn{border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn:nth-child(n+3),.btn-group>:not(.btn-check)+.btn,.btn-group>.btn-group:not(:first-child)>.btn{border-top-left-radius:0;border-bottom-left-radius:0}.dropdown-toggle-split{padding-right:.5625rem;padding-left:.5625rem}.dropdown-toggle-split::after,.dropup .dropdown-toggle-split::after,.dropend .dropdown-toggle-split::after{margin-left:0}.dropstart .dropdown-toggle-split::before{margin-right:0}.btn-sm+.dropdown-toggle-split,.btn-group-sm>.btn+.dropdown-toggle-split{padding-right:.375rem;padding-left:.375rem}.btn-lg+.dropdown-toggle-split,.btn-group-lg>.btn+.dropdown-toggle-split{padding-right:.75rem;padding-left:.75rem}.btn-group-vertical{flex-direction:column;align-items:flex-start;justify-content:center}.btn-group-vertical>.btn,.btn-group-vertical>.btn-group{width:100%}.btn-group-vertical>.btn:not(:first-child),.btn-group-vertical>.btn-group:not(:first-child){margin-top:-1px}.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle),.btn-group-vertical>.btn-group:not(:last-child)>.btn{border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn ~ .btn,.btn-group-vertical>.btn-group:not(:first-child)>.btn{border-top-left-radius:0;border-top-right-radius:0}.nav{display:flex;flex-wrap:wrap;padding-left:0;margin-bottom:0;list-style:none}.nav-link{display:block;padding:.5rem 1rem;color:#0d6efd;text-decoration:none;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.nav-link{transition:none}}.nav-link:hover,.nav-link:focus{color:#0a58ca}.nav-link.disabled{color:#6c757d;pointer-events:none;cursor:default}.nav-tabs{border-bottom:1px solid #dee2e6}.nav-tabs .nav-link{margin-bottom:-1px;background:none;border:1px solid transparent;border-top-left-radius:.25rem;border-top-right-radius:.25rem}.nav-tabs .nav-link:hover,.nav-tabs .nav-link:focus{border-color:#e9ecef #e9ecef #dee2e6;isolation:isolate}.nav-tabs .nav-link.disabled{color:#6c757d;background-color:transparent;border-color:transparent}.nav-tabs .nav-link.active,.nav-tabs .nav-item.show .nav-link{color:#495057;background-color:#fff;border-color:#dee2e6 #dee2e6 #fff}.nav-tabs .dropdown-menu{margin-top:-1px;border-top-left-radius:0;border-top-right-radius:0}.nav-pills .nav-link{background:none;border:0;border-radius:.25rem}.nav-pills .nav-link.active,.nav-pills .show>.nav-link{color:#fff;background-color:#0d6efd}.nav-fill>.nav-link,.nav-fill .nav-item{flex:1 1 auto;text-align:center}.nav-justified>.nav-link,.nav-justified .nav-item{flex-basis:0;flex-grow:1;text-align:center}.nav-fill .nav-item .nav-link,.nav-justified .nav-item .nav-link{width:100%}.tab-content>.tab-pane{display:none}.tab-content>.active{display:block}.navbar{position:relative;display:flex;flex-wrap:wrap;align-items:center;justify-content:space-between;padding-top:.5rem;padding-bottom:.5rem}.navbar>.container,.navbar>.container-fluid,.navbar>.container-sm,.navbar>.container-md,.navbar>.container-lg,.navbar>.container-xl,.navbar>.container-xxl{display:flex;flex-wrap:inherit;align-items:center;justify-content:space-between}.navbar-brand{padding-top:.3125rem;padding-bottom:.3125rem;margin-right:1rem;font-size:1.25rem;text-decoration:none;white-space:nowrap}.navbar-nav{display:flex;flex-direction:column;padding-left:0;margin-bottom:0;list-style:none}.navbar-nav .nav-link{padding-right:0;padding-left:0}.navbar-nav .dropdown-menu{position:static}.navbar-text{padding-top:.5rem;padding-bottom:.5rem}.navbar-collapse{flex-basis:100%;flex-grow:1;align-items:center}.navbar-toggler{padding:.25rem .75rem;font-size:1.25rem;line-height:1;background-color:transparent;border:1px solid transparent;border-radius:.25rem;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.navbar-toggler{transition:none}}.navbar-toggler:hover{text-decoration:none}.navbar-toggler:focus{text-decoration:none;outline:0;box-shadow:0 0 0 .25rem}.navbar-toggler-icon{display:inline-block;width:1.5em;height:1.5em;vertical-align:middle;background-repeat:no-repeat;background-position:center;background-size:100%}.navbar-nav-scroll{max-height:var(--bs-scroll-height, 75vh);overflow-y:auto}@media (min-width: 576px){.navbar-expand-sm{flex-wrap:nowrap;justify-content:flex-start}.navbar-expand-sm .navbar-nav{flex-direction:row}.navbar-expand-sm .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-sm .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-sm .navbar-nav-scroll{overflow:visible}.navbar-expand-sm .navbar-collapse{display:flex !important;flex-basis:auto}.navbar-expand-sm .navbar-toggler{display:none}.navbar-expand-sm .offcanvas-header{display:none}.navbar-expand-sm .offcanvas{position:inherit;bottom:0;z-index:1000;flex-grow:1;visibility:visible !important;background-color:transparent;border-right:0;border-left:0;transition:all 0.3s ease-in-out;transform:none}.navbar-expand-sm .offcanvas-top,.navbar-expand-sm .offcanvas-bottom{height:auto;border-top:0;border-bottom:0}.navbar-expand-sm .offcanvas-body{display:flex;flex-grow:0;padding:0;overflow-y:visible}}@media (min-width: 768px){.navbar-expand-md{flex-wrap:nowrap;justify-content:flex-start}.navbar-expand-md .navbar-nav{flex-direction:row}.navbar-expand-md .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-md .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-md .navbar-nav-scroll{overflow:visible}.navbar-expand-md .navbar-collapse{display:flex !important;flex-basis:auto}.navbar-expand-md .navbar-toggler{display:none}.navbar-expand-md .offcanvas-header{display:none}.navbar-expand-md .offcanvas{position:inherit;bottom:0;z-index:1000;flex-grow:1;visibility:visible !important;background-color:transparent;border-right:0;border-left:0;transition:all 0.3s ease-in-out;transform:none}.navbar-expand-md .offcanvas-top,.navbar-expand-md .offcanvas-bottom{height:auto;border-top:0;border-bottom:0}.navbar-expand-md .offcanvas-body{display:flex;flex-grow:0;padding:0;overflow-y:visible}}@media (min-width: 992px){.navbar-expand-lg{flex-wrap:nowrap;justify-content:flex-start}.navbar-expand-lg .navbar-nav{flex-direction:row}.navbar-expand-lg .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-lg .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-lg .navbar-nav-scroll{overflow:visible}.navbar-expand-lg .navbar-collapse{display:flex !important;flex-basis:auto}.navbar-expand-lg .navbar-toggler{display:none}.navbar-expand-lg .offcanvas-header{display:none}.navbar-expand-lg .offcanvas{position:inherit;bottom:0;z-index:1000;flex-grow:1;visibility:visible !important;background-color:transparent;border-right:0;border-left:0;transition:all 0.3s ease-in-out;transform:none}.navbar-expand-lg .offcanvas-top,.navbar-expand-lg .offcanvas-bottom{height:auto;border-top:0;border-bottom:0}.navbar-expand-lg .offcanvas-body{display:flex;flex-grow:0;padding:0;overflow-y:visible}}@media (min-width: 1200px){.navbar-expand-xl{flex-wrap:nowrap;justify-content:flex-start}.navbar-expand-xl .navbar-nav{flex-direction:row}.navbar-expand-xl .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-xl .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-xl .navbar-nav-scroll{overflow:visible}.navbar-expand-xl .navbar-collapse{display:flex !important;flex-basis:auto}.navbar-expand-xl .navbar-toggler{display:none}.navbar-expand-xl .offcanvas-header{display:none}.navbar-expand-xl .offcanvas{position:inherit;bottom:0;z-index:1000;flex-grow:1;visibility:visible !important;background-color:transparent;border-right:0;border-left:0;transition:all 0.3s ease-in-out;transform:none}.navbar-expand-xl .offcanvas-top,.navbar-expand-xl .offcanvas-bottom{height:auto;border-top:0;border-bottom:0}.navbar-expand-xl .offcanvas-body{display:flex;flex-grow:0;padding:0;overflow-y:visible}}@media (min-width: 1400px){.navbar-expand-xxl{flex-wrap:nowrap;justify-content:flex-start}.navbar-expand-xxl .navbar-nav{flex-direction:row}.navbar-expand-xxl .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-xxl .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand-xxl .navbar-nav-scroll{overflow:visible}.navbar-expand-xxl .navbar-collapse{display:flex !important;flex-basis:auto}.navbar-expand-xxl .navbar-toggler{display:none}.navbar-expand-xxl .offcanvas-header{display:none}.navbar-expand-xxl .offcanvas{position:inherit;bottom:0;z-index:1000;flex-grow:1;visibility:visible !important;background-color:transparent;border-right:0;border-left:0;transition:all 0.3s ease-in-out;transform:none}.navbar-expand-xxl .offcanvas-top,.navbar-expand-xxl .offcanvas-bottom{height:auto;border-top:0;border-bottom:0}.navbar-expand-xxl .offcanvas-body{display:flex;flex-grow:0;padding:0;overflow-y:visible}}.navbar-expand{flex-wrap:nowrap;justify-content:flex-start}.navbar-expand .navbar-nav{flex-direction:row}.navbar-expand .navbar-nav .dropdown-menu{position:absolute}.navbar-expand .navbar-nav .nav-link{padding-right:.5rem;padding-left:.5rem}.navbar-expand .navbar-nav-scroll{overflow:visible}.navbar-expand .navbar-collapse{display:flex !important;flex-basis:auto}.navbar-expand .navbar-toggler{display:none}.navbar-expand .offcanvas-header{display:none}.navbar-expand .offcanvas{position:inherit;bottom:0;z-index:1000;flex-grow:1;visibility:visible !important;background-color:transparent;border-right:0;border-left:0;transition:all 0.3s ease-in-out;transform:none}.navbar-expand .offcanvas-top,.navbar-expand .offcanvas-bottom{height:auto;border-top:0;border-bottom:0}.navbar-expand .offcanvas-body{display:flex;flex-grow:0;padding:0;overflow-y:visible}.navbar-light .navbar-brand{color:rgba(0,0,0,0.9)}.navbar-light .navbar-brand:hover,.navbar-light .navbar-brand:focus{color:rgba(0,0,0,0.9)}.navbar-light .navbar-nav .nav-link{color:rgba(0,0,0,0.55)}.navbar-light .navbar-nav .nav-link:hover,.navbar-light .navbar-nav .nav-link:focus{color:rgba(0,0,0,0.7)}.navbar-light .navbar-nav .nav-link.disabled{color:rgba(0,0,0,0.3)}.navbar-light .navbar-nav .show>.nav-link,.navbar-light .navbar-nav .nav-link.active{color:rgba(0,0,0,0.9)}.navbar-light .navbar-toggler{color:rgba(0,0,0,0.55);border-color:rgba(0,0,0,0.1)}.navbar-light .navbar-toggler-icon{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280,0,0,0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\")}.navbar-light .navbar-text{color:rgba(0,0,0,0.55)}.navbar-light .navbar-text a,.navbar-light .navbar-text a:hover,.navbar-light .navbar-text a:focus{color:rgba(0,0,0,0.9)}.navbar-dark .navbar-brand{color:#fff}.navbar-dark .navbar-brand:hover,.navbar-dark .navbar-brand:focus{color:#fff}.navbar-dark .navbar-nav .nav-link{color:rgba(255,255,255,0.55)}.navbar-dark .navbar-nav .nav-link:hover,.navbar-dark .navbar-nav .nav-link:focus{color:rgba(255,255,255,0.75)}.navbar-dark .navbar-nav .nav-link.disabled{color:rgba(255,255,255,0.25)}.navbar-dark .navbar-nav .show>.nav-link,.navbar-dark .navbar-nav .nav-link.active{color:#fff}.navbar-dark .navbar-toggler{color:rgba(255,255,255,0.55);border-color:rgba(255,255,255,0.1)}.navbar-dark .navbar-toggler-icon{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255,255,255,0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\")}.navbar-dark .navbar-text{color:rgba(255,255,255,0.55)}.navbar-dark .navbar-text a,.navbar-dark .navbar-text a:hover,.navbar-dark .navbar-text a:focus{color:#fff}.card{position:relative;display:flex;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:1px solid rgba(0,0,0,0.125);border-radius:.25rem}.card>hr{margin-right:0;margin-left:0}.card>.list-group{border-top:inherit;border-bottom:inherit}.card>.list-group:first-child{border-top-width:0;border-top-left-radius:calc(.25rem - 1px);border-top-right-radius:calc(.25rem - 1px)}.card>.list-group:last-child{border-bottom-width:0;border-bottom-right-radius:calc(.25rem - 1px);border-bottom-left-radius:calc(.25rem - 1px)}.card>.card-header+.list-group,.card>.list-group+.card-footer{border-top:0}.card-body{flex:1 1 auto;padding:1rem 1rem}.card-title{margin-bottom:.5rem}.card-subtitle{margin-top:-.25rem;margin-bottom:0}.card-text:last-child{margin-bottom:0}.card-link+.card-link{margin-left:1rem}.card-header{padding:.5rem 1rem;margin-bottom:0;background-color:rgba(0,0,0,0.03);border-bottom:1px solid rgba(0,0,0,0.125)}.card-header:first-child{border-radius:calc(.25rem - 1px) calc(.25rem - 1px) 0 0}.card-footer{padding:.5rem 1rem;background-color:rgba(0,0,0,0.03);border-top:1px solid rgba(0,0,0,0.125)}.card-footer:last-child{border-radius:0 0 calc(.25rem - 1px) calc(.25rem - 1px)}.card-header-tabs{margin-right:-.5rem;margin-bottom:-.5rem;margin-left:-.5rem;border-bottom:0}.card-header-pills{margin-right:-.5rem;margin-left:-.5rem}.card-img-overlay{position:absolute;top:0;right:0;bottom:0;left:0;padding:1rem;border-radius:calc(.25rem - 1px)}.card-img,.card-img-top,.card-img-bottom{width:100%}.card-img,.card-img-top{border-top-left-radius:calc(.25rem - 1px);border-top-right-radius:calc(.25rem - 1px)}.card-img,.card-img-bottom{border-bottom-right-radius:calc(.25rem - 1px);border-bottom-left-radius:calc(.25rem - 1px)}.card-group>.card{margin-bottom:.75rem}@media (min-width: 576px){.card-group{display:flex;flex-flow:row wrap}.card-group>.card{flex:1 0 0%;margin-bottom:0}.card-group>.card+.card{margin-left:0;border-left:0}.card-group>.card:not(:last-child){border-top-right-radius:0;border-bottom-right-radius:0}.card-group>.card:not(:last-child) .card-img-top,.card-group>.card:not(:last-child) .card-header{border-top-right-radius:0}.card-group>.card:not(:last-child) .card-img-bottom,.card-group>.card:not(:last-child) .card-footer{border-bottom-right-radius:0}.card-group>.card:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.card-group>.card:not(:first-child) .card-img-top,.card-group>.card:not(:first-child) .card-header{border-top-left-radius:0}.card-group>.card:not(:first-child) .card-img-bottom,.card-group>.card:not(:first-child) .card-footer{border-bottom-left-radius:0}}.accordion-button{position:relative;display:flex;align-items:center;width:100%;padding:1rem 1.25rem;font-size:1rem;color:#212529;text-align:left;background-color:#fff;border:0;border-radius:0;overflow-anchor:none;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.accordion-button{transition:none}}.accordion-button:not(.collapsed){color:#0c63e4;background-color:#e7f1ff;box-shadow:inset 0 -1px 0 rgba(0,0,0,0.125)}.accordion-button:not(.collapsed)::after{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230c63e4'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e\");transform:rotate(-180deg)}.accordion-button::after{flex-shrink:0;width:1.25rem;height:1.25rem;margin-left:auto;content:\"\";background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e\");background-repeat:no-repeat;background-size:1.25rem;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.accordion-button::after{transition:none}}.accordion-button:hover{z-index:2}.accordion-button:focus{z-index:3;border-color:#86b7fe;outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25)}.accordion-header{margin-bottom:0}.accordion-item{background-color:#fff;border:1px solid rgba(0,0,0,0.125)}.accordion-item:first-of-type{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.accordion-item:first-of-type .accordion-button{border-top-left-radius:calc(.25rem - 1px);border-top-right-radius:calc(.25rem - 1px)}.accordion-item:not(:first-of-type){border-top:0}.accordion-item:last-of-type{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.accordion-item:last-of-type .accordion-button.collapsed{border-bottom-right-radius:calc(.25rem - 1px);border-bottom-left-radius:calc(.25rem - 1px)}.accordion-item:last-of-type .accordion-collapse{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.accordion-body{padding:1rem 1.25rem}.accordion-flush .accordion-collapse{border-width:0}.accordion-flush .accordion-item{border-right:0;border-left:0;border-radius:0}.accordion-flush .accordion-item:first-child{border-top:0}.accordion-flush .accordion-item:last-child{border-bottom:0}.accordion-flush .accordion-item .accordion-button{border-radius:0}.breadcrumb{display:flex;flex-wrap:wrap;padding:0 0;margin-bottom:1rem;list-style:none}.breadcrumb-item+.breadcrumb-item{padding-left:.5rem}.breadcrumb-item+.breadcrumb-item::before{float:left;padding-right:.5rem;color:#6c757d;content:var(--bs-breadcrumb-divider, \"/\") /* rtl: var(--bs-breadcrumb-divider, \"/\") */}.breadcrumb-item.active{color:#6c757d}.pagination{display:flex;padding-left:0;list-style:none}.page-link{position:relative;display:block;color:#0d6efd;text-decoration:none;background-color:#fff;border:1px solid #dee2e6;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.page-link{transition:none}}.page-link:hover{z-index:2;color:#0a58ca;background-color:#e9ecef;border-color:#dee2e6}.page-link:focus{z-index:3;color:#0a58ca;background-color:#e9ecef;outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25)}.page-item:not(:first-child) .page-link{margin-left:-1px}.page-item.active .page-link{z-index:3;color:#fff;background-color:#0d6efd;border-color:#0d6efd}.page-item.disabled .page-link{color:#6c757d;pointer-events:none;background-color:#fff;border-color:#dee2e6}.page-link{padding:.375rem .75rem}.page-item:first-child .page-link{border-top-left-radius:.25rem;border-bottom-left-radius:.25rem}.page-item:last-child .page-link{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.pagination-lg .page-link{padding:.75rem 1.5rem;font-size:1.25rem}.pagination-lg .page-item:first-child .page-link{border-top-left-radius:.3rem;border-bottom-left-radius:.3rem}.pagination-lg .page-item:last-child .page-link{border-top-right-radius:.3rem;border-bottom-right-radius:.3rem}.pagination-sm .page-link{padding:.25rem .5rem;font-size:.875rem}.pagination-sm .page-item:first-child .page-link{border-top-left-radius:.2rem;border-bottom-left-radius:.2rem}.pagination-sm .page-item:last-child .page-link{border-top-right-radius:.2rem;border-bottom-right-radius:.2rem}.badge{display:inline-block;padding:.35em .65em;font-size:.75em;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25rem}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.alert{position:relative;padding:1rem 1rem;margin-bottom:1rem;border:1px solid transparent;border-radius:.25rem}.alert-heading{color:inherit}.alert-link{font-weight:700}.alert-dismissible{padding-right:3rem}.alert-dismissible .btn-close{position:absolute;top:0;right:0;z-index:2;padding:1.25rem 1rem}.alert-primary{color:#084298;background-color:#cfe2ff;border-color:#b6d4fe}.alert-primary .alert-link{color:#06357a}.alert-secondary{color:#41464b;background-color:#e2e3e5;border-color:#d3d6d8}.alert-secondary .alert-link{color:#34383c}.alert-success{color:#0f5132;background-color:#d1e7dd;border-color:#badbcc}.alert-success .alert-link{color:#0c4128}.alert-info{color:#055160;background-color:#cff4fc;border-color:#b6effb}.alert-info .alert-link{color:#04414d}.alert-warning{color:#664d03;background-color:#fff3cd;border-color:#ffecb5}.alert-warning .alert-link{color:#523e02}.alert-danger{color:#842029;background-color:#f8d7da;border-color:#f5c2c7}.alert-danger .alert-link{color:#6a1a21}.alert-light{color:#636464;background-color:#fefefe;border-color:#fdfdfe}.alert-light .alert-link{color:#4f5050}.alert-dark{color:#141619;background-color:#d3d3d4;border-color:#bcbebf}.alert-dark .alert-link{color:#101214}@keyframes progress-bar-stripes{0%{background-position-x:1rem}}.progress{display:flex;height:1rem;overflow:hidden;font-size:.75rem;background-color:#e9ecef;border-radius:.25rem}.progress-bar{display:flex;flex-direction:column;justify-content:center;overflow:hidden;color:#fff;text-align:center;white-space:nowrap;background-color:#0d6efd;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.progress-bar{transition:none}}.progress-bar-striped{background-image:linear-gradient(45deg, rgba(255,255,255,0.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.15) 50%, rgba(255,255,255,0.15) 75%, transparent 75%, transparent);background-size:1rem 1rem}.progress-bar-animated{animation:1s linear infinite progress-bar-stripes}@media (prefers-reduced-motion: reduce){.progress-bar-animated{animation:none}}.list-group{display:flex;flex-direction:column;padding-left:0;margin-bottom:0;border-radius:.25rem}.list-group-numbered{list-style-type:none;counter-reset:section}.list-group-numbered>li::before{content:counters(section, \".\") \". \";counter-increment:section}.list-group-item-action{width:100%;color:#495057;text-align:inherit}.list-group-item-action:hover,.list-group-item-action:focus{z-index:1;color:#495057;text-decoration:none;background-color:#f8f9fa}.list-group-item-action:active{color:#212529;background-color:#e9ecef}.list-group-item{position:relative;display:block;padding:.5rem 1rem;color:#212529;text-decoration:none;background-color:#fff;border:1px solid rgba(0,0,0,0.125)}.list-group-item:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.list-group-item:last-child{border-bottom-right-radius:inherit;border-bottom-left-radius:inherit}.list-group-item.disabled,.list-group-item:disabled{color:#6c757d;pointer-events:none;background-color:#fff}.list-group-item.active{z-index:2;color:#fff;background-color:#0d6efd;border-color:#0d6efd}.list-group-item+.list-group-item{border-top-width:0}.list-group-item+.list-group-item.active{margin-top:-1px;border-top-width:1px}.list-group-horizontal{flex-direction:row}.list-group-horizontal>.list-group-item:first-child{border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal>.list-group-item:last-child{border-top-right-radius:.25rem;border-bottom-left-radius:0}.list-group-horizontal>.list-group-item.active{margin-top:0}.list-group-horizontal>.list-group-item+.list-group-item{border-top-width:1px;border-left-width:0}.list-group-horizontal>.list-group-item+.list-group-item.active{margin-left:-1px;border-left-width:1px}@media (min-width: 576px){.list-group-horizontal-sm{flex-direction:row}.list-group-horizontal-sm>.list-group-item:first-child{border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-sm>.list-group-item:last-child{border-top-right-radius:.25rem;border-bottom-left-radius:0}.list-group-horizontal-sm>.list-group-item.active{margin-top:0}.list-group-horizontal-sm>.list-group-item+.list-group-item{border-top-width:1px;border-left-width:0}.list-group-horizontal-sm>.list-group-item+.list-group-item.active{margin-left:-1px;border-left-width:1px}}@media (min-width: 768px){.list-group-horizontal-md{flex-direction:row}.list-group-horizontal-md>.list-group-item:first-child{border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-md>.list-group-item:last-child{border-top-right-radius:.25rem;border-bottom-left-radius:0}.list-group-horizontal-md>.list-group-item.active{margin-top:0}.list-group-horizontal-md>.list-group-item+.list-group-item{border-top-width:1px;border-left-width:0}.list-group-horizontal-md>.list-group-item+.list-group-item.active{margin-left:-1px;border-left-width:1px}}@media (min-width: 992px){.list-group-horizontal-lg{flex-direction:row}.list-group-horizontal-lg>.list-group-item:first-child{border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-lg>.list-group-item:last-child{border-top-right-radius:.25rem;border-bottom-left-radius:0}.list-group-horizontal-lg>.list-group-item.active{margin-top:0}.list-group-horizontal-lg>.list-group-item+.list-group-item{border-top-width:1px;border-left-width:0}.list-group-horizontal-lg>.list-group-item+.list-group-item.active{margin-left:-1px;border-left-width:1px}}@media (min-width: 1200px){.list-group-horizontal-xl{flex-direction:row}.list-group-horizontal-xl>.list-group-item:first-child{border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-xl>.list-group-item:last-child{border-top-right-radius:.25rem;border-bottom-left-radius:0}.list-group-horizontal-xl>.list-group-item.active{margin-top:0}.list-group-horizontal-xl>.list-group-item+.list-group-item{border-top-width:1px;border-left-width:0}.list-group-horizontal-xl>.list-group-item+.list-group-item.active{margin-left:-1px;border-left-width:1px}}@media (min-width: 1400px){.list-group-horizontal-xxl{flex-direction:row}.list-group-horizontal-xxl>.list-group-item:first-child{border-bottom-left-radius:.25rem;border-top-right-radius:0}.list-group-horizontal-xxl>.list-group-item:last-child{border-top-right-radius:.25rem;border-bottom-left-radius:0}.list-group-horizontal-xxl>.list-group-item.active{margin-top:0}.list-group-horizontal-xxl>.list-group-item+.list-group-item{border-top-width:1px;border-left-width:0}.list-group-horizontal-xxl>.list-group-item+.list-group-item.active{margin-left:-1px;border-left-width:1px}}.list-group-flush{border-radius:0}.list-group-flush>.list-group-item{border-width:0 0 1px}.list-group-flush>.list-group-item:last-child{border-bottom-width:0}.list-group-item-primary{color:#084298;background-color:#cfe2ff}.list-group-item-primary.list-group-item-action:hover,.list-group-item-primary.list-group-item-action:focus{color:#084298;background-color:#bacbe6}.list-group-item-primary.list-group-item-action.active{color:#fff;background-color:#084298;border-color:#084298}.list-group-item-secondary{color:#41464b;background-color:#e2e3e5}.list-group-item-secondary.list-group-item-action:hover,.list-group-item-secondary.list-group-item-action:focus{color:#41464b;background-color:#cbccce}.list-group-item-secondary.list-group-item-action.active{color:#fff;background-color:#41464b;border-color:#41464b}.list-group-item-success{color:#0f5132;background-color:#d1e7dd}.list-group-item-success.list-group-item-action:hover,.list-group-item-success.list-group-item-action:focus{color:#0f5132;background-color:#bcd0c7}.list-group-item-success.list-group-item-action.active{color:#fff;background-color:#0f5132;border-color:#0f5132}.list-group-item-info{color:#055160;background-color:#cff4fc}.list-group-item-info.list-group-item-action:hover,.list-group-item-info.list-group-item-action:focus{color:#055160;background-color:#badce3}.list-group-item-info.list-group-item-action.active{color:#fff;background-color:#055160;border-color:#055160}.list-group-item-warning{color:#664d03;background-color:#fff3cd}.list-group-item-warning.list-group-item-action:hover,.list-group-item-warning.list-group-item-action:focus{color:#664d03;background-color:#e6dbb9}.list-group-item-warning.list-group-item-action.active{color:#fff;background-color:#664d03;border-color:#664d03}.list-group-item-danger{color:#842029;background-color:#f8d7da}.list-group-item-danger.list-group-item-action:hover,.list-group-item-danger.list-group-item-action:focus{color:#842029;background-color:#dfc2c4}.list-group-item-danger.list-group-item-action.active{color:#fff;background-color:#842029;border-color:#842029}.list-group-item-light{color:#636464;background-color:#fefefe}.list-group-item-light.list-group-item-action:hover,.list-group-item-light.list-group-item-action:focus{color:#636464;background-color:#e5e5e5}.list-group-item-light.list-group-item-action.active{color:#fff;background-color:#636464;border-color:#636464}.list-group-item-dark{color:#141619;background-color:#d3d3d4}.list-group-item-dark.list-group-item-action:hover,.list-group-item-dark.list-group-item-action:focus{color:#141619;background-color:#bebebf}.list-group-item-dark.list-group-item-action.active{color:#fff;background-color:#141619;border-color:#141619}.btn-close{box-sizing:content-box;width:1em;height:1em;padding:.25em .25em;color:#000;background:transparent url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e\") center/1em auto no-repeat;border:0;border-radius:.25rem;opacity:.5}.btn-close:hover{color:#000;text-decoration:none;opacity:.75}.btn-close:focus{outline:0;box-shadow:0 0 0 .25rem rgba(13,110,253,0.25);opacity:1}.btn-close:disabled,.btn-close.disabled{pointer-events:none;user-select:none;opacity:.25}.btn-close-white{filter:invert(1) grayscale(100%) brightness(200%)}.toast{width:350px;max-width:100%;font-size:.875rem;pointer-events:auto;background-color:rgba(255,255,255,0.85);background-clip:padding-box;border:1px solid rgba(0,0,0,0.1);box-shadow:0 0.5rem 1rem rgba(0,0,0,0.15);border-radius:.25rem}.toast.showing{opacity:0}.toast:not(.show){display:none}.toast-container{width:max-content;max-width:100%;pointer-events:none}.toast-container>:not(:last-child){margin-bottom:.75rem}.toast-header{display:flex;align-items:center;padding:.5rem .75rem;color:#6c757d;background-color:rgba(255,255,255,0.85);background-clip:padding-box;border-bottom:1px solid rgba(0,0,0,0.05);border-top-left-radius:calc(.25rem - 1px);border-top-right-radius:calc(.25rem - 1px)}.toast-header .btn-close{margin-right:-.375rem;margin-left:.75rem}.toast-body{padding:.75rem;word-wrap:break-word}.modal{position:fixed;top:0;left:0;z-index:1055;display:none;width:100%;height:100%;overflow-x:hidden;overflow-y:auto;outline:0}.modal-dialog{position:relative;width:auto;margin:.5rem;pointer-events:none}.modal.fade .modal-dialog{transition:all 0.3s ease-in-out;transform:translate(0, -50px)}@media (prefers-reduced-motion: reduce){.modal.fade .modal-dialog{transition:none}}.modal.show .modal-dialog{transform:none}.modal.modal-static .modal-dialog{transform:scale(1.02)}.modal-dialog-scrollable{height:calc(100% - 1rem)}.modal-dialog-scrollable .modal-content{max-height:100%;overflow:hidden}.modal-dialog-scrollable .modal-body{overflow-y:auto}.modal-dialog-centered{display:flex;align-items:center;min-height:calc(100% - 1rem)}.modal-content{position:relative;display:flex;flex-direction:column;width:100%;pointer-events:auto;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,0.2);border-radius:.3rem;outline:0}.modal-backdrop{position:fixed;top:0;left:0;z-index:1050;width:100vw;height:100vh;background-color:#000}.modal-backdrop.fade{opacity:0}.modal-backdrop.show{opacity:.5}.modal-header{display:flex;flex-shrink:0;align-items:center;justify-content:space-between;padding:1rem 1rem;border-bottom:1px solid #dee2e6;border-top-left-radius:calc(.3rem - 1px);border-top-right-radius:calc(.3rem - 1px)}.modal-header .btn-close{padding:.5rem .5rem;margin:-.5rem -.5rem -.5rem auto}.modal-title{margin-bottom:0;line-height:1.5}.modal-body{position:relative;flex:1 1 auto;padding:1rem}.modal-footer{display:flex;flex-wrap:wrap;flex-shrink:0;align-items:center;justify-content:flex-end;padding:.75rem;border-top:1px solid #dee2e6;border-bottom-right-radius:calc(.3rem - 1px);border-bottom-left-radius:calc(.3rem - 1px)}.modal-footer>*{margin:.25rem}@media (min-width: 576px){.modal-dialog{max-width:500px;margin:1.75rem auto}.modal-dialog-scrollable{height:calc(100% - 3.5rem)}.modal-dialog-centered{min-height:calc(100% - 3.5rem)}.modal-sm{max-width:300px}}@media (min-width: 992px){.modal-lg,.modal-xl{max-width:800px}}@media (min-width: 1200px){.modal-xl{max-width:1140px}}.modal-fullscreen{width:100vw;max-width:none;height:100%;margin:0}.modal-fullscreen .modal-content{height:100%;border:0;border-radius:0}.modal-fullscreen .modal-header{border-radius:0}.modal-fullscreen .modal-body{overflow-y:auto}.modal-fullscreen .modal-footer{border-radius:0}@media (max-width: 575.98px){.modal-fullscreen-sm-down{width:100vw;max-width:none;height:100%;margin:0}.modal-fullscreen-sm-down .modal-content{height:100%;border:0;border-radius:0}.modal-fullscreen-sm-down .modal-header{border-radius:0}.modal-fullscreen-sm-down .modal-body{overflow-y:auto}.modal-fullscreen-sm-down .modal-footer{border-radius:0}}@media (max-width: 767.98px){.modal-fullscreen-md-down{width:100vw;max-width:none;height:100%;margin:0}.modal-fullscreen-md-down .modal-content{height:100%;border:0;border-radius:0}.modal-fullscreen-md-down .modal-header{border-radius:0}.modal-fullscreen-md-down .modal-body{overflow-y:auto}.modal-fullscreen-md-down .modal-footer{border-radius:0}}@media (max-width: 991.98px){.modal-fullscreen-lg-down{width:100vw;max-width:none;height:100%;margin:0}.modal-fullscreen-lg-down .modal-content{height:100%;border:0;border-radius:0}.modal-fullscreen-lg-down .modal-header{border-radius:0}.modal-fullscreen-lg-down .modal-body{overflow-y:auto}.modal-fullscreen-lg-down .modal-footer{border-radius:0}}@media (max-width: 1199.98px){.modal-fullscreen-xl-down{width:100vw;max-width:none;height:100%;margin:0}.modal-fullscreen-xl-down .modal-content{height:100%;border:0;border-radius:0}.modal-fullscreen-xl-down .modal-header{border-radius:0}.modal-fullscreen-xl-down .modal-body{overflow-y:auto}.modal-fullscreen-xl-down .modal-footer{border-radius:0}}@media (max-width: 1399.98px){.modal-fullscreen-xxl-down{width:100vw;max-width:none;height:100%;margin:0}.modal-fullscreen-xxl-down .modal-content{height:100%;border:0;border-radius:0}.modal-fullscreen-xxl-down .modal-header{border-radius:0}.modal-fullscreen-xxl-down .modal-body{overflow-y:auto}.modal-fullscreen-xxl-down .modal-footer{border-radius:0}}.tooltip{position:absolute;z-index:1080;display:block;margin:0;font-family:var(--bs-font-sans-serif);font-style:normal;font-weight:400;line-height:1.5;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;white-space:normal;line-break:auto;font-size:.875rem;word-wrap:break-word;opacity:0}.tooltip.show{opacity:.9}.tooltip .tooltip-arrow{position:absolute;display:block;width:.8rem;height:.4rem}.tooltip .tooltip-arrow::before{position:absolute;content:\"\";border-color:transparent;border-style:solid}.bs-tooltip-top,.bs-tooltip-auto[data-popper-placement^=\"top\"]{padding:.4rem 0}.bs-tooltip-top .tooltip-arrow,.bs-tooltip-auto[data-popper-placement^=\"top\"] .tooltip-arrow{bottom:0}.bs-tooltip-top .tooltip-arrow::before,.bs-tooltip-auto[data-popper-placement^=\"top\"] .tooltip-arrow::before{top:-1px;border-width:.4rem .4rem 0;border-top-color:#000}.bs-tooltip-end,.bs-tooltip-auto[data-popper-placement^=\"right\"]{padding:0 .4rem}.bs-tooltip-end .tooltip-arrow,.bs-tooltip-auto[data-popper-placement^=\"right\"] .tooltip-arrow{left:0;width:.4rem;height:.8rem}.bs-tooltip-end .tooltip-arrow::before,.bs-tooltip-auto[data-popper-placement^=\"right\"] .tooltip-arrow::before{right:-1px;border-width:.4rem .4rem .4rem 0;border-right-color:#000}.bs-tooltip-bottom,.bs-tooltip-auto[data-popper-placement^=\"bottom\"]{padding:.4rem 0}.bs-tooltip-bottom .tooltip-arrow,.bs-tooltip-auto[data-popper-placement^=\"bottom\"] .tooltip-arrow{top:0}.bs-tooltip-bottom .tooltip-arrow::before,.bs-tooltip-auto[data-popper-placement^=\"bottom\"] .tooltip-arrow::before{bottom:-1px;border-width:0 .4rem .4rem;border-bottom-color:#000}.bs-tooltip-start,.bs-tooltip-auto[data-popper-placement^=\"left\"]{padding:0 .4rem}.bs-tooltip-start .tooltip-arrow,.bs-tooltip-auto[data-popper-placement^=\"left\"] .tooltip-arrow{right:0;width:.4rem;height:.8rem}.bs-tooltip-start .tooltip-arrow::before,.bs-tooltip-auto[data-popper-placement^=\"left\"] .tooltip-arrow::before{left:-1px;border-width:.4rem 0 .4rem .4rem;border-left-color:#000}.tooltip-inner{max-width:200px;padding:.25rem .5rem;color:#fff;text-align:center;background-color:#000;border-radius:.25rem}.popover{position:absolute;top:0;left:0 /* rtl:ignore */;z-index:1070;display:block;max-width:276px;font-family:var(--bs-font-sans-serif);font-style:normal;font-weight:400;line-height:1.5;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;white-space:normal;line-break:auto;font-size:.875rem;word-wrap:break-word;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,0.2);border-radius:.3rem}.popover .popover-arrow{position:absolute;display:block;width:1rem;height:.5rem}.popover .popover-arrow::before,.popover .popover-arrow::after{position:absolute;display:block;content:\"\";border-color:transparent;border-style:solid}.bs-popover-top>.popover-arrow,.bs-popover-auto[data-popper-placement^=\"top\"]>.popover-arrow{bottom:calc(-.5rem - 1px)}.bs-popover-top>.popover-arrow::before,.bs-popover-auto[data-popper-placement^=\"top\"]>.popover-arrow::before{bottom:0;border-width:.5rem .5rem 0;border-top-color:rgba(0,0,0,0.25)}.bs-popover-top>.popover-arrow::after,.bs-popover-auto[data-popper-placement^=\"top\"]>.popover-arrow::after{bottom:1px;border-width:.5rem .5rem 0;border-top-color:#fff}.bs-popover-end>.popover-arrow,.bs-popover-auto[data-popper-placement^=\"right\"]>.popover-arrow{left:calc(-.5rem - 1px);width:.5rem;height:1rem}.bs-popover-end>.popover-arrow::before,.bs-popover-auto[data-popper-placement^=\"right\"]>.popover-arrow::before{left:0;border-width:.5rem .5rem .5rem 0;border-right-color:rgba(0,0,0,0.25)}.bs-popover-end>.popover-arrow::after,.bs-popover-auto[data-popper-placement^=\"right\"]>.popover-arrow::after{left:1px;border-width:.5rem .5rem .5rem 0;border-right-color:#fff}.bs-popover-bottom>.popover-arrow,.bs-popover-auto[data-popper-placement^=\"bottom\"]>.popover-arrow{top:calc(-.5rem - 1px)}.bs-popover-bottom>.popover-arrow::before,.bs-popover-auto[data-popper-placement^=\"bottom\"]>.popover-arrow::before{top:0;border-width:0 .5rem .5rem .5rem;border-bottom-color:rgba(0,0,0,0.25)}.bs-popover-bottom>.popover-arrow::after,.bs-popover-auto[data-popper-placement^=\"bottom\"]>.popover-arrow::after{top:1px;border-width:0 .5rem .5rem .5rem;border-bottom-color:#fff}.bs-popover-bottom .popover-header::before,.bs-popover-auto[data-popper-placement^=\"bottom\"] .popover-header::before{position:absolute;top:0;left:50%;display:block;width:1rem;margin-left:-.5rem;content:\"\";border-bottom:1px solid #f0f0f0}.bs-popover-start>.popover-arrow,.bs-popover-auto[data-popper-placement^=\"left\"]>.popover-arrow{right:calc(-.5rem - 1px);width:.5rem;height:1rem}.bs-popover-start>.popover-arrow::before,.bs-popover-auto[data-popper-placement^=\"left\"]>.popover-arrow::before{right:0;border-width:.5rem 0 .5rem .5rem;border-left-color:rgba(0,0,0,0.25)}.bs-popover-start>.popover-arrow::after,.bs-popover-auto[data-popper-placement^=\"left\"]>.popover-arrow::after{right:1px;border-width:.5rem 0 .5rem .5rem;border-left-color:#fff}.popover-header{padding:.5rem 1rem;margin-bottom:0;font-size:1rem;background-color:#f0f0f0;border-bottom:1px solid rgba(0,0,0,0.2);border-top-left-radius:calc(.3rem - 1px);border-top-right-radius:calc(.3rem - 1px)}.popover-header:empty{display:none}.popover-body{padding:1rem 1rem;color:#212529}.carousel{position:relative}.carousel.pointer-event{touch-action:pan-y}.carousel-inner{position:relative;width:100%;overflow:hidden}.carousel-inner::after{display:block;clear:both;content:\"\"}.carousel-item{position:relative;display:none;float:left;width:100%;margin-right:-100%;backface-visibility:hidden;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.carousel-item{transition:none}}.carousel-item.active,.carousel-item-next,.carousel-item-prev{display:block}.carousel-item-next:not(.carousel-item-start),.active.carousel-item-end{transform:translateX(100%)}.carousel-item-prev:not(.carousel-item-end),.active.carousel-item-start{transform:translateX(-100%)}.carousel-fade .carousel-item{opacity:0;transition-property:opacity;transform:none}.carousel-fade .carousel-item.active,.carousel-fade .carousel-item-next.carousel-item-start,.carousel-fade .carousel-item-prev.carousel-item-end{z-index:1;opacity:1}.carousel-fade .active.carousel-item-start,.carousel-fade .active.carousel-item-end{z-index:0;opacity:0;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.carousel-fade .active.carousel-item-start,.carousel-fade .active.carousel-item-end{transition:none}}.carousel-control-prev,.carousel-control-next{position:absolute;top:0;bottom:0;z-index:1;display:flex;align-items:center;justify-content:center;width:15%;padding:0;color:#fff;text-align:center;background:none;border:0;opacity:.5;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.carousel-control-prev,.carousel-control-next{transition:none}}.carousel-control-prev:hover,.carousel-control-prev:focus,.carousel-control-next:hover,.carousel-control-next:focus{color:#fff;text-decoration:none;outline:0;opacity:.9}.carousel-control-prev{left:0}.carousel-control-next{right:0}.carousel-control-prev-icon,.carousel-control-next-icon{display:inline-block;width:2rem;height:2rem;background-repeat:no-repeat;background-position:50%;background-size:100% 100%}.carousel-control-prev-icon{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e\")}.carousel-control-next-icon{background-image:url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e\")}.carousel-indicators{position:absolute;right:0;bottom:0;left:0;z-index:2;display:flex;justify-content:center;padding:0;margin-right:15%;margin-bottom:1rem;margin-left:15%;list-style:none}.carousel-indicators [data-bs-target]{box-sizing:content-box;flex:0 1 auto;width:30px;height:3px;padding:0;margin-right:3px;margin-left:3px;text-indent:-999px;cursor:pointer;background-color:#fff;background-clip:padding-box;border:0;border-top:10px solid transparent;border-bottom:10px solid transparent;opacity:.5;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.carousel-indicators [data-bs-target]{transition:none}}.carousel-indicators .active{opacity:1}.carousel-caption{position:absolute;right:15%;bottom:1.25rem;left:15%;padding-top:1.25rem;padding-bottom:1.25rem;color:#fff;text-align:center}.carousel-dark .carousel-control-prev-icon,.carousel-dark .carousel-control-next-icon{filter:invert(1) grayscale(100)}.carousel-dark .carousel-indicators [data-bs-target]{background-color:#000}.carousel-dark .carousel-caption{color:#000}@keyframes spinner-border{to{transform:rotate(360deg) /* rtl:ignore */}}.spinner-border{display:inline-block;width:2rem;height:2rem;vertical-align:-.125em;border:.25em solid currentColor;border-right-color:transparent;border-radius:50%;animation:.75s linear infinite spinner-border}.spinner-border-sm{width:1rem;height:1rem;border-width:.2em}@keyframes spinner-grow{0%{transform:scale(0)}50%{opacity:1;transform:none}}.spinner-grow{display:inline-block;width:2rem;height:2rem;vertical-align:-.125em;background-color:currentColor;border-radius:50%;opacity:0;animation:.75s linear infinite spinner-grow}.spinner-grow-sm{width:1rem;height:1rem}@media (prefers-reduced-motion: reduce){.spinner-border,.spinner-grow{animation-duration:1.5s}}.offcanvas{position:fixed;bottom:0;z-index:1045;display:flex;flex-direction:column;max-width:100%;visibility:hidden;background-color:#fff;background-clip:padding-box;outline:0;transition:all 0.3s ease-in-out}@media (prefers-reduced-motion: reduce){.offcanvas{transition:none}}.offcanvas-backdrop{position:fixed;top:0;left:0;z-index:1040;width:100vw;height:100vh;background-color:#000}.offcanvas-backdrop.fade{opacity:0}.offcanvas-backdrop.show{opacity:.5}.offcanvas-header{display:flex;align-items:center;justify-content:space-between;padding:1rem 1rem}.offcanvas-header .btn-close{padding:.5rem .5rem;margin-top:-.5rem;margin-right:-.5rem;margin-bottom:-.5rem}.offcanvas-title{margin-bottom:0;line-height:1.5}.offcanvas-body{flex-grow:1;padding:1rem 1rem;overflow-y:auto}.offcanvas-start{top:0;left:0;width:400px;border-right:1px solid rgba(0,0,0,0.2);transform:translateX(-100%)}.offcanvas-end{top:0;right:0;width:400px;border-left:1px solid rgba(0,0,0,0.2);transform:translateX(100%)}.offcanvas-top{top:0;right:0;left:0;height:30vh;max-height:100%;border-bottom:1px solid rgba(0,0,0,0.2);transform:translateY(-100%)}.offcanvas-bottom{right:0;left:0;height:30vh;max-height:100%;border-top:1px solid rgba(0,0,0,0.2);transform:translateY(100%)}.offcanvas.show{transform:none}.placeholder{display:inline-block;min-height:1em;vertical-align:middle;cursor:wait;background-color:currentColor;opacity:.5}.placeholder.btn::before{display:inline-block;content:\"\"}.placeholder-xs{min-height:.6em}.placeholder-sm{min-height:.8em}.placeholder-lg{min-height:1.2em}.placeholder-glow .placeholder{animation:placeholder-glow 2s ease-in-out infinite}@keyframes placeholder-glow{50%{opacity:.2}}.placeholder-wave{mask-image:linear-gradient(130deg, #000 55%, rgba(0,0,0,0.8) 75%, #000 95%);mask-size:200% 100%;animation:placeholder-wave 2s linear infinite}@keyframes placeholder-wave{100%{mask-position:-200% 0%}}.clearfix::after{display:block;clear:both;content:\"\"}.link-primary{color:#0d6efd}.link-primary:hover,.link-primary:focus{color:#0a58ca}.link-secondary{color:#6c757d}.link-secondary:hover,.link-secondary:focus{color:#565e64}.link-success{color:#198754}.link-success:hover,.link-success:focus{color:#146c43}.link-info{color:#0dcaf0}.link-info:hover,.link-info:focus{color:#3dd5f3}.link-warning{color:#ffc107}.link-warning:hover,.link-warning:focus{color:#ffcd39}.link-danger{color:#dc3545}.link-danger:hover,.link-danger:focus{color:#b02a37}.link-light{color:#f8f9fa}.link-light:hover,.link-light:focus{color:#f9fafb}.link-dark{color:#212529}.link-dark:hover,.link-dark:focus{color:#1a1e21}.ratio{position:relative;width:100%}.ratio::before{display:block;padding-top:var(--bs-aspect-ratio);content:\"\"}.ratio>*{position:absolute;top:0;left:0;width:100%;height:100%}.ratio-1x1{--bs-aspect-ratio: 100%}.ratio-4x3{--bs-aspect-ratio: calc(3 / 4 * 100%)}.ratio-16x9{--bs-aspect-ratio: calc(9 / 16 * 100%)}.ratio-21x9{--bs-aspect-ratio: calc(9 / 21 * 100%)}.fixed-top{position:fixed;top:0;right:0;left:0;z-index:1030}.fixed-bottom{position:fixed;right:0;bottom:0;left:0;z-index:1030}.sticky-top{position:sticky;top:0;z-index:1020}@media (min-width: 576px){.sticky-sm-top{position:sticky;top:0;z-index:1020}}@media (min-width: 768px){.sticky-md-top{position:sticky;top:0;z-index:1020}}@media (min-width: 992px){.sticky-lg-top{position:sticky;top:0;z-index:1020}}@media (min-width: 1200px){.sticky-xl-top{position:sticky;top:0;z-index:1020}}@media (min-width: 1400px){.sticky-xxl-top{position:sticky;top:0;z-index:1020}}.hstack{display:flex;flex-direction:row;align-items:center;align-self:stretch}.vstack{display:flex;flex:1 1 auto;flex-direction:column;align-self:stretch}.visually-hidden,.visually-hidden-focusable:not(:focus):not(:focus-within){position:absolute !important;width:1px !important;height:1px !important;padding:0 !important;margin:-1px !important;overflow:hidden !important;clip:rect(0, 0, 0, 0) !important;white-space:nowrap !important;border:0 !important}.stretched-link::after{position:absolute;top:0;right:0;bottom:0;left:0;z-index:1;content:\"\"}.text-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.vr{display:inline-block;align-self:stretch;width:1px;min-height:1em;background-color:currentColor;opacity:.25}.align-baseline{vertical-align:baseline !important}.align-top{vertical-align:top !important}.align-middle{vertical-align:middle !important}.align-bottom{vertical-align:bottom !important}.align-text-bottom{vertical-align:text-bottom !important}.align-text-top{vertical-align:text-top !important}.float-start{float:left !important}.float-end{float:right !important}.float-none{float:none !important}.opacity-0{opacity:0 !important}.opacity-25{opacity:.25 !important}.opacity-50{opacity:.5 !important}.opacity-75{opacity:.75 !important}.opacity-100{opacity:1 !important}.overflow-auto{overflow:auto !important}.overflow-hidden{overflow:hidden !important}.overflow-visible{overflow:visible !important}.overflow-scroll{overflow:scroll !important}.d-inline{display:inline !important}.d-inline-block{display:inline-block !important}.d-block{display:block !important}.d-grid{display:grid !important}.d-table{display:table !important}.d-table-row{display:table-row !important}.d-table-cell{display:table-cell !important}.d-flex{display:flex !important}.d-inline-flex{display:inline-flex !important}.d-none{display:none !important}.shadow{box-shadow:0 0.5rem 1rem rgba(0,0,0,0.15) !important}.shadow-sm{box-shadow:0 0.125rem 0.25rem rgba(0,0,0,0.075) !important}.shadow-lg{box-shadow:0 1rem 3rem rgba(0,0,0,0.175) !important}.shadow-none{box-shadow:none !important}.position-static{position:static !important}.position-relative{position:relative !important}.position-absolute{position:absolute !important}.position-fixed{position:fixed !important}.position-sticky{position:sticky !important}.top-0{top:0 !important}.top-50{top:50% !important}.top-100{top:100% !important}.bottom-0{bottom:0 !important}.bottom-50{bottom:50% !important}.bottom-100{bottom:100% !important}.start-0{left:0 !important}.start-50{left:50% !important}.start-100{left:100% !important}.end-0{right:0 !important}.end-50{right:50% !important}.end-100{right:100% !important}.translate-middle{transform:translate(-50%, -50%) !important}.translate-middle-x{transform:translateX(-50%) !important}.translate-middle-y{transform:translateY(-50%) !important}.border{border:1px solid #dee2e6 !important}.border-0{border:0 !important}.border-top{border-top:1px solid #dee2e6 !important}.border-top-0{border-top:0 !important}.border-end{border-right:1px solid #dee2e6 !important}.border-end-0{border-right:0 !important}.border-bottom{border-bottom:1px solid #dee2e6 !important}.border-bottom-0{border-bottom:0 !important}.border-start{border-left:1px solid #dee2e6 !important}.border-start-0{border-left:0 !important}.border-primary{border-color:#0d6efd !important}.border-secondary{border-color:#6c757d !important}.border-success{border-color:#198754 !important}.border-info{border-color:#0dcaf0 !important}.border-warning{border-color:#ffc107 !important}.border-danger{border-color:#dc3545 !important}.border-light{border-color:#f8f9fa !important}.border-dark{border-color:#212529 !important}.border-white{border-color:#fff !important}.border-1{border-width:1px !important}.border-2{border-width:2px !important}.border-3{border-width:3px !important}.border-4{border-width:4px !important}.border-5{border-width:5px !important}.w-25{width:25% !important}.w-50{width:50% !important}.w-75{width:75% !important}.w-100{width:100% !important}.w-auto{width:auto !important}.mw-100{max-width:100% !important}.vw-100{width:100vw !important}.min-vw-100{min-width:100vw !important}.h-25{height:25% !important}.h-50{height:50% !important}.h-75{height:75% !important}.h-100{height:100% !important}.h-auto{height:auto !important}.mh-100{max-height:100% !important}.vh-100{height:100vh !important}.min-vh-100{min-height:100vh !important}.flex-fill{flex:1 1 auto !important}.flex-row{flex-direction:row !important}.flex-column{flex-direction:column !important}.flex-row-reverse{flex-direction:row-reverse !important}.flex-column-reverse{flex-direction:column-reverse !important}.flex-grow-0{flex-grow:0 !important}.flex-grow-1{flex-grow:1 !important}.flex-shrink-0{flex-shrink:0 !important}.flex-shrink-1{flex-shrink:1 !important}.flex-wrap{flex-wrap:wrap !important}.flex-nowrap{flex-wrap:nowrap !important}.flex-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-0{gap:0 !important}.gap-1{gap:.25rem !important}.gap-2{gap:.5rem !important}.gap-3{gap:1rem !important}.gap-4{gap:1.5rem !important}.gap-5{gap:3rem !important}.justify-content-start{justify-content:flex-start !important}.justify-content-end{justify-content:flex-end !important}.justify-content-center{justify-content:center !important}.justify-content-between{justify-content:space-between !important}.justify-content-around{justify-content:space-around !important}.justify-content-evenly{justify-content:space-evenly !important}.align-items-start{align-items:flex-start !important}.align-items-end{align-items:flex-end !important}.align-items-center{align-items:center !important}.align-items-baseline{align-items:baseline !important}.align-items-stretch{align-items:stretch !important}.align-content-start{align-content:flex-start !important}.align-content-end{align-content:flex-end !important}.align-content-center{align-content:center !important}.align-content-between{align-content:space-between !important}.align-content-around{align-content:space-around !important}.align-content-stretch{align-content:stretch !important}.align-self-auto{align-self:auto !important}.align-self-start{align-self:flex-start !important}.align-self-end{align-self:flex-end !important}.align-self-center{align-self:center !important}.align-self-baseline{align-self:baseline !important}.align-self-stretch{align-self:stretch !important}.order-first{order:-1 !important}.order-0{order:0 !important}.order-1{order:1 !important}.order-2{order:2 !important}.order-3{order:3 !important}.order-4{order:4 !important}.order-5{order:5 !important}.order-last{order:6 !important}.m-0{margin:0 !important}.m-1{margin:.25rem !important}.m-2{margin:.5rem !important}.m-3{margin:1rem !important}.m-4{margin:1.5rem !important}.m-5{margin:3rem !important}.m-auto{margin:auto !important}.mx-0{margin-right:0 !important;margin-left:0 !important}.mx-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-3{margin-right:1rem !important;margin-left:1rem !important}.mx-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-5{margin-right:3rem !important;margin-left:3rem !important}.mx-auto{margin-right:auto !important;margin-left:auto !important}.my-0{margin-top:0 !important;margin-bottom:0 !important}.my-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-0{margin-top:0 !important}.mt-1{margin-top:.25rem !important}.mt-2{margin-top:.5rem !important}.mt-3{margin-top:1rem !important}.mt-4{margin-top:1.5rem !important}.mt-5{margin-top:3rem !important}.mt-auto{margin-top:auto !important}.me-0{margin-right:0 !important}.me-1{margin-right:.25rem !important}.me-2{margin-right:.5rem !important}.me-3{margin-right:1rem !important}.me-4{margin-right:1.5rem !important}.me-5{margin-right:3rem !important}.me-auto{margin-right:auto !important}.mb-0{margin-bottom:0 !important}.mb-1{margin-bottom:.25rem !important}.mb-2{margin-bottom:.5rem !important}.mb-3{margin-bottom:1rem !important}.mb-4{margin-bottom:1.5rem !important}.mb-5{margin-bottom:3rem !important}.mb-auto{margin-bottom:auto !important}.ms-0{margin-left:0 !important}.ms-1{margin-left:.25rem !important}.ms-2{margin-left:.5rem !important}.ms-3{margin-left:1rem !important}.ms-4{margin-left:1.5rem !important}.ms-5{margin-left:3rem !important}.ms-auto{margin-left:auto !important}.p-0{padding:0 !important}.p-1{padding:.25rem !important}.p-2{padding:.5rem !important}.p-3{padding:1rem !important}.p-4{padding:1.5rem !important}.p-5{padding:3rem !important}.px-0{padding-right:0 !important;padding-left:0 !important}.px-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-3{padding-right:1rem !important;padding-left:1rem !important}.px-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-5{padding-right:3rem !important;padding-left:3rem !important}.py-0{padding-top:0 !important;padding-bottom:0 !important}.py-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-0{padding-top:0 !important}.pt-1{padding-top:.25rem !important}.pt-2{padding-top:.5rem !important}.pt-3{padding-top:1rem !important}.pt-4{padding-top:1.5rem !important}.pt-5{padding-top:3rem !important}.pe-0{padding-right:0 !important}.pe-1{padding-right:.25rem !important}.pe-2{padding-right:.5rem !important}.pe-3{padding-right:1rem !important}.pe-4{padding-right:1.5rem !important}.pe-5{padding-right:3rem !important}.pb-0{padding-bottom:0 !important}.pb-1{padding-bottom:.25rem !important}.pb-2{padding-bottom:.5rem !important}.pb-3{padding-bottom:1rem !important}.pb-4{padding-bottom:1.5rem !important}.pb-5{padding-bottom:3rem !important}.ps-0{padding-left:0 !important}.ps-1{padding-left:.25rem !important}.ps-2{padding-left:.5rem !important}.ps-3{padding-left:1rem !important}.ps-4{padding-left:1.5rem !important}.ps-5{padding-left:3rem !important}.font-monospace{font-family:var(--bs-font-monospace) !important}.fs-1{font-size:calc(1.375rem + 1.5vw) !important}.fs-2{font-size:calc(1.325rem + .9vw) !important}.fs-3{font-size:calc(1.3rem + .6vw) !important}.fs-4{font-size:calc(1.275rem + .3vw) !important}.fs-5{font-size:1.25rem !important}.fs-6{font-size:1rem !important}.fst-italic{font-style:italic !important}.fst-normal{font-style:normal !important}.fw-light{font-weight:300 !important}.fw-lighter{font-weight:lighter !important}.fw-normal{font-weight:400 !important}.fw-bold{font-weight:700 !important}.fw-bolder{font-weight:bolder !important}.lh-1{line-height:1 !important}.lh-sm{line-height:1.25 !important}.lh-base{line-height:1.5 !important}.lh-lg{line-height:2 !important}.text-start{text-align:left !important}.text-end{text-align:right !important}.text-center{text-align:center !important}.text-decoration-none{text-decoration:none !important}.text-decoration-underline{text-decoration:underline !important}.text-decoration-line-through{text-decoration:line-through !important}.text-lowercase{text-transform:lowercase !important}.text-uppercase{text-transform:uppercase !important}.text-capitalize{text-transform:capitalize !important}.text-wrap{white-space:normal !important}.text-nowrap{white-space:nowrap !important}.text-break{word-wrap:break-word !important;word-break:break-word !important}.text-primary{--bs-text-opacity: 1;color:rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important}.text-secondary{--bs-text-opacity: 1;color:rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important}.text-success{--bs-text-opacity: 1;color:rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important}.text-info{--bs-text-opacity: 1;color:rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important}.text-warning{--bs-text-opacity: 1;color:rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important}.text-danger{--bs-text-opacity: 1;color:rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important}.text-light{--bs-text-opacity: 1;color:rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important}.text-dark{--bs-text-opacity: 1;color:rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important}.text-black{--bs-text-opacity: 1;color:rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important}.text-white{--bs-text-opacity: 1;color:rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important}.text-body{--bs-text-opacity: 1;color:rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important}.text-muted{--bs-text-opacity: 1;color:#6c757d !important}.text-black-50{--bs-text-opacity: 1;color:rgba(0,0,0,0.5) !important}.text-white-50{--bs-text-opacity: 1;color:rgba(255,255,255,0.5) !important}.text-reset{--bs-text-opacity: 1;color:inherit !important}.text-opacity-25{--bs-text-opacity: .25}.text-opacity-50{--bs-text-opacity: .5}.text-opacity-75{--bs-text-opacity: .75}.text-opacity-100{--bs-text-opacity: 1}.bg-primary{--bs-bg-opacity: 1;background-color:rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important}.bg-secondary{--bs-bg-opacity: 1;background-color:rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important}.bg-success{--bs-bg-opacity: 1;background-color:rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important}.bg-info{--bs-bg-opacity: 1;background-color:rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important}.bg-warning{--bs-bg-opacity: 1;background-color:rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important}.bg-danger{--bs-bg-opacity: 1;background-color:rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important}.bg-light{--bs-bg-opacity: 1;background-color:rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important}.bg-dark{--bs-bg-opacity: 1;background-color:rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important}.bg-black{--bs-bg-opacity: 1;background-color:rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important}.bg-white{--bs-bg-opacity: 1;background-color:rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important}.bg-body{--bs-bg-opacity: 1;background-color:rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important}.bg-transparent{--bs-bg-opacity: 1;background-color:rgba(0,0,0,0) !important}.bg-opacity-10{--bs-bg-opacity: .1}.bg-opacity-25{--bs-bg-opacity: .25}.bg-opacity-50{--bs-bg-opacity: .5}.bg-opacity-75{--bs-bg-opacity: .75}.bg-opacity-100{--bs-bg-opacity: 1}.bg-gradient{background-image:var(--bs-gradient) !important}.user-select-all{user-select:all !important}.user-select-auto{user-select:auto !important}.user-select-none{user-select:none !important}.pe-none{pointer-events:none !important}.pe-auto{pointer-events:auto !important}.rounded{border-radius:.25rem !important}.rounded-0{border-radius:0 !important}.rounded-1{border-radius:.2rem !important}.rounded-2{border-radius:.25rem !important}.rounded-3{border-radius:.3rem !important}.rounded-circle{border-radius:50% !important}.rounded-pill{border-radius:50rem !important}.rounded-top{border-top-left-radius:.25rem !important;border-top-right-radius:.25rem !important}.rounded-end{border-top-right-radius:.25rem !important;border-bottom-right-radius:.25rem !important}.rounded-bottom{border-bottom-right-radius:.25rem !important;border-bottom-left-radius:.25rem !important}.rounded-start{border-bottom-left-radius:.25rem !important;border-top-left-radius:.25rem !important}.visible{visibility:visible !important}.invisible{visibility:hidden !important}@media (min-width: 576px){.float-sm-start{float:left !important}.float-sm-end{float:right !important}.float-sm-none{float:none !important}.d-sm-inline{display:inline !important}.d-sm-inline-block{display:inline-block !important}.d-sm-block{display:block !important}.d-sm-grid{display:grid !important}.d-sm-table{display:table !important}.d-sm-table-row{display:table-row !important}.d-sm-table-cell{display:table-cell !important}.d-sm-flex{display:flex !important}.d-sm-inline-flex{display:inline-flex !important}.d-sm-none{display:none !important}.flex-sm-fill{flex:1 1 auto !important}.flex-sm-row{flex-direction:row !important}.flex-sm-column{flex-direction:column !important}.flex-sm-row-reverse{flex-direction:row-reverse !important}.flex-sm-column-reverse{flex-direction:column-reverse !important}.flex-sm-grow-0{flex-grow:0 !important}.flex-sm-grow-1{flex-grow:1 !important}.flex-sm-shrink-0{flex-shrink:0 !important}.flex-sm-shrink-1{flex-shrink:1 !important}.flex-sm-wrap{flex-wrap:wrap !important}.flex-sm-nowrap{flex-wrap:nowrap !important}.flex-sm-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-sm-0{gap:0 !important}.gap-sm-1{gap:.25rem !important}.gap-sm-2{gap:.5rem !important}.gap-sm-3{gap:1rem !important}.gap-sm-4{gap:1.5rem !important}.gap-sm-5{gap:3rem !important}.justify-content-sm-start{justify-content:flex-start !important}.justify-content-sm-end{justify-content:flex-end !important}.justify-content-sm-center{justify-content:center !important}.justify-content-sm-between{justify-content:space-between !important}.justify-content-sm-around{justify-content:space-around !important}.justify-content-sm-evenly{justify-content:space-evenly !important}.align-items-sm-start{align-items:flex-start !important}.align-items-sm-end{align-items:flex-end !important}.align-items-sm-center{align-items:center !important}.align-items-sm-baseline{align-items:baseline !important}.align-items-sm-stretch{align-items:stretch !important}.align-content-sm-start{align-content:flex-start !important}.align-content-sm-end{align-content:flex-end !important}.align-content-sm-center{align-content:center !important}.align-content-sm-between{align-content:space-between !important}.align-content-sm-around{align-content:space-around !important}.align-content-sm-stretch{align-content:stretch !important}.align-self-sm-auto{align-self:auto !important}.align-self-sm-start{align-self:flex-start !important}.align-self-sm-end{align-self:flex-end !important}.align-self-sm-center{align-self:center !important}.align-self-sm-baseline{align-self:baseline !important}.align-self-sm-stretch{align-self:stretch !important}.order-sm-first{order:-1 !important}.order-sm-0{order:0 !important}.order-sm-1{order:1 !important}.order-sm-2{order:2 !important}.order-sm-3{order:3 !important}.order-sm-4{order:4 !important}.order-sm-5{order:5 !important}.order-sm-last{order:6 !important}.m-sm-0{margin:0 !important}.m-sm-1{margin:.25rem !important}.m-sm-2{margin:.5rem !important}.m-sm-3{margin:1rem !important}.m-sm-4{margin:1.5rem !important}.m-sm-5{margin:3rem !important}.m-sm-auto{margin:auto !important}.mx-sm-0{margin-right:0 !important;margin-left:0 !important}.mx-sm-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-sm-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-sm-3{margin-right:1rem !important;margin-left:1rem !important}.mx-sm-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-sm-5{margin-right:3rem !important;margin-left:3rem !important}.mx-sm-auto{margin-right:auto !important;margin-left:auto !important}.my-sm-0{margin-top:0 !important;margin-bottom:0 !important}.my-sm-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-sm-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-sm-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-sm-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-sm-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-sm-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-sm-0{margin-top:0 !important}.mt-sm-1{margin-top:.25rem !important}.mt-sm-2{margin-top:.5rem !important}.mt-sm-3{margin-top:1rem !important}.mt-sm-4{margin-top:1.5rem !important}.mt-sm-5{margin-top:3rem !important}.mt-sm-auto{margin-top:auto !important}.me-sm-0{margin-right:0 !important}.me-sm-1{margin-right:.25rem !important}.me-sm-2{margin-right:.5rem !important}.me-sm-3{margin-right:1rem !important}.me-sm-4{margin-right:1.5rem !important}.me-sm-5{margin-right:3rem !important}.me-sm-auto{margin-right:auto !important}.mb-sm-0{margin-bottom:0 !important}.mb-sm-1{margin-bottom:.25rem !important}.mb-sm-2{margin-bottom:.5rem !important}.mb-sm-3{margin-bottom:1rem !important}.mb-sm-4{margin-bottom:1.5rem !important}.mb-sm-5{margin-bottom:3rem !important}.mb-sm-auto{margin-bottom:auto !important}.ms-sm-0{margin-left:0 !important}.ms-sm-1{margin-left:.25rem !important}.ms-sm-2{margin-left:.5rem !important}.ms-sm-3{margin-left:1rem !important}.ms-sm-4{margin-left:1.5rem !important}.ms-sm-5{margin-left:3rem !important}.ms-sm-auto{margin-left:auto !important}.p-sm-0{padding:0 !important}.p-sm-1{padding:.25rem !important}.p-sm-2{padding:.5rem !important}.p-sm-3{padding:1rem !important}.p-sm-4{padding:1.5rem !important}.p-sm-5{padding:3rem !important}.px-sm-0{padding-right:0 !important;padding-left:0 !important}.px-sm-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-sm-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-sm-3{padding-right:1rem !important;padding-left:1rem !important}.px-sm-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-sm-5{padding-right:3rem !important;padding-left:3rem !important}.py-sm-0{padding-top:0 !important;padding-bottom:0 !important}.py-sm-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-sm-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-sm-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-sm-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-sm-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-sm-0{padding-top:0 !important}.pt-sm-1{padding-top:.25rem !important}.pt-sm-2{padding-top:.5rem !important}.pt-sm-3{padding-top:1rem !important}.pt-sm-4{padding-top:1.5rem !important}.pt-sm-5{padding-top:3rem !important}.pe-sm-0{padding-right:0 !important}.pe-sm-1{padding-right:.25rem !important}.pe-sm-2{padding-right:.5rem !important}.pe-sm-3{padding-right:1rem !important}.pe-sm-4{padding-right:1.5rem !important}.pe-sm-5{padding-right:3rem !important}.pb-sm-0{padding-bottom:0 !important}.pb-sm-1{padding-bottom:.25rem !important}.pb-sm-2{padding-bottom:.5rem !important}.pb-sm-3{padding-bottom:1rem !important}.pb-sm-4{padding-bottom:1.5rem !important}.pb-sm-5{padding-bottom:3rem !important}.ps-sm-0{padding-left:0 !important}.ps-sm-1{padding-left:.25rem !important}.ps-sm-2{padding-left:.5rem !important}.ps-sm-3{padding-left:1rem !important}.ps-sm-4{padding-left:1.5rem !important}.ps-sm-5{padding-left:3rem !important}.text-sm-start{text-align:left !important}.text-sm-end{text-align:right !important}.text-sm-center{text-align:center !important}}@media (min-width: 768px){.float-md-start{float:left !important}.float-md-end{float:right !important}.float-md-none{float:none !important}.d-md-inline{display:inline !important}.d-md-inline-block{display:inline-block !important}.d-md-block{display:block !important}.d-md-grid{display:grid !important}.d-md-table{display:table !important}.d-md-table-row{display:table-row !important}.d-md-table-cell{display:table-cell !important}.d-md-flex{display:flex !important}.d-md-inline-flex{display:inline-flex !important}.d-md-none{display:none !important}.flex-md-fill{flex:1 1 auto !important}.flex-md-row{flex-direction:row !important}.flex-md-column{flex-direction:column !important}.flex-md-row-reverse{flex-direction:row-reverse !important}.flex-md-column-reverse{flex-direction:column-reverse !important}.flex-md-grow-0{flex-grow:0 !important}.flex-md-grow-1{flex-grow:1 !important}.flex-md-shrink-0{flex-shrink:0 !important}.flex-md-shrink-1{flex-shrink:1 !important}.flex-md-wrap{flex-wrap:wrap !important}.flex-md-nowrap{flex-wrap:nowrap !important}.flex-md-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-md-0{gap:0 !important}.gap-md-1{gap:.25rem !important}.gap-md-2{gap:.5rem !important}.gap-md-3{gap:1rem !important}.gap-md-4{gap:1.5rem !important}.gap-md-5{gap:3rem !important}.justify-content-md-start{justify-content:flex-start !important}.justify-content-md-end{justify-content:flex-end !important}.justify-content-md-center{justify-content:center !important}.justify-content-md-between{justify-content:space-between !important}.justify-content-md-around{justify-content:space-around !important}.justify-content-md-evenly{justify-content:space-evenly !important}.align-items-md-start{align-items:flex-start !important}.align-items-md-end{align-items:flex-end !important}.align-items-md-center{align-items:center !important}.align-items-md-baseline{align-items:baseline !important}.align-items-md-stretch{align-items:stretch !important}.align-content-md-start{align-content:flex-start !important}.align-content-md-end{align-content:flex-end !important}.align-content-md-center{align-content:center !important}.align-content-md-between{align-content:space-between !important}.align-content-md-around{align-content:space-around !important}.align-content-md-stretch{align-content:stretch !important}.align-self-md-auto{align-self:auto !important}.align-self-md-start{align-self:flex-start !important}.align-self-md-end{align-self:flex-end !important}.align-self-md-center{align-self:center !important}.align-self-md-baseline{align-self:baseline !important}.align-self-md-stretch{align-self:stretch !important}.order-md-first{order:-1 !important}.order-md-0{order:0 !important}.order-md-1{order:1 !important}.order-md-2{order:2 !important}.order-md-3{order:3 !important}.order-md-4{order:4 !important}.order-md-5{order:5 !important}.order-md-last{order:6 !important}.m-md-0{margin:0 !important}.m-md-1{margin:.25rem !important}.m-md-2{margin:.5rem !important}.m-md-3{margin:1rem !important}.m-md-4{margin:1.5rem !important}.m-md-5{margin:3rem !important}.m-md-auto{margin:auto !important}.mx-md-0{margin-right:0 !important;margin-left:0 !important}.mx-md-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-md-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-md-3{margin-right:1rem !important;margin-left:1rem !important}.mx-md-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-md-5{margin-right:3rem !important;margin-left:3rem !important}.mx-md-auto{margin-right:auto !important;margin-left:auto !important}.my-md-0{margin-top:0 !important;margin-bottom:0 !important}.my-md-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-md-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-md-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-md-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-md-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-md-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-md-0{margin-top:0 !important}.mt-md-1{margin-top:.25rem !important}.mt-md-2{margin-top:.5rem !important}.mt-md-3{margin-top:1rem !important}.mt-md-4{margin-top:1.5rem !important}.mt-md-5{margin-top:3rem !important}.mt-md-auto{margin-top:auto !important}.me-md-0{margin-right:0 !important}.me-md-1{margin-right:.25rem !important}.me-md-2{margin-right:.5rem !important}.me-md-3{margin-right:1rem !important}.me-md-4{margin-right:1.5rem !important}.me-md-5{margin-right:3rem !important}.me-md-auto{margin-right:auto !important}.mb-md-0{margin-bottom:0 !important}.mb-md-1{margin-bottom:.25rem !important}.mb-md-2{margin-bottom:.5rem !important}.mb-md-3{margin-bottom:1rem !important}.mb-md-4{margin-bottom:1.5rem !important}.mb-md-5{margin-bottom:3rem !important}.mb-md-auto{margin-bottom:auto !important}.ms-md-0{margin-left:0 !important}.ms-md-1{margin-left:.25rem !important}.ms-md-2{margin-left:.5rem !important}.ms-md-3{margin-left:1rem !important}.ms-md-4{margin-left:1.5rem !important}.ms-md-5{margin-left:3rem !important}.ms-md-auto{margin-left:auto !important}.p-md-0{padding:0 !important}.p-md-1{padding:.25rem !important}.p-md-2{padding:.5rem !important}.p-md-3{padding:1rem !important}.p-md-4{padding:1.5rem !important}.p-md-5{padding:3rem !important}.px-md-0{padding-right:0 !important;padding-left:0 !important}.px-md-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-md-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-md-3{padding-right:1rem !important;padding-left:1rem !important}.px-md-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-md-5{padding-right:3rem !important;padding-left:3rem !important}.py-md-0{padding-top:0 !important;padding-bottom:0 !important}.py-md-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-md-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-md-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-md-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-md-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-md-0{padding-top:0 !important}.pt-md-1{padding-top:.25rem !important}.pt-md-2{padding-top:.5rem !important}.pt-md-3{padding-top:1rem !important}.pt-md-4{padding-top:1.5rem !important}.pt-md-5{padding-top:3rem !important}.pe-md-0{padding-right:0 !important}.pe-md-1{padding-right:.25rem !important}.pe-md-2{padding-right:.5rem !important}.pe-md-3{padding-right:1rem !important}.pe-md-4{padding-right:1.5rem !important}.pe-md-5{padding-right:3rem !important}.pb-md-0{padding-bottom:0 !important}.pb-md-1{padding-bottom:.25rem !important}.pb-md-2{padding-bottom:.5rem !important}.pb-md-3{padding-bottom:1rem !important}.pb-md-4{padding-bottom:1.5rem !important}.pb-md-5{padding-bottom:3rem !important}.ps-md-0{padding-left:0 !important}.ps-md-1{padding-left:.25rem !important}.ps-md-2{padding-left:.5rem !important}.ps-md-3{padding-left:1rem !important}.ps-md-4{padding-left:1.5rem !important}.ps-md-5{padding-left:3rem !important}.text-md-start{text-align:left !important}.text-md-end{text-align:right !important}.text-md-center{text-align:center !important}}@media (min-width: 992px){.float-lg-start{float:left !important}.float-lg-end{float:right !important}.float-lg-none{float:none !important}.d-lg-inline{display:inline !important}.d-lg-inline-block{display:inline-block !important}.d-lg-block{display:block !important}.d-lg-grid{display:grid !important}.d-lg-table{display:table !important}.d-lg-table-row{display:table-row !important}.d-lg-table-cell{display:table-cell !important}.d-lg-flex{display:flex !important}.d-lg-inline-flex{display:inline-flex !important}.d-lg-none{display:none !important}.flex-lg-fill{flex:1 1 auto !important}.flex-lg-row{flex-direction:row !important}.flex-lg-column{flex-direction:column !important}.flex-lg-row-reverse{flex-direction:row-reverse !important}.flex-lg-column-reverse{flex-direction:column-reverse !important}.flex-lg-grow-0{flex-grow:0 !important}.flex-lg-grow-1{flex-grow:1 !important}.flex-lg-shrink-0{flex-shrink:0 !important}.flex-lg-shrink-1{flex-shrink:1 !important}.flex-lg-wrap{flex-wrap:wrap !important}.flex-lg-nowrap{flex-wrap:nowrap !important}.flex-lg-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-lg-0{gap:0 !important}.gap-lg-1{gap:.25rem !important}.gap-lg-2{gap:.5rem !important}.gap-lg-3{gap:1rem !important}.gap-lg-4{gap:1.5rem !important}.gap-lg-5{gap:3rem !important}.justify-content-lg-start{justify-content:flex-start !important}.justify-content-lg-end{justify-content:flex-end !important}.justify-content-lg-center{justify-content:center !important}.justify-content-lg-between{justify-content:space-between !important}.justify-content-lg-around{justify-content:space-around !important}.justify-content-lg-evenly{justify-content:space-evenly !important}.align-items-lg-start{align-items:flex-start !important}.align-items-lg-end{align-items:flex-end !important}.align-items-lg-center{align-items:center !important}.align-items-lg-baseline{align-items:baseline !important}.align-items-lg-stretch{align-items:stretch !important}.align-content-lg-start{align-content:flex-start !important}.align-content-lg-end{align-content:flex-end !important}.align-content-lg-center{align-content:center !important}.align-content-lg-between{align-content:space-between !important}.align-content-lg-around{align-content:space-around !important}.align-content-lg-stretch{align-content:stretch !important}.align-self-lg-auto{align-self:auto !important}.align-self-lg-start{align-self:flex-start !important}.align-self-lg-end{align-self:flex-end !important}.align-self-lg-center{align-self:center !important}.align-self-lg-baseline{align-self:baseline !important}.align-self-lg-stretch{align-self:stretch !important}.order-lg-first{order:-1 !important}.order-lg-0{order:0 !important}.order-lg-1{order:1 !important}.order-lg-2{order:2 !important}.order-lg-3{order:3 !important}.order-lg-4{order:4 !important}.order-lg-5{order:5 !important}.order-lg-last{order:6 !important}.m-lg-0{margin:0 !important}.m-lg-1{margin:.25rem !important}.m-lg-2{margin:.5rem !important}.m-lg-3{margin:1rem !important}.m-lg-4{margin:1.5rem !important}.m-lg-5{margin:3rem !important}.m-lg-auto{margin:auto !important}.mx-lg-0{margin-right:0 !important;margin-left:0 !important}.mx-lg-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-lg-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-lg-3{margin-right:1rem !important;margin-left:1rem !important}.mx-lg-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-lg-5{margin-right:3rem !important;margin-left:3rem !important}.mx-lg-auto{margin-right:auto !important;margin-left:auto !important}.my-lg-0{margin-top:0 !important;margin-bottom:0 !important}.my-lg-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-lg-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-lg-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-lg-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-lg-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-lg-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-lg-0{margin-top:0 !important}.mt-lg-1{margin-top:.25rem !important}.mt-lg-2{margin-top:.5rem !important}.mt-lg-3{margin-top:1rem !important}.mt-lg-4{margin-top:1.5rem !important}.mt-lg-5{margin-top:3rem !important}.mt-lg-auto{margin-top:auto !important}.me-lg-0{margin-right:0 !important}.me-lg-1{margin-right:.25rem !important}.me-lg-2{margin-right:.5rem !important}.me-lg-3{margin-right:1rem !important}.me-lg-4{margin-right:1.5rem !important}.me-lg-5{margin-right:3rem !important}.me-lg-auto{margin-right:auto !important}.mb-lg-0{margin-bottom:0 !important}.mb-lg-1{margin-bottom:.25rem !important}.mb-lg-2{margin-bottom:.5rem !important}.mb-lg-3{margin-bottom:1rem !important}.mb-lg-4{margin-bottom:1.5rem !important}.mb-lg-5{margin-bottom:3rem !important}.mb-lg-auto{margin-bottom:auto !important}.ms-lg-0{margin-left:0 !important}.ms-lg-1{margin-left:.25rem !important}.ms-lg-2{margin-left:.5rem !important}.ms-lg-3{margin-left:1rem !important}.ms-lg-4{margin-left:1.5rem !important}.ms-lg-5{margin-left:3rem !important}.ms-lg-auto{margin-left:auto !important}.p-lg-0{padding:0 !important}.p-lg-1{padding:.25rem !important}.p-lg-2{padding:.5rem !important}.p-lg-3{padding:1rem !important}.p-lg-4{padding:1.5rem !important}.p-lg-5{padding:3rem !important}.px-lg-0{padding-right:0 !important;padding-left:0 !important}.px-lg-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-lg-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-lg-3{padding-right:1rem !important;padding-left:1rem !important}.px-lg-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-lg-5{padding-right:3rem !important;padding-left:3rem !important}.py-lg-0{padding-top:0 !important;padding-bottom:0 !important}.py-lg-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-lg-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-lg-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-lg-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-lg-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-lg-0{padding-top:0 !important}.pt-lg-1{padding-top:.25rem !important}.pt-lg-2{padding-top:.5rem !important}.pt-lg-3{padding-top:1rem !important}.pt-lg-4{padding-top:1.5rem !important}.pt-lg-5{padding-top:3rem !important}.pe-lg-0{padding-right:0 !important}.pe-lg-1{padding-right:.25rem !important}.pe-lg-2{padding-right:.5rem !important}.pe-lg-3{padding-right:1rem !important}.pe-lg-4{padding-right:1.5rem !important}.pe-lg-5{padding-right:3rem !important}.pb-lg-0{padding-bottom:0 !important}.pb-lg-1{padding-bottom:.25rem !important}.pb-lg-2{padding-bottom:.5rem !important}.pb-lg-3{padding-bottom:1rem !important}.pb-lg-4{padding-bottom:1.5rem !important}.pb-lg-5{padding-bottom:3rem !important}.ps-lg-0{padding-left:0 !important}.ps-lg-1{padding-left:.25rem !important}.ps-lg-2{padding-left:.5rem !important}.ps-lg-3{padding-left:1rem !important}.ps-lg-4{padding-left:1.5rem !important}.ps-lg-5{padding-left:3rem !important}.text-lg-start{text-align:left !important}.text-lg-end{text-align:right !important}.text-lg-center{text-align:center !important}}@media (min-width: 1200px){.float-xl-start{float:left !important}.float-xl-end{float:right !important}.float-xl-none{float:none !important}.d-xl-inline{display:inline !important}.d-xl-inline-block{display:inline-block !important}.d-xl-block{display:block !important}.d-xl-grid{display:grid !important}.d-xl-table{display:table !important}.d-xl-table-row{display:table-row !important}.d-xl-table-cell{display:table-cell !important}.d-xl-flex{display:flex !important}.d-xl-inline-flex{display:inline-flex !important}.d-xl-none{display:none !important}.flex-xl-fill{flex:1 1 auto !important}.flex-xl-row{flex-direction:row !important}.flex-xl-column{flex-direction:column !important}.flex-xl-row-reverse{flex-direction:row-reverse !important}.flex-xl-column-reverse{flex-direction:column-reverse !important}.flex-xl-grow-0{flex-grow:0 !important}.flex-xl-grow-1{flex-grow:1 !important}.flex-xl-shrink-0{flex-shrink:0 !important}.flex-xl-shrink-1{flex-shrink:1 !important}.flex-xl-wrap{flex-wrap:wrap !important}.flex-xl-nowrap{flex-wrap:nowrap !important}.flex-xl-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-xl-0{gap:0 !important}.gap-xl-1{gap:.25rem !important}.gap-xl-2{gap:.5rem !important}.gap-xl-3{gap:1rem !important}.gap-xl-4{gap:1.5rem !important}.gap-xl-5{gap:3rem !important}.justify-content-xl-start{justify-content:flex-start !important}.justify-content-xl-end{justify-content:flex-end !important}.justify-content-xl-center{justify-content:center !important}.justify-content-xl-between{justify-content:space-between !important}.justify-content-xl-around{justify-content:space-around !important}.justify-content-xl-evenly{justify-content:space-evenly !important}.align-items-xl-start{align-items:flex-start !important}.align-items-xl-end{align-items:flex-end !important}.align-items-xl-center{align-items:center !important}.align-items-xl-baseline{align-items:baseline !important}.align-items-xl-stretch{align-items:stretch !important}.align-content-xl-start{align-content:flex-start !important}.align-content-xl-end{align-content:flex-end !important}.align-content-xl-center{align-content:center !important}.align-content-xl-between{align-content:space-between !important}.align-content-xl-around{align-content:space-around !important}.align-content-xl-stretch{align-content:stretch !important}.align-self-xl-auto{align-self:auto !important}.align-self-xl-start{align-self:flex-start !important}.align-self-xl-end{align-self:flex-end !important}.align-self-xl-center{align-self:center !important}.align-self-xl-baseline{align-self:baseline !important}.align-self-xl-stretch{align-self:stretch !important}.order-xl-first{order:-1 !important}.order-xl-0{order:0 !important}.order-xl-1{order:1 !important}.order-xl-2{order:2 !important}.order-xl-3{order:3 !important}.order-xl-4{order:4 !important}.order-xl-5{order:5 !important}.order-xl-last{order:6 !important}.m-xl-0{margin:0 !important}.m-xl-1{margin:.25rem !important}.m-xl-2{margin:.5rem !important}.m-xl-3{margin:1rem !important}.m-xl-4{margin:1.5rem !important}.m-xl-5{margin:3rem !important}.m-xl-auto{margin:auto !important}.mx-xl-0{margin-right:0 !important;margin-left:0 !important}.mx-xl-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-xl-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-xl-3{margin-right:1rem !important;margin-left:1rem !important}.mx-xl-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-xl-5{margin-right:3rem !important;margin-left:3rem !important}.mx-xl-auto{margin-right:auto !important;margin-left:auto !important}.my-xl-0{margin-top:0 !important;margin-bottom:0 !important}.my-xl-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-xl-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-xl-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-xl-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-xl-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-xl-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-xl-0{margin-top:0 !important}.mt-xl-1{margin-top:.25rem !important}.mt-xl-2{margin-top:.5rem !important}.mt-xl-3{margin-top:1rem !important}.mt-xl-4{margin-top:1.5rem !important}.mt-xl-5{margin-top:3rem !important}.mt-xl-auto{margin-top:auto !important}.me-xl-0{margin-right:0 !important}.me-xl-1{margin-right:.25rem !important}.me-xl-2{margin-right:.5rem !important}.me-xl-3{margin-right:1rem !important}.me-xl-4{margin-right:1.5rem !important}.me-xl-5{margin-right:3rem !important}.me-xl-auto{margin-right:auto !important}.mb-xl-0{margin-bottom:0 !important}.mb-xl-1{margin-bottom:.25rem !important}.mb-xl-2{margin-bottom:.5rem !important}.mb-xl-3{margin-bottom:1rem !important}.mb-xl-4{margin-bottom:1.5rem !important}.mb-xl-5{margin-bottom:3rem !important}.mb-xl-auto{margin-bottom:auto !important}.ms-xl-0{margin-left:0 !important}.ms-xl-1{margin-left:.25rem !important}.ms-xl-2{margin-left:.5rem !important}.ms-xl-3{margin-left:1rem !important}.ms-xl-4{margin-left:1.5rem !important}.ms-xl-5{margin-left:3rem !important}.ms-xl-auto{margin-left:auto !important}.p-xl-0{padding:0 !important}.p-xl-1{padding:.25rem !important}.p-xl-2{padding:.5rem !important}.p-xl-3{padding:1rem !important}.p-xl-4{padding:1.5rem !important}.p-xl-5{padding:3rem !important}.px-xl-0{padding-right:0 !important;padding-left:0 !important}.px-xl-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-xl-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-xl-3{padding-right:1rem !important;padding-left:1rem !important}.px-xl-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-xl-5{padding-right:3rem !important;padding-left:3rem !important}.py-xl-0{padding-top:0 !important;padding-bottom:0 !important}.py-xl-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-xl-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-xl-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-xl-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-xl-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-xl-0{padding-top:0 !important}.pt-xl-1{padding-top:.25rem !important}.pt-xl-2{padding-top:.5rem !important}.pt-xl-3{padding-top:1rem !important}.pt-xl-4{padding-top:1.5rem !important}.pt-xl-5{padding-top:3rem !important}.pe-xl-0{padding-right:0 !important}.pe-xl-1{padding-right:.25rem !important}.pe-xl-2{padding-right:.5rem !important}.pe-xl-3{padding-right:1rem !important}.pe-xl-4{padding-right:1.5rem !important}.pe-xl-5{padding-right:3rem !important}.pb-xl-0{padding-bottom:0 !important}.pb-xl-1{padding-bottom:.25rem !important}.pb-xl-2{padding-bottom:.5rem !important}.pb-xl-3{padding-bottom:1rem !important}.pb-xl-4{padding-bottom:1.5rem !important}.pb-xl-5{padding-bottom:3rem !important}.ps-xl-0{padding-left:0 !important}.ps-xl-1{padding-left:.25rem !important}.ps-xl-2{padding-left:.5rem !important}.ps-xl-3{padding-left:1rem !important}.ps-xl-4{padding-left:1.5rem !important}.ps-xl-5{padding-left:3rem !important}.text-xl-start{text-align:left !important}.text-xl-end{text-align:right !important}.text-xl-center{text-align:center !important}}@media (min-width: 1400px){.float-xxl-start{float:left !important}.float-xxl-end{float:right !important}.float-xxl-none{float:none !important}.d-xxl-inline{display:inline !important}.d-xxl-inline-block{display:inline-block !important}.d-xxl-block{display:block !important}.d-xxl-grid{display:grid !important}.d-xxl-table{display:table !important}.d-xxl-table-row{display:table-row !important}.d-xxl-table-cell{display:table-cell !important}.d-xxl-flex{display:flex !important}.d-xxl-inline-flex{display:inline-flex !important}.d-xxl-none{display:none !important}.flex-xxl-fill{flex:1 1 auto !important}.flex-xxl-row{flex-direction:row !important}.flex-xxl-column{flex-direction:column !important}.flex-xxl-row-reverse{flex-direction:row-reverse !important}.flex-xxl-column-reverse{flex-direction:column-reverse !important}.flex-xxl-grow-0{flex-grow:0 !important}.flex-xxl-grow-1{flex-grow:1 !important}.flex-xxl-shrink-0{flex-shrink:0 !important}.flex-xxl-shrink-1{flex-shrink:1 !important}.flex-xxl-wrap{flex-wrap:wrap !important}.flex-xxl-nowrap{flex-wrap:nowrap !important}.flex-xxl-wrap-reverse{flex-wrap:wrap-reverse !important}.gap-xxl-0{gap:0 !important}.gap-xxl-1{gap:.25rem !important}.gap-xxl-2{gap:.5rem !important}.gap-xxl-3{gap:1rem !important}.gap-xxl-4{gap:1.5rem !important}.gap-xxl-5{gap:3rem !important}.justify-content-xxl-start{justify-content:flex-start !important}.justify-content-xxl-end{justify-content:flex-end !important}.justify-content-xxl-center{justify-content:center !important}.justify-content-xxl-between{justify-content:space-between !important}.justify-content-xxl-around{justify-content:space-around !important}.justify-content-xxl-evenly{justify-content:space-evenly !important}.align-items-xxl-start{align-items:flex-start !important}.align-items-xxl-end{align-items:flex-end !important}.align-items-xxl-center{align-items:center !important}.align-items-xxl-baseline{align-items:baseline !important}.align-items-xxl-stretch{align-items:stretch !important}.align-content-xxl-start{align-content:flex-start !important}.align-content-xxl-end{align-content:flex-end !important}.align-content-xxl-center{align-content:center !important}.align-content-xxl-between{align-content:space-between !important}.align-content-xxl-around{align-content:space-around !important}.align-content-xxl-stretch{align-content:stretch !important}.align-self-xxl-auto{align-self:auto !important}.align-self-xxl-start{align-self:flex-start !important}.align-self-xxl-end{align-self:flex-end !important}.align-self-xxl-center{align-self:center !important}.align-self-xxl-baseline{align-self:baseline !important}.align-self-xxl-stretch{align-self:stretch !important}.order-xxl-first{order:-1 !important}.order-xxl-0{order:0 !important}.order-xxl-1{order:1 !important}.order-xxl-2{order:2 !important}.order-xxl-3{order:3 !important}.order-xxl-4{order:4 !important}.order-xxl-5{order:5 !important}.order-xxl-last{order:6 !important}.m-xxl-0{margin:0 !important}.m-xxl-1{margin:.25rem !important}.m-xxl-2{margin:.5rem !important}.m-xxl-3{margin:1rem !important}.m-xxl-4{margin:1.5rem !important}.m-xxl-5{margin:3rem !important}.m-xxl-auto{margin:auto !important}.mx-xxl-0{margin-right:0 !important;margin-left:0 !important}.mx-xxl-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-xxl-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-xxl-3{margin-right:1rem !important;margin-left:1rem !important}.mx-xxl-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-xxl-5{margin-right:3rem !important;margin-left:3rem !important}.mx-xxl-auto{margin-right:auto !important;margin-left:auto !important}.my-xxl-0{margin-top:0 !important;margin-bottom:0 !important}.my-xxl-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-xxl-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-xxl-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-xxl-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-xxl-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-xxl-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-xxl-0{margin-top:0 !important}.mt-xxl-1{margin-top:.25rem !important}.mt-xxl-2{margin-top:.5rem !important}.mt-xxl-3{margin-top:1rem !important}.mt-xxl-4{margin-top:1.5rem !important}.mt-xxl-5{margin-top:3rem !important}.mt-xxl-auto{margin-top:auto !important}.me-xxl-0{margin-right:0 !important}.me-xxl-1{margin-right:.25rem !important}.me-xxl-2{margin-right:.5rem !important}.me-xxl-3{margin-right:1rem !important}.me-xxl-4{margin-right:1.5rem !important}.me-xxl-5{margin-right:3rem !important}.me-xxl-auto{margin-right:auto !important}.mb-xxl-0{margin-bottom:0 !important}.mb-xxl-1{margin-bottom:.25rem !important}.mb-xxl-2{margin-bottom:.5rem !important}.mb-xxl-3{margin-bottom:1rem !important}.mb-xxl-4{margin-bottom:1.5rem !important}.mb-xxl-5{margin-bottom:3rem !important}.mb-xxl-auto{margin-bottom:auto !important}.ms-xxl-0{margin-left:0 !important}.ms-xxl-1{margin-left:.25rem !important}.ms-xxl-2{margin-left:.5rem !important}.ms-xxl-3{margin-left:1rem !important}.ms-xxl-4{margin-left:1.5rem !important}.ms-xxl-5{margin-left:3rem !important}.ms-xxl-auto{margin-left:auto !important}.p-xxl-0{padding:0 !important}.p-xxl-1{padding:.25rem !important}.p-xxl-2{padding:.5rem !important}.p-xxl-3{padding:1rem !important}.p-xxl-4{padding:1.5rem !important}.p-xxl-5{padding:3rem !important}.px-xxl-0{padding-right:0 !important;padding-left:0 !important}.px-xxl-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-xxl-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-xxl-3{padding-right:1rem !important;padding-left:1rem !important}.px-xxl-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-xxl-5{padding-right:3rem !important;padding-left:3rem !important}.py-xxl-0{padding-top:0 !important;padding-bottom:0 !important}.py-xxl-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-xxl-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-xxl-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-xxl-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-xxl-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-xxl-0{padding-top:0 !important}.pt-xxl-1{padding-top:.25rem !important}.pt-xxl-2{padding-top:.5rem !important}.pt-xxl-3{padding-top:1rem !important}.pt-xxl-4{padding-top:1.5rem !important}.pt-xxl-5{padding-top:3rem !important}.pe-xxl-0{padding-right:0 !important}.pe-xxl-1{padding-right:.25rem !important}.pe-xxl-2{padding-right:.5rem !important}.pe-xxl-3{padding-right:1rem !important}.pe-xxl-4{padding-right:1.5rem !important}.pe-xxl-5{padding-right:3rem !important}.pb-xxl-0{padding-bottom:0 !important}.pb-xxl-1{padding-bottom:.25rem !important}.pb-xxl-2{padding-bottom:.5rem !important}.pb-xxl-3{padding-bottom:1rem !important}.pb-xxl-4{padding-bottom:1.5rem !important}.pb-xxl-5{padding-bottom:3rem !important}.ps-xxl-0{padding-left:0 !important}.ps-xxl-1{padding-left:.25rem !important}.ps-xxl-2{padding-left:.5rem !important}.ps-xxl-3{padding-left:1rem !important}.ps-xxl-4{padding-left:1.5rem !important}.ps-xxl-5{padding-left:3rem !important}.text-xxl-start{text-align:left !important}.text-xxl-end{text-align:right !important}.text-xxl-center{text-align:center !important}}@media (min-width: 1200px){.fs-1{font-size:2.5rem !important}.fs-2{font-size:2rem !important}.fs-3{font-size:1.75rem !important}.fs-4{font-size:1.5rem !important}}@media print{.d-print-inline{display:inline !important}.d-print-inline-block{display:inline-block !important}.d-print-block{display:block !important}.d-print-grid{display:grid !important}.d-print-table{display:table !important}.d-print-table-row{display:table-row !important}.d-print-table-cell{display:table-cell !important}.d-print-flex{display:flex !important}.d-print-inline-flex{display:inline-flex !important}.d-print-none{display:none !important}}\n", ":root {\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color,\n  $value in $colors {\n    --#{$variable-prefix}#{$color}: #{$value};\n  }\n\n  @each $color,\n  $value in $grays {\n    --#{$variable-prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color,\n  $value in $theme-colors {\n    --#{$variable-prefix}#{$color}: #{$value};\n  }\n\n  @each $color,\n  $value in $theme-colors-rgb {\n    --#{$variable-prefix}#{$color}-rgb: #{$value};\n  }\n\n  --#{$variable-prefix}white-rgb: #{to-rgb($white)};\n  --#{$variable-prefix}black-rgb: #{to-rgb($black)};\n  --#{$variable-prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$variable-prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$variable-prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$variable-prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$variable-prefix}gradient: #{$gradient};\n\n  // Root and body\n  // stylelint-disable custom-property-empty-line-before\n  // scss-docs-start root-body-variables\n  @if $font-size-root !=null {\n    --#{$variable-prefix}root-font-size: #{$font-size-root};\n  }\n\n  --#{$variable-prefix}body-font-family: #{$font-family-base};\n  --#{$variable-prefix}body-font-size: #{$font-size-base};\n  --#{$variable-prefix}body-font-weight: #{$font-weight-base};\n  --#{$variable-prefix}body-line-height: #{$line-height-base};\n  --#{$variable-prefix}body-color: #{$body-color};\n\n  @if $body-text-align !=null {\n    --#{$variable-prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$variable-prefix}body-bg: #{$body-bg};\n  // scss-docs-end root-body-variables\n  // stylelint-enable custom-property-empty-line-before\n}", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  @if $font-size-root !=null {\n    font-size: var(--#{$variable-prefix}root-font-size);\n  }\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n\n// scss-docs-start reboot-body-rules\nbody {\n  margin: 0; // 1\n  font-family: var(--#{$variable-prefix}body-font-family);\n  @include font-size(var(--#{$variable-prefix}body-font-size));\n  font-weight: var(--#{$variable-prefix}body-font-weight);\n  line-height: var(--#{$variable-prefix}body-line-height);\n  color: var(--#{$variable-prefix}body-color);\n  text-align: var(--#{$variable-prefix}body-text-align);\n  background-color: var(--#{$variable-prefix}body-bg); // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n}\n\n// scss-docs-end reboot-body-rules\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n// 2. Set correct height and prevent the `size` attribute to make the `hr` look like an input field\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  background-color: currentColor;\n  border: 0;\n  opacity: $hr-opacity;\n}\n\nhr:not([size]) {\n  height: $hr-height; // 2\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h1-font-size);\n}\n\nh2 {\n  @extend %heading;\n  @include font-size($h2-font-size);\n}\n\nh3 {\n  @extend %heading;\n  @include font-size($h3-font-size);\n}\n\nh4 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n}\n\nh5 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n}\n\nh6 {\n  @extend %heading;\n  @include font-size($h6-font-size);\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Duplicate behavior to the data-bs-* attribute for our tooltip plugin\n// 2. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 3. Add explicit cursor to indicate changed behavior.\n// 4. Prevent the text-decoration to be skipped.\n\nabbr[title],\nabbr[data-bs-original-title] {\n  // 1\n  text-decoration: underline dotted; // 2\n  cursor: help; // 3\n  text-decoration-skip-ink: none; // 4\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nstrong {\n  font-weight: $font-weight-bolder;\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  background-color: $mark-bg;\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -.25em;\n}\n\nsup {\n  top: -.5em;\n}\n\n\n// Links\n\na {\n  color: $link-color;\n  text-decoration: $link-decoration;\n\n  &:hover {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n  direction: ltr #{\"/* rtl:ignore */\"};\n  unicode-bidi: bidi-override;\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\ncode {\n  @include font-size($code-font-size);\n  color: $code-color;\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a>& {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// Prevent double borders\n\ntable {\n  caption-side: bottom;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-cell-padding-y;\n  padding-bottom: $table-cell-padding-y;\n  color: $table-caption-color;\n  text-align: left;\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n\nbutton:focus:not(:focus-visible) {\n  outline: 0;\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow in Chrome from inputs built with datalists.\n// See https://stackoverflow.com/a/54997118\n\n[list]::-webkit-calendar-picker-indicator {\n  display: none;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"],\n// 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  +* {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. Correct the outline style in Safari.\n// 2. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n\n[type=\"search\"] {\n  outline-offset: -2px; // 1\n  -webkit-appearance: textfield; // 2\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// Inherit font family and line height for file input buttons\n\n::file-selector-button {\n  font: inherit;\n}\n\n// 1. Change font properties to `inherit`\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::-webkit-file-upload-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit !=rem and $rfs-unit !=px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit !=px and $rfs-breakpoint-unit !=em and $rfs-breakpoint-unit !=rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) !=number or $rfs-factor <=1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n\n  @if $dividend==0 {\n    @return 0;\n  }\n\n  @if $divisor==0 {\n    @error \"Cannot divide by 0\";\n  }\n\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n\n  @while ($remainder > 0 and $precision >=0) {\n    $quotient: 0;\n\n    @while ($remainder >=$divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n\n    $result: $result * 10+$quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n\n    @if ($precision < 0 and $remainder >=$divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%);\n\n  @if ($dividend-unit !=$divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit==px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n\n@else if $rfs-base-value-unit==rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache==px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n\n@else if $rfs-breakpoint-unit-cache==rem or $rfs-breakpoint-unit-cache==\"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit==px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode==max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode==max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode==max-media-query {\n\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}),\n      (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class==disable and $rfs-mode==max-media-query {\n\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n\n  @else if $rfs-class==enable and $rfs-mode==min-media-query {\n\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  }\n\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class==enable {\n    @if $rfs-mode==min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query {\n\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n\n  @else {\n    @if $rfs-class==disable and $rfs-mode==min-media-query {\n\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n\n    @include _rfs-media-query {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) !=list, ($values, ), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value==0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value)==\"number\", unit($value), false);\n\n      @if $unit==px {\n        // Convert to rem if needed\n        $val: $val+' '+if($rfs-unit==rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n\n      @else if $unit==rem {\n        // Convert to px if needed\n        $val: $val+' '+if($rfs-unit==px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      }\n\n      @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + ' '+ $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) !=list, ($values, ), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value==0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value)==\"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit !=px and $unit !=rem {\n        $val: $val + ' '+ $value;\n      }\n\n      @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit==px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <=$rfs-base-value or not $enable-rfs {\n          $val: $val+' '+if($rfs-unit==rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit==rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + ' calc('+ $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values !=null {\n    $val: rfs-value($values);\n    $fluidVal: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val==$fluidVal {\n      #{$property}: $val;\n    }\n\n    @else {\n      @include _rfs-rule {\n        #{$property}: if($rfs-mode==max-media-query, $val, $fluidVal);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule {\n        #{$property}: if($rfs-mode==max-media-query, $fluidVal, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white: #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black: #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue: #0d6efd !default;\n$indigo: #6610f2 !default;\n$purple: #6f42c1 !default;\n$pink: #d63384 !default;\n$red: #dc3545 !default;\n$orange: #fd7e14 !default;\n$yellow: #ffc107 !default;\n$green: #198754 !default;\n$teal: #20c997 !default;\n$cyan: #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\"blue\": $blue,\n  \"indigo\": $indigo,\n  \"purple\": $purple,\n  \"pink\": $pink,\n  \"red\": $red,\n  \"orange\": $orange,\n  \"yellow\": $yellow,\n  \"green\": $green,\n  \"teal\": $teal,\n  \"cyan\": $cyan,\n  \"white\": $white,\n  \"gray\": $gray-600,\n  \"gray-dark\": $gray-800) !default;\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary: $blue !default;\n$secondary: $gray-600 !default;\n$success: $green !default;\n$info: $cyan !default;\n$warning: $yellow !default;\n$danger: $red !default;\n$light: $gray-100 !default;\n$dark: $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\"primary\": $primary,\n  \"secondary\": $secondary,\n  \"success\": $success,\n  \"info\": $info,\n  \"warning\": $warning,\n  \"danger\": $danger,\n  \"light\": $light,\n  \"dark\": $dark) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\") !default;\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio: 4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark: $black !default;\n$color-contrast-light: $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900) !default;\n\n$indigos: (\"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900) !default;\n\n$purples: (\"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900) !default;\n\n$pinks: (\"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900) !default;\n\n$reds: (\"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900) !default;\n\n$oranges: (\"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900) !default;\n\n$yellows: (\"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900) !default;\n\n$greens: (\"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900) !default;\n\n$teals: (\"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900) !default;\n\n$cyans: (\"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: ((\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret: true !default;\n$enable-rounded: true !default;\n$enable-shadows: false !default;\n$enable-gradients: false !default;\n$enable-transitions: true !default;\n$enable-reduced-motion: true !default;\n$enable-smooth-scroll: true !default;\n$enable-grid-classes: true !default;\n$enable-cssgrid: false !default;\n$enable-button-pointers: true !default;\n$enable-rfs: true !default;\n$enable-validation-icons: true !default;\n$enable-negative-margins: false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities: true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix: bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (0: 0,\n  50: 50%,\n  100: 100%) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg: $white !default;\n$body-color: $gray-900 !default;\n$body-text-align: null !default;\n\n// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// Come v6, we'll de-dupe these variables. Until then, for backward compatibility, we keep them to reassign.\n// scss-docs-start utilities-colors\n$utilities-colors: $theme-colors-rgb !default;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text: map-merge($utilities-colors,\n  (\"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-color))) !default;\n$utilities-text-colors: map-loop($utilities-text, rgba-css-var, \"$key\", \"text\") !default;\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg: map-merge($utilities-colors,\n  (\"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-bg))) !default;\n$utilities-bg-colors: map-loop($utilities-bg, rgba-css-var, \"$key\", \"bg\") !default;\n// scss-docs-end utilities-bg-colors\n\n// Links\n//\n// Style anchor elements.\n\n$link-color: $primary !default;\n$link-decoration: underline !default;\n$link-shade-percentage: 20% !default;\n$link-hover-color: shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration: null !default;\n\n$stretched-link-pseudo-element: after !default;\n$stretched-link-z-index: 1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom: 1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns: 12 !default;\n$grid-gutter-width: 1.5rem !default;\n$grid-row-columns: 6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * .5 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width: 1px !default;\n$border-widths: (1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px) !default;\n\n$border-color: $gray-300 !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius: .25rem !default;\n$border-radius-sm: .2rem !default;\n$border-radius-lg: .3rem !default;\n$border-radius-pill: 50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow: 0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm: 0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg: 0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset: inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color: $white !default;\n$component-active-bg: $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width: .3em !default;\n$caret-vertical-align: $caret-width * .85 !default;\n$caret-spacing: $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base: all .2s ease-in-out !default;\n$transition-fade: opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse: height .35s ease !default;\n$transition-collapse-width: width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif: system-ui,\n-apple-system,\n\"Segoe UI\",\nRoboto,\n\"Helvetica Neue\",\nArial,\n\"Noto Sans\",\n\"Liberation Sans\",\nsans-serif,\n\"Apple Color Emoji\",\n\"Segoe UI Emoji\",\n\"Segoe UI Symbol\",\n\"Noto Color Emoji\" !default;\n$font-family-monospace: SFMono-Regular,\nMenlo,\nMonaco,\nConsolas,\n\"Liberation Mono\",\n\"Courier New\",\nmonospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base: var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code: var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root: null !default;\n$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm: $font-size-base * .875 !default;\n$font-size-lg: $font-size-base * 1.25 !default;\n\n$font-weight-lighter: lighter !default;\n$font-weight-light: 300 !default;\n$font-weight-normal: 400 !default;\n$font-weight-bold: 700 !default;\n$font-weight-bolder: bolder !default;\n\n$font-weight-base: $font-weight-normal !default;\n\n$line-height-base: 1.5 !default;\n$line-height-sm: 1.25 !default;\n$line-height-lg: 2 !default;\n\n$h1-font-size: $font-size-base * 2.5 !default;\n$h2-font-size: $font-size-base * 2 !default;\n$h3-font-size: $font-size-base * 1.75 !default;\n$h4-font-size: $font-size-base * 1.5 !default;\n$h5-font-size: $font-size-base * 1.25 !default;\n$h6-font-size: $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom: $spacer * .5 !default;\n$headings-font-family: null !default;\n$headings-font-style: null !default;\n$headings-font-weight: 500 !default;\n$headings-line-height: 1.2 !default;\n$headings-color: null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size: $font-size-base * 1.25 !default;\n$lead-font-weight: 300 !default;\n\n$small-font-size: .875em !default;\n\n$sub-sup-font-size: .75em !default;\n\n$text-muted: $gray-600 !default;\n\n$initialism-font-size: $small-font-size !default;\n\n$blockquote-margin-y: $spacer !default;\n$blockquote-font-size: $font-size-base * 1.25 !default;\n$blockquote-footer-color: $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y: $spacer !default;\n$hr-color: inherit !default;\n$hr-height: $border-width !default;\n$hr-opacity: .25 !default;\n\n$legend-margin-bottom: .5rem !default;\n$legend-font-size: 1.5rem !default;\n$legend-font-weight: null !default;\n\n$mark-padding: .2em !default;\n\n$dt-font-weight: $font-weight-bold !default;\n\n$nested-kbd-font-weight: $font-weight-bold !default;\n\n$list-inline-padding: .5rem !default;\n\n$mark-bg: #fcf8e3 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y: .5rem !default;\n$table-cell-padding-x: .5rem !default;\n$table-cell-padding-y-sm: .25rem !default;\n$table-cell-padding-x-sm: .25rem !default;\n\n$table-cell-vertical-align: top !default;\n\n$table-color: $body-color !default;\n$table-bg: transparent !default;\n$table-accent-bg: transparent !default;\n\n$table-th-font-weight: null !default;\n\n$table-striped-color: $table-color !default;\n$table-striped-bg-factor: .05 !default;\n$table-striped-bg: rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color: $table-color !default;\n$table-active-bg-factor: .1 !default;\n$table-active-bg: rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color: $table-color !default;\n$table-hover-bg-factor: .075 !default;\n$table-hover-bg: rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor: .1 !default;\n$table-border-width: $border-width !default;\n$table-border-color: $border-color !default;\n\n$table-striped-order: odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color: $text-muted !default;\n\n$table-bg-scale: -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\"primary\": shift-color($primary, $table-bg-scale),\n  \"secondary\": shift-color($secondary, $table-bg-scale),\n  \"success\": shift-color($success, $table-bg-scale),\n  \"info\": shift-color($info, $table-bg-scale),\n  \"warning\": shift-color($warning, $table-bg-scale),\n  \"danger\": shift-color($danger, $table-bg-scale),\n  \"light\": $light,\n  \"dark\": $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y: .375rem !default;\n$input-btn-padding-x: .75rem !default;\n$input-btn-font-family: null !default;\n$input-btn-font-size: $font-size-base !default;\n$input-btn-line-height: $line-height-base !default;\n\n$input-btn-focus-width: .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color: rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur: 0 !default;\n$input-btn-focus-box-shadow: 0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm: .25rem !default;\n$input-btn-padding-x-sm: .5rem !default;\n$input-btn-font-size-sm: $font-size-sm !default;\n\n$input-btn-padding-y-lg: .5rem !default;\n$input-btn-padding-x-lg: 1rem !default;\n$input-btn-font-size-lg: $font-size-lg !default;\n\n$input-btn-border-width: $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y: $input-btn-padding-y !default;\n$btn-padding-x: $input-btn-padding-x !default;\n$btn-font-family: $input-btn-font-family !default;\n$btn-font-size: $input-btn-font-size !default;\n$btn-line-height: $input-btn-line-height !default;\n$btn-white-space: null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm: $input-btn-padding-y-sm !default;\n$btn-padding-x-sm: $input-btn-padding-x-sm !default;\n$btn-font-size-sm: $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg: $input-btn-padding-y-lg !default;\n$btn-padding-x-lg: $input-btn-padding-x-lg !default;\n$btn-font-size-lg: $input-btn-font-size-lg !default;\n\n$btn-border-width: $input-btn-border-width !default;\n\n$btn-font-weight: $font-weight-normal !default;\n$btn-box-shadow: inset 0 1px 0 rgba($white, .15),\n0 1px 1px rgba($black, .075) !default;\n$btn-focus-width: $input-btn-focus-width !default;\n$btn-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity: .65 !default;\n$btn-active-box-shadow: inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color: $link-color !default;\n$btn-link-hover-color: $link-hover-color !default;\n$btn-link-disabled-color: $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius: $border-radius !default;\n$btn-border-radius-sm: $border-radius-sm !default;\n$btn-border-radius-lg: $border-radius-lg !default;\n\n$btn-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount: 15% !default;\n$btn-hover-bg-tint-amount: 15% !default;\n$btn-hover-border-shade-amount: 20% !default;\n$btn-hover-border-tint-amount: 10% !default;\n$btn-active-bg-shade-amount: 20% !default;\n$btn-active-bg-tint-amount: 20% !default;\n$btn-active-border-shade-amount: 25% !default;\n$btn-active-border-tint-amount: 10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top: .25rem !default;\n$form-text-font-size: $small-font-size !default;\n$form-text-font-style: null !default;\n$form-text-font-weight: null !default;\n$form-text-color: $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom: .5rem !default;\n$form-label-font-size: null !default;\n$form-label-font-style: null !default;\n$form-label-font-weight: null !default;\n$form-label-color: null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y: $input-btn-padding-y !default;\n$input-padding-x: $input-btn-padding-x !default;\n$input-font-family: $input-btn-font-family !default;\n$input-font-size: $input-btn-font-size !default;\n$input-font-weight: $font-weight-base !default;\n$input-line-height: $input-btn-line-height !default;\n\n$input-padding-y-sm: $input-btn-padding-y-sm !default;\n$input-padding-x-sm: $input-btn-padding-x-sm !default;\n$input-font-size-sm: $input-btn-font-size-sm !default;\n\n$input-padding-y-lg: $input-btn-padding-y-lg !default;\n$input-padding-x-lg: $input-btn-padding-x-lg !default;\n$input-font-size-lg: $input-btn-font-size-lg !default;\n\n$input-bg: $body-bg !default;\n$input-disabled-bg: $gray-200 !default;\n$input-disabled-border-color: null !default;\n\n$input-color: $body-color !default;\n$input-border-color: $gray-400 !default;\n$input-border-width: $input-btn-border-width !default;\n$input-box-shadow: $box-shadow-inset !default;\n\n$input-border-radius: $border-radius !default;\n$input-border-radius-sm: $border-radius-sm !default;\n$input-border-radius-lg: $border-radius-lg !default;\n\n$input-focus-bg: $input-bg !default;\n$input-focus-border-color: tint-color($component-active-bg, 50%) !default;\n$input-focus-color: $input-color !default;\n$input-focus-width: $input-btn-focus-width !default;\n$input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color: $gray-600 !default;\n$input-plaintext-color: $body-color !default;\n\n$input-height-border: $input-border-width * 2 !default;\n\n$input-height-inner: add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half: add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter: add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height: add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm: add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg: add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition: border-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$form-color-width: 3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width: 1em !default;\n$form-check-min-height: $font-size-base * $line-height-base !default;\n$form-check-padding-start: $form-check-input-width+.5em !default;\n$form-check-margin-bottom: .125rem !default;\n$form-check-label-color: null !default;\n$form-check-label-cursor: null !default;\n$form-check-transition: null !default;\n\n$form-check-input-active-filter: brightness(90%) !default;\n\n$form-check-input-bg: $input-bg !default;\n$form-check-input-border: 1px solid rgba($black, .25) !default;\n$form-check-input-border-radius: .25em !default;\n$form-check-radio-border-radius: 50% !default;\n$form-check-input-focus-border: $input-focus-border-color !default;\n$form-check-input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color: $component-active-color !default;\n$form-check-input-checked-bg-color: $component-active-bg !default;\n$form-check-input-checked-border-color: $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color: $component-active-color !default;\n$form-check-input-indeterminate-bg-color: $component-active-bg !default;\n$form-check-input-indeterminate-border-color: $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity: .5 !default;\n$form-check-label-disabled-opacity: $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity: $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end: 1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color: rgba($black, .25) !default;\n$form-switch-width: 2em !default;\n$form-switch-padding-start: $form-switch-width+.5em !default;\n$form-switch-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius: $form-switch-width !default;\n$form-switch-transition: background-position .15s ease-in-out !default;\n\n$form-switch-focus-color: $input-focus-border-color !default;\n$form-switch-focus-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color: $component-active-color !default;\n$form-switch-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y: $input-padding-y !default;\n$input-group-addon-padding-x: $input-padding-x !default;\n$input-group-addon-font-weight: $input-font-weight !default;\n$input-group-addon-color: $input-color !default;\n$input-group-addon-bg: $gray-200 !default;\n$input-group-addon-border-color: $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y: $input-padding-y !default;\n$form-select-padding-x: $input-padding-x !default;\n$form-select-font-family: $input-font-family !default;\n$form-select-font-size: $input-font-size !default;\n$form-select-indicator-padding: $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight: $input-font-weight !default;\n$form-select-line-height: $input-line-height !default;\n$form-select-color: $input-color !default;\n$form-select-bg: $input-bg !default;\n$form-select-disabled-color: null !default;\n$form-select-disabled-bg: $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position: right $form-select-padding-x center !default;\n$form-select-bg-size: 16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color: $gray-800 !default;\n$form-select-indicator: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5+$form-select-indicator-padding !default;\n$form-select-feedback-icon-position: center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size: $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width: $input-border-width !default;\n$form-select-border-color: $input-border-color !default;\n$form-select-border-radius: $input-border-radius !default;\n$form-select-box-shadow: $box-shadow-inset !default;\n\n$form-select-focus-border-color: $input-focus-border-color !default;\n$form-select-focus-width: $input-focus-width !default;\n$form-select-focus-box-shadow: 0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm: $input-padding-y-sm !default;\n$form-select-padding-x-sm: $input-padding-x-sm !default;\n$form-select-font-size-sm: $input-font-size-sm !default;\n$form-select-border-radius-sm: $input-border-radius-sm !default;\n\n$form-select-padding-y-lg: $input-padding-y-lg !default;\n$form-select-padding-x-lg: $input-padding-x-lg !default;\n$form-select-font-size-lg: $input-font-size-lg !default;\n$form-select-border-radius-lg: $input-border-radius-lg !default;\n\n$form-select-transition: $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width: 100% !default;\n$form-range-track-height: .5rem !default;\n$form-range-track-cursor: pointer !default;\n$form-range-track-bg: $gray-300 !default;\n$form-range-track-border-radius: 1rem !default;\n$form-range-track-box-shadow: $box-shadow-inset !default;\n\n$form-range-thumb-width: 1rem !default;\n$form-range-thumb-height: $form-range-thumb-width !default;\n$form-range-thumb-bg: $component-active-bg !default;\n$form-range-thumb-border: 0 !default;\n$form-range-thumb-border-radius: 1rem !default;\n$form-range-thumb-box-shadow: 0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow: 0 0 0 1px $body-bg,\n$input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width: $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg: tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg: $gray-500 !default;\n$form-range-thumb-transition: background-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color: $input-color !default;\n$form-file-button-bg: $input-group-addon-bg !default;\n$form-file-button-hover-bg: shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height: add(3.5rem, $input-height-border) !default;\n$form-floating-line-height: 1.25 !default;\n$form-floating-padding-x: $input-padding-x !default;\n$form-floating-padding-y: 1rem !default;\n$form-floating-input-padding-t: 1.625rem !default;\n$form-floating-input-padding-b: .625rem !default;\n$form-floating-label-opacity: .65 !default;\n$form-floating-label-transform: scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition: opacity .1s ease-in-out,\ntransform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top: $form-text-margin-top !default;\n$form-feedback-font-size: $form-text-font-size !default;\n$form-feedback-font-style: $form-text-font-style !default;\n$form-feedback-valid-color: $success !default;\n$form-feedback-invalid-color: $danger !default;\n\n$form-feedback-icon-valid-color: $form-feedback-valid-color !default;\n$form-feedback-icon-valid: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color: $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\"valid\": (\"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid),\n  \"invalid\": (\"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid)) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown: 1000 !default;\n$zindex-sticky: 1020 !default;\n$zindex-fixed: 1030 !default;\n$zindex-offcanvas-backdrop: 1040 !default;\n$zindex-offcanvas: 1045 !default;\n$zindex-modal-backdrop: 1050 !default;\n$zindex-modal: 1055 !default;\n$zindex-popover: 1070 !default;\n$zindex-tooltip: 1080 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y: .5rem !default;\n$nav-link-padding-x: 1rem !default;\n$nav-link-font-size: null !default;\n$nav-link-font-weight: null !default;\n$nav-link-color: $link-color !default;\n$nav-link-hover-color: $link-hover-color !default;\n$nav-link-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out !default;\n$nav-link-disabled-color: $gray-600 !default;\n\n$nav-tabs-border-color: $gray-300 !default;\n$nav-tabs-border-width: $border-width !default;\n$nav-tabs-border-radius: $border-radius !default;\n$nav-tabs-link-hover-border-color: $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color: $gray-700 !default;\n$nav-tabs-link-active-bg: $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius: $border-radius !default;\n$nav-pills-link-active-color: $component-active-color !default;\n$nav-pills-link-active-bg: $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y: $spacer * .5 !default;\n$navbar-padding-x: null !default;\n\n$navbar-nav-link-padding-x: .5rem !default;\n\n$navbar-brand-font-size: $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height: $font-size-base * $line-height-base+$nav-link-padding-y * 2 !default;\n$navbar-brand-height: $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y: ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end: 1rem !default;\n\n$navbar-toggler-padding-y: .25rem !default;\n$navbar-toggler-padding-x: .75rem !default;\n$navbar-toggler-font-size: $font-size-lg !default;\n$navbar-toggler-border-radius: $btn-border-radius !default;\n$navbar-toggler-focus-width: $btn-focus-width !default;\n$navbar-toggler-transition: box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color: rgba($white, .55) !default;\n$navbar-dark-hover-color: rgba($white, .75) !default;\n$navbar-dark-active-color: $white !default;\n$navbar-dark-disabled-color: rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color: rgba($white, .1) !default;\n\n$navbar-light-color: rgba($black, .55) !default;\n$navbar-light-hover-color: rgba($black, .7) !default;\n$navbar-light-active-color: rgba($black, .9) !default;\n$navbar-light-disabled-color: rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color: $navbar-light-active-color !default;\n$navbar-light-brand-hover-color: $navbar-light-active-color !default;\n$navbar-dark-brand-color: $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color: $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width: 10rem !default;\n$dropdown-padding-x: 0 !default;\n$dropdown-padding-y: .5rem !default;\n$dropdown-spacer: .125rem !default;\n$dropdown-font-size: $font-size-base !default;\n$dropdown-color: $body-color !default;\n$dropdown-bg: $white !default;\n$dropdown-border-color: rgba($black, .15) !default;\n$dropdown-border-radius: $border-radius !default;\n$dropdown-border-width: $border-width !default;\n$dropdown-inner-border-radius: subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg: $dropdown-border-color !default;\n$dropdown-divider-margin-y: $spacer * .5 !default;\n$dropdown-box-shadow: $box-shadow !default;\n\n$dropdown-link-color: $gray-900 !default;\n$dropdown-link-hover-color: shade-color($dropdown-link-color, 10%) !default;\n$dropdown-link-hover-bg: $gray-200 !default;\n\n$dropdown-link-active-color: $component-active-color !default;\n$dropdown-link-active-bg: $component-active-bg !default;\n\n$dropdown-link-disabled-color: $gray-500 !default;\n\n$dropdown-item-padding-y: $spacer * .25 !default;\n$dropdown-item-padding-x: $spacer !default;\n\n$dropdown-header-color: $gray-600 !default;\n$dropdown-header-padding: $dropdown-padding-y $dropdown-item-padding-x !default;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color: $gray-300 !default;\n$dropdown-dark-bg: $gray-800 !default;\n$dropdown-dark-border-color: $dropdown-border-color !default;\n$dropdown-dark-divider-bg: $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow: null !default;\n$dropdown-dark-link-color: $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color: $white !default;\n$dropdown-dark-link-hover-bg: rgba($white, .15) !default;\n$dropdown-dark-link-active-color: $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg: $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color: $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y: .375rem !default;\n$pagination-padding-x: .75rem !default;\n$pagination-padding-y-sm: .25rem !default;\n$pagination-padding-x-sm: .5rem !default;\n$pagination-padding-y-lg: .75rem !default;\n$pagination-padding-x-lg: 1.5rem !default;\n\n$pagination-color: $link-color !default;\n$pagination-bg: $white !default;\n$pagination-border-width: $border-width !default;\n$pagination-border-radius: $border-radius !default;\n$pagination-margin-start: -$pagination-border-width !default;\n$pagination-border-color: $gray-300 !default;\n\n$pagination-focus-color: $link-hover-color !default;\n$pagination-focus-bg: $gray-200 !default;\n$pagination-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$pagination-focus-outline: 0 !default;\n\n$pagination-hover-color: $link-hover-color !default;\n$pagination-hover-bg: $gray-200 !default;\n$pagination-hover-border-color: $gray-300 !default;\n\n$pagination-active-color: $component-active-color !default;\n$pagination-active-bg: $component-active-bg !default;\n$pagination-active-border-color: $pagination-active-bg !default;\n\n$pagination-disabled-color: $gray-600 !default;\n$pagination-disabled-bg: $white !default;\n$pagination-disabled-border-color: $gray-300 !default;\n\n$pagination-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm: $border-radius-sm !default;\n$pagination-border-radius-lg: $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max: .5 !default;\n$placeholder-opacity-min: .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y: $spacer !default;\n$card-spacer-x: $spacer !default;\n$card-title-spacer-y: $spacer * .5 !default;\n$card-border-width: $border-width !default;\n$card-border-color: rgba($black, .125) !default;\n$card-border-radius: $border-radius !default;\n$card-box-shadow: null !default;\n$card-inner-border-radius: subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y: $card-spacer-y * .5 !default;\n$card-cap-padding-x: $card-spacer-x !default;\n$card-cap-bg: rgba($black, .03) !default;\n$card-cap-color: null !default;\n$card-height: null !default;\n$card-color: null !default;\n$card-bg: $white !default;\n$card-img-overlay-padding: $spacer !default;\n$card-group-margin: $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y: 1rem !default;\n$accordion-padding-x: 1.25rem !default;\n$accordion-color: $body-color !default;\n$accordion-bg: $body-bg !default;\n$accordion-border-width: $border-width !default;\n$accordion-border-color: rgba($black, .125) !default;\n$accordion-border-radius: $border-radius !default;\n$accordion-inner-border-radius: subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y: $accordion-padding-y !default;\n$accordion-body-padding-x: $accordion-padding-x !default;\n\n$accordion-button-padding-y: $accordion-padding-y !default;\n$accordion-button-padding-x: $accordion-padding-x !default;\n$accordion-button-color: $accordion-color !default;\n$accordion-button-bg: $accordion-bg !default;\n$accordion-transition: $btn-transition,\nborder-radius .15s ease !default;\n$accordion-button-active-bg: tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color: shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color: $input-focus-border-color !default;\n$accordion-button-focus-box-shadow: $btn-focus-box-shadow !default;\n\n$accordion-icon-width: 1.25rem !default;\n$accordion-icon-color: $accordion-button-color !default;\n$accordion-icon-active-color: $accordion-button-active-color !default;\n$accordion-icon-transition: transform .2s ease-in-out !default;\n$accordion-icon-transform: rotate(-180deg) !default;\n\n$accordion-button-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size: $font-size-sm !default;\n$tooltip-max-width: 200px !default;\n$tooltip-color: $white !default;\n$tooltip-bg: $black !default;\n$tooltip-border-radius: $border-radius !default;\n$tooltip-opacity: .9 !default;\n$tooltip-padding-y: $spacer * .25 !default;\n$tooltip-padding-x: $spacer * .5 !default;\n$tooltip-margin: 0 !default;\n\n$tooltip-arrow-width: .8rem !default;\n$tooltip-arrow-height: .4rem !default;\n$tooltip-arrow-color: $tooltip-bg !default;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y: $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x: $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size: $tooltip-font-size !default;\n$form-feedback-tooltip-line-height: null !default;\n$form-feedback-tooltip-opacity: $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size: $font-size-sm !default;\n$popover-bg: $white !default;\n$popover-max-width: 276px !default;\n$popover-border-width: $border-width !default;\n$popover-border-color: rgba($black, .2) !default;\n$popover-border-radius: $border-radius-lg !default;\n$popover-inner-border-radius: subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow: $box-shadow !default;\n\n$popover-header-bg: shade-color($popover-bg, 6%) !default;\n$popover-header-color: $headings-color !default;\n$popover-header-padding-y: .5rem !default;\n$popover-header-padding-x: $spacer !default;\n\n$popover-body-color: $body-color !default;\n$popover-body-padding-y: $spacer !default;\n$popover-body-padding-x: $spacer !default;\n\n$popover-arrow-width: 1rem !default;\n$popover-arrow-height: .5rem !default;\n$popover-arrow-color: $popover-bg !default;\n\n$popover-arrow-outer-color: fade-in($popover-border-color, .05) !default;\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width: 350px !default;\n$toast-padding-x: .75rem !default;\n$toast-padding-y: .5rem !default;\n$toast-font-size: .875rem !default;\n$toast-color: null !default;\n$toast-background-color: rgba($white, .85) !default;\n$toast-border-width: 1px !default;\n$toast-border-color: rgba($black, .1) !default;\n$toast-border-radius: $border-radius !default;\n$toast-box-shadow: $box-shadow !default;\n$toast-spacing: $container-padding-x !default;\n\n$toast-header-color: $gray-600 !default;\n$toast-header-background-color: rgba($white, .85) !default;\n$toast-header-border-color: rgba($black, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size: .75em !default;\n$badge-font-weight: $font-weight-bold !default;\n$badge-color: $white !default;\n$badge-padding-y: .35em !default;\n$badge-padding-x: .65em !default;\n$badge-border-radius: $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding: $spacer !default;\n\n$modal-footer-margin-between: .5rem !default;\n\n$modal-dialog-margin: .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height: $line-height-base !default;\n\n$modal-content-color: null !default;\n$modal-content-bg: $white !default;\n$modal-content-border-color: rgba($black, .2) !default;\n$modal-content-border-width: $border-width !default;\n$modal-content-border-radius: $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs: $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up: $box-shadow !default;\n\n$modal-backdrop-bg: $black !default;\n$modal-backdrop-opacity: .5 !default;\n$modal-header-border-color: $border-color !default;\n$modal-footer-border-color: $modal-header-border-color !default;\n$modal-header-border-width: $modal-content-border-width !default;\n$modal-footer-border-width: $modal-header-border-width !default;\n$modal-header-padding-y: $modal-inner-padding !default;\n$modal-header-padding-x: $modal-inner-padding !default;\n$modal-header-padding: $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm: 300px !default;\n$modal-md: 500px !default;\n$modal-lg: 800px !default;\n$modal-xl: 1140px !default;\n\n$modal-fade-transform: translate(0, -50px) !default;\n$modal-show-transform: none !default;\n$modal-transition: transform .3s ease-out !default;\n$modal-scale-transform: scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y: $spacer !default;\n$alert-padding-x: $spacer !default;\n$alert-margin-bottom: 1rem !default;\n$alert-border-radius: $border-radius !default;\n$alert-link-font-weight: $font-weight-bold !default;\n$alert-border-width: $border-width !default;\n$alert-bg-scale: -80% !default;\n$alert-border-scale: -70% !default;\n$alert-color-scale: 40% !default;\n$alert-dismissible-padding-r: $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height: 1rem !default;\n$progress-font-size: $font-size-base * .75 !default;\n$progress-bg: $gray-200 !default;\n$progress-border-radius: $border-radius !default;\n$progress-box-shadow: $box-shadow-inset !default;\n$progress-bar-color: $white !default;\n$progress-bar-bg: $primary !default;\n$progress-bar-animation-timing: 1s linear infinite !default;\n$progress-bar-transition: width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color: $gray-900 !default;\n$list-group-bg: $white !default;\n$list-group-border-color: rgba($black, .125) !default;\n$list-group-border-width: $border-width !default;\n$list-group-border-radius: $border-radius !default;\n\n$list-group-item-padding-y: $spacer * .5 !default;\n$list-group-item-padding-x: $spacer !default;\n$list-group-item-bg-scale: -80% !default;\n$list-group-item-color-scale: 40% !default;\n\n$list-group-hover-bg: $gray-100 !default;\n$list-group-active-color: $component-active-color !default;\n$list-group-active-bg: $component-active-bg !default;\n$list-group-active-border-color: $list-group-active-bg !default;\n\n$list-group-disabled-color: $gray-600 !default;\n$list-group-disabled-bg: $list-group-bg !default;\n\n$list-group-action-color: $gray-700 !default;\n$list-group-action-hover-color: $list-group-action-color !default;\n\n$list-group-action-active-color: $body-color !default;\n$list-group-action-active-bg: $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding: .25rem !default;\n$thumbnail-bg: $body-bg !default;\n$thumbnail-border-width: $border-width !default;\n$thumbnail-border-color: $gray-300 !default;\n$thumbnail-border-radius: $border-radius !default;\n$thumbnail-box-shadow: $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size: $small-font-size !default;\n$figure-caption-color: $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size: null !default;\n$breadcrumb-padding-y: 0 !default;\n$breadcrumb-padding-x: 0 !default;\n$breadcrumb-item-padding-x: .5rem !default;\n$breadcrumb-margin-bottom: 1rem !default;\n$breadcrumb-bg: null !default;\n$breadcrumb-divider-color: $gray-600 !default;\n$breadcrumb-active-color: $gray-600 !default;\n$breadcrumb-divider: quote(\"/\") !default;\n$breadcrumb-divider-flipped: $breadcrumb-divider !default;\n$breadcrumb-border-radius: null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color: $white !default;\n$carousel-control-width: 15% !default;\n$carousel-control-opacity: .5 !default;\n$carousel-control-hover-opacity: .9 !default;\n$carousel-control-transition: opacity .15s ease !default;\n\n$carousel-indicator-width: 30px !default;\n$carousel-indicator-height: 3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer: 3px !default;\n$carousel-indicator-opacity: .5 !default;\n$carousel-indicator-active-bg: $white !default;\n$carousel-indicator-active-opacity: 1 !default;\n$carousel-indicator-transition: opacity .6s ease !default;\n\n$carousel-caption-width: 70% !default;\n$carousel-caption-color: $white !default;\n$carousel-caption-padding-y: 1.25rem !default;\n$carousel-caption-spacer: 1.25rem !default;\n\n$carousel-control-icon-width: 2rem !default;\n\n$carousel-control-prev-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration: .6s !default;\n$carousel-transition: transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg: $black !default;\n$carousel-dark-caption-color: $black !default;\n$carousel-dark-control-icon-filter: invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width: 2rem !default;\n$spinner-height: $spinner-width !default;\n$spinner-vertical-align: -.125em !default;\n$spinner-border-width: .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm: 1rem !default;\n$spinner-height-sm: $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width: 1em !default;\n$btn-close-height: $btn-close-width !default;\n$btn-close-padding-x: .25em !default;\n$btn-close-padding-y: $btn-close-padding-x !default;\n$btn-close-color: $black !default;\n$btn-close-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow: $input-btn-focus-box-shadow !default;\n$btn-close-opacity: .5 !default;\n$btn-close-hover-opacity: .75 !default;\n$btn-close-focus-opacity: 1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter: invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y: $modal-inner-padding !default;\n$offcanvas-padding-x: $modal-inner-padding !default;\n$offcanvas-horizontal-width: 400px !default;\n$offcanvas-vertical-height: 30vh !default;\n$offcanvas-transition-duration: .3s !default;\n$offcanvas-border-color: $modal-content-border-color !default;\n$offcanvas-border-width: $modal-content-border-width !default;\n$offcanvas-title-line-height: $modal-title-line-height !default;\n$offcanvas-bg-color: $modal-content-bg !default;\n$offcanvas-color: $modal-content-color !default;\n$offcanvas-box-shadow: $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg: $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity: $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size: $small-font-size !default;\n$code-color: $pink !default;\n\n$kbd-padding-y: .2rem !default;\n$kbd-padding-x: .4rem !default;\n$kbd-font-size: $code-font-size !default;\n$kbd-color: $white !default;\n$kbd-bg: $gray-900 !default;\n\n$pre-color: null !default;", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Colors\n@function to-rgb($value) {\n  @return red($value), green($value), blue($value);\n}\n\n// stylelint-disable scss/dollar-variable-pattern\n@function rgba-css-var($identifier, $target) {\n  @if $identifier == \"body\" and $target == \"bg\" {\n    @return rgba(var(--#{$variable-prefix}#{$identifier}-bg-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n  } @if $identifier == \"body\" and $target == \"text\" {\n    @return rgba(var(--#{$variable-prefix}#{$identifier}-color-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n  } @else {\n    @return rgba(var(--#{$variable-prefix}#{$identifier}-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n  }\n}\n\n@function map-loop($map, $func, $args...) {\n  $_map: ();\n\n  @each $key, $value in $map {\n    // allow to pass the $key and $value of the map as an function argument\n    $_args: ();\n    @each $arg in $args {\n      $_args: append($_args, if($arg == \"$key\", $key, if($arg == \"$value\", $value, $arg)));\n    }\n\n    $_map: map-merge($_map, ($key: call(get-function($func), $_args...)));\n  }\n\n  @return $_map;\n}\n// stylelint-enable scss/dollar-variable-pattern\n\n@function varify($list) {\n  $result: null;\n  @each $entry in $list {\n    $result: append($result, var(--#{$variable-prefix}#{$entry}), space);\n  }\n  @return $result;\n}\n\n// Internal Bootstrap function to turn maps into its negative variant.\n// It prefixes the keys with `n` and makes the value negative.\n@function negativify-map($map) {\n  $result: ();\n  @each $key, $value in $map {\n    @if $key != 0 {\n      $result: map-merge($result, (\"n\" + $key: (-$value)));\n    }\n  }\n  @return $result;\n}\n\n// Get multiple keys from a sass map\n@function map-get-multiple($map, $values) {\n  $result: ();\n  @each $key, $value in $map {\n    @if (index($values, $key) != null) {\n      $result: map-merge($result, ($key: $value));\n    }\n  }\n  @return $result;\n}\n\n// Merge multiple maps\n@function map-merge-multiple($maps...) {\n  $merged-maps: ();\n\n  @each $map in $maps {\n    $merged-maps: map-merge($merged-maps, $map);\n  }\n  @return $merged-maps;\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n//\n// Requires the use of quotes around data URIs.\n\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n// See https://github.com/twbs/bootstrap/pull/30168\n\n// A list of pre-calculated numbers of pow(divide((divide($value, 255) + .055), 1.055), 2.4). (from 0 to 255)\n// stylelint-disable-next-line scss/dollar-variable-default, scss/dollar-variable-pattern\n$_luminance-list: .0008 .001 .0011 .0013 .0015 .0017 .002 .0022 .0025 .0027 .003 .0033 .0037 .004 .0044 .0048 .0052 .0056 .006 .0065 .007 .0075 .008 .0086 .0091 .0097 .0103 .011 .0116 .0123 .013 .0137 .0144 .0152 .016 .0168 .0176 .0185 .0194 .0203 .0212 .0222 .0232 .0242 .0252 .0262 .0273 .0284 .0296 .0307 .0319 .0331 .0343 .0356 .0369 .0382 .0395 .0409 .0423 .0437 .0452 .0467 .0482 .0497 .0513 .0529 .0545 .0561 .0578 .0595 .0612 .063 .0648 .0666 .0685 .0704 .0723 .0742 .0762 .0782 .0802 .0823 .0844 .0865 .0887 .0908 .0931 .0953 .0976 .0999 .1022 .1046 .107 .1095 .1119 .1144 .117 .1195 .1221 .1248 .1274 .1301 .1329 .1356 .1384 .1413 .1441 .147 .15 .1529 .1559 .159 .162 .1651 .1683 .1714 .1746 .1779 .1812 .1845 .1878 .1912 .1946 .1981 .2016 .2051 .2086 .2122 .2159 .2195 .2232 .227 .2307 .2346 .2384 .2423 .2462 .2502 .2542 .2582 .2623 .2664 .2705 .2747 .2789 .2831 .2874 .2918 .2961 .3005 .305 .3095 .314 .3185 .3231 .3278 .3325 .3372 .3419 .3467 .3515 .3564 .3613 .3663 .3712 .3763 .3813 .3864 .3916 .3968 .402 .4072 .4125 .4179 .4233 .4287 .4342 .4397 .4452 .4508 .4564 .4621 .4678 .4735 .4793 .4851 .491 .4969 .5029 .5089 .5149 .521 .5271 .5333 .5395 .5457 .552 .5583 .5647 .5711 .5776 .5841 .5906 .5972 .6038 .6105 .6172 .624 .6308 .6376 .6445 .6514 .6584 .6654 .6724 .6795 .6867 .6939 .7011 .7084 .7157 .7231 .7305 .7379 .7454 .7529 .7605 .7682 .7758 .7835 .7913 .7991 .807 .8148 .8228 .8308 .8388 .8469 .855 .8632 .8714 .8796 .8879 .8963 .9047 .9131 .9216 .9301 .9387 .9473 .956 .9647 .9734 .9823 .9911 1;\n\n@function color-contrast($background, $color-contrast-dark: $color-contrast-dark, $color-contrast-light: $color-contrast-light, $min-contrast-ratio: $min-contrast-ratio) {\n  $foregrounds: $color-contrast-light, $color-contrast-dark, $white, $black;\n  $max-ratio: 0;\n  $max-ratio-color: null;\n\n  @each $color in $foregrounds {\n    $contrast-ratio: contrast-ratio($background, $color);\n    @if $contrast-ratio > $min-contrast-ratio {\n      @return $color;\n    } @else if $contrast-ratio > $max-ratio {\n      $max-ratio: $contrast-ratio;\n      $max-ratio-color: $color;\n    }\n  }\n\n  @warn \"Found no color leading to #{$min-contrast-ratio}:1 contrast ratio against #{$background}...\";\n\n  @return $max-ratio-color;\n}\n\n@function contrast-ratio($background, $foreground: $color-contrast-light) {\n  $l1: luminance($background);\n  $l2: luminance(opaque($background, $foreground));\n\n  @return if($l1 > $l2, divide($l1 + .05, $l2 + .05), divide($l2 + .05, $l1 + .05));\n}\n\n// Return WCAG2.0 relative luminance\n// See https://www.w3.org/WAI/GL/wiki/Relative_luminance\n// See https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n@function luminance($color) {\n  $rgb: (\n    \"r\": red($color),\n    \"g\": green($color),\n    \"b\": blue($color)\n  );\n\n  @each $name, $value in $rgb {\n    $value: if(divide($value, 255) < .03928, divide(divide($value, 255), 12.92), nth($_luminance-list, $value + 1));\n    $rgb: map-merge($rgb, ($name: $value));\n  }\n\n  @return (map-get($rgb, \"r\") * .2126) + (map-get($rgb, \"g\") * .7152) + (map-get($rgb, \"b\") * .0722);\n}\n\n// Return opaque color\n// opaque(#fff, rgba(0, 0, 0, .5)) => #808080\n@function opaque($background, $foreground) {\n  @return mix(rgba($foreground, 1), $background, opacity($foreground) * 100);\n}\n\n// scss-docs-start color-functions\n// Tint a color: mix a color with white\n@function tint-color($color, $weight) {\n  @return mix(white, $color, $weight);\n}\n\n// Shade a color: mix a color with black\n@function shade-color($color, $weight) {\n  @return mix(black, $color, $weight);\n}\n\n// Shade the color if the weight is positive, else tint it\n@function shift-color($color, $weight) {\n  @return if($weight > 0, shade-color($color, $weight), tint-color($color, -$weight));\n}\n// scss-docs-end color-functions\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @if type-of($value2) != number {\n    $value2: unquote(\"(\") + $value2 + unquote(\")\");\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n// scss-docs-start border-radius-mixins\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n// scss-docs-end border-radius-mixins\n", "//\n// Headings\n//\n.h1 {\n  @extend h1;\n}\n\n.h2 {\n  @extend h2;\n}\n\n.h3 {\n  @extend h3;\n}\n\n.h4 {\n  @extend h4;\n}\n\n.h5 {\n  @extend h5;\n}\n\n.h6 {\n  @extend h6;\n}\n\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n@each $display,\n$font-size in $display-font-sizes {\n  .display-#{$display} {\n    @include font-size($font-size);\n    font-weight: $display-font-weight;\n    line-height: $display-line-height;\n  }\n}\n\n//\n// Emphasis\n//\n.small {\n  @extend small;\n}\n\n.mark {\n  @extend mark;\n}\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size($initialism-font-size);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-font-size);\n\n  > :last-child {\n    margin-bottom: 0;\n  }\n}\n\n.blockquote-footer {\n  margin-top: -$blockquote-margin-y;\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-footer-font-size);\n  color: $blockquote-footer-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer * .5;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  width: 100%;\n  padding-right: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  padding-left: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  margin-right: auto;\n  margin-left: auto;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint,\n  $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name,\n      $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint==$name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}", "// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    >* {\n      @include make-col-ready();\n    }\n  }\n}\n\n@if $enable-cssgrid {\n  .grid {\n    display: grid;\n    grid-template-rows: repeat(var(--#{$variable-prefix}rows, 1), 1fr);\n    grid-template-columns: repeat(var(--#{$variable-prefix}columns, #{$grid-columns}), 1fr);\n    gap: var(--#{$variable-prefix}gap, #{$grid-gutter-width});\n\n    @include make-cssgrid();\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$variable-prefix}gutter-x: #{$gutter};\n  --#{$variable-prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\n  margin-top: calc(-1 * var(--#{$variable-prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(-.5 * var(--#{$variable-prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$variable-prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$variable-prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$variable-prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$variable-prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage(divide($size, $columns));\n\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: divide($size, $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: divide(100%, $count);\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .g-col#{$infix}-#{$i} {\n            grid-column: auto / span $i;\n          }\n        }\n\n        // Start with `1` because `0` is and invalid value.\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\n        @for $i from 1 through ($columns - 1) {\n          .g-start#{$infix}-#{$i} {\n            grid-column-start: $i;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  --#{$variable-prefix}table-bg: #{$table-bg};\n  --#{$variable-prefix}table-accent-bg: #{$table-accent-bg};\n  --#{$variable-prefix}table-striped-color: #{$table-striped-color};\n  --#{$variable-prefix}table-striped-bg: #{$table-striped-bg};\n  --#{$variable-prefix}table-active-color: #{$table-active-color};\n  --#{$variable-prefix}table-active-bg: #{$table-active-bg};\n  --#{$variable-prefix}table-hover-color: #{$table-hover-color};\n  --#{$variable-prefix}table-hover-bg: #{$table-hover-bg};\n\n  width: 100%;\n  margin-bottom: $spacer;\n  color: $table-color;\n  vertical-align: $table-cell-vertical-align;\n  border-color: $table-border-color;\n\n  // Target th & td\n  // We need the child combinator to prevent styles leaking to nested tables which doesn't have a `.table` class.\n  // We use the universal selectors here to simplify the selector (else we would need 6 different selectors).\n  // Another advantage is that this generates less code and makes the selector less specific making it easier to override.\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption)>*>* {\n    padding: $table-cell-padding-y $table-cell-padding-x;\n    background-color: var(--#{$variable-prefix}table-bg);\n    border-bottom-width: $table-border-width;\n    box-shadow: inset 0 0 0 9999px var(--#{$variable-prefix}table-accent-bg);\n  }\n\n  >tbody {\n    vertical-align: inherit;\n  }\n\n  >thead {\n    vertical-align: bottom;\n  }\n\n  // Highlight border color between thead, tbody and tfoot.\n  > :not(:first-child) {\n    border-top: (2 * $table-border-width) solid $table-group-separator-color;\n  }\n}\n\n\n//\n// Change placement of captions with a class\n//\n\n.caption-top {\n  caption-side: top;\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption)>*>* {\n    padding: $table-cell-padding-y-sm $table-cell-padding-x-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n//\n// When borders are added on all sides of the cells, the corners can render odd when\n// these borders do not have the same color or if they are semi-transparent.\n// Therefor we add top and border bottoms to the `tr`s and left and right borders\n// to the `td`s or `th`s\n\n.table-bordered {\n  > :not(caption)>* {\n    border-width: $table-border-width 0;\n\n    // stylelint-disable-next-line selector-max-universal\n    >* {\n      border-width: 0 $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption)>*>* {\n    border-bottom-width: 0;\n  }\n\n  > :not(:first-child) {\n    border-top-width: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  >tbody>tr:nth-of-type(#{$table-striped-order})>* {\n    --#{$variable-prefix}table-accent-bg: var(--#{$variable-prefix}table-striped-bg);\n    color: var(--#{$variable-prefix}table-striped-color);\n  }\n}\n\n// Active table\n//\n// The `.table-active` class can be added to highlight rows or cells\n\n.table-active {\n  --#{$variable-prefix}table-accent-bg: var(--#{$variable-prefix}table-active-bg);\n  color: var(--#{$variable-prefix}table-active-color);\n}\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  >tbody>tr:hover>* {\n    --#{$variable-prefix}table-accent-bg: var(--#{$variable-prefix}table-hover-bg);\n    color: var(--#{$variable-prefix}table-hover-color);\n  }\n}\n\n\n// Table variants\n//\n// Table variants set the table cell backgrounds, border colors\n// and the colors of the striped, hovered & active tables\n\n@each $color,\n$value in $table-variants {\n  @include table-variant($color, $value);\n}\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n  @include media-breakpoint-down($breakpoint) {\n    .table-responsive#{$infix} {\n      overflow-x: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n  }\n}", "// scss-docs-start table-variant\n@mixin table-variant($state, $background) {\n  .table-#{$state} {\n    $color: color-contrast(opaque($body-bg, $background));\n    $hover-bg: mix($color, $background, percentage($table-hover-bg-factor));\n    $striped-bg: mix($color, $background, percentage($table-striped-bg-factor));\n    $active-bg: mix($color, $background, percentage($table-active-bg-factor));\n\n    --#{$variable-prefix}table-bg: #{$background};\n    --#{$variable-prefix}table-striped-bg: #{$striped-bg};\n    --#{$variable-prefix}table-striped-color: #{color-contrast($striped-bg)};\n    --#{$variable-prefix}table-active-bg: #{$active-bg};\n    --#{$variable-prefix}table-active-color: #{color-contrast($active-bg)};\n    --#{$variable-prefix}table-hover-bg: #{$hover-bg};\n    --#{$variable-prefix}table-hover-color: #{color-contrast($hover-bg)};\n\n    color: $color;\n    border-color: mix($color, $background, percentage($table-border-factor));\n  }\n}\n// scss-docs-end table-variant\n", "//\n// Labels\n//\n\n.form-label {\n  margin-bottom: $form-label-margin-bottom;\n  @include font-size($form-label-font-size);\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  color: $form-label-color;\n}\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  line-height: $input-line-height;\n  color: $form-label-color;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n}\n", "//\n// Form text\n//\n\n.form-text {\n  margin-top: $form-text-margin-top;\n  @include font-size($form-text-font-size);\n  font-style: $form-text-font-style;\n  font-weight: $form-text-font-weight;\n  color: $form-text-color;\n}\n", "//\n// General form controls (plus a few specific high-level interventions)\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n  appearance: none; // Fix appearance for date inputs in Safari\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  &[type=\"file\"] {\n    overflow: hidden; // prevent pseudo element button overlap\n\n    &:not(:disabled):not([readonly]) {\n      cursor: pointer;\n    }\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n\n    @if $enable-shadows {\n      @include box-shadow($input-box-shadow, $input-focus-box-shadow);\n    }\n\n    @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n\n  // Add some height to date inputs on iOS\n  // https://github.com/twbs/bootstrap/issues/23307\n  // TODO: we can remove this workaround once https://bugs.webkit.org/show_bug.cgi?id=198959 is resolved\n  &::-webkit-date-and-time-value {\n    // Multiply line-height by 1em if it has no unit\n    height: if(unit($input-line-height)==\"\", $input-line-height * 1em, $input-line-height);\n  }\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled,\n  &[readonly] {\n    background-color: $input-disabled-bg;\n    border-color: $input-disabled-border-color;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n\n  // File input buttons theming\n  &::file-selector-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::file-selector-button {\n    background-color: $form-file-button-hover-bg;\n  }\n\n  &::-webkit-file-upload-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {\n    background-color: $form-file-button-hover-bg;\n  }\n}\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  min-height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n\n  &::file-selector-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n\n  &::-webkit-file-upload-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n}\n\n.form-control-lg {\n  min-height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n\n  &::file-selector-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n\n  &::-webkit-file-upload-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n}\n\n// Make sure textareas don't shrink too much when resized\n// https://github.com/twbs/bootstrap/pull/29124\n// stylelint-disable selector-no-qualifying-type\ntextarea {\n  &.form-control {\n    min-height: $input-height;\n  }\n\n  &.form-control-sm {\n    min-height: $input-height-sm;\n  }\n\n  &.form-control-lg {\n    min-height: $input-height-lg;\n  }\n}\n\n// stylelint-enable selector-no-qualifying-type\n\n.form-control-color {\n  width: $form-color-width;\n  height: auto; // Override fixed browser height\n  padding: $input-padding-y;\n\n  &:not(:disabled):not([readonly]) {\n    cursor: pointer;\n  }\n\n  &::-moz-color-swatch {\n    height: if(unit($input-line-height)==\"\", $input-line-height * 1em, $input-line-height);\n    @include border-radius($input-border-radius);\n  }\n\n  &::-webkit-color-swatch {\n    height: if(unit($input-line-height)==\"\", $input-line-height * 1em, $input-line-height);\n    @include border-radius($input-border-radius);\n  }\n}", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: all 0.3s ease-in-out;\n    }\n\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Gradients\n\n// scss-docs-start gradient-bg-mixin\n@mixin gradient-bg($color: null) {\n  background-color: $color;\n\n  @if $enable-gradients {\n    background-image: var(--#{$variable-prefix}gradient);\n  }\n}\n// scss-docs-end gradient-bg-mixin\n\n// scss-docs-start gradient-mixins\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: null, $end-percent: null) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n}\n\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n}\n\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n// scss-docs-end gradient-mixins\n", "// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n\n.form-select {\n  display: block;\n  width: 100%;\n  padding: $form-select-padding-y $form-select-indicator-padding $form-select-padding-y $form-select-padding-x;\n  // stylelint-disable-next-line property-no-vendor-prefix\n  -moz-padding-start: subtract($form-select-padding-x, 3px); // See https://github.com/twbs/bootstrap/issues/32636\n  font-family: $form-select-font-family;\n  @include font-size($form-select-font-size);\n  font-weight: $form-select-font-weight;\n  line-height: $form-select-line-height;\n  color: $form-select-color;\n  background-color: $form-select-bg;\n  background-image: escape-svg($form-select-indicator);\n  background-repeat: no-repeat;\n  background-position: $form-select-bg-position;\n  background-size: $form-select-bg-size;\n  border: $form-select-border-width solid $form-select-border-color;\n  @include border-radius($form-select-border-radius, 0);\n  @include box-shadow($form-select-box-shadow);\n  @include transition($form-select-transition);\n  appearance: none;\n\n  &:focus {\n    border-color: $form-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($form-select-box-shadow, $form-select-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $form-select-focus-box-shadow;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    padding-right: $form-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $form-select-disabled-color;\n    background-color: $form-select-disabled-bg;\n    border-color: $form-select-disabled-border-color;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $form-select-color;\n  }\n}\n\n.form-select-sm {\n  padding-top: $form-select-padding-y-sm;\n  padding-bottom: $form-select-padding-y-sm;\n  padding-left: $form-select-padding-x-sm;\n  @include font-size($form-select-font-size-sm);\n  @include border-radius($form-select-border-radius-sm);\n}\n\n.form-select-lg {\n  padding-top: $form-select-padding-y-lg;\n  padding-bottom: $form-select-padding-y-lg;\n  padding-left: $form-select-padding-x-lg;\n  @include font-size($form-select-font-size-lg);\n  @include border-radius($form-select-border-radius-lg);\n}\n", "//\n// Check/radio\n//\n\n.form-check {\n    display: block;\n    min-height: $form-check-min-height;\n    padding-left: $form-check-padding-start;\n    margin-bottom: $form-check-margin-bottom;\n\n    .form-check-input {\n        float: left;\n        margin-left: $form-check-padding-start * -1;\n    }\n}\n\n.form-check-input {\n    width: $form-check-input-width;\n    height: $form-check-input-width;\n    margin-top: ($line-height-base - $form-check-input-width) * .5; // line-height minus check height\n    vertical-align: top;\n    background-color: $form-check-input-bg;\n    background-repeat: no-repeat;\n    background-position: center;\n    background-size: contain;\n    border: $form-check-input-border;\n    appearance: none;\n    color-adjust: exact; // Keep themed appearance for print\n    @include transition($form-check-transition);\n\n    &[type=\"checkbox\"] {\n        @include border-radius($form-check-input-border-radius);\n    }\n\n    &[type=\"radio\"] {\n        // stylelint-disable-next-line property-disallowed-list\n        border-radius: $form-check-radio-border-radius;\n    }\n\n    &:active {\n        filter: $form-check-input-active-filter;\n    }\n\n    &:focus {\n        border-color: $form-check-input-focus-border;\n        outline: 0;\n        box-shadow: $form-check-input-focus-box-shadow;\n    }\n\n    &:checked {\n        background-color: $form-check-input-checked-bg-color;\n        border-color: $form-check-input-checked-border-color;\n\n        &[type=\"checkbox\"] {\n            @if $enable-gradients {\n                background-image: escape-svg($form-check-input-checked-bg-image),\n                var(--#{$variable-prefix}gradient);\n            }\n\n            @else {\n                background-image: escape-svg($form-check-input-checked-bg-image);\n            }\n        }\n\n        &[type=\"radio\"] {\n            @if $enable-gradients {\n                background-image: escape-svg($form-check-radio-checked-bg-image),\n                var(--#{$variable-prefix}gradient);\n            }\n\n            @else {\n                background-image: escape-svg($form-check-radio-checked-bg-image);\n            }\n        }\n    }\n\n    &[type=\"checkbox\"]:indeterminate {\n        background-color: $form-check-input-indeterminate-bg-color;\n        border-color: $form-check-input-indeterminate-border-color;\n\n        @if $enable-gradients {\n            background-image: escape-svg($form-check-input-indeterminate-bg-image),\n            var(--#{$variable-prefix}gradient);\n        }\n\n        @else {\n            background-image: escape-svg($form-check-input-indeterminate-bg-image);\n        }\n    }\n\n    &:disabled {\n        pointer-events: none;\n        filter: none;\n        opacity: $form-check-input-disabled-opacity;\n    }\n\n    // Use disabled attribute in addition of :disabled pseudo-class\n    // See: https://github.com/twbs/bootstrap/issues/28247\n    &[disabled],\n    &:disabled {\n        ~.form-check-label {\n            opacity: $form-check-label-disabled-opacity;\n        }\n    }\n}\n\n.form-check-label {\n    color: $form-check-label-color;\n    cursor: $form-check-label-cursor;\n}\n\n//\n// Switch\n//\n\n.form-switch {\n    padding-left: $form-switch-padding-start;\n\n    .form-check-input {\n        width: $form-switch-width;\n        margin-left: $form-switch-padding-start * -1;\n        background-image: escape-svg($form-switch-bg-image);\n        background-position: left center;\n        @include border-radius($form-switch-border-radius);\n        @include transition($form-switch-transition);\n\n        &:focus {\n            background-image: escape-svg($form-switch-focus-bg-image);\n        }\n\n        &:checked {\n            background-position: $form-switch-checked-bg-position;\n\n            @if $enable-gradients {\n                background-image: escape-svg($form-switch-checked-bg-image),\n                var(--#{$variable-prefix}gradient);\n            }\n\n            @else {\n                background-image: escape-svg($form-switch-checked-bg-image);\n            }\n        }\n    }\n}\n\n.form-check-inline {\n    display: inline-block;\n    margin-right: $form-check-inline-margin-end;\n}\n\n.btn-check {\n    position: absolute;\n    clip: rect(0, 0, 0, 0);\n    pointer-events: none;\n\n    &[disabled],\n    &:disabled {\n        +.btn {\n            pointer-events: none;\n            filter: none;\n            opacity: $form-check-btn-check-disabled-opacity;\n        }\n    }\n}", "// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.form-range {\n  width: 100%;\n  height: add($form-range-thumb-height, $form-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: 0;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb {\n      box-shadow: $form-range-thumb-focus-box-shadow;\n    }\n\n    &::-moz-range-thumb {\n      box-shadow: $form-range-thumb-focus-box-shadow;\n    }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    margin-top: ($form-range-track-height - $form-range-thumb-height) * .5; // Webkit specific\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent; // Why?\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent;\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent;\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &:disabled {\n    pointer-events: none;\n\n    &::-webkit-slider-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n  }\n}", ".form-floating {\n    position: relative;\n\n    >.form-control,\n    >.form-select {\n        height: $form-floating-height;\n        line-height: $form-floating-line-height;\n    }\n\n    >label {\n        position: absolute;\n        top: 0;\n        left: 0;\n        height: 100%; // allow textareas\n        padding: $form-floating-padding-y $form-floating-padding-x;\n        pointer-events: none;\n        border: $input-border-width solid transparent; // Required for aligning label's text with the input as it affects inner box model\n        transform-origin: 0 0;\n        @include transition($form-floating-transition);\n    }\n\n    // stylelint-disable no-duplicate-selectors\n    >.form-control {\n        padding: $form-floating-padding-y $form-floating-padding-x;\n\n        &::placeholder {\n            color: transparent;\n        }\n\n        &:focus,\n        &:not(:placeholder-shown) {\n            padding-top: $form-floating-input-padding-t;\n            padding-bottom: $form-floating-input-padding-b;\n        }\n\n        // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n        &:-webkit-autofill {\n            padding-top: $form-floating-input-padding-t;\n            padding-bottom: $form-floating-input-padding-b;\n        }\n    }\n\n    >.form-select {\n        padding-top: $form-floating-input-padding-t;\n        padding-bottom: $form-floating-input-padding-b;\n    }\n\n    >.form-control:focus,\n    >.form-control:not(:placeholder-shown),\n    >.form-select {\n        ~label {\n            opacity: $form-floating-label-opacity;\n            transform: $form-floating-label-transform;\n        }\n    }\n\n    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n    >.form-control:-webkit-autofill {\n        ~label {\n            opacity: $form-floating-label-opacity;\n            transform: $form-floating-label-transform;\n        }\n    }\n\n    // stylelint-enable no-duplicate-selectors\n}", "//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-select {\n    position: relative; // For focus state's z-index\n    flex: 1 1 auto;\n    width: 1%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .form-select:focus {\n    z-index: 3;\n  }\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n}\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-group-addon-padding-y $input-group-addon-padding-x;\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $input-group-addon-font-weight;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .form-select,\n.input-group-lg > .input-group-text,\n.input-group-lg > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .form-select,\n.input-group-sm > .input-group-text,\n.input-group-sm > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .form-select,\n.input-group-sm > .form-select {\n  padding-right: $form-select-padding-x + $form-select-indicator-padding;\n}\n\n\n// Rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.input-group {\n  &:not(.has-validation) {\n    > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),\n    > .dropdown-toggle:nth-last-child(n + 3) {\n      @include border-end-radius(0);\n    }\n  }\n\n  &.has-validation {\n    > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu),\n    > .dropdown-toggle:nth-last-child(n + 4) {\n      @include border-end-radius(0);\n    }\n  }\n\n  $validation-messages: \"\";\n  @each $state in map-keys($form-validation-states) {\n    $validation-messages: $validation-messages + \":not(.\" + unquote($state) + \"-tooltip)\" + \":not(.\" + unquote($state) + \"-feedback)\";\n  }\n\n  > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {\n    margin-left: -$input-border-width;\n    @include border-start-radius(0);\n  }\n}\n", "// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-152293725 for more details\n\n// scss-docs-start form-validation-mixins\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state(\n  $state,\n  $color,\n  $icon,\n  $tooltip-color: color-contrast($color),\n  $tooltip-bg-color: rgba($color, $form-feedback-tooltip-opacity),\n  $focus-box-shadow: 0 0 $input-btn-focus-blur $input-focus-width rgba($color, $input-btn-focus-color-opacity)\n) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    font-style: $form-feedback-font-style;\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: $tooltip-color;\n    background-color: $tooltip-bg-color;\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .form-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        &:not([multiple]):not([size]),\n        &:not([multiple])[size=\"1\"] {\n          padding-right: $form-select-feedback-icon-padding-end;\n          background-image: escape-svg($form-select-indicator), escape-svg($icon);\n          background-position: $form-select-bg-position, $form-select-feedback-icon-position;\n          background-size: $form-select-bg-size, $form-select-feedback-icon-size;\n        }\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      &:checked {\n        background-color: $color;\n      }\n\n      &:focus {\n        box-shadow: $focus-box-shadow;\n      }\n\n      ~ .form-check-label {\n        color: $color;\n      }\n    }\n  }\n  .form-check-inline .form-check-input {\n    ~ .#{$state}-feedback {\n      margin-left: .5em;\n    }\n  }\n\n  .input-group .form-control,\n  .input-group .form-select {\n    @include form-validation-state-selector($state) {\n      @if $state == \"valid\" {\n        z-index: 1;\n      } @else if $state == \"invalid\" {\n        z-index: 2;\n      }\n      &:focus {\n        z-index: 3;\n      }\n    }\n  }\n}\n// scss-docs-end form-validation-mixins\n", "//\n// Base styles\n//\n\n.btn {\n  display: inline-block;\n  font-family: $btn-font-family;\n  font-weight: $btn-font-weight;\n  line-height: $btn-line-height;\n  color: $body-color;\n  text-align: center;\n  text-decoration: if($link-decoration==none, null, none);\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-button-pointers, pointer, null);\n  user-select: none;\n  background-color: transparent;\n  border: $btn-border-width solid transparent;\n  @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-border-radius);\n  @include transition($btn-transition);\n\n  &:hover {\n    color: $body-color;\n    text-decoration: if($link-hover-decoration==underline, none, null);\n  }\n\n  .btn-check:focus+&,\n  &:focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n\n  .btn-check:checked+&,\n  .btn-check:active+&,\n  &:active,\n  &.active {\n    @include box-shadow($btn-active-box-shadow);\n\n    &:focus {\n      @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\n    }\n  }\n\n  &:disabled,\n  &.disabled,\n  fieldset:disabled & {\n    pointer-events: none;\n    opacity: $btn-disabled-opacity;\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Alternate buttons\n//\n\n// scss-docs-start btn-variant-loops\n@each $color,\n$value in $theme-colors {\n  .btn-#{$color} {\n    @include button-variant($value, $value);\n  }\n}\n\n@each $color,\n$value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n\n// scss-docs-end btn-variant-loops\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  font-weight: $font-weight-normal;\n  color: $btn-link-color;\n  text-decoration: $link-decoration;\n\n  &:hover {\n    color: $btn-link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus {\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $btn-link-disabled-color;\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-border-radius-sm);\n}", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n// scss-docs-start btn-variant-mixin\n@mixin button-variant(\n  $background,\n  $border,\n  $color: color-contrast($background),\n  $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount)),\n  $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount)),\n  $hover-color: color-contrast($hover-background),\n  $active-background: if($color == $color-contrast-light, shade-color($background, $btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount)),\n  $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount)),\n  $active-color: color-contrast($active-background),\n  $disabled-background: $background,\n  $disabled-border: $border,\n  $disabled-color: color-contrast($disabled-background)\n) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  &:hover {\n    color: $hover-color;\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  .btn-check:focus + &,\n  &:focus {\n    color: $hover-color;\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    @if $enable-shadows {\n      @include box-shadow($btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5));\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5);\n    }\n  }\n\n  .btn-check:checked + &,\n  .btn-check:active + &,\n  &:active,\n  &.active,\n  .show > &.dropdown-toggle {\n    color: $active-color;\n    background-color: $active-background;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix($color, $border, 15%), .5);\n      }\n    }\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $disabled-color;\n    background-color: $disabled-background;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: $disabled-border;\n  }\n}\n// scss-docs-end btn-variant-mixin\n\n// scss-docs-start btn-outline-variant-mixin\n@mixin button-outline-variant(\n  $color,\n  $color-hover: color-contrast($color),\n  $active-background: $color,\n  $active-border: $color,\n  $active-color: color-contrast($active-background)\n) {\n  color: $color;\n  border-color: $color;\n\n  &:hover {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  .btn-check:focus + &,\n  &:focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  .btn-check:checked + &,\n  .btn-check:active + &,\n  &:active,\n  &.active,\n  &.dropdown-toggle.show {\n    color: $active-color;\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      @if $enable-shadows {\n        @include box-shadow($btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5));\n      } @else {\n        // Avoid using mixin so we can pass custom focus shadow properly\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $color;\n    background-color: transparent;\n  }\n}\n// scss-docs-end btn-outline-variant-mixin\n\n// scss-docs-start btn-size-mixin\n@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n// scss-docs-end btn-size-mixin\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n// scss-docs-start collapse-classes\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n\n  &.collapse-horizontal {\n    width: 0;\n    height: auto;\n    @include transition($transition-collapse-width);\n  }\n}\n\n// scss-docs-end collapse-classes", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropend,\n.dropdown,\n.dropstart {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  position: absolute;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  min-width: $dropdown-min-width;\n  padding: $dropdown-padding-y $dropdown-padding-x;\n  margin: 0; // Override default margin of ul\n  @include font-size($dropdown-font-size);\n  color: $dropdown-color;\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: $dropdown-bg;\n  background-clip: padding-box;\n  border: $dropdown-border-width solid $dropdown-border-color;\n  @include border-radius($dropdown-border-radius);\n  @include box-shadow($dropdown-box-shadow);\n\n  &[data-bs-popper] {\n    top: 100%;\n    left: 0;\n    margin-top: $dropdown-spacer;\n  }\n}\n\n// scss-docs-start responsive-breakpoints\n// We deliberately hardcode the `bs-` prefix because we check\n// this custom property in JS to determine <PERSON><PERSON>'s positioning\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-start {\n      --bs-position: start;\n\n      &[data-bs-popper] {\n        right: auto;\n        left: 0;\n      }\n    }\n\n    .dropdown-menu#{$infix}-end {\n      --bs-position: end;\n\n      &[data-bs-popper] {\n        right: 0;\n        left: auto;\n      }\n    }\n  }\n}\n\n// scss-docs-end responsive-breakpoints\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu[data-bs-popper] {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropend {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(end);\n\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropstart {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(start);\n\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  height: 0;\n  margin: $dropdown-divider-margin-y 0;\n  overflow: hidden;\n  border-top: 1px solid $dropdown-divider-bg;\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: $dropdown-link-color;\n  text-align: inherit; // For `<button>`s\n  text-decoration: if($link-decoration==none, null, none);\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n\n  // Prevent dropdown overflow if there's no padding\n  // See https://github.com/twbs/bootstrap/pull/27703\n  @if $dropdown-padding-y==0 {\n    &:first-child {\n      @include border-top-radius($dropdown-inner-border-radius);\n    }\n\n    &:last-child {\n      @include border-bottom-radius($dropdown-inner-border-radius);\n    }\n  }\n\n  &:hover,\n  &:focus {\n    color: $dropdown-link-hover-color;\n    text-decoration: if($link-hover-decoration==underline, none, null);\n    @include gradient-bg($dropdown-link-hover-bg);\n  }\n\n  &.active,\n  &:active {\n    color: $dropdown-link-active-color;\n    text-decoration: none;\n    @include gradient-bg($dropdown-link-active-bg);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $dropdown-link-disabled-color;\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: $dropdown-header-padding;\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: $dropdown-header-color;\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  color: $dropdown-link-color;\n}\n\n// Dark dropdowns\n.dropdown-menu-dark {\n  color: $dropdown-dark-color;\n  background-color: $dropdown-dark-bg;\n  border-color: $dropdown-dark-border-color;\n  @include box-shadow($dropdown-dark-box-shadow);\n\n  .dropdown-item {\n    color: $dropdown-dark-link-color;\n\n    &:hover,\n    &:focus {\n      color: $dropdown-dark-link-hover-color;\n      @include gradient-bg($dropdown-dark-link-hover-bg);\n    }\n\n    &.active,\n    &:active {\n      color: $dropdown-dark-link-active-color;\n      @include gradient-bg($dropdown-dark-link-active-bg);\n    }\n\n    &.disabled,\n    &:disabled {\n      color: $dropdown-dark-link-disabled-color;\n    }\n  }\n\n  .dropdown-divider {\n    border-color: $dropdown-dark-divider-bg;\n  }\n\n  .dropdown-item-text {\n    color: $dropdown-dark-link-color;\n  }\n\n  .dropdown-header {\n    color: $dropdown-dark-header-color;\n  }\n}", "// scss-docs-start caret-mixins\n@mixin caret-down {\n  border-top: $caret-width solid;\n  border-right: $caret-width solid transparent;\n  border-bottom: 0;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-up {\n  border-top: 0;\n  border-right: $caret-width solid transparent;\n  border-bottom: $caret-width solid;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-end {\n  border-top: $caret-width solid transparent;\n  border-right: 0;\n  border-bottom: $caret-width solid transparent;\n  border-left: $caret-width solid;\n}\n\n@mixin caret-start {\n  border-top: $caret-width solid transparent;\n  border-right: $caret-width solid;\n  border-bottom: $caret-width solid transparent;\n}\n\n@mixin caret($direction: down) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $caret-spacing;\n      vertical-align: $caret-vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down();\n      } @else if $direction == up {\n        @include caret-up();\n      } @else if $direction == end {\n        @include caret-end();\n      }\n    }\n\n    @if $direction == start {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $caret-spacing;\n        vertical-align: $caret-vertical-align;\n        content: \"\";\n        @include caret-start();\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n// scss-docs-end caret-mixins\n", "// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  >.btn {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  // Bring the hover, focused, and \"active\" buttons to the front to overlay\n  // the borders properly\n  >.btn-check:checked+.btn,\n  >.btn-check:focus+.btn,\n  >.btn:hover,\n  >.btn:focus,\n  >.btn:active,\n  >.btn.active {\n    z-index: 1;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n\n  // Prevent double borders when buttons are next to each other\n  >.btn:not(:first-child),\n  >.btn-group:not(:first-child) {\n    margin-left: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  >.btn:not(:last-child):not(.dropdown-toggle),\n  >.btn-group:not(:last-child)>.btn {\n    @include border-end-radius(0);\n  }\n\n  // The left radius should be 0 if the button is:\n  // - the \"third or more\" child\n  // - the second child and the previous element isn't `.btn-check` (making it the first child visually)\n  // - part of a btn-group which isn't the first child\n  >.btn:nth-child(n + 3),\n  > :not(.btn-check)+.btn,\n  >.btn-group:not(:first-child)>.btn {\n    @include border-start-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm>.btn {\n  @extend .btn-sm;\n}\n\n.btn-group-lg>.btn {\n  @extend .btn-lg;\n}\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropend &::after {\n    margin-left: 0;\n  }\n\n  .dropstart &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm+.dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg+.dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  >.btn,\n  >.btn-group {\n    width: 100%;\n  }\n\n  >.btn:not(:first-child),\n  >.btn-group:not(:first-child) {\n    margin-top: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  >.btn:not(:last-child):not(.dropdown-toggle),\n  >.btn-group:not(:last-child)>.btn {\n    @include border-bottom-radius(0);\n  }\n\n  >.btn~.btn,\n  >.btn-group:not(:first-child)>.btn {\n    @include border-top-radius(0);\n  }\n}", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: $nav-link-padding-y $nav-link-padding-x;\n  @include font-size($nav-link-font-size);\n  font-weight: $nav-link-font-weight;\n  color: $nav-link-color;\n  text-decoration: if($link-decoration==none, null, none);\n  @include transition($nav-link-transition);\n\n  &:hover,\n  &:focus {\n    color: $nav-link-hover-color;\n    text-decoration: if($link-hover-decoration==underline, none, null);\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: $nav-link-disabled-color;\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\n\n  .nav-link {\n    margin-bottom: -$nav-tabs-border-width;\n    background: none;\n    border: $nav-tabs-border-width solid transparent;\n    @include border-top-radius($nav-tabs-border-radius);\n\n    &:hover,\n    &:focus {\n      border-color: $nav-tabs-link-hover-border-color;\n      // Prevents active .nav-link tab overlapping focus outline of previous/next .nav-link\n      isolation: isolate;\n    }\n\n    &.disabled {\n      color: $nav-link-disabled-color;\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: $nav-tabs-link-active-color;\n    background-color: $nav-tabs-link-active-bg;\n    border-color: $nav-tabs-link-active-border-color;\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: -$nav-tabs-border-width;\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  .nav-link {\n    background: none;\n    border: 0;\n    @include border-radius($nav-pills-border-radius);\n  }\n\n  .nav-link.active,\n  .show>.nav-link {\n    color: $nav-pills-link-active-color;\n    @include gradient-bg($nav-pills-link-active-bg);\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n\n  >.nav-link,\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n\n  >.nav-link,\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n.nav-fill,\n.nav-justified {\n  .nav-item .nav-link {\n    width: 100%; // Make sure button will grow\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  >.tab-pane {\n    display: none;\n  }\n\n  >.active {\n    display: block;\n  }\n}", "// Contents\n//\n// Navbar\n// Navbar brand\n// Navbar nav\n// Navbar text\n// Responsive navbar\n// Navbar position\n// Navbar themes\n\n\n// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding-top: $navbar-padding-y;\n  padding-right: $navbar-padding-x; // default: null\n  padding-bottom: $navbar-padding-y;\n  padding-left: $navbar-padding-x; // default: null\n  @include gradient-bg();\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  // The `flex-wrap` property is inherited to simplify the expanded navbars\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: inherit;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  >.container,\n  >.container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint,\n  $container-max-width in $container-max-widths {\n    >.container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  padding-top: $navbar-brand-padding-y;\n  padding-bottom: $navbar-brand-padding-y;\n  margin-right: $navbar-brand-margin-end;\n  @include font-size($navbar-brand-font-size);\n  text-decoration: if($link-decoration==none, null, none);\n  white-space: nowrap;\n\n  &:hover,\n  &:focus {\n    text-decoration: if($link-hover-decoration==underline, none, null);\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n  }\n\n  .dropdown-menu {\n    position: static;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: $navbar-toggler-padding-y $navbar-toggler-padding-x;\n  @include font-size($navbar-toggler-font-size);\n  line-height: 1;\n  background-color: transparent; // remove default button style\n  border: $border-width solid transparent; // remove default button style\n  @include border-radius($navbar-toggler-border-radius);\n  @include transition($navbar-toggler-transition);\n\n  &:hover {\n    text-decoration: none;\n  }\n\n  &:focus {\n    text-decoration: none;\n    outline: 0;\n    box-shadow: 0 0 0 $navbar-toggler-focus-width;\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 100%;\n}\n\n.navbar-nav-scroll {\n  max-height: var(--#{$variable-prefix}scroll-height, 75vh);\n  overflow-y: auto;\n}\n\n// scss-docs-start navbar-expand-loop\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    // stylelint-disable-next-line scss/selector-no-union-class-name\n    &#{$infix} {\n      @include media-breakpoint-up($next) {\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: $navbar-nav-link-padding-x;\n            padding-left: $navbar-nav-link-padding-x;\n          }\n        }\n\n        .navbar-nav-scroll {\n          overflow: visible;\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n\n        .offcanvas-header {\n          display: none;\n        }\n\n        .offcanvas {\n          position: inherit;\n          bottom: 0;\n          z-index: 1000;\n          flex-grow: 1;\n          visibility: visible !important; // stylelint-disable-line declaration-no-important\n          background-color: transparent;\n          border-right: 0;\n          border-left: 0;\n          @include transition(none);\n          transform: none;\n        }\n\n        .offcanvas-top,\n        .offcanvas-bottom {\n          height: auto;\n          border-top: 0;\n          border-bottom: 0;\n        }\n\n        .offcanvas-body {\n          display: flex;\n          flex-grow: 0;\n          padding: 0;\n          overflow-y: visible;\n        }\n      }\n    }\n  }\n}\n\n// scss-docs-end navbar-expand-loop\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n// Dark links against a light background\n.navbar-light {\n  .navbar-brand {\n    color: $navbar-light-brand-color;\n\n    &:hover,\n    &:focus {\n      color: $navbar-light-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-light-color;\n\n      &:hover,\n      &:focus {\n        color: $navbar-light-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-light-disabled-color;\n      }\n    }\n\n    .show>.nav-link,\n    .nav-link.active {\n      color: $navbar-light-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-light-color;\n    border-color: $navbar-light-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-light-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-light-color;\n\n    a,\n    a:hover,\n    a:focus {\n      color: $navbar-light-active-color;\n    }\n  }\n}\n\n// White links against a dark background\n.navbar-dark {\n  .navbar-brand {\n    color: $navbar-dark-brand-color;\n\n    &:hover,\n    &:focus {\n      color: $navbar-dark-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-dark-color;\n\n      &:hover,\n      &:focus {\n        color: $navbar-dark-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-dark-disabled-color;\n      }\n    }\n\n    .show>.nav-link,\n    .nav-link.active {\n      color: $navbar-dark-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-dark-color;\n    border-color: $navbar-dark-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-dark-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-dark-color;\n\n    a,\n    a:hover,\n    a:focus {\n      color: $navbar-dark-active-color;\n    }\n  }\n}", "//\n// Base styles\n//\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: $card-height;\n  word-wrap: break-word;\n  background-color: $card-bg;\n  background-clip: border-box;\n  border: $card-border-width solid $card-border-color;\n  @include border-radius($card-border-radius);\n  @include box-shadow($card-box-shadow);\n\n  >hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  >.list-group {\n    border-top: inherit;\n    border-bottom: inherit;\n\n    &:first-child {\n      border-top-width: 0;\n      @include border-top-radius($card-inner-border-radius);\n    }\n\n    &:last-child {\n      border-bottom-width: 0;\n      @include border-bottom-radius($card-inner-border-radius);\n    }\n  }\n\n  // Due to specificity of the above selector (`.card > .list-group`), we must\n  // use a child selector here to prevent double borders.\n  >.card-header+.list-group,\n  >.list-group+.card-footer {\n    border-top: 0;\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  padding: $card-spacer-y $card-spacer-x;\n  color: $card-color;\n}\n\n.card-title {\n  margin-bottom: $card-title-spacer-y;\n}\n\n.card-subtitle {\n  margin-top: -$card-title-spacer-y * .5;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  &:hover {\n    text-decoration: if($link-hover-decoration==underline, none, null);\n  }\n\n  +.card-link {\n    margin-left: $card-spacer-x;\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: $card-cap-padding-y $card-cap-padding-x;\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: $card-cap-color;\n  background-color: $card-cap-bg;\n  border-bottom: $card-border-width solid $card-border-color;\n\n  &:first-child {\n    @include border-radius($card-inner-border-radius $card-inner-border-radius 0 0);\n  }\n}\n\n.card-footer {\n  padding: $card-cap-padding-y $card-cap-padding-x;\n  color: $card-cap-color;\n  background-color: $card-cap-bg;\n  border-top: $card-border-width solid $card-border-color;\n\n  &:last-child {\n    @include border-radius(0 0 $card-inner-border-radius $card-inner-border-radius);\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: -$card-cap-padding-x * .5;\n  margin-bottom: -$card-cap-padding-y;\n  margin-left: -$card-cap-padding-x * .5;\n  border-bottom: 0;\n\n  @if $nav-tabs-link-active-bg !=$card-bg {\n    .nav-link.active {\n      background-color: $card-bg;\n      border-bottom-color: $card-bg;\n    }\n  }\n}\n\n.card-header-pills {\n  margin-right: -$card-cap-padding-x * .5;\n  margin-left: -$card-cap-padding-x * .5;\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: $card-img-overlay-padding;\n  @include border-radius($card-inner-border-radius);\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius($card-inner-border-radius);\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius($card-inner-border-radius);\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  >.card {\n    margin-bottom: $card-group-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    >.card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      +.card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-end-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-right-radius: 0;\n          }\n\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-start-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-left-radius: 0;\n          }\n\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}", "//\n// Base styles\n//\n\n.accordion-button {\n  position: relative;\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: $accordion-button-padding-y $accordion-button-padding-x;\n  @include font-size($font-size-base);\n  color: $accordion-button-color;\n  text-align: left; // Reset button style\n  background-color: $accordion-button-bg;\n  border: 0;\n  @include border-radius(0);\n  overflow-anchor: none;\n  @include transition($accordion-transition);\n\n  &:not(.collapsed) {\n    color: $accordion-button-active-color;\n    background-color: $accordion-button-active-bg;\n    box-shadow: inset 0 ($accordion-border-width * -1) 0 $accordion-border-color;\n\n    &::after {\n      background-image: escape-svg($accordion-button-active-icon);\n      transform: $accordion-icon-transform;\n    }\n  }\n\n  // Accordion icon\n  &::after {\n    flex-shrink: 0;\n    width: $accordion-icon-width;\n    height: $accordion-icon-width;\n    margin-left: auto;\n    content: \"\";\n    background-image: escape-svg($accordion-button-icon);\n    background-repeat: no-repeat;\n    background-size: $accordion-icon-width;\n    @include transition($accordion-icon-transition);\n  }\n\n  &:hover {\n    z-index: 2;\n  }\n\n  &:focus {\n    z-index: 3;\n    border-color: $accordion-button-focus-border-color;\n    outline: 0;\n    box-shadow: $accordion-button-focus-box-shadow;\n  }\n}\n\n.accordion-header {\n  margin-bottom: 0;\n}\n\n.accordion-item {\n  background-color: $accordion-bg;\n  border: $accordion-border-width solid $accordion-border-color;\n\n  &:first-of-type {\n    @include border-top-radius($accordion-border-radius);\n\n    .accordion-button {\n      @include border-top-radius($accordion-inner-border-radius);\n    }\n  }\n\n  &:not(:first-of-type) {\n    border-top: 0;\n  }\n\n  // Only set a border-radius on the last item if the accordion is collapsed\n  &:last-of-type {\n    @include border-bottom-radius($accordion-border-radius);\n\n    .accordion-button {\n      &.collapsed {\n        @include border-bottom-radius($accordion-inner-border-radius);\n      }\n    }\n\n    .accordion-collapse {\n      @include border-bottom-radius($accordion-border-radius);\n    }\n  }\n}\n\n.accordion-body {\n  padding: $accordion-body-padding-y $accordion-body-padding-x;\n}\n\n\n// Flush accordion items\n//\n// Remove borders and border-radius to keep accordion items edge-to-edge.\n\n.accordion-flush {\n  .accordion-collapse {\n    border-width: 0;\n  }\n\n  .accordion-item {\n    border-right: 0;\n    border-left: 0;\n    @include border-radius(0);\n\n    &:first-child {\n      border-top: 0;\n    }\n\n    &:last-child {\n      border-bottom: 0;\n    }\n\n    .accordion-button {\n      @include border-radius(0);\n    }\n  }\n}", ".breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: $breadcrumb-padding-y $breadcrumb-padding-x;\n  margin-bottom: $breadcrumb-margin-bottom;\n  @include font-size($breadcrumb-font-size);\n  list-style: none;\n  background-color: $breadcrumb-bg;\n  @include border-radius($breadcrumb-border-radius);\n}\n\n.breadcrumb-item {\n\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  +.breadcrumb-item {\n    padding-left: $breadcrumb-item-padding-x;\n\n    &::before {\n      float: left; // Suppress inline spacings and underlining of the separator\n      padding-right: $breadcrumb-item-padding-x;\n      color: $breadcrumb-divider-color;\n      content: var(--#{$variable-prefix}breadcrumb-divider, escape-svg($breadcrumb-divider)) #{\"/* rtl:\"} var(--#{$variable-prefix}breadcrumb-divider, escape-svg($breadcrumb-divider-flipped)) #{\"*/\"};\n    }\n  }\n\n  &.active {\n    color: $breadcrumb-active-color;\n  }\n}", ".pagination {\n  display: flex;\n  @include list-unstyled();\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  color: $pagination-color;\n  text-decoration: if($link-decoration==none, null, none);\n  background-color: $pagination-bg;\n  border: $pagination-border-width solid $pagination-border-color;\n  @include transition($pagination-transition);\n\n  &:hover {\n    z-index: 2;\n    color: $pagination-hover-color;\n    text-decoration: if($link-hover-decoration==underline, none, null);\n    background-color: $pagination-hover-bg;\n    border-color: $pagination-hover-border-color;\n  }\n\n  &:focus {\n    z-index: 3;\n    color: $pagination-focus-color;\n    background-color: $pagination-focus-bg;\n    outline: $pagination-focus-outline;\n    box-shadow: $pagination-focus-box-shadow;\n  }\n}\n\n.page-item {\n  &:not(:first-child) .page-link {\n    margin-left: $pagination-margin-start;\n  }\n\n  &.active .page-link {\n    z-index: 3;\n    color: $pagination-active-color;\n    @include gradient-bg($pagination-active-bg);\n    border-color: $pagination-active-border-color;\n  }\n\n  &.disabled .page-link {\n    color: $pagination-disabled-color;\n    pointer-events: none;\n    background-color: $pagination-disabled-bg;\n    border-color: $pagination-disabled-border-color;\n  }\n}\n\n\n//\n// Sizing\n//\n@include pagination-size($pagination-padding-y, $pagination-padding-x, null, $pagination-border-radius);\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $pagination-border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $pagination-border-radius-sm);\n}", "// Pagination\n\n// scss-docs-start pagination-mixin\n@mixin pagination-size($padding-y, $padding-x, $font-size, $border-radius) {\n  .page-link {\n    padding: $padding-y $padding-x;\n    @include font-size($font-size);\n  }\n\n  .page-item {\n    @if $pagination-margin-start == (-$pagination-border-width) {\n      &:first-child {\n        .page-link {\n          @include border-start-radius($border-radius);\n        }\n      }\n\n      &:last-child {\n        .page-link {\n          @include border-end-radius($border-radius);\n        }\n      }\n    } @else {\n      //Add border-radius to all pageLinks in case they have left margin\n      .page-link {\n        @include border-radius($border-radius);\n      }\n    }\n  }\n}\n// scss-docs-end pagination-mixin\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  display: inline-block;\n  padding: $badge-padding-y $badge-padding-x;\n  @include font-size($badge-font-size);\n  font-weight: $badge-font-weight;\n  line-height: 1;\n  color: $badge-color;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius($badge-border-radius);\n  @include gradient-bg();\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}", "//\n// Base styles\n//\n\n.alert {\n  position: relative;\n  padding: $alert-padding-y $alert-padding-x;\n  margin-bottom: $alert-margin-bottom;\n  border: $alert-border-width solid transparent;\n  @include border-radius($alert-border-radius);\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  padding-right: $alert-dismissible-padding-r;\n\n  // Adjust close link position\n  .btn-close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    z-index: $stretched-link-z-index + 1;\n    padding: $alert-padding-y * 1.25 $alert-padding-x;\n  }\n}\n\n\n// scss-docs-start alert-modifiers\n// Generate contextual modifier classes for colorizing the alert.\n\n@each $state,\n$value in $theme-colors {\n  $alert-background: shift-color($value, $alert-bg-scale);\n  $alert-border: shift-color($value, $alert-border-scale);\n  $alert-color: shift-color($value, $alert-color-scale);\n\n  @if (contrast-ratio($alert-background, $alert-color) < $min-contrast-ratio) {\n    $alert-color: mix($value, color-contrast($alert-background), abs($alert-color-scale));\n  }\n\n  .alert-#{$state} {\n    @include alert-variant($alert-background, $alert-border, $alert-color);\n  }\n}\n\n// scss-docs-end alert-modifiers", "// scss-docs-start alert-variant-mixin\n@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  .alert-link {\n    color: shade-color($color, 20%);\n  }\n}\n// scss-docs-end alert-variant-mixin\n", "// Disable animation if transitions are disabled\n\n// scss-docs-start progress-keyframes\n@if $enable-transitions {\n  @keyframes progress-bar-stripes {\n    0% {\n      background-position-x: $progress-height;\n    }\n  }\n}\n\n// scss-docs-end progress-keyframes\n\n.progress {\n  display: flex;\n  height: $progress-height;\n  overflow: hidden; // force rounded corners by cropping it\n  @include font-size($progress-font-size);\n  background-color: $progress-bg;\n  @include border-radius($progress-border-radius);\n  @include box-shadow($progress-box-shadow);\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: $progress-bar-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $progress-bar-bg;\n  @include transition($progress-bar-transition);\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: $progress-height $progress-height;\n}\n\n@if $enable-transitions {\n  .progress-bar-animated {\n    animation: $progress-bar-animation-timing progress-bar-stripes;\n\n    @if $enable-reduced-motion {\n      @media (prefers-reduced-motion: reduce) {\n        animation: none;\n      }\n    }\n  }\n}", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n  @include border-radius($list-group-border-radius);\n}\n\n.list-group-numbered {\n  list-style-type: none;\n  counter-reset: section;\n\n  >li::before {\n    // Increments only this instance of the section counter\n    content: counters(section, \".\") \". \";\n    counter-increment: section;\n  }\n}\n\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: $list-group-action-color;\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  &:hover,\n  &:focus {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: $list-group-action-hover-color;\n    text-decoration: none;\n    background-color: $list-group-hover-bg;\n  }\n\n  &:active {\n    color: $list-group-action-active-color;\n    background-color: $list-group-action-active-bg;\n  }\n}\n\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: $list-group-item-padding-y $list-group-item-padding-x;\n  color: $list-group-color;\n  text-decoration: if($link-decoration==none, null, none);\n  background-color: $list-group-bg;\n  border: $list-group-border-width solid $list-group-border-color;\n\n  &:first-child {\n    @include border-top-radius(inherit);\n  }\n\n  &:last-child {\n    @include border-bottom-radius(inherit);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $list-group-disabled-color;\n    pointer-events: none;\n    background-color: $list-group-disabled-bg;\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: $list-group-active-color;\n    background-color: $list-group-active-bg;\n    border-color: $list-group-active-border-color;\n  }\n\n  &+& {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: -$list-group-border-width;\n      border-top-width: $list-group-border-width;\n    }\n  }\n}\n\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      >.list-group-item {\n        &:first-child {\n          @include border-bottom-start-radius($list-group-border-radius);\n          @include border-top-end-radius(0);\n        }\n\n        &:last-child {\n          @include border-top-end-radius($list-group-border-radius);\n          @include border-bottom-start-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        +.list-group-item {\n          border-top-width: $list-group-border-width;\n          border-left-width: 0;\n\n          &.active {\n            margin-left: -$list-group-border-width;\n            border-left-width: $list-group-border-width;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  @include border-radius(0);\n\n  >.list-group-item {\n    border-width: 0 0 $list-group-border-width;\n\n    &:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// scss-docs-start list-group-modifiers\n// List group contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $state,\n$value in $theme-colors {\n  $list-group-variant-bg: shift-color($value, $list-group-item-bg-scale);\n  $list-group-variant-color: shift-color($value, $list-group-item-color-scale);\n\n  @if (contrast-ratio($list-group-variant-bg, $list-group-variant-color) < $min-contrast-ratio) {\n    $list-group-variant-color: mix($value, color-contrast($list-group-variant-bg), abs($list-group-item-color-scale));\n  }\n\n  @include list-group-item-variant($state, $list-group-variant-bg, $list-group-variant-color);\n}\n\n// scss-docs-end list-group-modifiers", "// List Groups\n\n// scss-docs-start list-group-mixin\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    &.list-group-item-action {\n      &:hover,\n      &:focus {\n        color: $color;\n        background-color: shade-color($background, 10%);\n      }\n\n      &.active {\n        color: $white;\n        background-color: $color;\n        border-color: $color;\n      }\n    }\n  }\n}\n// scss-docs-end list-group-mixin\n", "// transparent background and border properties included for button version.\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n.btn-close {\n  box-sizing: content-box;\n  width: $btn-close-width;\n  height: $btn-close-height;\n  padding: $btn-close-padding-y $btn-close-padding-x;\n  color: $btn-close-color;\n  background: transparent escape-svg($btn-close-bg) center / $btn-close-width auto no-repeat; // include transparent for button elements\n  border: 0; // for button elements\n  @include border-radius();\n  opacity: $btn-close-opacity;\n\n  // Override <a>'s hover style\n  &:hover {\n    color: $btn-close-color;\n    text-decoration: none;\n    opacity: $btn-close-hover-opacity;\n  }\n\n  &:focus {\n    outline: 0;\n    box-shadow: $btn-close-focus-shadow;\n    opacity: $btn-close-focus-opacity;\n  }\n\n  &:disabled,\n  &.disabled {\n    pointer-events: none;\n    user-select: none;\n    opacity: $btn-close-disabled-opacity;\n  }\n}\n\n.btn-close-white {\n  filter: $btn-close-white-filter;\n}", ".toast {\n  width: $toast-max-width;\n  max-width: 100%;\n  @include font-size($toast-font-size);\n  color: $toast-color;\n  pointer-events: auto;\n  background-color: $toast-background-color;\n  background-clip: padding-box;\n  border: $toast-border-width solid $toast-border-color;\n  box-shadow: $toast-box-shadow;\n  @include border-radius($toast-border-radius);\n\n  &.showing {\n    opacity: 0;\n  }\n\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.toast-container {\n  width: max-content;\n  max-width: 100%;\n  pointer-events: none;\n\n  > :not(:last-child) {\n    margin-bottom: $toast-spacing;\n  }\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: $toast-padding-y $toast-padding-x;\n  color: $toast-header-color;\n  background-color: $toast-header-background-color;\n  background-clip: padding-box;\n  border-bottom: $toast-border-width solid $toast-header-border-color;\n  @include border-top-radius(subtract($toast-border-radius, $toast-border-width));\n\n  .btn-close {\n    margin-right: $toast-padding-x * -.5;\n    margin-left: $toast-padding-x;\n  }\n}\n\n.toast-body {\n  padding: $toast-padding-x; // apply to both vertical and horizontal\n  word-wrap: break-word;\n}", "// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n// Container that the modal scrolls within\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden;\n  overflow-y: auto;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: $modal-dialog-margin;\n  // allow clicks to pass through for custom click handling to close modal\n  pointer-events: none;\n\n  // When fading in the modal, animate it to slide down\n  .modal.fade & {\n    @include transition($modal-transition);\n    transform: $modal-fade-transform;\n  }\n\n  .modal.show & {\n    transform: $modal-show-transform;\n  }\n\n  // When trying to close, animate focus to scale\n  .modal.modal-static & {\n    transform: $modal-scale-transform;\n  }\n}\n\n.modal-dialog-scrollable {\n  height: subtract(100%, $modal-dialog-margin * 2);\n\n  .modal-content {\n    max-height: 100%;\n    overflow: hidden;\n  }\n\n  .modal-body {\n    overflow-y: auto;\n  }\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: subtract(100%, $modal-dialog-margin * 2);\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\n  // counteract the pointer-events: none; in the .modal-dialog\n  color: $modal-content-color;\n  pointer-events: auto;\n  background-color: $modal-content-bg;\n  background-clip: padding-box;\n  border: $modal-content-border-width solid $modal-content-border-color;\n  @include border-radius($modal-content-border-radius);\n  @include box-shadow($modal-content-box-shadow-xs);\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  @include overlay-backdrop($zindex-modal-backdrop, $modal-backdrop-bg, $modal-backdrop-opacity);\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\n  padding: $modal-header-padding;\n  border-bottom: $modal-header-border-width solid $modal-header-border-color;\n  @include border-top-radius($modal-content-inner-border-radius);\n\n  .btn-close {\n    padding: ($modal-header-padding-y * .5) ($modal-header-padding-x * .5);\n    margin: ($modal-header-padding-y * -.5) ($modal-header-padding-x * -.5) ($modal-header-padding-y * -.5) auto;\n  }\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: $modal-title-line-height;\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when there should be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: $modal-inner-padding;\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  flex-wrap: wrap;\n  flex-shrink: 0;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: $modal-inner-padding - $modal-footer-margin-between * .5;\n  border-top: $modal-footer-border-width solid $modal-footer-border-color;\n  @include border-bottom-radius($modal-content-inner-border-radius);\n\n  // Place margin between footer elements\n  // This solution is far from ideal because of the universal selector usage,\n  // but is needed to fix https://github.com/twbs/bootstrap/issues/24800\n  >* {\n    margin: $modal-footer-margin-between * .5;\n  }\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: $modal-md;\n    margin: $modal-dialog-margin-y-sm-up auto;\n  }\n\n  .modal-dialog-scrollable {\n    height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n  }\n\n  .modal-dialog-centered {\n    min-height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n  }\n\n  .modal-content {\n    @include box-shadow($modal-content-box-shadow-sm-up);\n  }\n\n  .modal-sm {\n    max-width: $modal-sm;\n  }\n}\n\n@include media-breakpoint-up(lg) {\n\n  .modal-lg,\n  .modal-xl {\n    max-width: $modal-lg;\n  }\n}\n\n@include media-breakpoint-up(xl) {\n  .modal-xl {\n    max-width: $modal-xl;\n  }\n}\n\n// scss-docs-start modal-fullscreen-loop\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n  $postfix: if($infix !=\"\", $infix + \"-down\", \"\");\n\n  @include media-breakpoint-down($breakpoint) {\n    .modal-fullscreen#{$postfix} {\n      width: 100vw;\n      max-width: none;\n      height: 100%;\n      margin: 0;\n\n      .modal-content {\n        height: 100%;\n        border: 0;\n        @include border-radius(0);\n      }\n\n      .modal-header {\n        @include border-radius(0);\n      }\n\n      .modal-body {\n        overflow-y: auto;\n      }\n\n      .modal-footer {\n        @include border-radius(0);\n      }\n    }\n  }\n}\n\n// scss-docs-end modal-fullscreen-loop", "// Shared between modals and offcanvases\n@mixin overlay-backdrop($zindex, $backdrop-bg, $backdrop-opacity) {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex;\n  width: 100vw;\n  height: 100vh;\n  background-color: $backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $backdrop-opacity; }\n}\n", "// Base class\n.tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  margin: $tooltip-margin;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($tooltip-font-size);\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show {\n    opacity: $tooltip-opacity;\n  }\n\n  .tooltip-arrow {\n    position: absolute;\n    display: block;\n    width: $tooltip-arrow-width;\n    height: $tooltip-arrow-height;\n\n    &::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-tooltip-top {\n  padding: $tooltip-arrow-height 0;\n\n  .tooltip-arrow {\n    bottom: 0;\n\n    &::before {\n      top: -1px;\n      border-width: $tooltip-arrow-height ($tooltip-arrow-width * .5) 0;\n      border-top-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-end {\n  padding: 0 $tooltip-arrow-height;\n\n  .tooltip-arrow {\n    left: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      right: -1px;\n      border-width: ($tooltip-arrow-width * .5) $tooltip-arrow-height ($tooltip-arrow-width * .5) 0;\n      border-right-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-bottom {\n  padding: $tooltip-arrow-height 0;\n\n  .tooltip-arrow {\n    top: 0;\n\n    &::before {\n      bottom: -1px;\n      border-width: 0 ($tooltip-arrow-width * .5) $tooltip-arrow-height;\n      border-bottom-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-start {\n  padding: 0 $tooltip-arrow-height;\n\n  .tooltip-arrow {\n    right: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      left: -1px;\n      border-width: ($tooltip-arrow-width * .5) 0 ($tooltip-arrow-width * .5) $tooltip-arrow-height;\n      border-left-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-tooltip-top;\n  }\n\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-tooltip-end;\n  }\n\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-tooltip-bottom;\n  }\n\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-tooltip-start;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: $tooltip-max-width;\n  padding: $tooltip-padding-y $tooltip-padding-x;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n  @include border-radius($tooltip-border-radius);\n}", "@mixin reset-text {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or overflow-wrap / word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", ".popover {\n  position: absolute;\n  top: 0;\n  left: 0 #{\"/* rtl:ignore */\"};\n  z-index: $zindex-popover;\n  display: block;\n  max-width: $popover-max-width;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($popover-font-size);\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: $popover-bg;\n  background-clip: padding-box;\n  border: $popover-border-width solid $popover-border-color;\n  @include border-radius($popover-border-radius);\n  @include box-shadow($popover-box-shadow);\n\n  .popover-arrow {\n    position: absolute;\n    display: block;\n    width: $popover-arrow-width;\n    height: $popover-arrow-height;\n\n    &::before,\n    &::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-popover-top {\n  >.popover-arrow {\n    bottom: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      bottom: 0;\n      border-width: $popover-arrow-height ($popover-arrow-width * .5) 0;\n      border-top-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      bottom: $popover-border-width;\n      border-width: $popover-arrow-height ($popover-arrow-width * .5) 0;\n      border-top-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-end {\n  >.popover-arrow {\n    left: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n\n    &::before {\n      left: 0;\n      border-width: ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5) 0;\n      border-right-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      left: $popover-border-width;\n      border-width: ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5) 0;\n      border-right-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-bottom {\n  >.popover-arrow {\n    top: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      top: 0;\n      border-width: 0 ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5);\n      border-bottom-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      top: $popover-border-width;\n      border-width: 0 ($popover-arrow-width * .5) $popover-arrow-height ($popover-arrow-width * .5);\n      border-bottom-color: $popover-arrow-color;\n    }\n  }\n\n  // This will remove the popover-header's border just below the arrow\n  .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: $popover-arrow-width;\n    margin-left: -$popover-arrow-width * .5;\n    content: \"\";\n    border-bottom: $popover-border-width solid $popover-header-bg;\n  }\n}\n\n.bs-popover-start {\n  >.popover-arrow {\n    right: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n\n    &::before {\n      right: 0;\n      border-width: ($popover-arrow-width * .5) 0 ($popover-arrow-width * .5) $popover-arrow-height;\n      border-left-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      right: $popover-border-width;\n      border-width: ($popover-arrow-width * .5) 0 ($popover-arrow-width * .5) $popover-arrow-height;\n      border-left-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-auto {\n  &[data-popper-placement^=\"top\"] {\n    @extend .bs-popover-top;\n  }\n\n  &[data-popper-placement^=\"right\"] {\n    @extend .bs-popover-end;\n  }\n\n  &[data-popper-placement^=\"bottom\"] {\n    @extend .bs-popover-bottom;\n  }\n\n  &[data-popper-placement^=\"left\"] {\n    @extend .bs-popover-start;\n  }\n}\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: $popover-header-padding-y $popover-header-padding-x;\n  margin-bottom: 0; // Reset the default from Reboot\n  @include font-size($font-size-base);\n  color: $popover-header-color;\n  background-color: $popover-header-bg;\n  border-bottom: $popover-border-width solid $popover-border-color;\n  @include border-top-radius($popover-inner-border-radius);\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: $popover-body-padding-y $popover-body-padding-x;\n  color: $popover-body-color;\n}", "// Notes on the classes:\n//\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\n//    we're preventing all actions instead\n// 2. The .carousel-item-start and .carousel-item-end is used to indicate where\n//    the active slide is heading.\n// 3. .active.carousel-item is the current slide.\n// 4. .active.carousel-item-start and .active.carousel-item-end is the current\n//    slide in its in-transition state. Only one of these occurs at a time.\n// 5. .carousel-item-next.carousel-item-start and .carousel-item-prev.carousel-item-end\n//    is the upcoming slide in transition.\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n  @include clearfix();\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  @include transition($carousel-transition);\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n/* rtl:begin:ignore */\n.carousel-item-next:not(.carousel-item-start),\n.active.carousel-item-end {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-end),\n.active.carousel-item-start {\n  transform: translateX(-100%);\n}\n\n/* rtl:end:ignore */\n\n\n//\n// Alternate transitions\n//\n\n.carousel-fade {\n  .carousel-item {\n    opacity: 0;\n    transition-property: opacity;\n    transform: none;\n  }\n\n  .carousel-item.active,\n  .carousel-item-next.carousel-item-start,\n  .carousel-item-prev.carousel-item-end {\n    z-index: 1;\n    opacity: 1;\n  }\n\n  .active.carousel-item-start,\n  .active.carousel-item-end {\n    z-index: 0;\n    opacity: 0;\n    @include transition(opacity 0s $carousel-transition-duration);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  padding: 0;\n  color: $carousel-control-color;\n  text-align: center;\n  background: none;\n  border: 0;\n  opacity: $carousel-control-opacity;\n  @include transition($carousel-control-transition);\n\n  // Hover/focus state\n  &:hover,\n  &:focus {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: $carousel-control-hover-opacity;\n  }\n}\n\n.carousel-control-prev {\n  left: 0;\n  background-image: if($enable-gradients, linear-gradient(90deg, rgba($black, .25), rgba($black, .001)), null);\n}\n\n.carousel-control-next {\n  right: 0;\n  background-image: if($enable-gradients, linear-gradient(270deg, rgba($black, .25), rgba($black, .001)), null);\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background-repeat: no-repeat;\n  background-position: 50%;\n  background-size: 100% 100%;\n}\n\n/* rtl:options: {\n  \"autoRename\": true,\n  \"stringMap\":[ {\n    \"name\"    : \"prev-next\",\n    \"search\"  : \"prev\",\n    \"replace\" : \"next\"\n  } ]\n} */\n.carousel-control-prev-icon {\n  background-image: escape-svg($carousel-control-prev-icon-bg);\n}\n\n.carousel-control-next-icon {\n  background-image: escape-svg($carousel-control-next-icon-bg);\n}\n\n// Optional indicator pips/controls\n//\n// Add a container (such as a list) with the following class and add an item (ideally a focusable control,\n// like a button) with data-bs-target for each slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 2;\n  display: flex;\n  justify-content: center;\n  padding: 0;\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-bottom: 1rem;\n  margin-left: $carousel-control-width;\n  list-style: none;\n\n  [data-bs-target] {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    padding: 0;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: $carousel-indicator-active-bg;\n    background-clip: padding-box;\n    border: 0;\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\n    border-top: $carousel-indicator-hit-area-height solid transparent;\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\n    opacity: $carousel-indicator-opacity;\n    @include transition($carousel-indicator-transition);\n  }\n\n  .active {\n    opacity: $carousel-indicator-active-opacity;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: (100% - $carousel-caption-width) * .5;\n  bottom: $carousel-caption-spacer;\n  left: (100% - $carousel-caption-width) * .5;\n  padding-top: $carousel-caption-padding-y;\n  padding-bottom: $carousel-caption-padding-y;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n\n// Dark mode carousel\n\n.carousel-dark {\n\n  .carousel-control-prev-icon,\n  .carousel-control-next-icon {\n    filter: $carousel-dark-control-icon-filter;\n  }\n\n  .carousel-indicators [data-bs-target] {\n    background-color: $carousel-dark-indicator-active-bg;\n  }\n\n  .carousel-caption {\n    color: $carousel-dark-caption-color;\n  }\n}", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "//\n// Rotating border\n//\n\n// scss-docs-start spinner-border-keyframes\n@keyframes spinner-border {\n  to {\n    transform: rotate(360deg) #{\"/* rtl:ignore */\"};\n  }\n}\n\n// scss-docs-end spinner-border-keyframes\n\n.spinner-border {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: $spinner-vertical-align;\n  border: $spinner-border-width solid currentColor;\n  border-right-color: transparent;\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 50%;\n  animation: $spinner-animation-speed linear infinite spinner-border;\n}\n\n.spinner-border-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n  border-width: $spinner-border-width-sm;\n}\n\n//\n// Growing circle\n//\n\n// scss-docs-start spinner-grow-keyframes\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n\n  50% {\n    opacity: 1;\n    transform: none;\n  }\n}\n\n// scss-docs-end spinner-grow-keyframes\n\n.spinner-grow {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: $spinner-vertical-align;\n  background-color: currentColor;\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 50%;\n  opacity: 0;\n  animation: $spinner-animation-speed linear infinite spinner-grow;\n}\n\n.spinner-grow-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n}\n\n@if $enable-reduced-motion {\n  @media (prefers-reduced-motion: reduce) {\n\n    .spinner-border,\n    .spinner-grow {\n      animation-duration: $spinner-animation-speed * 2;\n    }\n  }\n}", ".offcanvas {\n  position: fixed;\n  bottom: 0;\n  z-index: $zindex-offcanvas;\n  display: flex;\n  flex-direction: column;\n  max-width: 100%;\n  color: $offcanvas-color;\n  visibility: hidden;\n  background-color: $offcanvas-bg-color;\n  background-clip: padding-box;\n  outline: 0;\n  @include box-shadow($offcanvas-box-shadow);\n  @include transition(transform $offcanvas-transition-duration ease-in-out);\n}\n\n.offcanvas-backdrop {\n  @include overlay-backdrop($zindex-offcanvas-backdrop, $offcanvas-backdrop-bg, $offcanvas-backdrop-opacity);\n}\n\n.offcanvas-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: $offcanvas-padding-y $offcanvas-padding-x;\n\n  .btn-close {\n    padding: ($offcanvas-padding-y * .5) ($offcanvas-padding-x * .5);\n    margin-top: $offcanvas-padding-y * -.5;\n    margin-right: $offcanvas-padding-x * -.5;\n    margin-bottom: $offcanvas-padding-y * -.5;\n  }\n}\n\n.offcanvas-title {\n  margin-bottom: 0;\n  line-height: $offcanvas-title-line-height;\n}\n\n.offcanvas-body {\n  flex-grow: 1;\n  padding: $offcanvas-padding-y $offcanvas-padding-x;\n  overflow-y: auto;\n}\n\n.offcanvas-start {\n  top: 0;\n  left: 0;\n  width: $offcanvas-horizontal-width;\n  border-right: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateX(-100%);\n}\n\n.offcanvas-end {\n  top: 0;\n  right: 0;\n  width: $offcanvas-horizontal-width;\n  border-left: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateX(100%);\n}\n\n.offcanvas-top {\n  top: 0;\n  right: 0;\n  left: 0;\n  height: $offcanvas-vertical-height;\n  max-height: 100%;\n  border-bottom: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateY(-100%);\n}\n\n.offcanvas-bottom {\n  right: 0;\n  left: 0;\n  height: $offcanvas-vertical-height;\n  max-height: 100%;\n  border-top: $offcanvas-border-width solid $offcanvas-border-color;\n  transform: translateY(100%);\n}\n\n.offcanvas.show {\n  transform: none;\n}", ".placeholder {\n  display: inline-block;\n  min-height: 1em;\n  vertical-align: middle;\n  cursor: wait;\n  background-color: currentColor;\n  opacity: $placeholder-opacity-max;\n\n  &.btn::before {\n    display: inline-block;\n    content: \"\";\n  }\n}\n\n// Sizing\n.placeholder-xs {\n  min-height: .6em;\n}\n\n.placeholder-sm {\n  min-height: .8em;\n}\n\n.placeholder-lg {\n  min-height: 1.2em;\n}\n\n// Animation\n.placeholder-glow {\n  .placeholder {\n    animation: placeholder-glow 2s ease-in-out infinite;\n  }\n}\n\n@keyframes placeholder-glow {\n  50% {\n    opacity: $placeholder-opacity-min;\n  }\n}\n\n.placeholder-wave {\n  mask-image: linear-gradient(130deg, $black 55%, rgba(0, 0, 0, (1 - $placeholder-opacity-min)) 75%, $black 95%);\n  mask-size: 200% 100%;\n  animation: placeholder-wave 2s linear infinite;\n}\n\n@keyframes placeholder-wave {\n  100% {\n    mask-position: -200% 0%;\n  }\n}", "@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: $value;\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n      }\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$variable-prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$variable-prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentColor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$variable-prefix}#{$property-class}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$variable-prefix}#{$property-class}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $value in $is-local-vars {\n                --#{$variable-prefix}#{$local-var}: #{$value};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}