{"version": 3, "sources": ["vendors/bootstrap/bootstrap-grid.scss", "vendors/bootstrap/bootstrap-grid.css", "vendors/bootstrap/_root.scss", "vendors/bootstrap/mixins/_container.scss", "vendors/bootstrap/mixins/_breakpoints.scss", "vendors/bootstrap/_containers.scss", "vendors/bootstrap/_variables.scss", "vendors/bootstrap/_grid.scss", "vendors/bootstrap/mixins/_grid.scss", "vendors/bootstrap/vendor/_rfs.scss", "vendors/bootstrap/mixins/_utilities.scss", "vendors/bootstrap/utilities/_api.scss"], "names": [], "mappings": "AAAA;;;;;ECKE,CDAC,MEIC,kBAAiC,CAAjC,oBAAiC,CAAjC,oBAAiC,CAAjC,kBAAiC,CAAjC,iBAAiC,CAAjC,oBAAiC,CAAjC,oBAAiC,CAAjC,mBAAiC,CAAjC,kBAAiC,CAAjC,kBAAiC,CAAjC,gBAAiC,CAAjC,kBAAiC,CAAjC,uBAAiC,CAKjC,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAA3C,sBAA2C,CAK3C,qBAAiC,CAAjC,uBAAiC,CAAjC,qBAAiC,CAAjC,kBAAiC,CAAjC,qBAAiC,CAAjC,oBAAiC,CAAjC,mBAAiC,CAAjC,kBAAiC,CAKjC,4BAAyC,CAAzC,+BAAyC,CAAzC,2BAAyC,CAAzC,yBAAyC,CAAzC,2BAAyC,CAAzC,0BAAyC,CAAzC,2BAAyC,CAAzC,uBAAyC,CAG3C,2BAA0C,CAC1C,qBAA0C,CAC1C,6BAAoD,CACpD,6BAA8C,CAM9C,qNAAsD,CACtD,yGAAoD,CACpD,mFAAwC,CASxC,gDAAwD,CACxD,yBAAoD,CACpD,0BAAwD,CACxD,0BAAwD,CACxD,wBAA4C,CAM5C,kBAAsC,CAGvC,mGCzDC,UAAW,CACX,wCAAuE,CACvE,uCAAsE,CACtE,iBAAkB,CAClB,gBAAiB,CCwDf,0BC1CE,yBACE,eCuayB,CDta1B,CDwCH,0BC1CE,uCACE,eCwaG,CDvaJ,CDwCH,0BC1CE,qDACE,eCyaG,CDxaJ,CDwCH,2BC1CE,mEACE,gBC0aI,CDzaL,CDwCH,2BC1CE,kFACE,gBC2aK,CD1aN,CElBL,KCAA,qBAAwC,CACxC,gBAAwC,CACxC,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAe,CAAf,cAAe,CAEf,wCAAmE,CACnE,2CAAsE,CACtE,0CAAqE,CDPrE,OCYA,6BAAoG,CAApG,qBAAoG,CAIpG,mBAAc,CAAd,aAAc,CACd,UAAW,CACX,cAAe,CACf,2CAAsE,CACtE,0CAAqE,CACrE,6BAAwD,CA+CpD,KACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,iBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UCwCqD,CD1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CD1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,cACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CDvCtD,UAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,OA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,OAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,QAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,QAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,QAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,UAlDT,oBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,qBAA8C,CAwDpC,UAxDV,eAA8C,CAwDpC,WAxDV,qBAA8C,CAwDpC,WAxDV,qBAA8C,CA0DnC,WAWH,gBAAwC,CACzC,WAIC,gBAAwC,CAP1C,WAEE,qBAAwC,CACzC,WAIC,qBAAwC,CAP1C,WAEE,oBAAwC,CACzC,WAIC,oBAAwC,CAP1C,WAEE,mBAAwC,CACzC,WAIC,mBAAwC,CAP1C,WAEE,qBAAwC,CACzC,WAIC,qBAAwC,CAP1C,WAEE,mBAAwC,CACzC,WAIC,mBAAwC,CJ1D9C,0BIUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CDvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CJ3DL,0BIUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CDvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CJ3DL,0BIUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CDvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CJ3DL,2BIUE,QACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,oBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,iBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CDvCtD,aAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,UA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,UAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,aAlDT,aAA4B,CAwDlB,aAxDV,oBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,qBAA8C,CAwDpC,aAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CA0DnC,iBAWH,gBAAwC,CACzC,iBAIC,gBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,oBAAwC,CACzC,iBAIC,oBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CAP1C,iBAEE,qBAAwC,CACzC,iBAIC,qBAAwC,CAP1C,iBAEE,mBAAwC,CACzC,iBAIC,mBAAwC,CACzC,CJ3DL,2BIUE,SACE,kBAAM,CAAN,eAAM,CAAN,WAAY,CACb,qBAlCL,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAcX,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UCwCqD,CD1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CD1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SCwCqD,CD1CvD,kBACE,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eCwCqD,CDvCtD,cAlBD,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAAW,CAiDN,WA5DH,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,cAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,WAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,SAA0C,CA+DpC,YAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,YAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,eAA0C,CA+DpC,YAhEN,kBAAc,CAAd,iBAAc,CAAd,aAAc,CACd,UAA0C,CAiEnC,cAlDT,aAA4B,CAwDlB,cAxDV,oBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,qBAA8C,CAwDpC,cAxDV,eAA8C,CAwDpC,eAxDV,qBAA8C,CAwDpC,eAxDV,qBAA8C,CA0DnC,mBAWH,gBAAwC,CACzC,mBAIC,gBAAwC,CAP1C,mBAEE,qBAAwC,CACzC,mBAIC,qBAAwC,CAP1C,mBAEE,oBAAwC,CACzC,mBAIC,oBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CAP1C,mBAEE,qBAAwC,CACzC,mBAIC,qBAAwC,CAP1C,mBAEE,mBAAwC,CACzC,mBAIC,mBAAwC,CACzC,CE3DD,UAOI,yBAA+D,CAPnE,gBAOI,+BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,aAOI,4BAA+D,CAPnE,cAOI,6BAA+D,CAPnE,QAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,eAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,QAOI,uBAA+D,CAPnE,WAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,UAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,aAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,kBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,qBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,aAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,aAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,eAOI,8BAAqD,CAArD,wBAA+D,CAPnE,eAOI,8BAAqD,CAArD,wBAA+D,CAPnE,WAOI,6BAAqD,CAArD,yBAA+D,CAPnE,aAOI,+BAAqD,CAArD,2BAA+D,CAPnE,mBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,uBAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,qBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,wBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,yBAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,wBAOI,mCAAqD,CAArD,uCAA+D,CAPnE,wBAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,iBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,oBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,qBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,mBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,sBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,sCAA+D,CAPnE,sBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,uBAOI,qCAAqD,CAArD,gCAA+D,CAPnE,iBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,kBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,gBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,mBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,qBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,oBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,aAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,KAOI,mBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,KAOI,sBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,sBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,MAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,MAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,uBAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,KAOI,oBAA+D,CAPnE,KAOI,yBAA+D,CAPnE,KAOI,wBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,KAOI,yBAA+D,CAPnE,KAOI,uBAA+D,CAPnE,MAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,MAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,MAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,MAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,MAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,MAOI,wBAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,0BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,2BAA+D,CAPnE,MAOI,gCAA+D,CAPnE,MAOI,+BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,gCAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,yBAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,6BAA+D,CAPnE,MAOI,4BAA+D,CAPnE,MAOI,8BAA+D,CAPnE,MAOI,4BAA+D,CNPvE,0BMAI,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAElE,CNTL,0BMAI,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAElE,CNTL,0BMAI,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAElE,CNTL,2BMAI,aAOI,yBAA+D,CAPnE,mBAOI,+BAA+D,CAPnE,YAOI,wBAA+D,CAPnE,WAOI,uBAA+D,CAPnE,YAOI,wBAA+D,CAPnE,gBAOI,4BAA+D,CAPnE,iBAOI,6BAA+D,CAPnE,WAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,kBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,WAOI,uBAA+D,CAPnE,cAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,aAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,wBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,gBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,kBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,cAOI,6BAAqD,CAArD,yBAA+D,CAPnE,gBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,wBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,2BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,2BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,2BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,sBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,oBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,uBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,yBAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,wBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,sBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,yBAOI,oCAAqD,CAArD,+BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,yBAOI,wCAAqD,CAArD,qCAA+D,CAPnE,0BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,qBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,mBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,sBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,wBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,gBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,YAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,eAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,QAOI,mBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,sBAA+D,CAPnE,WAOI,sBAA+D,CAPnE,SAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,YAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,SAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,YAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,YAOI,0BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,YAOI,4BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,YAOI,6BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,YAOI,2BAA+D,CAPnE,QAOI,oBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,wBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,QAOI,yBAA+D,CAPnE,QAOI,uBAA+D,CAPnE,SAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,SAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,SAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,0BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,2BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,+BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,gCAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,6BAA+D,CAPnE,SAOI,4BAA+D,CAPnE,SAOI,8BAA+D,CAPnE,SAOI,4BAA+D,CAElE,CNTL,2BMAI,cAOI,yBAA+D,CAPnE,oBAOI,+BAA+D,CAPnE,aAOI,wBAA+D,CAPnE,YAOI,uBAA+D,CAPnE,aAOI,wBAA+D,CAPnE,iBAOI,4BAA+D,CAPnE,kBAOI,6BAA+D,CAPnE,YAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,mBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,YAOI,uBAA+D,CAPnE,eAOI,6BAAqD,CAArD,4BAAqD,CAArD,wBAA+D,CAPnE,cAOI,wCAAqD,CAArD,uCAAqD,CAArD,iCAAqD,CAArD,6BAA+D,CAPnE,iBAOI,sCAAqD,CAArD,uCAAqD,CAArD,oCAAqD,CAArD,gCAA+D,CAPnE,sBAOI,wCAAqD,CAArD,wCAAqD,CAArD,yCAAqD,CAArD,qCAA+D,CAPnE,yBAOI,sCAAqD,CAArD,wCAAqD,CAArD,4CAAqD,CAArD,wCAA+D,CAPnE,iBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,iBAOI,6BAAqD,CAArD,8BAAqD,CAArD,sBAA+D,CAPnE,mBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,mBAOI,8BAAqD,CAArD,wBAA+D,CAPnE,eAOI,6BAAqD,CAArD,yBAA+D,CAPnE,iBAOI,+BAAqD,CAArD,2BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,iCAA+D,CAPnE,2BAOI,iCAAqD,CAArD,8BAAqD,CAArD,qCAA+D,CAPnE,yBAOI,+BAAqD,CAArD,4BAAqD,CAArD,mCAA+D,CAPnE,4BAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,6BAOI,mCAAqD,CAArD,gCAAqD,CAArD,wCAA+D,CAPnE,4BAOI,mCAAqD,CAArD,uCAA+D,CAPnE,4BAOI,wCAAqD,CAArD,qCAAqD,CAArD,uCAA+D,CAPnE,uBAOI,kCAAqD,CAArD,+BAAqD,CAArD,iCAA+D,CAPnE,qBAOI,gCAAqD,CAArD,6BAAqD,CAArD,+BAA+D,CAPnE,wBAOI,mCAAqD,CAArD,gCAAqD,CAArD,6BAA+D,CAPnE,0BAOI,qCAAqD,CAArD,kCAAqD,CAArD,+BAA+D,CAPnE,yBAOI,oCAAqD,CAArD,iCAAqD,CAArD,8BAA+D,CAPnE,yBAOI,mCAAqD,CAArD,mCAA+D,CAPnE,uBAOI,iCAAqD,CAArD,iCAA+D,CAPnE,0BAOI,oCAAqD,CAArD,+BAA+D,CAPnE,2BAOI,qCAAqD,CAArD,sCAA+D,CAPnE,0BAOI,wCAAqD,CAArD,qCAA+D,CAPnE,2BAOI,qCAAqD,CAArD,gCAA+D,CAPnE,qBAOI,mCAAqD,CAArD,0BAA+D,CAPnE,sBAOI,oCAAqD,CAArD,gCAA+D,CAPnE,oBAOI,kCAAqD,CAArD,8BAA+D,CAPnE,uBAOI,qCAAqD,CAArD,4BAA+D,CAPnE,yBAOI,uCAAqD,CAArD,8BAA+D,CAPnE,wBAOI,sCAAqD,CAArD,6BAA+D,CAPnE,iBAOI,sCAAqD,CAArD,4BAAqD,CAArD,mBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,aAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,gBAOI,sCAAqD,CAArD,2BAAqD,CAArD,kBAA+D,CAPnE,SAOI,mBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,sBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,sBAA+D,CAPnE,YAOI,sBAA+D,CAPnE,UAOI,yBAA+D,CAA/D,wBAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,aAOI,4BAA+D,CAA/D,2BAA+D,CAPnE,UAOI,uBAA+D,CAA/D,0BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,aAOI,0BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,uBAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,aAOI,0BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,aAOI,4BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,aAOI,6BAA+D,CAPnE,UAOI,wBAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,aAOI,2BAA+D,CAPnE,SAOI,oBAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,wBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,SAOI,yBAA+D,CAPnE,SAOI,uBAA+D,CAPnE,UAOI,0BAA+D,CAA/D,yBAA+D,CAPnE,UAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,8BAA+D,CAA/D,6BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,+BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,4BAA+D,CAPnE,UAOI,wBAA+D,CAA/D,2BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,UAOI,4BAA+D,CAA/D,+BAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,6BAA+D,CAA/D,gCAA+D,CAPnE,UAOI,2BAA+D,CAA/D,8BAA+D,CAPnE,UAOI,wBAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,0BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,2BAA+D,CAPnE,UAOI,gCAA+D,CAPnE,UAOI,+BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,gCAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,yBAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,6BAA+D,CAPnE,UAOI,4BAA+D,CAPnE,UAOI,8BAA+D,CAPnE,UAOI,4BAA+D,CAElE,CClCT,aDyBQ,gBAOI,yBAA+D,CAPnE,sBAOI,+BAA+D,CAPnE,eAOI,wBAA+D,CAPnE,cAOI,uBAA+D,CAPnE,eAOI,wBAA+D,CAPnE,mBAOI,4BAA+D,CAPnE,oBAOI,6BAA+D,CAPnE,cAOI,8BAAqD,CAArD,8BAAqD,CAArD,uBAA+D,CAPnE,qBAOI,qCAAqD,CAArD,qCAAqD,CAArD,8BAA+D,CAPnE,cAOI,uBAA+D,CAElE", "file": "vendors/bootstrap/bootstrap-grid.css", "sourcesContent": ["/*!\n * Bootstrap Grid v5.1.3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n$include-column-box-sizing: true !default;\n\n@import \"functions\";\n@import \"variables\";\n\n@import \"mixins/lists\";\n@import \"mixins/breakpoints\";\n@import \"mixins/container\";\n@import \"mixins/grid\";\n@import \"mixins/utilities\";\n\n@import \"vendor/rfs\";\n\n@import \"root\";\n\n@import \"containers\";\n@import \"grid\";\n\n@import \"utilities\";\n// Only use the utilities we need\n// stylelint-disable-next-line scss/dollar-variable-default\n$utilities: map-get-multiple($utilities,\n  (\"display\",\n    \"order\",\n    \"flex\",\n    \"flex-direction\",\n    \"flex-grow\",\n    \"flex-shrink\",\n    \"flex-wrap\",\n    \"justify-content\",\n    \"align-items\",\n    \"align-content\",\n    \"align-self\",\n    \"margin\",\n    \"margin-x\",\n    \"margin-y\",\n    \"margin-top\",\n    \"margin-end\",\n    \"margin-bottom\",\n    \"margin-start\",\n    \"negative-margin\",\n    \"negative-margin-x\",\n    \"negative-margin-y\",\n    \"negative-margin-top\",\n    \"negative-margin-end\",\n    \"negative-margin-bottom\",\n    \"negative-margin-start\",\n    \"padding\",\n    \"padding-x\",\n    \"padding-y\",\n    \"padding-top\",\n    \"padding-end\",\n    \"padding-bottom\",\n    \"padding-start\",\n  ));\n\n@import \"utilities/api\";", "/*!\n * Bootstrap Grid v5.1.3 (https://getbootstrap.com/)\n * Copyright 2011-2021 The Bootstrap Authors\n * Copyright 2011-2021 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */:root{--bs-blue: #0d6efd;--bs-indigo: #6610f2;--bs-purple: #6f42c1;--bs-pink: #d63384;--bs-red: #dc3545;--bs-orange: #fd7e14;--bs-yellow: #ffc107;--bs-green: #198754;--bs-teal: #20c997;--bs-cyan: #0dcaf0;--bs-white: #fff;--bs-gray: #6c757d;--bs-gray-dark: #343a40;--bs-gray-100: #f8f9fa;--bs-gray-200: #e9ecef;--bs-gray-300: #dee2e6;--bs-gray-400: #ced4da;--bs-gray-500: #adb5bd;--bs-gray-600: #6c757d;--bs-gray-700: #495057;--bs-gray-800: #343a40;--bs-gray-900: #212529;--bs-primary: #0d6efd;--bs-secondary: #6c757d;--bs-success: #198754;--bs-info: #0dcaf0;--bs-warning: #ffc107;--bs-danger: #dc3545;--bs-light: #f8f9fa;--bs-dark: #212529;--bs-primary-rgb: 13,110,253;--bs-secondary-rgb: 108,117,125;--bs-success-rgb: 25,135,84;--bs-info-rgb: 13,202,240;--bs-warning-rgb: 255,193,7;--bs-danger-rgb: 220,53,69;--bs-light-rgb: 248,249,250;--bs-dark-rgb: 33,37,41;--bs-white-rgb: 255,255,255;--bs-black-rgb: 0,0,0;--bs-body-color-rgb: 33,37,41;--bs-body-bg-rgb: 255,255,255;--bs-font-sans-serif: system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";--bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;--bs-gradient: linear-gradient(180deg, rgba(255,255,255,0.15), rgba(255,255,255,0));--bs-body-font-family: var(--bs-font-sans-serif);--bs-body-font-size: 1rem;--bs-body-font-weight: 400;--bs-body-line-height: 1.5;--bs-body-color: #212529;--bs-body-bg: #fff}.container,.container-fluid,.container-sm,.container-md,.container-lg,.container-xl,.container-xxl{width:100%;padding-right:var(--bs-gutter-x, .75rem);padding-left:var(--bs-gutter-x, .75rem);margin-right:auto;margin-left:auto}@media (min-width: 576px){.container,.container-sm{max-width:540px}}@media (min-width: 768px){.container,.container-sm,.container-md{max-width:720px}}@media (min-width: 992px){.container,.container-sm,.container-md,.container-lg{max-width:960px}}@media (min-width: 1200px){.container,.container-sm,.container-md,.container-lg,.container-xl{max-width:1140px}}@media (min-width: 1400px){.container,.container-sm,.container-md,.container-lg,.container-xl,.container-xxl{max-width:1320px}}.row{--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display:flex;flex-wrap:wrap;margin-top:calc(-1 * var(--bs-gutter-y));margin-right:calc(-.5 * var(--bs-gutter-x));margin-left:calc(-.5 * var(--bs-gutter-x))}.row>*{box-sizing:border-box;flex-shrink:0;width:100%;max-width:100%;padding-right:calc(var(--bs-gutter-x) * .5);padding-left:calc(var(--bs-gutter-x) * .5);margin-top:var(--bs-gutter-y)}.col{flex:1 0 0%}.row-cols-auto>*{flex:0 0 auto;width:auto}.row-cols-1>*{flex:0 0 auto;width:100%}.row-cols-2>*{flex:0 0 auto;width:50%}.row-cols-3>*{flex:0 0 auto;width:33.33333%}.row-cols-4>*{flex:0 0 auto;width:25%}.row-cols-5>*{flex:0 0 auto;width:20%}.row-cols-6>*{flex:0 0 auto;width:16.66667%}.col-auto{flex:0 0 auto;width:auto}.col-1{flex:0 0 auto;width:8.33333%}.col-2{flex:0 0 auto;width:16.66667%}.col-3{flex:0 0 auto;width:25%}.col-4{flex:0 0 auto;width:33.33333%}.col-5{flex:0 0 auto;width:41.66667%}.col-6{flex:0 0 auto;width:50%}.col-7{flex:0 0 auto;width:58.33333%}.col-8{flex:0 0 auto;width:66.66667%}.col-9{flex:0 0 auto;width:75%}.col-10{flex:0 0 auto;width:83.33333%}.col-11{flex:0 0 auto;width:91.66667%}.col-12{flex:0 0 auto;width:100%}.offset-1{margin-left:8.33333%}.offset-2{margin-left:16.66667%}.offset-3{margin-left:25%}.offset-4{margin-left:33.33333%}.offset-5{margin-left:41.66667%}.offset-6{margin-left:50%}.offset-7{margin-left:58.33333%}.offset-8{margin-left:66.66667%}.offset-9{margin-left:75%}.offset-10{margin-left:83.33333%}.offset-11{margin-left:91.66667%}.g-0,.gx-0{--bs-gutter-x: 0}.g-0,.gy-0{--bs-gutter-y: 0}.g-1,.gx-1{--bs-gutter-x: .25rem}.g-1,.gy-1{--bs-gutter-y: .25rem}.g-2,.gx-2{--bs-gutter-x: .5rem}.g-2,.gy-2{--bs-gutter-y: .5rem}.g-3,.gx-3{--bs-gutter-x: 1rem}.g-3,.gy-3{--bs-gutter-y: 1rem}.g-4,.gx-4{--bs-gutter-x: 1.5rem}.g-4,.gy-4{--bs-gutter-y: 1.5rem}.g-5,.gx-5{--bs-gutter-x: 3rem}.g-5,.gy-5{--bs-gutter-y: 3rem}@media (min-width: 576px){.col-sm{flex:1 0 0%}.row-cols-sm-auto>*{flex:0 0 auto;width:auto}.row-cols-sm-1>*{flex:0 0 auto;width:100%}.row-cols-sm-2>*{flex:0 0 auto;width:50%}.row-cols-sm-3>*{flex:0 0 auto;width:33.33333%}.row-cols-sm-4>*{flex:0 0 auto;width:25%}.row-cols-sm-5>*{flex:0 0 auto;width:20%}.row-cols-sm-6>*{flex:0 0 auto;width:16.66667%}.col-sm-auto{flex:0 0 auto;width:auto}.col-sm-1{flex:0 0 auto;width:8.33333%}.col-sm-2{flex:0 0 auto;width:16.66667%}.col-sm-3{flex:0 0 auto;width:25%}.col-sm-4{flex:0 0 auto;width:33.33333%}.col-sm-5{flex:0 0 auto;width:41.66667%}.col-sm-6{flex:0 0 auto;width:50%}.col-sm-7{flex:0 0 auto;width:58.33333%}.col-sm-8{flex:0 0 auto;width:66.66667%}.col-sm-9{flex:0 0 auto;width:75%}.col-sm-10{flex:0 0 auto;width:83.33333%}.col-sm-11{flex:0 0 auto;width:91.66667%}.col-sm-12{flex:0 0 auto;width:100%}.offset-sm-0{margin-left:0}.offset-sm-1{margin-left:8.33333%}.offset-sm-2{margin-left:16.66667%}.offset-sm-3{margin-left:25%}.offset-sm-4{margin-left:33.33333%}.offset-sm-5{margin-left:41.66667%}.offset-sm-6{margin-left:50%}.offset-sm-7{margin-left:58.33333%}.offset-sm-8{margin-left:66.66667%}.offset-sm-9{margin-left:75%}.offset-sm-10{margin-left:83.33333%}.offset-sm-11{margin-left:91.66667%}.g-sm-0,.gx-sm-0{--bs-gutter-x: 0}.g-sm-0,.gy-sm-0{--bs-gutter-y: 0}.g-sm-1,.gx-sm-1{--bs-gutter-x: .25rem}.g-sm-1,.gy-sm-1{--bs-gutter-y: .25rem}.g-sm-2,.gx-sm-2{--bs-gutter-x: .5rem}.g-sm-2,.gy-sm-2{--bs-gutter-y: .5rem}.g-sm-3,.gx-sm-3{--bs-gutter-x: 1rem}.g-sm-3,.gy-sm-3{--bs-gutter-y: 1rem}.g-sm-4,.gx-sm-4{--bs-gutter-x: 1.5rem}.g-sm-4,.gy-sm-4{--bs-gutter-y: 1.5rem}.g-sm-5,.gx-sm-5{--bs-gutter-x: 3rem}.g-sm-5,.gy-sm-5{--bs-gutter-y: 3rem}}@media (min-width: 768px){.col-md{flex:1 0 0%}.row-cols-md-auto>*{flex:0 0 auto;width:auto}.row-cols-md-1>*{flex:0 0 auto;width:100%}.row-cols-md-2>*{flex:0 0 auto;width:50%}.row-cols-md-3>*{flex:0 0 auto;width:33.33333%}.row-cols-md-4>*{flex:0 0 auto;width:25%}.row-cols-md-5>*{flex:0 0 auto;width:20%}.row-cols-md-6>*{flex:0 0 auto;width:16.66667%}.col-md-auto{flex:0 0 auto;width:auto}.col-md-1{flex:0 0 auto;width:8.33333%}.col-md-2{flex:0 0 auto;width:16.66667%}.col-md-3{flex:0 0 auto;width:25%}.col-md-4{flex:0 0 auto;width:33.33333%}.col-md-5{flex:0 0 auto;width:41.66667%}.col-md-6{flex:0 0 auto;width:50%}.col-md-7{flex:0 0 auto;width:58.33333%}.col-md-8{flex:0 0 auto;width:66.66667%}.col-md-9{flex:0 0 auto;width:75%}.col-md-10{flex:0 0 auto;width:83.33333%}.col-md-11{flex:0 0 auto;width:91.66667%}.col-md-12{flex:0 0 auto;width:100%}.offset-md-0{margin-left:0}.offset-md-1{margin-left:8.33333%}.offset-md-2{margin-left:16.66667%}.offset-md-3{margin-left:25%}.offset-md-4{margin-left:33.33333%}.offset-md-5{margin-left:41.66667%}.offset-md-6{margin-left:50%}.offset-md-7{margin-left:58.33333%}.offset-md-8{margin-left:66.66667%}.offset-md-9{margin-left:75%}.offset-md-10{margin-left:83.33333%}.offset-md-11{margin-left:91.66667%}.g-md-0,.gx-md-0{--bs-gutter-x: 0}.g-md-0,.gy-md-0{--bs-gutter-y: 0}.g-md-1,.gx-md-1{--bs-gutter-x: .25rem}.g-md-1,.gy-md-1{--bs-gutter-y: .25rem}.g-md-2,.gx-md-2{--bs-gutter-x: .5rem}.g-md-2,.gy-md-2{--bs-gutter-y: .5rem}.g-md-3,.gx-md-3{--bs-gutter-x: 1rem}.g-md-3,.gy-md-3{--bs-gutter-y: 1rem}.g-md-4,.gx-md-4{--bs-gutter-x: 1.5rem}.g-md-4,.gy-md-4{--bs-gutter-y: 1.5rem}.g-md-5,.gx-md-5{--bs-gutter-x: 3rem}.g-md-5,.gy-md-5{--bs-gutter-y: 3rem}}@media (min-width: 992px){.col-lg{flex:1 0 0%}.row-cols-lg-auto>*{flex:0 0 auto;width:auto}.row-cols-lg-1>*{flex:0 0 auto;width:100%}.row-cols-lg-2>*{flex:0 0 auto;width:50%}.row-cols-lg-3>*{flex:0 0 auto;width:33.33333%}.row-cols-lg-4>*{flex:0 0 auto;width:25%}.row-cols-lg-5>*{flex:0 0 auto;width:20%}.row-cols-lg-6>*{flex:0 0 auto;width:16.66667%}.col-lg-auto{flex:0 0 auto;width:auto}.col-lg-1{flex:0 0 auto;width:8.33333%}.col-lg-2{flex:0 0 auto;width:16.66667%}.col-lg-3{flex:0 0 auto;width:25%}.col-lg-4{flex:0 0 auto;width:33.33333%}.col-lg-5{flex:0 0 auto;width:41.66667%}.col-lg-6{flex:0 0 auto;width:50%}.col-lg-7{flex:0 0 auto;width:58.33333%}.col-lg-8{flex:0 0 auto;width:66.66667%}.col-lg-9{flex:0 0 auto;width:75%}.col-lg-10{flex:0 0 auto;width:83.33333%}.col-lg-11{flex:0 0 auto;width:91.66667%}.col-lg-12{flex:0 0 auto;width:100%}.offset-lg-0{margin-left:0}.offset-lg-1{margin-left:8.33333%}.offset-lg-2{margin-left:16.66667%}.offset-lg-3{margin-left:25%}.offset-lg-4{margin-left:33.33333%}.offset-lg-5{margin-left:41.66667%}.offset-lg-6{margin-left:50%}.offset-lg-7{margin-left:58.33333%}.offset-lg-8{margin-left:66.66667%}.offset-lg-9{margin-left:75%}.offset-lg-10{margin-left:83.33333%}.offset-lg-11{margin-left:91.66667%}.g-lg-0,.gx-lg-0{--bs-gutter-x: 0}.g-lg-0,.gy-lg-0{--bs-gutter-y: 0}.g-lg-1,.gx-lg-1{--bs-gutter-x: .25rem}.g-lg-1,.gy-lg-1{--bs-gutter-y: .25rem}.g-lg-2,.gx-lg-2{--bs-gutter-x: .5rem}.g-lg-2,.gy-lg-2{--bs-gutter-y: .5rem}.g-lg-3,.gx-lg-3{--bs-gutter-x: 1rem}.g-lg-3,.gy-lg-3{--bs-gutter-y: 1rem}.g-lg-4,.gx-lg-4{--bs-gutter-x: 1.5rem}.g-lg-4,.gy-lg-4{--bs-gutter-y: 1.5rem}.g-lg-5,.gx-lg-5{--bs-gutter-x: 3rem}.g-lg-5,.gy-lg-5{--bs-gutter-y: 3rem}}@media (min-width: 1200px){.col-xl{flex:1 0 0%}.row-cols-xl-auto>*{flex:0 0 auto;width:auto}.row-cols-xl-1>*{flex:0 0 auto;width:100%}.row-cols-xl-2>*{flex:0 0 auto;width:50%}.row-cols-xl-3>*{flex:0 0 auto;width:33.33333%}.row-cols-xl-4>*{flex:0 0 auto;width:25%}.row-cols-xl-5>*{flex:0 0 auto;width:20%}.row-cols-xl-6>*{flex:0 0 auto;width:16.66667%}.col-xl-auto{flex:0 0 auto;width:auto}.col-xl-1{flex:0 0 auto;width:8.33333%}.col-xl-2{flex:0 0 auto;width:16.66667%}.col-xl-3{flex:0 0 auto;width:25%}.col-xl-4{flex:0 0 auto;width:33.33333%}.col-xl-5{flex:0 0 auto;width:41.66667%}.col-xl-6{flex:0 0 auto;width:50%}.col-xl-7{flex:0 0 auto;width:58.33333%}.col-xl-8{flex:0 0 auto;width:66.66667%}.col-xl-9{flex:0 0 auto;width:75%}.col-xl-10{flex:0 0 auto;width:83.33333%}.col-xl-11{flex:0 0 auto;width:91.66667%}.col-xl-12{flex:0 0 auto;width:100%}.offset-xl-0{margin-left:0}.offset-xl-1{margin-left:8.33333%}.offset-xl-2{margin-left:16.66667%}.offset-xl-3{margin-left:25%}.offset-xl-4{margin-left:33.33333%}.offset-xl-5{margin-left:41.66667%}.offset-xl-6{margin-left:50%}.offset-xl-7{margin-left:58.33333%}.offset-xl-8{margin-left:66.66667%}.offset-xl-9{margin-left:75%}.offset-xl-10{margin-left:83.33333%}.offset-xl-11{margin-left:91.66667%}.g-xl-0,.gx-xl-0{--bs-gutter-x: 0}.g-xl-0,.gy-xl-0{--bs-gutter-y: 0}.g-xl-1,.gx-xl-1{--bs-gutter-x: .25rem}.g-xl-1,.gy-xl-1{--bs-gutter-y: .25rem}.g-xl-2,.gx-xl-2{--bs-gutter-x: .5rem}.g-xl-2,.gy-xl-2{--bs-gutter-y: .5rem}.g-xl-3,.gx-xl-3{--bs-gutter-x: 1rem}.g-xl-3,.gy-xl-3{--bs-gutter-y: 1rem}.g-xl-4,.gx-xl-4{--bs-gutter-x: 1.5rem}.g-xl-4,.gy-xl-4{--bs-gutter-y: 1.5rem}.g-xl-5,.gx-xl-5{--bs-gutter-x: 3rem}.g-xl-5,.gy-xl-5{--bs-gutter-y: 3rem}}@media (min-width: 1400px){.col-xxl{flex:1 0 0%}.row-cols-xxl-auto>*{flex:0 0 auto;width:auto}.row-cols-xxl-1>*{flex:0 0 auto;width:100%}.row-cols-xxl-2>*{flex:0 0 auto;width:50%}.row-cols-xxl-3>*{flex:0 0 auto;width:33.33333%}.row-cols-xxl-4>*{flex:0 0 auto;width:25%}.row-cols-xxl-5>*{flex:0 0 auto;width:20%}.row-cols-xxl-6>*{flex:0 0 auto;width:16.66667%}.col-xxl-auto{flex:0 0 auto;width:auto}.col-xxl-1{flex:0 0 auto;width:8.33333%}.col-xxl-2{flex:0 0 auto;width:16.66667%}.col-xxl-3{flex:0 0 auto;width:25%}.col-xxl-4{flex:0 0 auto;width:33.33333%}.col-xxl-5{flex:0 0 auto;width:41.66667%}.col-xxl-6{flex:0 0 auto;width:50%}.col-xxl-7{flex:0 0 auto;width:58.33333%}.col-xxl-8{flex:0 0 auto;width:66.66667%}.col-xxl-9{flex:0 0 auto;width:75%}.col-xxl-10{flex:0 0 auto;width:83.33333%}.col-xxl-11{flex:0 0 auto;width:91.66667%}.col-xxl-12{flex:0 0 auto;width:100%}.offset-xxl-0{margin-left:0}.offset-xxl-1{margin-left:8.33333%}.offset-xxl-2{margin-left:16.66667%}.offset-xxl-3{margin-left:25%}.offset-xxl-4{margin-left:33.33333%}.offset-xxl-5{margin-left:41.66667%}.offset-xxl-6{margin-left:50%}.offset-xxl-7{margin-left:58.33333%}.offset-xxl-8{margin-left:66.66667%}.offset-xxl-9{margin-left:75%}.offset-xxl-10{margin-left:83.33333%}.offset-xxl-11{margin-left:91.66667%}.g-xxl-0,.gx-xxl-0{--bs-gutter-x: 0}.g-xxl-0,.gy-xxl-0{--bs-gutter-y: 0}.g-xxl-1,.gx-xxl-1{--bs-gutter-x: .25rem}.g-xxl-1,.gy-xxl-1{--bs-gutter-y: .25rem}.g-xxl-2,.gx-xxl-2{--bs-gutter-x: .5rem}.g-xxl-2,.gy-xxl-2{--bs-gutter-y: .5rem}.g-xxl-3,.gx-xxl-3{--bs-gutter-x: 1rem}.g-xxl-3,.gy-xxl-3{--bs-gutter-y: 1rem}.g-xxl-4,.gx-xxl-4{--bs-gutter-x: 1.5rem}.g-xxl-4,.gy-xxl-4{--bs-gutter-y: 1.5rem}.g-xxl-5,.gx-xxl-5{--bs-gutter-x: 3rem}.g-xxl-5,.gy-xxl-5{--bs-gutter-y: 3rem}}.d-inline{display:inline !important}.d-inline-block{display:inline-block !important}.d-block{display:block !important}.d-grid{display:grid !important}.d-table{display:table !important}.d-table-row{display:table-row !important}.d-table-cell{display:table-cell !important}.d-flex{display:flex !important}.d-inline-flex{display:inline-flex !important}.d-none{display:none !important}.flex-fill{flex:1 1 auto !important}.flex-row{flex-direction:row !important}.flex-column{flex-direction:column !important}.flex-row-reverse{flex-direction:row-reverse !important}.flex-column-reverse{flex-direction:column-reverse !important}.flex-grow-0{flex-grow:0 !important}.flex-grow-1{flex-grow:1 !important}.flex-shrink-0{flex-shrink:0 !important}.flex-shrink-1{flex-shrink:1 !important}.flex-wrap{flex-wrap:wrap !important}.flex-nowrap{flex-wrap:nowrap !important}.flex-wrap-reverse{flex-wrap:wrap-reverse !important}.justify-content-start{justify-content:flex-start !important}.justify-content-end{justify-content:flex-end !important}.justify-content-center{justify-content:center !important}.justify-content-between{justify-content:space-between !important}.justify-content-around{justify-content:space-around !important}.justify-content-evenly{justify-content:space-evenly !important}.align-items-start{align-items:flex-start !important}.align-items-end{align-items:flex-end !important}.align-items-center{align-items:center !important}.align-items-baseline{align-items:baseline !important}.align-items-stretch{align-items:stretch !important}.align-content-start{align-content:flex-start !important}.align-content-end{align-content:flex-end !important}.align-content-center{align-content:center !important}.align-content-between{align-content:space-between !important}.align-content-around{align-content:space-around !important}.align-content-stretch{align-content:stretch !important}.align-self-auto{align-self:auto !important}.align-self-start{align-self:flex-start !important}.align-self-end{align-self:flex-end !important}.align-self-center{align-self:center !important}.align-self-baseline{align-self:baseline !important}.align-self-stretch{align-self:stretch !important}.order-first{order:-1 !important}.order-0{order:0 !important}.order-1{order:1 !important}.order-2{order:2 !important}.order-3{order:3 !important}.order-4{order:4 !important}.order-5{order:5 !important}.order-last{order:6 !important}.m-0{margin:0 !important}.m-1{margin:.25rem !important}.m-2{margin:.5rem !important}.m-3{margin:1rem !important}.m-4{margin:1.5rem !important}.m-5{margin:3rem !important}.m-auto{margin:auto !important}.mx-0{margin-right:0 !important;margin-left:0 !important}.mx-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-3{margin-right:1rem !important;margin-left:1rem !important}.mx-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-5{margin-right:3rem !important;margin-left:3rem !important}.mx-auto{margin-right:auto !important;margin-left:auto !important}.my-0{margin-top:0 !important;margin-bottom:0 !important}.my-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-0{margin-top:0 !important}.mt-1{margin-top:.25rem !important}.mt-2{margin-top:.5rem !important}.mt-3{margin-top:1rem !important}.mt-4{margin-top:1.5rem !important}.mt-5{margin-top:3rem !important}.mt-auto{margin-top:auto !important}.me-0{margin-right:0 !important}.me-1{margin-right:.25rem !important}.me-2{margin-right:.5rem !important}.me-3{margin-right:1rem !important}.me-4{margin-right:1.5rem !important}.me-5{margin-right:3rem !important}.me-auto{margin-right:auto !important}.mb-0{margin-bottom:0 !important}.mb-1{margin-bottom:.25rem !important}.mb-2{margin-bottom:.5rem !important}.mb-3{margin-bottom:1rem !important}.mb-4{margin-bottom:1.5rem !important}.mb-5{margin-bottom:3rem !important}.mb-auto{margin-bottom:auto !important}.ms-0{margin-left:0 !important}.ms-1{margin-left:.25rem !important}.ms-2{margin-left:.5rem !important}.ms-3{margin-left:1rem !important}.ms-4{margin-left:1.5rem !important}.ms-5{margin-left:3rem !important}.ms-auto{margin-left:auto !important}.p-0{padding:0 !important}.p-1{padding:.25rem !important}.p-2{padding:.5rem !important}.p-3{padding:1rem !important}.p-4{padding:1.5rem !important}.p-5{padding:3rem !important}.px-0{padding-right:0 !important;padding-left:0 !important}.px-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-3{padding-right:1rem !important;padding-left:1rem !important}.px-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-5{padding-right:3rem !important;padding-left:3rem !important}.py-0{padding-top:0 !important;padding-bottom:0 !important}.py-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-0{padding-top:0 !important}.pt-1{padding-top:.25rem !important}.pt-2{padding-top:.5rem !important}.pt-3{padding-top:1rem !important}.pt-4{padding-top:1.5rem !important}.pt-5{padding-top:3rem !important}.pe-0{padding-right:0 !important}.pe-1{padding-right:.25rem !important}.pe-2{padding-right:.5rem !important}.pe-3{padding-right:1rem !important}.pe-4{padding-right:1.5rem !important}.pe-5{padding-right:3rem !important}.pb-0{padding-bottom:0 !important}.pb-1{padding-bottom:.25rem !important}.pb-2{padding-bottom:.5rem !important}.pb-3{padding-bottom:1rem !important}.pb-4{padding-bottom:1.5rem !important}.pb-5{padding-bottom:3rem !important}.ps-0{padding-left:0 !important}.ps-1{padding-left:.25rem !important}.ps-2{padding-left:.5rem !important}.ps-3{padding-left:1rem !important}.ps-4{padding-left:1.5rem !important}.ps-5{padding-left:3rem !important}@media (min-width: 576px){.d-sm-inline{display:inline !important}.d-sm-inline-block{display:inline-block !important}.d-sm-block{display:block !important}.d-sm-grid{display:grid !important}.d-sm-table{display:table !important}.d-sm-table-row{display:table-row !important}.d-sm-table-cell{display:table-cell !important}.d-sm-flex{display:flex !important}.d-sm-inline-flex{display:inline-flex !important}.d-sm-none{display:none !important}.flex-sm-fill{flex:1 1 auto !important}.flex-sm-row{flex-direction:row !important}.flex-sm-column{flex-direction:column !important}.flex-sm-row-reverse{flex-direction:row-reverse !important}.flex-sm-column-reverse{flex-direction:column-reverse !important}.flex-sm-grow-0{flex-grow:0 !important}.flex-sm-grow-1{flex-grow:1 !important}.flex-sm-shrink-0{flex-shrink:0 !important}.flex-sm-shrink-1{flex-shrink:1 !important}.flex-sm-wrap{flex-wrap:wrap !important}.flex-sm-nowrap{flex-wrap:nowrap !important}.flex-sm-wrap-reverse{flex-wrap:wrap-reverse !important}.justify-content-sm-start{justify-content:flex-start !important}.justify-content-sm-end{justify-content:flex-end !important}.justify-content-sm-center{justify-content:center !important}.justify-content-sm-between{justify-content:space-between !important}.justify-content-sm-around{justify-content:space-around !important}.justify-content-sm-evenly{justify-content:space-evenly !important}.align-items-sm-start{align-items:flex-start !important}.align-items-sm-end{align-items:flex-end !important}.align-items-sm-center{align-items:center !important}.align-items-sm-baseline{align-items:baseline !important}.align-items-sm-stretch{align-items:stretch !important}.align-content-sm-start{align-content:flex-start !important}.align-content-sm-end{align-content:flex-end !important}.align-content-sm-center{align-content:center !important}.align-content-sm-between{align-content:space-between !important}.align-content-sm-around{align-content:space-around !important}.align-content-sm-stretch{align-content:stretch !important}.align-self-sm-auto{align-self:auto !important}.align-self-sm-start{align-self:flex-start !important}.align-self-sm-end{align-self:flex-end !important}.align-self-sm-center{align-self:center !important}.align-self-sm-baseline{align-self:baseline !important}.align-self-sm-stretch{align-self:stretch !important}.order-sm-first{order:-1 !important}.order-sm-0{order:0 !important}.order-sm-1{order:1 !important}.order-sm-2{order:2 !important}.order-sm-3{order:3 !important}.order-sm-4{order:4 !important}.order-sm-5{order:5 !important}.order-sm-last{order:6 !important}.m-sm-0{margin:0 !important}.m-sm-1{margin:.25rem !important}.m-sm-2{margin:.5rem !important}.m-sm-3{margin:1rem !important}.m-sm-4{margin:1.5rem !important}.m-sm-5{margin:3rem !important}.m-sm-auto{margin:auto !important}.mx-sm-0{margin-right:0 !important;margin-left:0 !important}.mx-sm-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-sm-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-sm-3{margin-right:1rem !important;margin-left:1rem !important}.mx-sm-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-sm-5{margin-right:3rem !important;margin-left:3rem !important}.mx-sm-auto{margin-right:auto !important;margin-left:auto !important}.my-sm-0{margin-top:0 !important;margin-bottom:0 !important}.my-sm-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-sm-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-sm-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-sm-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-sm-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-sm-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-sm-0{margin-top:0 !important}.mt-sm-1{margin-top:.25rem !important}.mt-sm-2{margin-top:.5rem !important}.mt-sm-3{margin-top:1rem !important}.mt-sm-4{margin-top:1.5rem !important}.mt-sm-5{margin-top:3rem !important}.mt-sm-auto{margin-top:auto !important}.me-sm-0{margin-right:0 !important}.me-sm-1{margin-right:.25rem !important}.me-sm-2{margin-right:.5rem !important}.me-sm-3{margin-right:1rem !important}.me-sm-4{margin-right:1.5rem !important}.me-sm-5{margin-right:3rem !important}.me-sm-auto{margin-right:auto !important}.mb-sm-0{margin-bottom:0 !important}.mb-sm-1{margin-bottom:.25rem !important}.mb-sm-2{margin-bottom:.5rem !important}.mb-sm-3{margin-bottom:1rem !important}.mb-sm-4{margin-bottom:1.5rem !important}.mb-sm-5{margin-bottom:3rem !important}.mb-sm-auto{margin-bottom:auto !important}.ms-sm-0{margin-left:0 !important}.ms-sm-1{margin-left:.25rem !important}.ms-sm-2{margin-left:.5rem !important}.ms-sm-3{margin-left:1rem !important}.ms-sm-4{margin-left:1.5rem !important}.ms-sm-5{margin-left:3rem !important}.ms-sm-auto{margin-left:auto !important}.p-sm-0{padding:0 !important}.p-sm-1{padding:.25rem !important}.p-sm-2{padding:.5rem !important}.p-sm-3{padding:1rem !important}.p-sm-4{padding:1.5rem !important}.p-sm-5{padding:3rem !important}.px-sm-0{padding-right:0 !important;padding-left:0 !important}.px-sm-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-sm-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-sm-3{padding-right:1rem !important;padding-left:1rem !important}.px-sm-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-sm-5{padding-right:3rem !important;padding-left:3rem !important}.py-sm-0{padding-top:0 !important;padding-bottom:0 !important}.py-sm-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-sm-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-sm-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-sm-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-sm-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-sm-0{padding-top:0 !important}.pt-sm-1{padding-top:.25rem !important}.pt-sm-2{padding-top:.5rem !important}.pt-sm-3{padding-top:1rem !important}.pt-sm-4{padding-top:1.5rem !important}.pt-sm-5{padding-top:3rem !important}.pe-sm-0{padding-right:0 !important}.pe-sm-1{padding-right:.25rem !important}.pe-sm-2{padding-right:.5rem !important}.pe-sm-3{padding-right:1rem !important}.pe-sm-4{padding-right:1.5rem !important}.pe-sm-5{padding-right:3rem !important}.pb-sm-0{padding-bottom:0 !important}.pb-sm-1{padding-bottom:.25rem !important}.pb-sm-2{padding-bottom:.5rem !important}.pb-sm-3{padding-bottom:1rem !important}.pb-sm-4{padding-bottom:1.5rem !important}.pb-sm-5{padding-bottom:3rem !important}.ps-sm-0{padding-left:0 !important}.ps-sm-1{padding-left:.25rem !important}.ps-sm-2{padding-left:.5rem !important}.ps-sm-3{padding-left:1rem !important}.ps-sm-4{padding-left:1.5rem !important}.ps-sm-5{padding-left:3rem !important}}@media (min-width: 768px){.d-md-inline{display:inline !important}.d-md-inline-block{display:inline-block !important}.d-md-block{display:block !important}.d-md-grid{display:grid !important}.d-md-table{display:table !important}.d-md-table-row{display:table-row !important}.d-md-table-cell{display:table-cell !important}.d-md-flex{display:flex !important}.d-md-inline-flex{display:inline-flex !important}.d-md-none{display:none !important}.flex-md-fill{flex:1 1 auto !important}.flex-md-row{flex-direction:row !important}.flex-md-column{flex-direction:column !important}.flex-md-row-reverse{flex-direction:row-reverse !important}.flex-md-column-reverse{flex-direction:column-reverse !important}.flex-md-grow-0{flex-grow:0 !important}.flex-md-grow-1{flex-grow:1 !important}.flex-md-shrink-0{flex-shrink:0 !important}.flex-md-shrink-1{flex-shrink:1 !important}.flex-md-wrap{flex-wrap:wrap !important}.flex-md-nowrap{flex-wrap:nowrap !important}.flex-md-wrap-reverse{flex-wrap:wrap-reverse !important}.justify-content-md-start{justify-content:flex-start !important}.justify-content-md-end{justify-content:flex-end !important}.justify-content-md-center{justify-content:center !important}.justify-content-md-between{justify-content:space-between !important}.justify-content-md-around{justify-content:space-around !important}.justify-content-md-evenly{justify-content:space-evenly !important}.align-items-md-start{align-items:flex-start !important}.align-items-md-end{align-items:flex-end !important}.align-items-md-center{align-items:center !important}.align-items-md-baseline{align-items:baseline !important}.align-items-md-stretch{align-items:stretch !important}.align-content-md-start{align-content:flex-start !important}.align-content-md-end{align-content:flex-end !important}.align-content-md-center{align-content:center !important}.align-content-md-between{align-content:space-between !important}.align-content-md-around{align-content:space-around !important}.align-content-md-stretch{align-content:stretch !important}.align-self-md-auto{align-self:auto !important}.align-self-md-start{align-self:flex-start !important}.align-self-md-end{align-self:flex-end !important}.align-self-md-center{align-self:center !important}.align-self-md-baseline{align-self:baseline !important}.align-self-md-stretch{align-self:stretch !important}.order-md-first{order:-1 !important}.order-md-0{order:0 !important}.order-md-1{order:1 !important}.order-md-2{order:2 !important}.order-md-3{order:3 !important}.order-md-4{order:4 !important}.order-md-5{order:5 !important}.order-md-last{order:6 !important}.m-md-0{margin:0 !important}.m-md-1{margin:.25rem !important}.m-md-2{margin:.5rem !important}.m-md-3{margin:1rem !important}.m-md-4{margin:1.5rem !important}.m-md-5{margin:3rem !important}.m-md-auto{margin:auto !important}.mx-md-0{margin-right:0 !important;margin-left:0 !important}.mx-md-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-md-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-md-3{margin-right:1rem !important;margin-left:1rem !important}.mx-md-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-md-5{margin-right:3rem !important;margin-left:3rem !important}.mx-md-auto{margin-right:auto !important;margin-left:auto !important}.my-md-0{margin-top:0 !important;margin-bottom:0 !important}.my-md-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-md-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-md-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-md-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-md-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-md-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-md-0{margin-top:0 !important}.mt-md-1{margin-top:.25rem !important}.mt-md-2{margin-top:.5rem !important}.mt-md-3{margin-top:1rem !important}.mt-md-4{margin-top:1.5rem !important}.mt-md-5{margin-top:3rem !important}.mt-md-auto{margin-top:auto !important}.me-md-0{margin-right:0 !important}.me-md-1{margin-right:.25rem !important}.me-md-2{margin-right:.5rem !important}.me-md-3{margin-right:1rem !important}.me-md-4{margin-right:1.5rem !important}.me-md-5{margin-right:3rem !important}.me-md-auto{margin-right:auto !important}.mb-md-0{margin-bottom:0 !important}.mb-md-1{margin-bottom:.25rem !important}.mb-md-2{margin-bottom:.5rem !important}.mb-md-3{margin-bottom:1rem !important}.mb-md-4{margin-bottom:1.5rem !important}.mb-md-5{margin-bottom:3rem !important}.mb-md-auto{margin-bottom:auto !important}.ms-md-0{margin-left:0 !important}.ms-md-1{margin-left:.25rem !important}.ms-md-2{margin-left:.5rem !important}.ms-md-3{margin-left:1rem !important}.ms-md-4{margin-left:1.5rem !important}.ms-md-5{margin-left:3rem !important}.ms-md-auto{margin-left:auto !important}.p-md-0{padding:0 !important}.p-md-1{padding:.25rem !important}.p-md-2{padding:.5rem !important}.p-md-3{padding:1rem !important}.p-md-4{padding:1.5rem !important}.p-md-5{padding:3rem !important}.px-md-0{padding-right:0 !important;padding-left:0 !important}.px-md-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-md-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-md-3{padding-right:1rem !important;padding-left:1rem !important}.px-md-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-md-5{padding-right:3rem !important;padding-left:3rem !important}.py-md-0{padding-top:0 !important;padding-bottom:0 !important}.py-md-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-md-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-md-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-md-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-md-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-md-0{padding-top:0 !important}.pt-md-1{padding-top:.25rem !important}.pt-md-2{padding-top:.5rem !important}.pt-md-3{padding-top:1rem !important}.pt-md-4{padding-top:1.5rem !important}.pt-md-5{padding-top:3rem !important}.pe-md-0{padding-right:0 !important}.pe-md-1{padding-right:.25rem !important}.pe-md-2{padding-right:.5rem !important}.pe-md-3{padding-right:1rem !important}.pe-md-4{padding-right:1.5rem !important}.pe-md-5{padding-right:3rem !important}.pb-md-0{padding-bottom:0 !important}.pb-md-1{padding-bottom:.25rem !important}.pb-md-2{padding-bottom:.5rem !important}.pb-md-3{padding-bottom:1rem !important}.pb-md-4{padding-bottom:1.5rem !important}.pb-md-5{padding-bottom:3rem !important}.ps-md-0{padding-left:0 !important}.ps-md-1{padding-left:.25rem !important}.ps-md-2{padding-left:.5rem !important}.ps-md-3{padding-left:1rem !important}.ps-md-4{padding-left:1.5rem !important}.ps-md-5{padding-left:3rem !important}}@media (min-width: 992px){.d-lg-inline{display:inline !important}.d-lg-inline-block{display:inline-block !important}.d-lg-block{display:block !important}.d-lg-grid{display:grid !important}.d-lg-table{display:table !important}.d-lg-table-row{display:table-row !important}.d-lg-table-cell{display:table-cell !important}.d-lg-flex{display:flex !important}.d-lg-inline-flex{display:inline-flex !important}.d-lg-none{display:none !important}.flex-lg-fill{flex:1 1 auto !important}.flex-lg-row{flex-direction:row !important}.flex-lg-column{flex-direction:column !important}.flex-lg-row-reverse{flex-direction:row-reverse !important}.flex-lg-column-reverse{flex-direction:column-reverse !important}.flex-lg-grow-0{flex-grow:0 !important}.flex-lg-grow-1{flex-grow:1 !important}.flex-lg-shrink-0{flex-shrink:0 !important}.flex-lg-shrink-1{flex-shrink:1 !important}.flex-lg-wrap{flex-wrap:wrap !important}.flex-lg-nowrap{flex-wrap:nowrap !important}.flex-lg-wrap-reverse{flex-wrap:wrap-reverse !important}.justify-content-lg-start{justify-content:flex-start !important}.justify-content-lg-end{justify-content:flex-end !important}.justify-content-lg-center{justify-content:center !important}.justify-content-lg-between{justify-content:space-between !important}.justify-content-lg-around{justify-content:space-around !important}.justify-content-lg-evenly{justify-content:space-evenly !important}.align-items-lg-start{align-items:flex-start !important}.align-items-lg-end{align-items:flex-end !important}.align-items-lg-center{align-items:center !important}.align-items-lg-baseline{align-items:baseline !important}.align-items-lg-stretch{align-items:stretch !important}.align-content-lg-start{align-content:flex-start !important}.align-content-lg-end{align-content:flex-end !important}.align-content-lg-center{align-content:center !important}.align-content-lg-between{align-content:space-between !important}.align-content-lg-around{align-content:space-around !important}.align-content-lg-stretch{align-content:stretch !important}.align-self-lg-auto{align-self:auto !important}.align-self-lg-start{align-self:flex-start !important}.align-self-lg-end{align-self:flex-end !important}.align-self-lg-center{align-self:center !important}.align-self-lg-baseline{align-self:baseline !important}.align-self-lg-stretch{align-self:stretch !important}.order-lg-first{order:-1 !important}.order-lg-0{order:0 !important}.order-lg-1{order:1 !important}.order-lg-2{order:2 !important}.order-lg-3{order:3 !important}.order-lg-4{order:4 !important}.order-lg-5{order:5 !important}.order-lg-last{order:6 !important}.m-lg-0{margin:0 !important}.m-lg-1{margin:.25rem !important}.m-lg-2{margin:.5rem !important}.m-lg-3{margin:1rem !important}.m-lg-4{margin:1.5rem !important}.m-lg-5{margin:3rem !important}.m-lg-auto{margin:auto !important}.mx-lg-0{margin-right:0 !important;margin-left:0 !important}.mx-lg-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-lg-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-lg-3{margin-right:1rem !important;margin-left:1rem !important}.mx-lg-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-lg-5{margin-right:3rem !important;margin-left:3rem !important}.mx-lg-auto{margin-right:auto !important;margin-left:auto !important}.my-lg-0{margin-top:0 !important;margin-bottom:0 !important}.my-lg-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-lg-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-lg-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-lg-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-lg-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-lg-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-lg-0{margin-top:0 !important}.mt-lg-1{margin-top:.25rem !important}.mt-lg-2{margin-top:.5rem !important}.mt-lg-3{margin-top:1rem !important}.mt-lg-4{margin-top:1.5rem !important}.mt-lg-5{margin-top:3rem !important}.mt-lg-auto{margin-top:auto !important}.me-lg-0{margin-right:0 !important}.me-lg-1{margin-right:.25rem !important}.me-lg-2{margin-right:.5rem !important}.me-lg-3{margin-right:1rem !important}.me-lg-4{margin-right:1.5rem !important}.me-lg-5{margin-right:3rem !important}.me-lg-auto{margin-right:auto !important}.mb-lg-0{margin-bottom:0 !important}.mb-lg-1{margin-bottom:.25rem !important}.mb-lg-2{margin-bottom:.5rem !important}.mb-lg-3{margin-bottom:1rem !important}.mb-lg-4{margin-bottom:1.5rem !important}.mb-lg-5{margin-bottom:3rem !important}.mb-lg-auto{margin-bottom:auto !important}.ms-lg-0{margin-left:0 !important}.ms-lg-1{margin-left:.25rem !important}.ms-lg-2{margin-left:.5rem !important}.ms-lg-3{margin-left:1rem !important}.ms-lg-4{margin-left:1.5rem !important}.ms-lg-5{margin-left:3rem !important}.ms-lg-auto{margin-left:auto !important}.p-lg-0{padding:0 !important}.p-lg-1{padding:.25rem !important}.p-lg-2{padding:.5rem !important}.p-lg-3{padding:1rem !important}.p-lg-4{padding:1.5rem !important}.p-lg-5{padding:3rem !important}.px-lg-0{padding-right:0 !important;padding-left:0 !important}.px-lg-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-lg-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-lg-3{padding-right:1rem !important;padding-left:1rem !important}.px-lg-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-lg-5{padding-right:3rem !important;padding-left:3rem !important}.py-lg-0{padding-top:0 !important;padding-bottom:0 !important}.py-lg-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-lg-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-lg-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-lg-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-lg-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-lg-0{padding-top:0 !important}.pt-lg-1{padding-top:.25rem !important}.pt-lg-2{padding-top:.5rem !important}.pt-lg-3{padding-top:1rem !important}.pt-lg-4{padding-top:1.5rem !important}.pt-lg-5{padding-top:3rem !important}.pe-lg-0{padding-right:0 !important}.pe-lg-1{padding-right:.25rem !important}.pe-lg-2{padding-right:.5rem !important}.pe-lg-3{padding-right:1rem !important}.pe-lg-4{padding-right:1.5rem !important}.pe-lg-5{padding-right:3rem !important}.pb-lg-0{padding-bottom:0 !important}.pb-lg-1{padding-bottom:.25rem !important}.pb-lg-2{padding-bottom:.5rem !important}.pb-lg-3{padding-bottom:1rem !important}.pb-lg-4{padding-bottom:1.5rem !important}.pb-lg-5{padding-bottom:3rem !important}.ps-lg-0{padding-left:0 !important}.ps-lg-1{padding-left:.25rem !important}.ps-lg-2{padding-left:.5rem !important}.ps-lg-3{padding-left:1rem !important}.ps-lg-4{padding-left:1.5rem !important}.ps-lg-5{padding-left:3rem !important}}@media (min-width: 1200px){.d-xl-inline{display:inline !important}.d-xl-inline-block{display:inline-block !important}.d-xl-block{display:block !important}.d-xl-grid{display:grid !important}.d-xl-table{display:table !important}.d-xl-table-row{display:table-row !important}.d-xl-table-cell{display:table-cell !important}.d-xl-flex{display:flex !important}.d-xl-inline-flex{display:inline-flex !important}.d-xl-none{display:none !important}.flex-xl-fill{flex:1 1 auto !important}.flex-xl-row{flex-direction:row !important}.flex-xl-column{flex-direction:column !important}.flex-xl-row-reverse{flex-direction:row-reverse !important}.flex-xl-column-reverse{flex-direction:column-reverse !important}.flex-xl-grow-0{flex-grow:0 !important}.flex-xl-grow-1{flex-grow:1 !important}.flex-xl-shrink-0{flex-shrink:0 !important}.flex-xl-shrink-1{flex-shrink:1 !important}.flex-xl-wrap{flex-wrap:wrap !important}.flex-xl-nowrap{flex-wrap:nowrap !important}.flex-xl-wrap-reverse{flex-wrap:wrap-reverse !important}.justify-content-xl-start{justify-content:flex-start !important}.justify-content-xl-end{justify-content:flex-end !important}.justify-content-xl-center{justify-content:center !important}.justify-content-xl-between{justify-content:space-between !important}.justify-content-xl-around{justify-content:space-around !important}.justify-content-xl-evenly{justify-content:space-evenly !important}.align-items-xl-start{align-items:flex-start !important}.align-items-xl-end{align-items:flex-end !important}.align-items-xl-center{align-items:center !important}.align-items-xl-baseline{align-items:baseline !important}.align-items-xl-stretch{align-items:stretch !important}.align-content-xl-start{align-content:flex-start !important}.align-content-xl-end{align-content:flex-end !important}.align-content-xl-center{align-content:center !important}.align-content-xl-between{align-content:space-between !important}.align-content-xl-around{align-content:space-around !important}.align-content-xl-stretch{align-content:stretch !important}.align-self-xl-auto{align-self:auto !important}.align-self-xl-start{align-self:flex-start !important}.align-self-xl-end{align-self:flex-end !important}.align-self-xl-center{align-self:center !important}.align-self-xl-baseline{align-self:baseline !important}.align-self-xl-stretch{align-self:stretch !important}.order-xl-first{order:-1 !important}.order-xl-0{order:0 !important}.order-xl-1{order:1 !important}.order-xl-2{order:2 !important}.order-xl-3{order:3 !important}.order-xl-4{order:4 !important}.order-xl-5{order:5 !important}.order-xl-last{order:6 !important}.m-xl-0{margin:0 !important}.m-xl-1{margin:.25rem !important}.m-xl-2{margin:.5rem !important}.m-xl-3{margin:1rem !important}.m-xl-4{margin:1.5rem !important}.m-xl-5{margin:3rem !important}.m-xl-auto{margin:auto !important}.mx-xl-0{margin-right:0 !important;margin-left:0 !important}.mx-xl-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-xl-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-xl-3{margin-right:1rem !important;margin-left:1rem !important}.mx-xl-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-xl-5{margin-right:3rem !important;margin-left:3rem !important}.mx-xl-auto{margin-right:auto !important;margin-left:auto !important}.my-xl-0{margin-top:0 !important;margin-bottom:0 !important}.my-xl-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-xl-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-xl-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-xl-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-xl-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-xl-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-xl-0{margin-top:0 !important}.mt-xl-1{margin-top:.25rem !important}.mt-xl-2{margin-top:.5rem !important}.mt-xl-3{margin-top:1rem !important}.mt-xl-4{margin-top:1.5rem !important}.mt-xl-5{margin-top:3rem !important}.mt-xl-auto{margin-top:auto !important}.me-xl-0{margin-right:0 !important}.me-xl-1{margin-right:.25rem !important}.me-xl-2{margin-right:.5rem !important}.me-xl-3{margin-right:1rem !important}.me-xl-4{margin-right:1.5rem !important}.me-xl-5{margin-right:3rem !important}.me-xl-auto{margin-right:auto !important}.mb-xl-0{margin-bottom:0 !important}.mb-xl-1{margin-bottom:.25rem !important}.mb-xl-2{margin-bottom:.5rem !important}.mb-xl-3{margin-bottom:1rem !important}.mb-xl-4{margin-bottom:1.5rem !important}.mb-xl-5{margin-bottom:3rem !important}.mb-xl-auto{margin-bottom:auto !important}.ms-xl-0{margin-left:0 !important}.ms-xl-1{margin-left:.25rem !important}.ms-xl-2{margin-left:.5rem !important}.ms-xl-3{margin-left:1rem !important}.ms-xl-4{margin-left:1.5rem !important}.ms-xl-5{margin-left:3rem !important}.ms-xl-auto{margin-left:auto !important}.p-xl-0{padding:0 !important}.p-xl-1{padding:.25rem !important}.p-xl-2{padding:.5rem !important}.p-xl-3{padding:1rem !important}.p-xl-4{padding:1.5rem !important}.p-xl-5{padding:3rem !important}.px-xl-0{padding-right:0 !important;padding-left:0 !important}.px-xl-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-xl-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-xl-3{padding-right:1rem !important;padding-left:1rem !important}.px-xl-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-xl-5{padding-right:3rem !important;padding-left:3rem !important}.py-xl-0{padding-top:0 !important;padding-bottom:0 !important}.py-xl-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-xl-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-xl-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-xl-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-xl-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-xl-0{padding-top:0 !important}.pt-xl-1{padding-top:.25rem !important}.pt-xl-2{padding-top:.5rem !important}.pt-xl-3{padding-top:1rem !important}.pt-xl-4{padding-top:1.5rem !important}.pt-xl-5{padding-top:3rem !important}.pe-xl-0{padding-right:0 !important}.pe-xl-1{padding-right:.25rem !important}.pe-xl-2{padding-right:.5rem !important}.pe-xl-3{padding-right:1rem !important}.pe-xl-4{padding-right:1.5rem !important}.pe-xl-5{padding-right:3rem !important}.pb-xl-0{padding-bottom:0 !important}.pb-xl-1{padding-bottom:.25rem !important}.pb-xl-2{padding-bottom:.5rem !important}.pb-xl-3{padding-bottom:1rem !important}.pb-xl-4{padding-bottom:1.5rem !important}.pb-xl-5{padding-bottom:3rem !important}.ps-xl-0{padding-left:0 !important}.ps-xl-1{padding-left:.25rem !important}.ps-xl-2{padding-left:.5rem !important}.ps-xl-3{padding-left:1rem !important}.ps-xl-4{padding-left:1.5rem !important}.ps-xl-5{padding-left:3rem !important}}@media (min-width: 1400px){.d-xxl-inline{display:inline !important}.d-xxl-inline-block{display:inline-block !important}.d-xxl-block{display:block !important}.d-xxl-grid{display:grid !important}.d-xxl-table{display:table !important}.d-xxl-table-row{display:table-row !important}.d-xxl-table-cell{display:table-cell !important}.d-xxl-flex{display:flex !important}.d-xxl-inline-flex{display:inline-flex !important}.d-xxl-none{display:none !important}.flex-xxl-fill{flex:1 1 auto !important}.flex-xxl-row{flex-direction:row !important}.flex-xxl-column{flex-direction:column !important}.flex-xxl-row-reverse{flex-direction:row-reverse !important}.flex-xxl-column-reverse{flex-direction:column-reverse !important}.flex-xxl-grow-0{flex-grow:0 !important}.flex-xxl-grow-1{flex-grow:1 !important}.flex-xxl-shrink-0{flex-shrink:0 !important}.flex-xxl-shrink-1{flex-shrink:1 !important}.flex-xxl-wrap{flex-wrap:wrap !important}.flex-xxl-nowrap{flex-wrap:nowrap !important}.flex-xxl-wrap-reverse{flex-wrap:wrap-reverse !important}.justify-content-xxl-start{justify-content:flex-start !important}.justify-content-xxl-end{justify-content:flex-end !important}.justify-content-xxl-center{justify-content:center !important}.justify-content-xxl-between{justify-content:space-between !important}.justify-content-xxl-around{justify-content:space-around !important}.justify-content-xxl-evenly{justify-content:space-evenly !important}.align-items-xxl-start{align-items:flex-start !important}.align-items-xxl-end{align-items:flex-end !important}.align-items-xxl-center{align-items:center !important}.align-items-xxl-baseline{align-items:baseline !important}.align-items-xxl-stretch{align-items:stretch !important}.align-content-xxl-start{align-content:flex-start !important}.align-content-xxl-end{align-content:flex-end !important}.align-content-xxl-center{align-content:center !important}.align-content-xxl-between{align-content:space-between !important}.align-content-xxl-around{align-content:space-around !important}.align-content-xxl-stretch{align-content:stretch !important}.align-self-xxl-auto{align-self:auto !important}.align-self-xxl-start{align-self:flex-start !important}.align-self-xxl-end{align-self:flex-end !important}.align-self-xxl-center{align-self:center !important}.align-self-xxl-baseline{align-self:baseline !important}.align-self-xxl-stretch{align-self:stretch !important}.order-xxl-first{order:-1 !important}.order-xxl-0{order:0 !important}.order-xxl-1{order:1 !important}.order-xxl-2{order:2 !important}.order-xxl-3{order:3 !important}.order-xxl-4{order:4 !important}.order-xxl-5{order:5 !important}.order-xxl-last{order:6 !important}.m-xxl-0{margin:0 !important}.m-xxl-1{margin:.25rem !important}.m-xxl-2{margin:.5rem !important}.m-xxl-3{margin:1rem !important}.m-xxl-4{margin:1.5rem !important}.m-xxl-5{margin:3rem !important}.m-xxl-auto{margin:auto !important}.mx-xxl-0{margin-right:0 !important;margin-left:0 !important}.mx-xxl-1{margin-right:.25rem !important;margin-left:.25rem !important}.mx-xxl-2{margin-right:.5rem !important;margin-left:.5rem !important}.mx-xxl-3{margin-right:1rem !important;margin-left:1rem !important}.mx-xxl-4{margin-right:1.5rem !important;margin-left:1.5rem !important}.mx-xxl-5{margin-right:3rem !important;margin-left:3rem !important}.mx-xxl-auto{margin-right:auto !important;margin-left:auto !important}.my-xxl-0{margin-top:0 !important;margin-bottom:0 !important}.my-xxl-1{margin-top:.25rem !important;margin-bottom:.25rem !important}.my-xxl-2{margin-top:.5rem !important;margin-bottom:.5rem !important}.my-xxl-3{margin-top:1rem !important;margin-bottom:1rem !important}.my-xxl-4{margin-top:1.5rem !important;margin-bottom:1.5rem !important}.my-xxl-5{margin-top:3rem !important;margin-bottom:3rem !important}.my-xxl-auto{margin-top:auto !important;margin-bottom:auto !important}.mt-xxl-0{margin-top:0 !important}.mt-xxl-1{margin-top:.25rem !important}.mt-xxl-2{margin-top:.5rem !important}.mt-xxl-3{margin-top:1rem !important}.mt-xxl-4{margin-top:1.5rem !important}.mt-xxl-5{margin-top:3rem !important}.mt-xxl-auto{margin-top:auto !important}.me-xxl-0{margin-right:0 !important}.me-xxl-1{margin-right:.25rem !important}.me-xxl-2{margin-right:.5rem !important}.me-xxl-3{margin-right:1rem !important}.me-xxl-4{margin-right:1.5rem !important}.me-xxl-5{margin-right:3rem !important}.me-xxl-auto{margin-right:auto !important}.mb-xxl-0{margin-bottom:0 !important}.mb-xxl-1{margin-bottom:.25rem !important}.mb-xxl-2{margin-bottom:.5rem !important}.mb-xxl-3{margin-bottom:1rem !important}.mb-xxl-4{margin-bottom:1.5rem !important}.mb-xxl-5{margin-bottom:3rem !important}.mb-xxl-auto{margin-bottom:auto !important}.ms-xxl-0{margin-left:0 !important}.ms-xxl-1{margin-left:.25rem !important}.ms-xxl-2{margin-left:.5rem !important}.ms-xxl-3{margin-left:1rem !important}.ms-xxl-4{margin-left:1.5rem !important}.ms-xxl-5{margin-left:3rem !important}.ms-xxl-auto{margin-left:auto !important}.p-xxl-0{padding:0 !important}.p-xxl-1{padding:.25rem !important}.p-xxl-2{padding:.5rem !important}.p-xxl-3{padding:1rem !important}.p-xxl-4{padding:1.5rem !important}.p-xxl-5{padding:3rem !important}.px-xxl-0{padding-right:0 !important;padding-left:0 !important}.px-xxl-1{padding-right:.25rem !important;padding-left:.25rem !important}.px-xxl-2{padding-right:.5rem !important;padding-left:.5rem !important}.px-xxl-3{padding-right:1rem !important;padding-left:1rem !important}.px-xxl-4{padding-right:1.5rem !important;padding-left:1.5rem !important}.px-xxl-5{padding-right:3rem !important;padding-left:3rem !important}.py-xxl-0{padding-top:0 !important;padding-bottom:0 !important}.py-xxl-1{padding-top:.25rem !important;padding-bottom:.25rem !important}.py-xxl-2{padding-top:.5rem !important;padding-bottom:.5rem !important}.py-xxl-3{padding-top:1rem !important;padding-bottom:1rem !important}.py-xxl-4{padding-top:1.5rem !important;padding-bottom:1.5rem !important}.py-xxl-5{padding-top:3rem !important;padding-bottom:3rem !important}.pt-xxl-0{padding-top:0 !important}.pt-xxl-1{padding-top:.25rem !important}.pt-xxl-2{padding-top:.5rem !important}.pt-xxl-3{padding-top:1rem !important}.pt-xxl-4{padding-top:1.5rem !important}.pt-xxl-5{padding-top:3rem !important}.pe-xxl-0{padding-right:0 !important}.pe-xxl-1{padding-right:.25rem !important}.pe-xxl-2{padding-right:.5rem !important}.pe-xxl-3{padding-right:1rem !important}.pe-xxl-4{padding-right:1.5rem !important}.pe-xxl-5{padding-right:3rem !important}.pb-xxl-0{padding-bottom:0 !important}.pb-xxl-1{padding-bottom:.25rem !important}.pb-xxl-2{padding-bottom:.5rem !important}.pb-xxl-3{padding-bottom:1rem !important}.pb-xxl-4{padding-bottom:1.5rem !important}.pb-xxl-5{padding-bottom:3rem !important}.ps-xxl-0{padding-left:0 !important}.ps-xxl-1{padding-left:.25rem !important}.ps-xxl-2{padding-left:.5rem !important}.ps-xxl-3{padding-left:1rem !important}.ps-xxl-4{padding-left:1.5rem !important}.ps-xxl-5{padding-left:3rem !important}}@media print{.d-print-inline{display:inline !important}.d-print-inline-block{display:inline-block !important}.d-print-block{display:block !important}.d-print-grid{display:grid !important}.d-print-table{display:table !important}.d-print-table-row{display:table-row !important}.d-print-table-cell{display:table-cell !important}.d-print-flex{display:flex !important}.d-print-inline-flex{display:inline-flex !important}.d-print-none{display:none !important}}\n", ":root {\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color,\n  $value in $colors {\n    --#{$variable-prefix}#{$color}: #{$value};\n  }\n\n  @each $color,\n  $value in $grays {\n    --#{$variable-prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color,\n  $value in $theme-colors {\n    --#{$variable-prefix}#{$color}: #{$value};\n  }\n\n  @each $color,\n  $value in $theme-colors-rgb {\n    --#{$variable-prefix}#{$color}-rgb: #{$value};\n  }\n\n  --#{$variable-prefix}white-rgb: #{to-rgb($white)};\n  --#{$variable-prefix}black-rgb: #{to-rgb($black)};\n  --#{$variable-prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$variable-prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$variable-prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$variable-prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$variable-prefix}gradient: #{$gradient};\n\n  // Root and body\n  // stylelint-disable custom-property-empty-line-before\n  // scss-docs-start root-body-variables\n  @if $font-size-root !=null {\n    --#{$variable-prefix}root-font-size: #{$font-size-root};\n  }\n\n  --#{$variable-prefix}body-font-family: #{$font-family-base};\n  --#{$variable-prefix}body-font-size: #{$font-size-base};\n  --#{$variable-prefix}body-font-weight: #{$font-weight-base};\n  --#{$variable-prefix}body-line-height: #{$line-height-base};\n  --#{$variable-prefix}body-color: #{$body-color};\n\n  @if $body-text-align !=null {\n    --#{$variable-prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$variable-prefix}body-bg: #{$body-bg};\n  // scss-docs-end root-body-variables\n  // stylelint-enable custom-property-empty-line-before\n}", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  width: 100%;\n  padding-right: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  padding-left: var(--#{$variable-prefix}gutter-x, #{$gutter});\n  margin-right: auto;\n  margin-left: auto;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint,\n  $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name,\n      $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint==$name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white: #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black: #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue: #0d6efd !default;\n$indigo: #6610f2 !default;\n$purple: #6f42c1 !default;\n$pink: #d63384 !default;\n$red: #dc3545 !default;\n$orange: #fd7e14 !default;\n$yellow: #ffc107 !default;\n$green: #198754 !default;\n$teal: #20c997 !default;\n$cyan: #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\"blue\": $blue,\n  \"indigo\": $indigo,\n  \"purple\": $purple,\n  \"pink\": $pink,\n  \"red\": $red,\n  \"orange\": $orange,\n  \"yellow\": $yellow,\n  \"green\": $green,\n  \"teal\": $teal,\n  \"cyan\": $cyan,\n  \"white\": $white,\n  \"gray\": $gray-600,\n  \"gray-dark\": $gray-800) !default;\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary: $blue !default;\n$secondary: $gray-600 !default;\n$success: $green !default;\n$info: $cyan !default;\n$warning: $yellow !default;\n$danger: $red !default;\n$light: $gray-100 !default;\n$dark: $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\"primary\": $primary,\n  \"secondary\": $secondary,\n  \"success\": $success,\n  \"info\": $info,\n  \"warning\": $warning,\n  \"danger\": $danger,\n  \"light\": $light,\n  \"dark\": $dark) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\") !default;\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio: 4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark: $black !default;\n$color-contrast-light: $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900) !default;\n\n$indigos: (\"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900) !default;\n\n$purples: (\"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900) !default;\n\n$pinks: (\"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900) !default;\n\n$reds: (\"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900) !default;\n\n$oranges: (\"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900) !default;\n\n$yellows: (\"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900) !default;\n\n$greens: (\"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900) !default;\n\n$teals: (\"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900) !default;\n\n$cyans: (\"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: ((\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret: true !default;\n$enable-rounded: true !default;\n$enable-shadows: false !default;\n$enable-gradients: false !default;\n$enable-transitions: true !default;\n$enable-reduced-motion: true !default;\n$enable-smooth-scroll: true !default;\n$enable-grid-classes: true !default;\n$enable-cssgrid: false !default;\n$enable-button-pointers: true !default;\n$enable-rfs: true !default;\n$enable-validation-icons: true !default;\n$enable-negative-margins: false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities: true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix: bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (0: 0,\n  50: 50%,\n  100: 100%) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg: $white !default;\n$body-color: $gray-900 !default;\n$body-text-align: null !default;\n\n// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// Come v6, we'll de-dupe these variables. Until then, for backward compatibility, we keep them to reassign.\n// scss-docs-start utilities-colors\n$utilities-colors: $theme-colors-rgb !default;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text: map-merge($utilities-colors,\n  (\"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-color))) !default;\n$utilities-text-colors: map-loop($utilities-text, rgba-css-var, \"$key\", \"text\") !default;\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg: map-merge($utilities-colors,\n  (\"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-bg))) !default;\n$utilities-bg-colors: map-loop($utilities-bg, rgba-css-var, \"$key\", \"bg\") !default;\n// scss-docs-end utilities-bg-colors\n\n// Links\n//\n// Style anchor elements.\n\n$link-color: $primary !default;\n$link-decoration: underline !default;\n$link-shade-percentage: 20% !default;\n$link-hover-color: shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration: null !default;\n\n$stretched-link-pseudo-element: after !default;\n$stretched-link-z-index: 1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom: 1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns: 12 !default;\n$grid-gutter-width: 1.5rem !default;\n$grid-row-columns: 6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * .5 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width: 1px !default;\n$border-widths: (1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px) !default;\n\n$border-color: $gray-300 !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius: .25rem !default;\n$border-radius-sm: .2rem !default;\n$border-radius-lg: .3rem !default;\n$border-radius-pill: 50rem !default;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow: 0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm: 0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg: 0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset: inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color: $white !default;\n$component-active-bg: $primary !default;\n\n// scss-docs-start caret-variables\n$caret-width: .3em !default;\n$caret-vertical-align: $caret-width * .85 !default;\n$caret-spacing: $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base: all .2s ease-in-out !default;\n$transition-fade: opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse: height .35s ease !default;\n$transition-collapse-width: width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif: system-ui,\n-apple-system,\n\"Segoe UI\",\nRoboto,\n\"Helvetica Neue\",\nArial,\n\"Noto Sans\",\n\"Liberation Sans\",\nsans-serif,\n\"Apple Color Emoji\",\n\"Segoe UI Emoji\",\n\"Segoe UI Symbol\",\n\"Noto Color Emoji\" !default;\n$font-family-monospace: SFMono-Regular,\nMenlo,\nMonaco,\nConsolas,\n\"Liberation Mono\",\n\"Courier New\",\nmonospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base: var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code: var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root: null !default;\n$font-size-base: 1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm: $font-size-base * .875 !default;\n$font-size-lg: $font-size-base * 1.25 !default;\n\n$font-weight-lighter: lighter !default;\n$font-weight-light: 300 !default;\n$font-weight-normal: 400 !default;\n$font-weight-bold: 700 !default;\n$font-weight-bolder: bolder !default;\n\n$font-weight-base: $font-weight-normal !default;\n\n$line-height-base: 1.5 !default;\n$line-height-sm: 1.25 !default;\n$line-height-lg: 2 !default;\n\n$h1-font-size: $font-size-base * 2.5 !default;\n$h2-font-size: $font-size-base * 2 !default;\n$h3-font-size: $font-size-base * 1.75 !default;\n$h4-font-size: $font-size-base * 1.5 !default;\n$h5-font-size: $font-size-base * 1.25 !default;\n$h6-font-size: $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom: $spacer * .5 !default;\n$headings-font-family: null !default;\n$headings-font-style: null !default;\n$headings-font-weight: 500 !default;\n$headings-line-height: 1.2 !default;\n$headings-color: null !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size: $font-size-base * 1.25 !default;\n$lead-font-weight: 300 !default;\n\n$small-font-size: .875em !default;\n\n$sub-sup-font-size: .75em !default;\n\n$text-muted: $gray-600 !default;\n\n$initialism-font-size: $small-font-size !default;\n\n$blockquote-margin-y: $spacer !default;\n$blockquote-font-size: $font-size-base * 1.25 !default;\n$blockquote-footer-color: $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y: $spacer !default;\n$hr-color: inherit !default;\n$hr-height: $border-width !default;\n$hr-opacity: .25 !default;\n\n$legend-margin-bottom: .5rem !default;\n$legend-font-size: 1.5rem !default;\n$legend-font-weight: null !default;\n\n$mark-padding: .2em !default;\n\n$dt-font-weight: $font-weight-bold !default;\n\n$nested-kbd-font-weight: $font-weight-bold !default;\n\n$list-inline-padding: .5rem !default;\n\n$mark-bg: #fcf8e3 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y: .5rem !default;\n$table-cell-padding-x: .5rem !default;\n$table-cell-padding-y-sm: .25rem !default;\n$table-cell-padding-x-sm: .25rem !default;\n\n$table-cell-vertical-align: top !default;\n\n$table-color: $body-color !default;\n$table-bg: transparent !default;\n$table-accent-bg: transparent !default;\n\n$table-th-font-weight: null !default;\n\n$table-striped-color: $table-color !default;\n$table-striped-bg-factor: .05 !default;\n$table-striped-bg: rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color: $table-color !default;\n$table-active-bg-factor: .1 !default;\n$table-active-bg: rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color: $table-color !default;\n$table-hover-bg-factor: .075 !default;\n$table-hover-bg: rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor: .1 !default;\n$table-border-width: $border-width !default;\n$table-border-color: $border-color !default;\n\n$table-striped-order: odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color: $text-muted !default;\n\n$table-bg-scale: -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\"primary\": shift-color($primary, $table-bg-scale),\n  \"secondary\": shift-color($secondary, $table-bg-scale),\n  \"success\": shift-color($success, $table-bg-scale),\n  \"info\": shift-color($info, $table-bg-scale),\n  \"warning\": shift-color($warning, $table-bg-scale),\n  \"danger\": shift-color($danger, $table-bg-scale),\n  \"light\": $light,\n  \"dark\": $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y: .375rem !default;\n$input-btn-padding-x: .75rem !default;\n$input-btn-font-family: null !default;\n$input-btn-font-size: $font-size-base !default;\n$input-btn-line-height: $line-height-base !default;\n\n$input-btn-focus-width: .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color: rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-blur: 0 !default;\n$input-btn-focus-box-shadow: 0 0 $input-btn-focus-blur $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm: .25rem !default;\n$input-btn-padding-x-sm: .5rem !default;\n$input-btn-font-size-sm: $font-size-sm !default;\n\n$input-btn-padding-y-lg: .5rem !default;\n$input-btn-padding-x-lg: 1rem !default;\n$input-btn-font-size-lg: $font-size-lg !default;\n\n$input-btn-border-width: $border-width !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y: $input-btn-padding-y !default;\n$btn-padding-x: $input-btn-padding-x !default;\n$btn-font-family: $input-btn-font-family !default;\n$btn-font-size: $input-btn-font-size !default;\n$btn-line-height: $input-btn-line-height !default;\n$btn-white-space: null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm: $input-btn-padding-y-sm !default;\n$btn-padding-x-sm: $input-btn-padding-x-sm !default;\n$btn-font-size-sm: $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg: $input-btn-padding-y-lg !default;\n$btn-padding-x-lg: $input-btn-padding-x-lg !default;\n$btn-font-size-lg: $input-btn-font-size-lg !default;\n\n$btn-border-width: $input-btn-border-width !default;\n\n$btn-font-weight: $font-weight-normal !default;\n$btn-box-shadow: inset 0 1px 0 rgba($white, .15),\n0 1px 1px rgba($black, .075) !default;\n$btn-focus-width: $input-btn-focus-width !default;\n$btn-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity: .65 !default;\n$btn-active-box-shadow: inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color: $link-color !default;\n$btn-link-hover-color: $link-hover-color !default;\n$btn-link-disabled-color: $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius: $border-radius !default;\n$btn-border-radius-sm: $border-radius-sm !default;\n$btn-border-radius-lg: $border-radius-lg !default;\n\n$btn-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount: 15% !default;\n$btn-hover-bg-tint-amount: 15% !default;\n$btn-hover-border-shade-amount: 20% !default;\n$btn-hover-border-tint-amount: 10% !default;\n$btn-active-bg-shade-amount: 20% !default;\n$btn-active-bg-tint-amount: 20% !default;\n$btn-active-border-shade-amount: 25% !default;\n$btn-active-border-tint-amount: 10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top: .25rem !default;\n$form-text-font-size: $small-font-size !default;\n$form-text-font-style: null !default;\n$form-text-font-weight: null !default;\n$form-text-color: $text-muted !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom: .5rem !default;\n$form-label-font-size: null !default;\n$form-label-font-style: null !default;\n$form-label-font-weight: null !default;\n$form-label-color: null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y: $input-btn-padding-y !default;\n$input-padding-x: $input-btn-padding-x !default;\n$input-font-family: $input-btn-font-family !default;\n$input-font-size: $input-btn-font-size !default;\n$input-font-weight: $font-weight-base !default;\n$input-line-height: $input-btn-line-height !default;\n\n$input-padding-y-sm: $input-btn-padding-y-sm !default;\n$input-padding-x-sm: $input-btn-padding-x-sm !default;\n$input-font-size-sm: $input-btn-font-size-sm !default;\n\n$input-padding-y-lg: $input-btn-padding-y-lg !default;\n$input-padding-x-lg: $input-btn-padding-x-lg !default;\n$input-font-size-lg: $input-btn-font-size-lg !default;\n\n$input-bg: $body-bg !default;\n$input-disabled-bg: $gray-200 !default;\n$input-disabled-border-color: null !default;\n\n$input-color: $body-color !default;\n$input-border-color: $gray-400 !default;\n$input-border-width: $input-btn-border-width !default;\n$input-box-shadow: $box-shadow-inset !default;\n\n$input-border-radius: $border-radius !default;\n$input-border-radius-sm: $border-radius-sm !default;\n$input-border-radius-lg: $border-radius-lg !default;\n\n$input-focus-bg: $input-bg !default;\n$input-focus-border-color: tint-color($component-active-bg, 50%) !default;\n$input-focus-color: $input-color !default;\n$input-focus-width: $input-btn-focus-width !default;\n$input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color: $gray-600 !default;\n$input-plaintext-color: $body-color !default;\n\n$input-height-border: $input-border-width * 2 !default;\n\n$input-height-inner: add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half: add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter: add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height: add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm: add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg: add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition: border-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$form-color-width: 3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width: 1em !default;\n$form-check-min-height: $font-size-base * $line-height-base !default;\n$form-check-padding-start: $form-check-input-width+.5em !default;\n$form-check-margin-bottom: .125rem !default;\n$form-check-label-color: null !default;\n$form-check-label-cursor: null !default;\n$form-check-transition: null !default;\n\n$form-check-input-active-filter: brightness(90%) !default;\n\n$form-check-input-bg: $input-bg !default;\n$form-check-input-border: 1px solid rgba($black, .25) !default;\n$form-check-input-border-radius: .25em !default;\n$form-check-radio-border-radius: 50% !default;\n$form-check-input-focus-border: $input-focus-border-color !default;\n$form-check-input-focus-box-shadow: $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color: $component-active-color !default;\n$form-check-input-checked-bg-color: $component-active-bg !default;\n$form-check-input-checked-border-color: $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color: $component-active-color !default;\n$form-check-input-indeterminate-bg-color: $component-active-bg !default;\n$form-check-input-indeterminate-border-color: $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity: .5 !default;\n$form-check-label-disabled-opacity: $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity: $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end: 1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color: rgba($black, .25) !default;\n$form-switch-width: 2em !default;\n$form-switch-padding-start: $form-switch-width+.5em !default;\n$form-switch-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius: $form-switch-width !default;\n$form-switch-transition: background-position .15s ease-in-out !default;\n\n$form-switch-focus-color: $input-focus-border-color !default;\n$form-switch-focus-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color: $component-active-color !default;\n$form-switch-checked-bg-image: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y: $input-padding-y !default;\n$input-group-addon-padding-x: $input-padding-x !default;\n$input-group-addon-font-weight: $input-font-weight !default;\n$input-group-addon-color: $input-color !default;\n$input-group-addon-bg: $gray-200 !default;\n$input-group-addon-border-color: $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y: $input-padding-y !default;\n$form-select-padding-x: $input-padding-x !default;\n$form-select-font-family: $input-font-family !default;\n$form-select-font-size: $input-font-size !default;\n$form-select-indicator-padding: $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight: $input-font-weight !default;\n$form-select-line-height: $input-line-height !default;\n$form-select-color: $input-color !default;\n$form-select-bg: $input-bg !default;\n$form-select-disabled-color: null !default;\n$form-select-disabled-bg: $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position: right $form-select-padding-x center !default;\n$form-select-bg-size: 16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color: $gray-800 !default;\n$form-select-indicator: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5+$form-select-indicator-padding !default;\n$form-select-feedback-icon-position: center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size: $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width: $input-border-width !default;\n$form-select-border-color: $input-border-color !default;\n$form-select-border-radius: $input-border-radius !default;\n$form-select-box-shadow: $box-shadow-inset !default;\n\n$form-select-focus-border-color: $input-focus-border-color !default;\n$form-select-focus-width: $input-focus-width !default;\n$form-select-focus-box-shadow: 0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm: $input-padding-y-sm !default;\n$form-select-padding-x-sm: $input-padding-x-sm !default;\n$form-select-font-size-sm: $input-font-size-sm !default;\n$form-select-border-radius-sm: $input-border-radius-sm !default;\n\n$form-select-padding-y-lg: $input-padding-y-lg !default;\n$form-select-padding-x-lg: $input-padding-x-lg !default;\n$form-select-font-size-lg: $input-font-size-lg !default;\n$form-select-border-radius-lg: $input-border-radius-lg !default;\n\n$form-select-transition: $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width: 100% !default;\n$form-range-track-height: .5rem !default;\n$form-range-track-cursor: pointer !default;\n$form-range-track-bg: $gray-300 !default;\n$form-range-track-border-radius: 1rem !default;\n$form-range-track-box-shadow: $box-shadow-inset !default;\n\n$form-range-thumb-width: 1rem !default;\n$form-range-thumb-height: $form-range-thumb-width !default;\n$form-range-thumb-bg: $component-active-bg !default;\n$form-range-thumb-border: 0 !default;\n$form-range-thumb-border-radius: 1rem !default;\n$form-range-thumb-box-shadow: 0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow: 0 0 0 1px $body-bg,\n$input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width: $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg: tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg: $gray-500 !default;\n$form-range-thumb-transition: background-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color: $input-color !default;\n$form-file-button-bg: $input-group-addon-bg !default;\n$form-file-button-hover-bg: shade-color($form-file-button-bg, 5%) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height: add(3.5rem, $input-height-border) !default;\n$form-floating-line-height: 1.25 !default;\n$form-floating-padding-x: $input-padding-x !default;\n$form-floating-padding-y: 1rem !default;\n$form-floating-input-padding-t: 1.625rem !default;\n$form-floating-input-padding-b: .625rem !default;\n$form-floating-label-opacity: .65 !default;\n$form-floating-label-transform: scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition: opacity .1s ease-in-out,\ntransform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top: $form-text-margin-top !default;\n$form-feedback-font-size: $form-text-font-size !default;\n$form-feedback-font-style: $form-text-font-style !default;\n$form-feedback-valid-color: $success !default;\n$form-feedback-invalid-color: $danger !default;\n\n$form-feedback-icon-valid-color: $form-feedback-valid-color !default;\n$form-feedback-icon-valid: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color: $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\"valid\": (\"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid),\n  \"invalid\": (\"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid)) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown: 1000 !default;\n$zindex-sticky: 1020 !default;\n$zindex-fixed: 1030 !default;\n$zindex-offcanvas-backdrop: 1040 !default;\n$zindex-offcanvas: 1045 !default;\n$zindex-modal-backdrop: 1050 !default;\n$zindex-modal: 1055 !default;\n$zindex-popover: 1070 !default;\n$zindex-tooltip: 1080 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y: .5rem !default;\n$nav-link-padding-x: 1rem !default;\n$nav-link-font-size: null !default;\n$nav-link-font-weight: null !default;\n$nav-link-color: $link-color !default;\n$nav-link-hover-color: $link-hover-color !default;\n$nav-link-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out !default;\n$nav-link-disabled-color: $gray-600 !default;\n\n$nav-tabs-border-color: $gray-300 !default;\n$nav-tabs-border-width: $border-width !default;\n$nav-tabs-border-radius: $border-radius !default;\n$nav-tabs-link-hover-border-color: $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color: $gray-700 !default;\n$nav-tabs-link-active-bg: $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius: $border-radius !default;\n$nav-pills-link-active-color: $component-active-color !default;\n$nav-pills-link-active-bg: $component-active-bg !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y: $spacer * .5 !default;\n$navbar-padding-x: null !default;\n\n$navbar-nav-link-padding-x: .5rem !default;\n\n$navbar-brand-font-size: $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height: $font-size-base * $line-height-base+$nav-link-padding-y * 2 !default;\n$navbar-brand-height: $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y: ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end: 1rem !default;\n\n$navbar-toggler-padding-y: .25rem !default;\n$navbar-toggler-padding-x: .75rem !default;\n$navbar-toggler-font-size: $font-size-lg !default;\n$navbar-toggler-border-radius: $btn-border-radius !default;\n$navbar-toggler-focus-width: $btn-focus-width !default;\n$navbar-toggler-transition: box-shadow .15s ease-in-out !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color: rgba($white, .55) !default;\n$navbar-dark-hover-color: rgba($white, .75) !default;\n$navbar-dark-active-color: $white !default;\n$navbar-dark-disabled-color: rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color: rgba($white, .1) !default;\n\n$navbar-light-color: rgba($black, .55) !default;\n$navbar-light-hover-color: rgba($black, .7) !default;\n$navbar-light-active-color: rgba($black, .9) !default;\n$navbar-light-disabled-color: rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color: $navbar-light-active-color !default;\n$navbar-light-brand-hover-color: $navbar-light-active-color !default;\n$navbar-dark-brand-color: $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color: $navbar-dark-active-color !default;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width: 10rem !default;\n$dropdown-padding-x: 0 !default;\n$dropdown-padding-y: .5rem !default;\n$dropdown-spacer: .125rem !default;\n$dropdown-font-size: $font-size-base !default;\n$dropdown-color: $body-color !default;\n$dropdown-bg: $white !default;\n$dropdown-border-color: rgba($black, .15) !default;\n$dropdown-border-radius: $border-radius !default;\n$dropdown-border-width: $border-width !default;\n$dropdown-inner-border-radius: subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg: $dropdown-border-color !default;\n$dropdown-divider-margin-y: $spacer * .5 !default;\n$dropdown-box-shadow: $box-shadow !default;\n\n$dropdown-link-color: $gray-900 !default;\n$dropdown-link-hover-color: shade-color($dropdown-link-color, 10%) !default;\n$dropdown-link-hover-bg: $gray-200 !default;\n\n$dropdown-link-active-color: $component-active-color !default;\n$dropdown-link-active-bg: $component-active-bg !default;\n\n$dropdown-link-disabled-color: $gray-500 !default;\n\n$dropdown-item-padding-y: $spacer * .25 !default;\n$dropdown-item-padding-x: $spacer !default;\n\n$dropdown-header-color: $gray-600 !default;\n$dropdown-header-padding: $dropdown-padding-y $dropdown-item-padding-x !default;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color: $gray-300 !default;\n$dropdown-dark-bg: $gray-800 !default;\n$dropdown-dark-border-color: $dropdown-border-color !default;\n$dropdown-dark-divider-bg: $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow: null !default;\n$dropdown-dark-link-color: $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color: $white !default;\n$dropdown-dark-link-hover-bg: rgba($white, .15) !default;\n$dropdown-dark-link-active-color: $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg: $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color: $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y: .375rem !default;\n$pagination-padding-x: .75rem !default;\n$pagination-padding-y-sm: .25rem !default;\n$pagination-padding-x-sm: .5rem !default;\n$pagination-padding-y-lg: .75rem !default;\n$pagination-padding-x-lg: 1.5rem !default;\n\n$pagination-color: $link-color !default;\n$pagination-bg: $white !default;\n$pagination-border-width: $border-width !default;\n$pagination-border-radius: $border-radius !default;\n$pagination-margin-start: -$pagination-border-width !default;\n$pagination-border-color: $gray-300 !default;\n\n$pagination-focus-color: $link-hover-color !default;\n$pagination-focus-bg: $gray-200 !default;\n$pagination-focus-box-shadow: $input-btn-focus-box-shadow !default;\n$pagination-focus-outline: 0 !default;\n\n$pagination-hover-color: $link-hover-color !default;\n$pagination-hover-bg: $gray-200 !default;\n$pagination-hover-border-color: $gray-300 !default;\n\n$pagination-active-color: $component-active-color !default;\n$pagination-active-bg: $component-active-bg !default;\n$pagination-active-border-color: $pagination-active-bg !default;\n\n$pagination-disabled-color: $gray-600 !default;\n$pagination-disabled-bg: $white !default;\n$pagination-disabled-border-color: $gray-300 !default;\n\n$pagination-transition: color .15s ease-in-out,\nbackground-color .15s ease-in-out,\nborder-color .15s ease-in-out,\nbox-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm: $border-radius-sm !default;\n$pagination-border-radius-lg: $border-radius-lg !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max: .5 !default;\n$placeholder-opacity-min: .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y: $spacer !default;\n$card-spacer-x: $spacer !default;\n$card-title-spacer-y: $spacer * .5 !default;\n$card-border-width: $border-width !default;\n$card-border-color: rgba($black, .125) !default;\n$card-border-radius: $border-radius !default;\n$card-box-shadow: null !default;\n$card-inner-border-radius: subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y: $card-spacer-y * .5 !default;\n$card-cap-padding-x: $card-spacer-x !default;\n$card-cap-bg: rgba($black, .03) !default;\n$card-cap-color: null !default;\n$card-height: null !default;\n$card-color: null !default;\n$card-bg: $white !default;\n$card-img-overlay-padding: $spacer !default;\n$card-group-margin: $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y: 1rem !default;\n$accordion-padding-x: 1.25rem !default;\n$accordion-color: $body-color !default;\n$accordion-bg: $body-bg !default;\n$accordion-border-width: $border-width !default;\n$accordion-border-color: rgba($black, .125) !default;\n$accordion-border-radius: $border-radius !default;\n$accordion-inner-border-radius: subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y: $accordion-padding-y !default;\n$accordion-body-padding-x: $accordion-padding-x !default;\n\n$accordion-button-padding-y: $accordion-padding-y !default;\n$accordion-button-padding-x: $accordion-padding-x !default;\n$accordion-button-color: $accordion-color !default;\n$accordion-button-bg: $accordion-bg !default;\n$accordion-transition: $btn-transition,\nborder-radius .15s ease !default;\n$accordion-button-active-bg: tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color: shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color: $input-focus-border-color !default;\n$accordion-button-focus-box-shadow: $btn-focus-box-shadow !default;\n\n$accordion-icon-width: 1.25rem !default;\n$accordion-icon-color: $accordion-button-color !default;\n$accordion-icon-active-color: $accordion-button-active-color !default;\n$accordion-icon-transition: transform .2s ease-in-out !default;\n$accordion-icon-transform: rotate(-180deg) !default;\n\n$accordion-button-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size: $font-size-sm !default;\n$tooltip-max-width: 200px !default;\n$tooltip-color: $white !default;\n$tooltip-bg: $black !default;\n$tooltip-border-radius: $border-radius !default;\n$tooltip-opacity: .9 !default;\n$tooltip-padding-y: $spacer * .25 !default;\n$tooltip-padding-x: $spacer * .5 !default;\n$tooltip-margin: 0 !default;\n\n$tooltip-arrow-width: .8rem !default;\n$tooltip-arrow-height: .4rem !default;\n$tooltip-arrow-color: $tooltip-bg !default;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y: $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x: $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size: $tooltip-font-size !default;\n$form-feedback-tooltip-line-height: null !default;\n$form-feedback-tooltip-opacity: $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size: $font-size-sm !default;\n$popover-bg: $white !default;\n$popover-max-width: 276px !default;\n$popover-border-width: $border-width !default;\n$popover-border-color: rgba($black, .2) !default;\n$popover-border-radius: $border-radius-lg !default;\n$popover-inner-border-radius: subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow: $box-shadow !default;\n\n$popover-header-bg: shade-color($popover-bg, 6%) !default;\n$popover-header-color: $headings-color !default;\n$popover-header-padding-y: .5rem !default;\n$popover-header-padding-x: $spacer !default;\n\n$popover-body-color: $body-color !default;\n$popover-body-padding-y: $spacer !default;\n$popover-body-padding-x: $spacer !default;\n\n$popover-arrow-width: 1rem !default;\n$popover-arrow-height: .5rem !default;\n$popover-arrow-color: $popover-bg !default;\n\n$popover-arrow-outer-color: fade-in($popover-border-color, .05) !default;\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width: 350px !default;\n$toast-padding-x: .75rem !default;\n$toast-padding-y: .5rem !default;\n$toast-font-size: .875rem !default;\n$toast-color: null !default;\n$toast-background-color: rgba($white, .85) !default;\n$toast-border-width: 1px !default;\n$toast-border-color: rgba($black, .1) !default;\n$toast-border-radius: $border-radius !default;\n$toast-box-shadow: $box-shadow !default;\n$toast-spacing: $container-padding-x !default;\n\n$toast-header-color: $gray-600 !default;\n$toast-header-background-color: rgba($white, .85) !default;\n$toast-header-border-color: rgba($black, .05) !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size: .75em !default;\n$badge-font-weight: $font-weight-bold !default;\n$badge-color: $white !default;\n$badge-padding-y: .35em !default;\n$badge-padding-x: .65em !default;\n$badge-border-radius: $border-radius !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding: $spacer !default;\n\n$modal-footer-margin-between: .5rem !default;\n\n$modal-dialog-margin: .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height: $line-height-base !default;\n\n$modal-content-color: null !default;\n$modal-content-bg: $white !default;\n$modal-content-border-color: rgba($black, .2) !default;\n$modal-content-border-width: $border-width !default;\n$modal-content-border-radius: $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs: $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up: $box-shadow !default;\n\n$modal-backdrop-bg: $black !default;\n$modal-backdrop-opacity: .5 !default;\n$modal-header-border-color: $border-color !default;\n$modal-footer-border-color: $modal-header-border-color !default;\n$modal-header-border-width: $modal-content-border-width !default;\n$modal-footer-border-width: $modal-header-border-width !default;\n$modal-header-padding-y: $modal-inner-padding !default;\n$modal-header-padding-x: $modal-inner-padding !default;\n$modal-header-padding: $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm: 300px !default;\n$modal-md: 500px !default;\n$modal-lg: 800px !default;\n$modal-xl: 1140px !default;\n\n$modal-fade-transform: translate(0, -50px) !default;\n$modal-show-transform: none !default;\n$modal-transition: transform .3s ease-out !default;\n$modal-scale-transform: scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y: $spacer !default;\n$alert-padding-x: $spacer !default;\n$alert-margin-bottom: 1rem !default;\n$alert-border-radius: $border-radius !default;\n$alert-link-font-weight: $font-weight-bold !default;\n$alert-border-width: $border-width !default;\n$alert-bg-scale: -80% !default;\n$alert-border-scale: -70% !default;\n$alert-color-scale: 40% !default;\n$alert-dismissible-padding-r: $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height: 1rem !default;\n$progress-font-size: $font-size-base * .75 !default;\n$progress-bg: $gray-200 !default;\n$progress-border-radius: $border-radius !default;\n$progress-box-shadow: $box-shadow-inset !default;\n$progress-bar-color: $white !default;\n$progress-bar-bg: $primary !default;\n$progress-bar-animation-timing: 1s linear infinite !default;\n$progress-bar-transition: width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color: $gray-900 !default;\n$list-group-bg: $white !default;\n$list-group-border-color: rgba($black, .125) !default;\n$list-group-border-width: $border-width !default;\n$list-group-border-radius: $border-radius !default;\n\n$list-group-item-padding-y: $spacer * .5 !default;\n$list-group-item-padding-x: $spacer !default;\n$list-group-item-bg-scale: -80% !default;\n$list-group-item-color-scale: 40% !default;\n\n$list-group-hover-bg: $gray-100 !default;\n$list-group-active-color: $component-active-color !default;\n$list-group-active-bg: $component-active-bg !default;\n$list-group-active-border-color: $list-group-active-bg !default;\n\n$list-group-disabled-color: $gray-600 !default;\n$list-group-disabled-bg: $list-group-bg !default;\n\n$list-group-action-color: $gray-700 !default;\n$list-group-action-hover-color: $list-group-action-color !default;\n\n$list-group-action-active-color: $body-color !default;\n$list-group-action-active-bg: $gray-200 !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding: .25rem !default;\n$thumbnail-bg: $body-bg !default;\n$thumbnail-border-width: $border-width !default;\n$thumbnail-border-color: $gray-300 !default;\n$thumbnail-border-radius: $border-radius !default;\n$thumbnail-box-shadow: $box-shadow-sm !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size: $small-font-size !default;\n$figure-caption-color: $gray-600 !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size: null !default;\n$breadcrumb-padding-y: 0 !default;\n$breadcrumb-padding-x: 0 !default;\n$breadcrumb-item-padding-x: .5rem !default;\n$breadcrumb-margin-bottom: 1rem !default;\n$breadcrumb-bg: null !default;\n$breadcrumb-divider-color: $gray-600 !default;\n$breadcrumb-active-color: $gray-600 !default;\n$breadcrumb-divider: quote(\"/\") !default;\n$breadcrumb-divider-flipped: $breadcrumb-divider !default;\n$breadcrumb-border-radius: null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color: $white !default;\n$carousel-control-width: 15% !default;\n$carousel-control-opacity: .5 !default;\n$carousel-control-hover-opacity: .9 !default;\n$carousel-control-transition: opacity .15s ease !default;\n\n$carousel-indicator-width: 30px !default;\n$carousel-indicator-height: 3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer: 3px !default;\n$carousel-indicator-opacity: .5 !default;\n$carousel-indicator-active-bg: $white !default;\n$carousel-indicator-active-opacity: 1 !default;\n$carousel-indicator-transition: opacity .6s ease !default;\n\n$carousel-caption-width: 70% !default;\n$carousel-caption-color: $white !default;\n$carousel-caption-padding-y: 1.25rem !default;\n$carousel-caption-spacer: 1.25rem !default;\n\n$carousel-control-icon-width: 2rem !default;\n\n$carousel-control-prev-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration: .6s !default;\n$carousel-transition: transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg: $black !default;\n$carousel-dark-caption-color: $black !default;\n$carousel-dark-control-icon-filter: invert(1) grayscale(100) !default;\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width: 2rem !default;\n$spinner-height: $spinner-width !default;\n$spinner-vertical-align: -.125em !default;\n$spinner-border-width: .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm: 1rem !default;\n$spinner-height-sm: $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width: 1em !default;\n$btn-close-height: $btn-close-width !default;\n$btn-close-padding-x: .25em !default;\n$btn-close-padding-y: $btn-close-padding-x !default;\n$btn-close-color: $black !default;\n$btn-close-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow: $input-btn-focus-box-shadow !default;\n$btn-close-opacity: .5 !default;\n$btn-close-hover-opacity: .75 !default;\n$btn-close-focus-opacity: 1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter: invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y: $modal-inner-padding !default;\n$offcanvas-padding-x: $modal-inner-padding !default;\n$offcanvas-horizontal-width: 400px !default;\n$offcanvas-vertical-height: 30vh !default;\n$offcanvas-transition-duration: .3s !default;\n$offcanvas-border-color: $modal-content-border-color !default;\n$offcanvas-border-width: $modal-content-border-width !default;\n$offcanvas-title-line-height: $modal-title-line-height !default;\n$offcanvas-bg-color: $modal-content-bg !default;\n$offcanvas-color: $modal-content-color !default;\n$offcanvas-box-shadow: $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg: $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity: $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size: $small-font-size !default;\n$code-color: $pink !default;\n\n$kbd-padding-y: .2rem !default;\n$kbd-padding-x: .4rem !default;\n$kbd-font-size: $code-font-size !default;\n$kbd-color: $white !default;\n$kbd-bg: $gray-900 !default;\n\n$pre-color: null !default;", "// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    >* {\n      @include make-col-ready();\n    }\n  }\n}\n\n@if $enable-cssgrid {\n  .grid {\n    display: grid;\n    grid-template-rows: repeat(var(--#{$variable-prefix}rows, 1), 1fr);\n    grid-template-columns: repeat(var(--#{$variable-prefix}columns, #{$grid-columns}), 1fr);\n    gap: var(--#{$variable-prefix}gap, #{$grid-gutter-width});\n\n    @include make-cssgrid();\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$variable-prefix}gutter-x: #{$gutter};\n  --#{$variable-prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\n  margin-top: calc(-1 * var(--#{$variable-prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(-.5 * var(--#{$variable-prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$variable-prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$variable-prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$variable-prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$variable-prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage(divide($size, $columns));\n\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: divide($size, $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: divide(100%, $count);\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .g-col#{$infix}-#{$i} {\n            grid-column: auto / span $i;\n          }\n        }\n\n        // Start with `1` because `0` is and invalid value.\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\n        @for $i from 1 through ($columns - 1) {\n          .g-start#{$infix}-#{$i} {\n            grid-column-start: $i;\n          }\n        }\n      }\n    }\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit !=rem and $rfs-unit !=px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit !=px and $rfs-breakpoint-unit !=em and $rfs-breakpoint-unit !=rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) !=number or $rfs-factor <=1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n\n  @if $dividend==0 {\n    @return 0;\n  }\n\n  @if $divisor==0 {\n    @error \"Cannot divide by 0\";\n  }\n\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n\n  @while ($remainder > 0 and $precision >=0) {\n    $quotient: 0;\n\n    @while ($remainder >=$divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n\n    $result: $result * 10+$quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n\n    @if ($precision < 0 and $remainder >=$divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%);\n\n  @if ($dividend-unit !=$divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit==px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n\n@else if $rfs-base-value-unit==rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache==px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n\n@else if $rfs-breakpoint-unit-cache==rem or $rfs-breakpoint-unit-cache==\"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit==px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode==max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode==max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode==max-media-query {\n\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}),\n      (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class==disable and $rfs-mode==max-media-query {\n\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n\n  @else if $rfs-class==enable and $rfs-mode==min-media-query {\n\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  }\n\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class==enable {\n    @if $rfs-mode==min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query {\n\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n\n  @else {\n    @if $rfs-class==disable and $rfs-mode==min-media-query {\n\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n\n    @include _rfs-media-query {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) !=list, ($values, ), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value==0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value)==\"number\", unit($value), false);\n\n      @if $unit==px {\n        // Convert to rem if needed\n        $val: $val+' '+if($rfs-unit==rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n\n      @else if $unit==rem {\n        // Convert to px if needed\n        $val: $val+' '+if($rfs-unit==px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      }\n\n      @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + ' '+ $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) !=list, ($values, ), $values);\n\n  $val: '';\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value==0 {\n      $val: $val + ' 0';\n    }\n\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value)==\"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit !=px and $unit !=rem {\n        $val: $val + ' '+ $value;\n      }\n\n      @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit==px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <=$rfs-base-value or not $enable-rfs {\n          $val: $val+' '+if($rfs-unit==rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit==rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + ' calc('+ $min-width + if($value < 0, ' - ', ' + ') + $variable-width + ')';\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values !=null {\n    $val: rfs-value($values);\n    $fluidVal: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val==$fluidVal {\n      #{$property}: $val;\n    }\n\n    @else {\n      @include _rfs-rule {\n        #{$property}: if($rfs-mode==max-media-query, $val, $fluidVal);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule {\n        #{$property}: if($rfs-mode==max-media-query, $fluidVal, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$variable-prefix}#{$property-class}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$variable-prefix}#{$property-class}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $value in $is-local-vars {\n                --#{$variable-prefix}#{$local-var}: #{$value};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}