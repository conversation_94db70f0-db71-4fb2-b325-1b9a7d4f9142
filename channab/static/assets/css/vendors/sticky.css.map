{"version": 3, "sources": ["utils/_variables.scss", "vendors/sticky/_sticky.scss"], "names": [], "mappings": "AAAA,MACI,sBAAc,CAEjB,QCDA,uBAAwB,CACxB,iBAAkB,CAClB,gBAIA,QAAS,CACT,kBAAmB,CACnB,UAAW,CACX,WAAY,CACZ,eAAgB,CAChB,SAAU,CACV,iBAAkB,CAClB,SAAU,CACV,iEAMA,SAAU,CACV,WAAY,CACZ,QAAS,CACT,gBAAiB,CACjB,eAAgB,CAChB,UAAW,CACX,WAKA,iBAAkB,CAClB,iCAIA,WAAY,CAEZ,aAAc,CAEd,gBAGA,UAAW,CACX,uBAGA,wBAAiB,CAAjB,gBAAiB,CACjB,kCAA2B,CAA3B,0BAA2B,CAC3B,sCAAW,CAAX,8BAA+B,CAC/B,aAGA,YAAa,CACb,MAGA,UAAW,CACX,aAAc,CACd,iBAAkB,CAClB,WAAY,CACZ,sBAAuB,CACvB,gBAAiB,CACjB,WAAY,CACZ,iBAAkB,CAClB,qCD3CsB,CC4CtB,yCAA0B,CAA1B,iCAA0B,CAA1B,yBAA0B,CAA1B,iDAA0B,CAC1B,SAAU,CAXX,YAeE,WAAY,CAfd,yCAmBE,iDAA4C,CAA5C,yCAA4C,CAC5C,yCAAkC,CAAlC,iCAAkC,CAClC,WAAY,CACZ,WAAY,CACZ,0CAAY,CAAZ,kCAAY,CAAZ,0BAAY,CAAZ,mDAA2B,CAvB7B,eA2BE,4BAA6B,CAC7B,WAAY,CACZ,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,WAAY,CAhCd,qBAmCG,YAAa,CACb,WAAY,CACZ,oDAA0C,CAA1C,4CAA+C,CArClD,qBAyCG,cAAe,CACf,eAAgB,CAChB,UD/CS,CCgDT,WAAY,CACZ,eAAgB,CA7CnB,mBAiDG,gBAAiB,CAjDpB,oBAsDE,8BDvFuB,CCiCzB,oBA0DE,gCD1FqB,CCgCvB,oBA8DE,+BD7FkB,CC+BpB,oBAkEE,gCDhGqB,CC8BvB,oBAsEE,gCDnGoB,CCoGpB,QAKD,UD9EW,CC+EX,eAAgB,CAChB,oBAAqB,CACrB,2DAAY,CAAZ,mDAAY,CAAZ,2CAAY,CAAZ,oEAA4C,CAJ7C,eAQE,iBAAkB,CAClB,KAAM,CACN,WAAY,CACZ,UD5Hc,CC6Hd,2CAvFF,MA4FE,eAAgB,CAChB,gBAAiB,CACjB", "file": "vendors/sticky.css", "sourcesContent": [":root {\n    --theme-color: #0da487;\n    // --theme-color-rgb: 13, 164, 135;\n}\n\n// General variables\n$theme-font-color: #222222;\n$theme-body-font-color: #4a5568;\n$theme-body-sub-title-color: rgba($theme-body-font-color, 0.7);\n$body-color: #fbfeff;\n\n// Theme colors variables\n$box-shadow: #dedede;\n$star-color: #ef8f3b;\n$grey-3: #333333;\n$grey-13: #9a9a9a;\n$upcoming-color: #3ccbf0;\n$past-color: #198754;\n$cancel-color: #6c757d;\n$x-color: rgba(43, 43, 43, 0.71);\n$grey-9: #999999;\n$inner-bg: #f9f9f6;\n$grey-darker: #222222;\n$bg-color: #fafafb;\n$primary-color: #0da487;\n$secondary-color: #ef3f3e;\n$success-color: #9e65c2;\n$info-color: #a927f9;\n$warning-color: #747dc6;\n$danger-color: #6670bd;\n$light-color: #f4f4f4;\n$light-gray: #ecf3fa;\n$input-bg-color: #f8f8f8;\n$btn-hover-color: #144eee;\n$light-semi-gray: #efefef;\n$light-shade-silver: #f9f9f9;\n$light-silver: #f1f1f1;\n$dark-light: $theme-font-color;\n$dark-gray: #898989;\n$dark-color: #2c323f;\n$gray-60: #cccccc;\n$card-body-color: #f9f9fa;\n$transparent-color: transparent;\n$auth-bg-color: #fafafa;\n$light: #fdfeff;\n$semi-dark: #59667a;\n$light-text: #999999;\n$light-background: #d7e2e9;\n$light-font: #1b3155;\n$light-semi-font: $semi-dark;\n$light-chartist-grid-color: #f6f9fd;\n$pitch-color: #fe8a7d;\n$color-scarpa: #4c5667;\n$color-fiord: #455a64;\n$black: #000;\n$black1: #232323;\n$white: #fff;\n$theme-medium-color: #eae8ff;\n$light-blue: #eff2f7;\n$font-light: #7e7e7e;\n$gray: #c7c7c5;\n$gray-light: #ddd;\n$silver-light: #efefee;\n$light-yellow: #ff9f40;\n$gray-dark: #555;\n$black1: #212529;\n$light-purple: #e3e0ff;\n$mainly-blue: #f8f9fe;\n$suva-grey: #e8ebf2;\n$v-light-silver: #eeeeee;\n$purple: #7468f0;\n$yellow: #ff9f44;\n$light-green: #28c870;\n$green: #008000;\n$lavender: #e8eaf6;\n$white-ice: #ebf7fc;\n$skin-tutu: #fcebeb;\n$pattens-blue: #dee2e6;\n\n$title-color: #222222;\n$content-color: #4a5568;\n$rating-color: #ffb321;\n$border-color: #ececec;\n\n// gradient colors\n$gradient-primary: linear-gradient(26deg, var(--theme-color) 0%, #a26cf8 100%);\n\n//social colors\n$fb: #50598e;\n$twitter: #6fa2d8;\n$google-plus: #c64e40;\n$linkedin: #0077b5;\n\n//error input color start\n$light-body-bg-color: #fafdff;\n\n//fonts\n$public-sans: \"Public Sans\", sans-serif;\n$font-rubik: Rubik;\n$font-serif: sans-serif;\n$font-themify: themify;\n$font-awesome: FontAwesome;\n$font-ICO: IcoFont;\n\n// dark layout variables\n$dark-body-background: #1d1e26;\n$dark-card-background: #262932;\n$dark-card-inbox: #323846;\n$dark-small-font-color: #98a6ad;\n$dark-all-font-color: rgba(255, 255, 255, 0.6);\n$light-all-font-color: rgba(255, 255, 255, 0.2);\n$sidebar-submenu-font-color: rgba(255, 255, 255, 0.6);\n$dark-border-color: $dark-body-background;\n$dark-card-border: #374558;\n$dark-editor-document: #2b2b2b;\n$bg-dark-color: #1f2533;\n$dark-datatable-odd: #1f232b;\n$dark-datatable-sorting: #1c212b;\n$dark-datatable-sorting-even: #22262f;\n$dark-even-hover-sorting: #191e27;\n\n//General tag settings\n$body-font-size: 14px;\n$body-font-color: $theme-body-font-color;\n$ul-padding-left: 0px;\n$ancher-color: var(--theme-color);\n$btn-focus-box-shadow: none;\n$all-focus-outline-color: var(--theme-color);\n\n$paragraph-font-size: 13px;\n$paragraph-line-height: 1.7;\n$paragraph-letter-spacing: 0.7px;\n\n$code-tag-color: var(--theme-color);\n$code-tag-bg-color: rgba($primary-color, 0.03);\n$code-tag-padding: 3px;\n$code-tag-margin: 0 3px;\n$code-tag-border-radious: 2px;\n\n$blockquote-padding: 15px;\n$blockquote-border: 4px solid $light-gray;\n\n$small-tag-padding: 10px;\n$small-tag-color: $dark-color;\n\n$pre-tag-bg-color: rgba($primary-color, 0.03);\n$pre-tag-padding: 20px 0;\n\n$list-group-active-bg-color: var(--theme-color);\n$list-group-active-border-color: var(--theme-color);\n$list-group-img-size: 40px;\n$list-group-margin: 10px;\n\n// Tables settings\n$table-b-margin: 0px;\n$table-heading-color: $theme-body-font-color;\n$table-row-color: $theme-body-font-color;\n$table-footer-font-weight: bold;\n$table-inverse-color: $white;\n$table-hover-color: $light;\n$table-padding: 0.75rem;\n$table-xl-padding: 1.25rem 2rem;\n$table-xl-font: 120%;\n$table-lg-padding: 0.9rem 2rem;\n$table-lg-font: 110%;\n$table-de-padding: 0.75rem 2rem;\n$table-de-font: 100%;\n$table-sm-padding: 0.5rem 2rem;\n$table-sm-font: 90%;\n$table-xs-padding: 0.2rem 2rem;\n$table-xs-font: 80%;\n$horizontal-border-color: #f2f4ff;\n$horizontal-padding: 0.75rem 2rem;\n$table-inverse-bg-color: #292b2c;\n\n//According setting\n$according-card-top-margin: 10px;\n$according-btn-weight: 500;\n$according-btn-color: $theme-body-font-color;\n$according-card-header-padding: 0.75rem 1.25rem;\n$according-card-header-icon: $font-ICO;\n$according-open-icon: \"\\eb73\";\n$according-close-icon: \"\\eb7a\";\n\n//Alert settings\n$alert-hover-color: $dark-color;\n$alert-border-radious: 0.15rem;\n$alert-padding: 15px;\n$alert-msg-icon-size: 16px;\n\n//badge settings\n$badge-padding: 0.44em 0.7em;\n$badge-svg-size: 10px;\n\n//form input settings\n$form-group-margin-bottom: 1.25em;\n$col-form-label-font-size: 14px;\n$form-control-font-size: 14px;\n$form-control-border-radious: 2px;\n$form-control-border-color: $light-gray;\n\n//breadcrumb setting\n$breadcrumb-ancher-color: $white;\n$breadcrumb-ancher-dark-color: $black;\n\n//buttons setting\n$btn-font-size: 14px;\n$btn-padding: 0.375rem 1.75rem;\n$btn-lg-font-size: 18px;\n$btn-sm-font-size: 12px;\n$btn-xs-font-size: 11px;\n$btn-xs-padding: 0.05rem 0.4rem;\n\n//Card settings\n$card-padding: 30px 40px;\n$card-margin-bottom: 30px;\n$card-border-color: 1px solid $light-gray;\n$card-border-radious: 15px;\n$common-shadow-color: rgba($info-color, 0.08);\n$card-box-shadow: 0 0 20px rgba(8, 21, 66, 0.05);\n$card-header-bg-color: $white;\n$card-header-font-transform: capitalize;\n$card-header-span-size: 12px;\n$card-header-span-color: $theme-body-sub-title-color;\n$card-body-bg-color: $transparent-color;\n$card-footer-bg-color: $white;\n$card-hover-box-shadow: 0 0 40px rgba(8, 21, 66, 0.05);\n\n//footer settings\n$footer_bg_color: $white;\n$footer_box_shadow: 0 0 20px rgba($semi-dark, 0.1);\n$footer_dark_color: $theme-font-color;\n$footer_dark__txt_color: $white;\n\n//form settings\n$form-group-margin-bottom: 20px;\n$sm-form-margin-bottom: 14px;\n$form-placeholder-color: $dark-gray;\n$form-placeholder-font-size: calc(14px + (16 - 14) * ((100vw - 320px) / (1920 - 320)));\n$form-placeholder-letter-spacing: 1px;\n$form-placeholder-font-weight: 100;\n$form-input-border-color: $light-semi-gray;\n$form-input-bg-color: $white;\n\n//Tour settings\n$tour-color: var(--theme-color);\n\n//sidabr main settings\n$sidebar-width: 255px;\n$sidebar-position: fixed;\n$sidebar-background-color: $white;\n$sidebar-shadow: 0 0 11px rgba(69, 110, 243, 0.13);\n$sidebar-overflow: auto;\n$sidebar-z-index: 9;\n$sidebar-transition: 0.5s;\n\n//sidebar profile settings\n$sidebar-profile-edit-icon-size: 14px;\n$sidebar-profile-name-txt-color: var(--theme-color);\n$sidebar-profile-name-txt-transfer: uppercase;\n$sidebar-profile-name-txt-weight: 600;\n$sidebar-profile-name-letter-specing: 1.5px;\n$sidebar-profile-name-marging: 3px;\n$sidebar-profile-sub-title-font-size: 10px;\n$sidebar-profile-sub-title-margin: 0px;\n$sidebar-profile-img-shadow: 0 0 15px rgba(68, 102, 242, 0.3);\n\n//Sidebar main menu setting\n$sidebar-menu-padding: 15px;\n$sidebar-menu-list-style: none;\n$sidebar-menu-list-margin: 0;\n$sidebar-icon-size: 14px;\n$sidebar-icon-margin: 14px;\n$sidebar-icon-stroke-width: 3px;\n$sidebar-font-size: 14px;\n$sidebar-letter-specing: 0.5px;\n$sidebar-txt-transform: capitalize;\n$sidebar-font-weight: 600;\n$sidebar-font-color: $theme-body-font-color;\n$sidebar-padding-top: 12px;\n$sidebar-padding-bottom: 12px;\n$sidebar-sub-header-padding: 15px;\n$sidebar-sub-header-margin: 0;\n$sidebar-heading-hover-padding: 5px;\n$sidebar-hover-txt-color: var(--theme-color);\n$sidebar-arrow-margin-top: 2px;\n$sidebar-arrow-size: 15px;\n$sidebar-arrow-color: $theme-body-font-color;\n$sidebar-open-icon: \"\\f107\";\n$sidebar-close-icon: \"\\f105\";\n$sidebar-icon-font-family: $font-awesome;\n$sidebar-height: calc(100vh - 130px);\n\n//Header settings\n$main-header-position: fixed;\n$main-header-top: 0;\n$main-header-shadow: 0 0 20px rgba($semi-dark, 0.1);\n\n//page settings\n$page-body-padding: 0 15px 0 15px;\n$page-body-bg-color: $light-color;\n$page-body-margin-bottom: 0px;\n$page-title-padding: 30px;\n$common-box-shadow: 2px 3.464px 8px 0px rgba(22, 145, 248, 0.18);\n$page-title-font-size: 24px;\n$page-title-margin-bottom: 0;\n$page-title-text-tranform: capitalize;\n$page-small-title-display: block;\n$page-small-title-font-size: 12px;\n$page-small-title-margin-bottom: 5px;\n$page-small-title-text-transform: capitalize;\n$page-small-title-text-color: $theme-body-sub-title-color;\n$breadcrumb-size: 16px;\n$breadcrumb-content: \"/\";\n\n//main header left settings\n$header-left-bg-color: $white;\n$main-header-padding: 27px 22px;\n$main-header-z-index: 5;\n$header-wrapper-padding: 22px 30px;\n$header-wrapper-nav-right: 0px;\n$header-wrapper-nav-icon-size: 20px;\n$header-wrapper-nav-icon-color: $theme-font-color;\n$header-wrapper-nav-icon-align: middle;\n$header-size: 85px;\n$box-layout-space: 40px;\n\n//Breake Points settings\n\n$min-breakpoints: (\n    lg: 992px,\n    xl: 1200px,\n    2xl: 1366px,\n);\n\n$max-breakpoints: (\n    2xs: 360px,\n    xs: 480px,\n    sm: 575px,\n    md: 767px,\n    lg: 991px,\n    xl: 1199px,\n    2xl: 1366px,\n    3xl: 1460px,\n    4xl: 1660px,\n);\n", "/* * Hide from both screenreaders and browsers: h5bp.com/u */\n.hidden {\n\tdisplay: none !important;\n\tvisibility: hidden;\n}\n\n/* * Hide only visually, but have it available for screenreaders: h5bp.com/v */\n.visuallyhidden {\n\tborder: 0;\n\tclip: rect(0 0 0 0);\n\theight: 1px;\n\tmargin: -1px;\n\toverflow: hidden;\n\tpadding: 0;\n\tposition: absolute;\n\twidth: 1px;\n}\n\n/* * Extends the .visuallyhidden class to allow the element to be focusable * when navigated to via the keyboard: h5bp.com/p */\n\n.visuallyhidden.focusable:active,\n.visuallyhidden.focusable:focus {\n\tclip: auto;\n\theight: auto;\n\tmargin: 0;\n\toverflow: visible;\n\tposition: static;\n\twidth: auto;\n}\n\n/* * Hide visually and from screenreaders, but maintain layout */\n\n.invisible {\n\tvisibility: hidden;\n}\n\n.clearfix:before,\n.clearfix:after {\n\tcontent: \" \";\n\t/* 1 */\n\tdisplay: table;\n\t/* 2 */\n}\n\n.clearfix:after {\n\tclear: both;\n}\n\n.noflick {\n\tperspective: 1000;\n\tbackface-visibility: hidden;\n\ttransform: translate3d(0, 0, 0);\n}\n\n.sticky-note {\n\tmargin: -15px;\n}\n\n.note {\n\tfloat: left;\n\tdisplay: block;\n\tposition: relative;\n\tpadding: 1em;\n\twidth: calc(25% - 30px);\n\tmin-height: 300px;\n\tmargin: 15px;\n\tborder-radius: 5px;\n\tbackground-color: rgba($primary-color, 0.1);\n\ttransition: transform .15s;\n\tz-index: 1;\n\t@extend .noflick;\n\n\t&:hover {\n\t\tcursor: move;\n\t}\n\n\t&.ui-draggable-dragging:nth-child(n) {\n\t\tbox-shadow: 5px 5px 15px 0 rgba(0, 0, 0, .3);\n\t\ttransform: scale(1.125) !important;\n\t\tz-index: 100;\n\t\tcursor: move;\n\t\ttransition: transform .150s;\n\t}\n\n\ttextarea {\n\t\tbackground-color: transparent;\n\t\tborder: none;\n\t\tresize: vertical;\n\t\tfont-style: italic;\n\t\twidth: 100%;\n\t\tpadding: 5px;\n\n\t\t&:focus {\n\t\t\toutline: none;\n\t\t\tborder: none;\n\t\t\tbox-shadow: 0 0 0px 1px rgba(0, 0, 0, .2) inset;\n\t\t}\n\n\t\t&.title {\n\t\t\tfont-size: 24px;\n\t\t\tline-height: 1.2;\n\t\t\tcolor: $black;\n\t\t\theight: 64px;\n\t\t\tmargin-top: 20px;\n\t\t}\n\n\t\t&.cnt {\n\t\t\tmin-height: 200px;\n\t\t}\n\t}\n\n\t&:nth-child(2n) {\n\t\tbackground: rgba($secondary-color, 0.2);\n\t}\n\n\t&:nth-child(3n) {\n\t\tbackground: rgba($success-color, 0.2);\n\t}\n\n\t&:nth-child(4n) {\n\t\tbackground: rgba($info-color, 0.1);\n\t}\n\n\t&:nth-child(5n) {\n\t\tbackground: rgba($warning-color, 0.2);\n\t}\n\n\t&:nth-child(6n) {\n\t\tbackground: rgba($danger-color, 0.3);\n\t}\n}\n\n/* Button style  */\n.button {\n\tcolor: $white;\n\tpadding: 1em 2em;\n\ttext-decoration: none;\n\ttransition: transform .150s, background .01s;\n\t@extend .noflick;\n\n\t&.remove {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tright: -10px;\n\t\tcolor: $grey-9;\n\t}\n}\n\n@media only screen and (max-width: 1199px) {\n\t.note {\n\t\tmin-width: 260px;\n\t\tmin-height: 260px;\n\t}\n}"]}