{"version": 3, "sources": ["vendors/echart/_reset.scss"], "names": [], "mappings": "AAmBA,2BAII,UAAW,CACX,WAAY,CACZ,QAAS,CACT,SAAU,CACV,iBAAkB,CACrB,YAIG,kBAAmB,CACnB,cAAe,CAClB,kBAGG,oBAAqB,EACrB,cAAgB,CAChB,MAAO,CACP,eAAgB,CACnB,mBAGG,YAAa,CACb,eAAgB,CAChB,4DAA6D,CAC7D,gBAAiB,CACjB,iBAAkB,CACrB,qBAGG,eAAgB,CACnB,kBAGG,iBAAkB,CACrB,4BAGG,eAAgB,CACnB,oDAGG,iBAAkB,CAClB,UAAW,CACX,eAAgB,CAChB,UAAW,CACX,WAAY,CACZ,cAAe,CACf,0BAA2B,CAC3B,4BAA6B,CAChC,mDAGG,kBAAmB,CACtB,WAGG,iBAAkB,CAClB,aAAc,CACjB,sBAGG,cAAe,CACf,mDAAoD,CACvD,YAGG,YAAa,CAChB,iBAGG,iBAAkB,CAClB,iBAAkB,CACrB,uBAGG,oBAAqB,CACrB,kBAAmB,CACnB,qBAAsB,CACtB,gBAAiB,CACjB,gBAAiB,CACpB,oBAGG,qBAAsB,CACtB,UAAW,CACX,eAAgB,CAChB,cAAe,CAClB,uBAGG,cAAe,CACf,aAAwB,CAC3B,6BAGG,YAAa,CACb,iBAAkB,CAClB,eAAgB,CAChB,SAAU,CACV,QAAS,CACT,qBAAsB,CACtB,YAAa,CACb,YAAa,CACb,+BAAY,CAAZ,uBAAwB,CAC3B,wBAGG,WAAY,CACZ,YAAa,CAChB,yBAGG,cAAe,CACf,QAAS,CACT,SAAU,CACV,+BAAwB,CAAxB,uBAAwB,CACxB,gBAAiB,CACjB,WAAY,CACf,6CAGG,aAAc,CACd,UAAW,CACX,cAAe", "file": "vendors/echart.css", "sourcesContent": ["/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\nhtml,\nbody,\n#main,\nbody>.main {\n    width: 100%;\n    height: 100%;\n    margin: 0;\n    padding: 0;\n    font-family: arial;\n}\n\n\n.test-title {\n    font-weight: normal;\n    font-size: 16px;\n}\n\n.test-title-inner {\n    display: inline-block;\n    *display: inline;\n    zoom: 1;\n    text-align: left;\n}\n\n.test-title strong {\n    color: yellow;\n    font-weight: 700;\n    text-shadow: -1px 0 #000, 0 1px #000, 1px 0 #000, 0 -1px #000;\n    padding-left: 2px;\n    padding-right: 2px;\n}\n\n.test-buttons button {\n    margin: 10px 5px;\n}\n\n.test-chart-block {\n    position: relative;\n}\n\n.test-chart-block-has-right {\n    overflow: hidden;\n}\n\n.test-chart-block-has-right .test-chart-block-right {\n    position: absolute;\n    right: 10px;\n    background: #fff;\n    z-index: 99;\n    width: 300px;\n    max-height: 99%;\n    border-left: 1px solid #ddd;\n    border-bottom: 1px solid #ddd;\n}\n\n.test-chart-block-has-right .test-chart-block-left {\n    margin-right: 320px;\n}\n\n.test-info {\n    padding-left: 10px;\n    overflow: auto;\n}\n\npre.test-print-object {\n    font-size: 12px;\n    font-family: Menlo, Monaco, 'Courier New', monospace;\n}\n\n.test-chart {\n    height: 400px;\n}\n\n.test-data-table {\n    position: relative;\n    text-align: center;\n}\n\n.test-data-table table {\n    display: inline-block;\n    vertical-align: top;\n    border: 1px solid #ccc;\n    border-spacing: 0;\n    margin: 30px 15px;\n}\n\n.test-data-table td {\n    border: 1px solid #ccc;\n    color: #777;\n    padding: 3px 5px;\n    font-size: 13px;\n}\n\ntd.test-data-table-key {\n    font-size: 12px;\n    color: rgb(69, 162, 238)\n}\n\n.record-canvas .content-area {\n    display: none;\n    position: absolute;\n    background: #fff;\n    left: 10px;\n    top: 20px;\n    border: 2px solid #000;\n    padding: 10px;\n    z-index: 9999;\n    box-shadow: 0 0 3px #000;\n}\n\n.record-canvas textarea {\n    width: 300px;\n    height: 500px;\n}\n\n.control-frame-btn-panel {\n    position: fixed;\n    top: 10px;\n    left: 10px;\n    box-shadow: 0 0 3px #000;\n    background: green;\n    padding: 5px;\n}\n\n.control-frame-btn-panel .control-frame-info {\n    display: block;\n    color: #fff;\n    font-size: 10px;\n}"]}