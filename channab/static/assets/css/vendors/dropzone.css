@-webkit-keyframes loading {
    0% {
        background-position: 0 -400px;
    }
    100% {
        background-position: -7px -400px;
    }
}
@keyframes loading {
    0% {
        background-position: 0 -400px;
    }
    100% {
        background-position: -7px -400px;
    }
}
.dropzone {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    border: 1px solid #f6f6f7;
    background: #f6f6f7;
    padding: 1em;
    min-height: 360px;
    border-radius: 3px;
}
.dropzone * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.dropzone .dz-message {
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone .dz-preview {
    background: rgba(255, 255, 255, 0.8);
    position: relative;
    display: inline-block;
    margin: 17px;
    vertical-align: top;
    border: 1px solid #acacac;
    padding: 6px 6px 6px 6px;
    -webkit-box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.16);
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.16);
    font-size: 14px;
}
.dropzone .dz-preview .dz-details {
    position: absolute;
    top: 10px;
    background-color: #eeeeee;
    width: 50%;
    left: 32px;
    text-align: center;
}
.dropzone .dz-preview .dz-details .dz-filename {
    overflow: hidden;
    height: 0;
}
.dropzone .dz-preview .dz-details img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
}
.dropzone .dz-preview:hover .dz-details img {
    display: none;
}
.dropzone .dz-preview .dz-success-mark {
    display: none;
    position: absolute;
    width: 40px;
    height: 40px;
    font-size: 30px;
    text-align: center;
    right: -10px;
    top: -10px;
    color: #8cc657;
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.4s ease-in-out;
    transition: opacity 0.4s ease-in-out;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: no-repeat;
    background-position: -268px -163px;
}
.dropzone .dz-preview .dz-success-mark span {
    display: none;
}
.dropzone .dz-preview .dz-error-mark {
    display: none;
    position: absolute;
    width: 40px;
    height: 40px;
    font-size: 30px;
    text-align: center;
    right: -10px;
    top: -10px;
    color: #ee162d;
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.4s ease-in-out;
    transition: opacity 0.4s ease-in-out;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: no-repeat;
    background-position: -268px -123px;
}
.dropzone .dz-preview .dz-error-mark span {
    display: none;
}
.dropzone .dz-preview .dz-progress {
    position: absolute;
    top: 100px;
    left: 6px;
    right: 6px;
    height: 6px;
    background: #51bb25;
    display: none;
}
.dropzone .dz-preview .dz-progress .dz-upload {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 0%;
    background-color: #8cc657;
    -webkit-animation: loading 0.4s linear infinite;
    animation: loading 0.4s linear infinite;
    -webkit-transition: width 0.3s ease-in-out;
    transition: width 0.3s ease-in-out;
    border-radius: 2px;
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: repeat-x;
    background-position: 0px -400px;
}
.dropzone .dz-preview .dz-error-message {
    display: none;
    position: absolute;
    top: -5px;
    left: -20px;
    background: rgba(245, 245, 245, 0.8);
    padding: 8px 10px;
    color: #800;
    min-width: 140px;
    max-width: 500px;
    z-index: 500;
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.3s ease-in-out;
    transition: opacity 0.3s ease-in-out;
}
.dropzone .dz-preview:hover.dz-error .dz-error-message {
    display: block;
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone .dz-preview.dz-file-preview [data-dz-thumbnail] {
    display: none;
}
.dropzone .dz-preview.dz-error .dz-error-mark {
    display: block;
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone .dz-preview.dz-error .dz-progress .dz-upload {
    background: #51bb25;
}
.dropzone .dz-preview.dz-success .dz-success-mark {
    display: block;
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone .dz-preview.dz-success .dz-progress {
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.4s ease-in-out;
    transition: opacity 0.4s ease-in-out;
}
.dropzone .dz-preview.dz-processing .dz-progress {
    display: block;
}
.dropzone .dz-default.dz-message {
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
    -webkit-transition: opacity 0.3s ease-in-out;
    transition: opacity 0.3s ease-in-out;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    position: absolute;
    width: 428px;
    height: 123px;
    margin-left: -214px;
    margin-top: -61.5px;
    top: 50%;
    left: 50%;
}
.dropzone .dz-default.dz-message span {
    display: none;
}
.dropzone .dz-preview.dz-image-preview:hover .dz-details img {
    display: block;
    opacity: 0.1;
    filter: alpha(opacity=10);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)";
}
.dropzone-previews {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.dropzone-previews * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.dropzone-previews .dz-preview {
    background: rgba(255, 255, 255, 0.8);
    position: relative;
    display: inline-block;
    margin: 17px;
    vertical-align: top;
    border: 1px solid #acacac;
    padding: 6px 6px 6px 6px;
    -webkit-box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.16);
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.16);
    font-size: 14px;
}
.dropzone-previews .dz-preview .dz-details {
    width: 100px;
    height: 100px;
    position: relative;
    background: #ebebeb;
    padding: 5px;
    margin-bottom: 22px;
}
.dropzone-previews .dz-preview .dz-details .dz-filename {
    overflow: hidden;
    height: 100%;
}
.dropzone-previews .dz-preview .dz-details img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
}
.dropzone-previews .dz-preview .dz-details .dz-size {
    position: absolute;
    bottom: -28px;
    left: 3px;
    height: 28px;
    line-height: 28px;
}
.dropzone-previews .dz-preview:hover .dz-details img {
    display: none;
}
.dropzone-previews .dz-preview .dz-success-mark {
    display: none;
    position: absolute;
    width: 40px;
    height: 40px;
    font-size: 30px;
    text-align: center;
    right: -10px;
    top: -10px;
    color: #8cc657;
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.4s ease-in-out;
    transition: opacity 0.4s ease-in-out;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: no-repeat;
    background-position: -268px -163px;
}
.dropzone-previews .dz-preview .dz-success-mark span {
    display: none;
}
.dropzone-previews .dz-preview .dz-error-mark {
    display: none;
    position: absolute;
    width: 40px;
    height: 40px;
    font-size: 30px;
    text-align: center;
    right: -10px;
    top: -10px;
    color: #ee162d;
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.4s ease-in-out;
    transition: opacity 0.4s ease-in-out;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: no-repeat;
    background-position: -268px -123px;
}
.dropzone-previews .dz-preview .dz-error-mark span {
    display: none;
}
.dropzone-previews .dz-preview .dz-progress {
    position: absolute;
    top: 100px;
    left: 6px;
    right: 6px;
    height: 6px;
    background: #51bb25;
    display: none;
}
.dropzone-previews .dz-preview .dz-progress .dz-upload {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 0%;
    background-color: #8cc657;
    -webkit-animation: loading 0.4s linear infinite;
    animation: loading 0.4s linear infinite;
    -webkit-transition: width 0.3s ease-in-out;
    transition: width 0.3s ease-in-out;
    border-radius: 2px;
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-image: image-url("dropzone-spritemap.png");
    background-repeat: repeat-x;
    background-position: 0px -400px;
}
.dropzone-previews .dz-preview .dz-error-message {
    display: none;
    position: absolute;
    top: -5px;
    left: -20px;
    background: rgba(245, 245, 245, 0.8);
    padding: 8px 10px;
    color: #800;
    min-width: 140px;
    max-width: 500px;
    z-index: 500;
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.3s ease-in-out;
    transition: opacity 0.3s ease-in-out;
}
.dropzone-previews .dz-preview:hover.dz-error .dz-error-message {
    display: block;
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone-previews .dz-preview.dz-file-preview [data-dz-thumbnail] {
    display: none;
}
.dropzone-previews .dz-preview.dz-error .dz-error-mark {
    display: block;
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone-previews .dz-preview.dz-error .dz-progress .dz-upload {
    background: #ee1e2d;
}
.dropzone-previews .dz-preview.dz-success .dz-success-mark {
    display: block;
    opacity: 1;
    -ms-filter: none;
    -webkit-filter: none;
    filter: none;
}
.dropzone-previews .dz-preview.dz-success .dz-progress {
    display: block;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: opacity 0.4s ease-in-out;
    transition: opacity 0.4s ease-in-out;
}
.dropzone-previews .dz-preview.dz-processing .dz-progress {
    display: block;
}
.dropzone-previews .dz-preview.dz-image-preview:hover .dz-details img {
    display: block;
    opacity: 0.1;
    filter: alpha(opacity=10);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)";
}
.dropzone.dz-clickable {
    cursor: pointer;
    text-align: center;
}
.dropzone.dz-clickable .dz-message {
    cursor: pointer;
}
.dropzone.dz-clickable .dz-message * {
    cursor: default;
}
.dropzone.dz-clickable .dz-message .dz-message {
    cursor: pointer;
}
.dropzone.dz-clickable * {
    cursor: default;
    border-radius: 5px;
}
.dropzone.dz-drag-hover {
    border-color: rgba(0, 0, 0, 0.15);
    background: rgba(0, 0, 0, 0.04);
}
.dropzone.dz-drag-hover .dz-message {
    opacity: 0.15;
    filter: alpha(opacity=15);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=15)";
}
.dropzone.dz-started .dz-message {
    display: none;
    opacity: 0;
    filter: alpha(opacity=0);
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.dropzone.dz-square .dz-default.dz-message {
    background-position: 0 -123px;
    width: 268px;
    margin-left: -134px;
    height: 174px;
    margin-top: -87px;
}
.dropzone {
    margin-right: auto;
    margin-left: auto;
    padding: 50px;
    border: 2px dashed #ced4da;
    background-color: #f2f9fc;
    border-radius: 15px;
    -o-border-image: none;
    border-image: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    min-height: 150px;
    position: relative;
}
.dropzone * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.dropzone i {
    font-size: 50px;
    color: var(--theme-color);
}
.dropzone .dz-message {
    text-align: center;
    margin: 25px 0;
}
.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    margin: 0.5em;
    padding: 0;
    border: none;
    background-color: #eeeeee;
    width: 120px;
    height: 120px;
    -webkit-box-shadow: 0px 0px 3px var(--theme-color);
    box-shadow: 0px 0px 3px var(--theme-color);
}
.dropzone .dz-preview .dz-progress {
    display: block;
    height: 10px;
    border: 1px solid #51bb25;
    left: 12px;
    right: 12px;
}
.dropzone .dz-preview .dz-progress .dz-upload {
    display: block;
    height: 100%;
    width: 0;
    background: #51bb25;
}
.dropzone .dz-preview .dz-error-message {
    color: red;
    display: none;
    top: 131px;
    left: -12px;
    pointer-events: none;
}
.dropzone .dz-preview .dz-error-message:after {
    content: "";
    position: absolute;
    top: -6px;
    left: 64px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #be2626;
}
.dropzone .dz-preview.dz-error .dz-error-message,
.dropzone .dz-preview.dz-error .dz-error-mark {
    display: block;
}
.dropzone .dz-preview.dz-success .dz-success-mark {
    display: block;
}
.dropzone .dz-preview .dz-error-mark,
.dropzone .dz-preview .dz-success-mark {
    position: absolute;
    display: none;
    left: 30px;
    top: 30px;
    width: 54px;
    height: 58px;
    left: 50%;
    margin-left: -27px;
}
/*# sourceMappingURL=dropzone.css.map */
