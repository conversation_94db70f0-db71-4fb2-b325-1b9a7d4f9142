{"version": 3, "sources": ["vendors/chartist/_chartist.scss", "vendors/chartist/_chartist-settings.scss"], "names": [], "mappings": "AA+HE,UA9DA,oBClCgC,CDmChC,qBCnCgC,CDoChC,gBCnCoB,CDoCpB,aCjCqB,CD8FpB,iDAzED,aAAc,CACd,mBAAoB,CAEpB,mBAAoB,CAEpB,YAAa,CAyEZ,kDAIC,yBAA0B,CAC3B,iCA5GD,0BA+GoC,CA7GpC,uBA6GoC,CA5GpC,oBA4GoC,CA3GpC,2BA2GgD,CAzGhD,wBAyGgD,CAxGhD,0BAwGgD,CApG9C,eAAgB,CAsGhB,iBAAkB,CACnB,+BAlHD,4BAqHsC,CAnHtC,yBAmHsC,CAlHtC,sBAkHsC,CAjHtC,2BAiHkD,CA/GlD,wBA+GkD,CA9GlD,0BA8GkD,CA1GhD,eAAgB,CA4GhB,iBAAkB,CACnB,+BAxHD,0BA2HoC,CAzHpC,uBAyHoC,CAxHpC,oBAwHoC,CAvHpC,yBAuH8C,CArH9C,sBAqH8C,CApH9C,wBAoH8C,CA5G5C,gBAAiB,CA8GjB,eAAgB,CACjB,6BA9HD,0BAiIoC,CA/HpC,uBA+HoC,CA9HpC,oBA8HoC,CA7HpC,2BA6HgD,CA3HhD,wBA2HgD,CA1HhD,0BA0HgD,CAtH9C,eAAgB,CAwHhB,iBAAkB,CACnB,+CApID,0BAuIoC,CArIpC,uBAqIoC,CApIpC,oBAoIoC,CAnIpC,uBAmI4C,CAjI5C,oBAiI4C,CAhI5C,sBAgI4C,CApH1C,iBAAkB,CAsHlB,iBAAkB,CACnB,6CA1ID,4BA6IsC,CA3ItC,yBA2IsC,CA1ItC,sBA0IsC,CAzItC,uBAyI8C,CAvI9C,oBAuI8C,CAtI9C,sBAsI8C,CA1H5C,iBAAkB,CA4HlB,iBAAkB,CACnB,kEAhJD,0BAmJoC,CAjJpC,uBAiJoC,CAhJpC,oBAgJoC,CA/IpC,2BA+IgD,CA7IhD,wBA6IgD,CA5IhD,0BA4IgD,CAxI9C,eAAgB,CA0IhB,iBAAkB,CACnB,gEAtJD,4BAyJsC,CAvJtC,yBAuJsC,CAtJtC,sBAsJsC,CArJtC,2BAqJkD,CAnJlD,wBAmJkD,CAlJlD,0BAkJkD,CA9IhD,eAAgB,CAgJhB,iBAAkB,CACnB,gEA5JD,wBAgKkC,CA9JlC,qBA8JkC,CA7JlC,kBA6JkC,CA5JlC,yBA4J4C,CA1J5C,sBA0J4C,CAzJ5C,wBAyJ4C,CAjJ1C,gBAAiB,CAmJjB,eAAgB,CACjB,8DAnKD,wBAsKkC,CApKlC,qBAoKkC,CAnKlC,kBAmKkC,CAlKlC,2BAkK8C,CAhK9C,wBAgK8C,CA/J9C,0BA+J8C,CA3J5C,eAAgB,CA6JhB,eAAgB,CACjB,SAhID,sBClCgC,CDmChC,gBCjCiB,CDoCf,oBCrCmB,CDqKpB,oBAGC,SCtK0B,CDuK3B,UA/HD,iBCnCkB,CDoClB,oBClCoB,CDoKnB,SA9HD,SAAU,CACV,gBC3CiB,CD4KhB,SAzHD,WAAY,CACZ,eC9CmB,CD0KlB,QAxHD,SAAU,CACV,iBChDiB,CD2KhB,gBAvHD,SAAU,CACV,iBClDmB,CDuDnB,+FAIE,cC5CuB,CD6CxB,oFAKC,YClDuB,CDwCzB,+FAIE,cC3CK,CD4CN,oFAKC,YCjDK,CDuCP,+FAIE,cC1CK,CD2CN,oFAKC,YChDK,CDsCP,+FAIE,cCzCK,CD0CN,oFAKC,YC/CK,CDqCP,+FAIE,cCxCK,CDyCN,oFAKC,YC9CK,CDoCP,+FAIE,cCvCK,CDwCN,oFAKC,YC7CK,CDmCP,+FAIE,cCtCK,CDuCN,oFAKC,YC5CK,CDkCP,+FAIE,cCrCK,CDsCN,oFAKC,YC3CK,CDiCP,+FAIE,cCpCK,CDqCN,oFAKC,YC1CK,CDgCP,+FAIE,cCnCK,CDoCN,oFAKC,YCzCK,CD+BP,+FAIE,cClCK,CDmCN,oFAKC,YCxCK,CD8BP,+FAIE,cCjCK,CDkCN,oFAKC,YCvCK,CD6BP,+FAIE,cChCK,CDiCN,oFAKC,YCtCK,CD4BP,+FAIE,cC/BK,CDgCN,oFAKC,YCrCK,CD2BP,+FAIE,cC9BK,CD+BN,oFAKC,YCpCK,CDqCN,WAvHD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,kBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,mBAA6B,CAC9B,iBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,eAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,iBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,wBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,qBAA6B,CAC9B,uBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,qBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,iBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,wBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,wBAA6B,CAC9B,uBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,qBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,gBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,uBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,wBAA6B,CAC9B,sBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,oBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,gBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,uBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,kBAA6B,CAC9B,sBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,oBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,mBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,0BACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,kBAA6B,CAC9B,yBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,uBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,kBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,yBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,wBAA6B,CAC9B,wBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,sBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,gBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,uBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,oBAA6B,CAC9B,sBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,oBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,mBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,0BACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,uBAA6B,CAC9B,yBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,uBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,gBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,uBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,kBAA6B,CAC9B,sBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,oBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,kBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,yBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,qBAA6B,CAC9B,wBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,sBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,kBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,yBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,wBAA6B,CAC9B,wBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,sBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,WAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,kBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,kBAA6B,CAC9B,iBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,eAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,gBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,uBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,kBAA6B,CAC9B,sBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,oBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,mBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,0BACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,oBAA6B,CAC9B,yBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,uBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,kBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,yBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,wBAA6B,CAC9B,wBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,sBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO,CACR,kBAxBD,aAAc,CACd,iBAAkB,CAClB,UAH6C,CAK7C,yBACE,aAAc,CACd,UAAW,CACX,UAAW,CACX,OAAQ,CACR,QAAS,CACT,kBAA6B,CAC9B,wBAGC,UAAW,CACX,aAAc,CACd,UAAW,CACZ,sBAGC,aAAc,CACd,iBAAkB,CAClB,KAAM,CACN,MAAO", "file": "vendors/chartist.css", "sourcesContent": ["@import \"chartist-settings\";\n\n@mixin ct-responsive-svg-container($width: 100%, $ratio: $ct-container-ratio) {\n  display: block;\n  position: relative;\n  width: $width;\n\n  &:before {\n    display: block;\n    float: left;\n    content: \"\";\n    width: 0;\n    height: 0;\n    padding-bottom: $ratio * 100%;\n  }\n\n  &:after {\n    content: \"\";\n    display: table;\n    clear: both;\n  }\n\n  >svg {\n    display: block;\n    position: absolute;\n    top: 0;\n    left: 0;\n  }\n}\n\n@mixin ct-align-justify($ct-text-align: $ct-text-align, $ct-text-justify: $ct-text-justify) {\n  -webkit-box-align: $ct-text-align;\n  -webkit-align-items: $ct-text-align;\n  -ms-flex-align: $ct-text-align;\n  align-items: $ct-text-align;\n  -webkit-box-pack: $ct-text-justify;\n  -webkit-justify-content: $ct-text-justify;\n  -ms-flex-pack: $ct-text-justify;\n  justify-content: $ct-text-justify;\n\n  // Fallback to text-align for non-flex browsers\n  @if($ct-text-justify=='flex-start') {\n    text-align: left;\n  }\n\n  @else if ($ct-text-justify=='flex-end') {\n    text-align: right;\n  }\n\n  @else {\n    text-align: center;\n  }\n}\n\n@mixin ct-flex() {\n  // Fallback to block\n  display: block;\n  display: -webkit-box;\n  display: -moz-box;\n  display: -ms-flexbox;\n  display: -webkit-flex;\n  display: flex;\n}\n\n@mixin ct-chart-label($ct-text-color: $ct-text-color, $ct-text-size: $ct-text-size, $ct-text-line-height: $ct-text-line-height) {\n  fill: $ct-text-color;\n  color: $ct-text-color;\n  font-size: $ct-text-size;\n  line-height: $ct-text-line-height;\n}\n\n@mixin ct-chart-grid($ct-grid-color: $ct-grid-color, $ct-grid-width: $ct-grid-width, $ct-grid-dasharray: $ct-grid-dasharray) {\n  stroke: $ct-grid-color;\n  stroke-width: $ct-grid-width;\n\n  @if ($ct-grid-dasharray) {\n    stroke-dasharray: $ct-grid-dasharray;\n  }\n}\n\n@mixin ct-chart-point($ct-point-size: $ct-point-size, $ct-point-shape: $ct-point-shape) {\n  stroke-width: $ct-point-size;\n  stroke-linecap: $ct-point-shape;\n}\n\n@mixin ct-chart-line($ct-line-width: $ct-line-width, $ct-line-dasharray: $ct-line-dasharray) {\n  fill: none;\n  stroke-width: $ct-line-width;\n\n  @if ($ct-line-dasharray) {\n    stroke-dasharray: $ct-line-dasharray;\n  }\n}\n\n@mixin ct-chart-area($ct-area-opacity: $ct-area-opacity) {\n  stroke: none;\n  fill-opacity: $ct-area-opacity;\n}\n\n@mixin ct-chart-bar($ct-bar-width: $ct-bar-width) {\n  fill: none;\n  stroke-width: $ct-bar-width;\n}\n\n@mixin ct-chart-donut($ct-donut-width: $ct-donut-width) {\n  fill: none;\n  stroke-width: $ct-donut-width;\n}\n\n@mixin ct-chart-series-color($color) {\n\n  .#{$ct-class-point},\n  .#{$ct-class-line},\n  .#{$ct-class-bar},\n  .#{$ct-class-slice-donut} {\n    stroke: $color;\n  }\n\n  .#{$ct-class-slice-pie},\n  .#{$ct-class-slice-donut-solid},\n  .#{$ct-class-area} {\n    fill: $color;\n  }\n}\n\n@mixin ct-chart($ct-container-ratio: $ct-container-ratio, $ct-text-color: $ct-text-color, $ct-text-size: $ct-text-size, $ct-grid-color: $ct-grid-color, $ct-grid-width: $ct-grid-width, $ct-grid-dasharray: $ct-grid-dasharray, $ct-point-size: $ct-point-size, $ct-point-shape: $ct-point-shape, $ct-line-width: $ct-line-width, $ct-bar-width: $ct-bar-width, $ct-donut-width: $ct-donut-width, $ct-series-names: $ct-series-names, $ct-series-colors: $ct-series-colors) {\n\n  .#{$ct-class-label} {\n    @include ct-chart-label($ct-text-color, $ct-text-size);\n  }\n\n  .#{$ct-class-chart-line} .#{$ct-class-label},\n  .#{$ct-class-chart-bar} .#{$ct-class-label} {\n    @include ct-flex();\n  }\n\n  .#{$ct-class-chart-pie} .#{$ct-class-label},\n  .#{$ct-class-chart-donut} .#{$ct-class-label} {\n    dominant-baseline: central;\n  }\n\n  .#{$ct-class-label}.#{$ct-class-horizontal}.#{$ct-class-start} {\n    @include ct-align-justify(flex-end, flex-start);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-label}.#{$ct-class-horizontal}.#{$ct-class-end} {\n    @include ct-align-justify(flex-start, flex-start);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-label}.#{$ct-class-vertical}.#{$ct-class-start} {\n    @include ct-align-justify(flex-end, flex-end);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: end;\n  }\n\n  .#{$ct-class-label}.#{$ct-class-vertical}.#{$ct-class-end} {\n    @include ct-align-justify(flex-end, flex-start);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-chart-bar} .#{$ct-class-label}.#{$ct-class-horizontal}.#{$ct-class-start} {\n    @include ct-align-justify(flex-end, center);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-chart-bar} .#{$ct-class-label}.#{$ct-class-horizontal}.#{$ct-class-end} {\n    @include ct-align-justify(flex-start, center);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-chart-bar}.#{$ct-class-horizontal-bars} .#{$ct-class-label}.#{$ct-class-horizontal}.#{$ct-class-start} {\n    @include ct-align-justify(flex-end, flex-start);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-chart-bar}.#{$ct-class-horizontal-bars} .#{$ct-class-label}.#{$ct-class-horizontal}.#{$ct-class-end} {\n    @include ct-align-justify(flex-start, flex-start);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: start;\n  }\n\n  .#{$ct-class-chart-bar}.#{$ct-class-horizontal-bars} .#{$ct-class-label}.#{$ct-class-vertical}.#{$ct-class-start} {\n    //@include ct-chart-label($ct-text-color, $ct-text-size, center, $ct-vertical-text-justify);\n    @include ct-align-justify(center, flex-end);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: end;\n  }\n\n  .#{$ct-class-chart-bar}.#{$ct-class-horizontal-bars} .#{$ct-class-label}.#{$ct-class-vertical}.#{$ct-class-end} {\n    @include ct-align-justify(center, flex-start);\n    // Fallback for browsers that don't support foreignObjects\n    text-anchor: end;\n  }\n\n  .#{$ct-class-grid} {\n    @include ct-chart-grid($ct-grid-color, $ct-grid-width, $ct-grid-dasharray);\n  }\n\n  .#{$ct-class-grid-background} {\n    fill: $ct-grid-background-fill;\n  }\n\n  .#{$ct-class-point} {\n    @include ct-chart-point($ct-point-size, $ct-point-shape);\n  }\n\n  .#{$ct-class-line} {\n    @include ct-chart-line($ct-line-width);\n  }\n\n  .#{$ct-class-area} {\n    @include ct-chart-area();\n  }\n\n  .#{$ct-class-bar} {\n    @include ct-chart-bar($ct-bar-width);\n  }\n\n  .#{$ct-class-slice-donut} {\n    @include ct-chart-donut($ct-donut-width);\n  }\n\n  @if $ct-include-colored-series {\n    @for $i from 0 to length($ct-series-names) {\n      .#{$ct-class-series}-#{nth($ct-series-names, $i + 1)} {\n        $color: nth($ct-series-colors, $i + 1);\n\n        @include ct-chart-series-color($color);\n      }\n    }\n  }\n}\n\n@if $ct-include-classes {\n  @include ct-chart();\n\n  @if $ct-include-alternative-responsive-containers {\n    @for $i from 0 to length($ct-scales-names) {\n      .#{nth($ct-scales-names, $i + 1)} {\n        @include ct-responsive-svg-container($ratio: nth($ct-scales, $i + 1));\n      }\n    }\n  }\n}", "// Scales for responsive SVG containers\n$ct-scales: ((1), (15/16), (8/9), (5/6), (4/5), (3/4), (2/3), (5/8), (1/1.618), (3/5), (9/16), (8/15), (1/2), (2/5), (3/8), (1/3), (1/4)) !default;\n$ct-scales-names: (ct-square, ct-minor-second, ct-major-second, ct-minor-third, ct-major-third, ct-perfect-fourth, ct-perfect-fifth, ct-minor-sixth, ct-golden-section, ct-major-sixth, ct-minor-seventh, ct-major-seventh, ct-octave, ct-major-tenth, ct-major-eleventh, ct-major-twelfth, ct-double-octave) !default;\n\n// Class names to be used when generating CSS\n$ct-class-chart: ct-chart !default;\n$ct-class-chart-line: ct-chart-line !default;\n$ct-class-chart-bar: ct-chart-bar !default;\n$ct-class-horizontal-bars: ct-horizontal-bars !default;\n$ct-class-chart-pie: ct-chart-pie !default;\n$ct-class-chart-donut: ct-chart-donut !default;\n$ct-class-label: ct-label !default;\n$ct-class-series: ct-series !default;\n$ct-class-line: ct-line !default;\n$ct-class-point: ct-point !default;\n$ct-class-area: ct-area !default;\n$ct-class-bar: ct-bar !default;\n$ct-class-slice-pie: ct-slice-pie !default;\n$ct-class-slice-donut: ct-slice-donut !default;\n$ct-class-slice-donut-solid: ct-slice-donut-solid !default;\n$ct-class-grid: ct-grid !default;\n$ct-class-grid-background: ct-grid-background !default;\n$ct-class-vertical: ct-vertical !default;\n$ct-class-horizontal: ct-horizontal !default;\n$ct-class-start: ct-start !default;\n$ct-class-end: ct-end !default;\n\n// Container ratio\n$ct-container-ratio: (1/1.618) !default;\n\n// Text styles for labels\n$ct-text-color: rgba(0, 0, 0, 0.4) !default;\n$ct-text-size: 0.75rem !default;\n$ct-text-align: flex-start !default;\n$ct-text-justify: flex-start !default;\n$ct-text-line-height: 1;\n\n// Grid styles\n$ct-grid-color: rgba(0, 0, 0, 0.2) !default;\n$ct-grid-dasharray: 2px !default;\n$ct-grid-width: 1px !default;\n$ct-grid-background-fill: none !default;\n\n// Line chart properties\n$ct-line-width: 4px !default;\n$ct-line-dasharray: false !default;\n$ct-point-size: 10px !default;\n// Line chart point, can be either round or square\n$ct-point-shape: round !default;\n// Area fill transparency between 0 and 1\n$ct-area-opacity: 0.1 !default;\n\n// Bar chart bar width\n$ct-bar-width: 10px !default;\n\n// Donut width (If donut width is to big it can cause issues where the shape gets distorted)\n$ct-donut-width: 60px !default;\n\n// If set to true it will include the default classes and generate CSS output. If you're planning to use the mixins you\n// should set this property to false\n$ct-include-classes: true !default;\n\n// If this is set to true the CSS will contain colored series. You can extend or change the color with the\n// properties below\n$ct-include-colored-series: $ct-include-classes !default;\n\n// If set to true this will include all responsive container variations using the scales defined at the top of the script\n$ct-include-alternative-responsive-containers: $ct-include-classes !default;\n\n// Series names and colors. This can be extended or customized as desired. Just add more series and colors.\n$ct-series-names: (a, b, c, d, e, f, g, h, i, j, k, l, m, n, o) !default;\n$ct-series-colors: (#7366ff,\n  #f73164,\n  #51bb25,\n  #f8d62b,\n  #f8d62b,\n  #dc3545,\n  #7366ff,\n  #7366ff,\n  #7366ff,\n  #7366ff,\n  #7366ff,\n  #7366ff,\n  #7366ff,\n  #7366ff,\n  #7366ff) !default;"]}