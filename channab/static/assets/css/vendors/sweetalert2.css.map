{"version": 3, "sources": ["vendors/sweetalert2-master/_animations.scss", "vendors/sweetalert2-master/_toasts.scss", "vendors/sweetalert2-master/_variables.scss", "vendors/sweetalert2.scss"], "names": [], "mappings": "AACA,8BACE,GACE,4BAAW,CAAX,oBAAoB,CAGtB,IACE,6BAAW,CAAX,qBAAsB,CAGxB,IACE,6BAAW,CAAX,qBAAqB,CAGvB,KACE,0BAAW,CAAX,kBAAmB,CAAA,CAdvB,sBACE,GACE,4BAAW,CAAX,oBAAoB,CAGtB,IACE,6BAAW,CAAX,qBAAsB,CAGxB,IACE,6BAAW,CAAX,qBAAqB,CAGvB,KACE,0BAAW,CAAX,kBAAmB,CAAA,CAKvB,8BACE,GACE,0BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CAGZ,KACE,4BAAoB,CAApB,oBAAoB,CACpB,SAAU,CAAA,CARd,sBACE,GACE,0BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CAGZ,KACE,4BAAoB,CAApB,oBAAoB,CACpB,SAAU,CAAA,CAKd,kDACE,GACE,YAAa,CACb,YAAa,CACb,OAAQ,CAGV,IACE,YAAa,CACb,WAAY,CACZ,OAAQ,CAGV,IACE,YAAa,CACb,YAAa,CACb,aAAc,CAGhB,IACE,OAAQ,CACR,aAAc,CACd,cAAe,CAGjB,KACE,YAAa,CACb,WAAY,CACZ,cAAe,CAAA,CA5BnB,0CACE,GACE,YAAa,CACb,YAAa,CACb,OAAQ,CAGV,IACE,YAAa,CACb,WAAY,CACZ,OAAQ,CAGV,IACE,YAAa,CACb,YAAa,CACb,aAAc,CAGhB,IACE,OAAQ,CACR,aAAc,CACd,cAAe,CAGjB,KACE,YAAa,CACb,WAAY,CACZ,cAAe,CAAA,CAInB,mDACE,GACE,WAAY,CACZ,aAAc,CACd,OAAQ,CAGV,IACE,WAAY,CACZ,aAAc,CACd,OAAQ,CAGV,IACE,YAAa,CACb,OAAQ,CACR,cAAe,CAGjB,KACE,WAAY,CACZ,UAAW,CACX,cAAe,CAAA,CAtBnB,2CACE,GACE,WAAY,CACZ,aAAc,CACd,OAAQ,CAGV,IACE,WAAY,CACZ,aAAc,CACd,OAAQ,CAGV,IACE,YAAa,CACb,OAAQ,CACR,cAAe,CAGjB,KACE,WAAY,CACZ,UAAW,CACX,cAAe,CAAA,CAInB,sDACE,GACE,gCAAW,CAAX,wBAAyB,CAG3B,GACE,gCAAW,CAAX,wBAAyB,CAG3B,IACE,iCAAW,CAAX,yBAA0B,CAG5B,KACE,iCAAW,CAAX,yBAA0B,CAAA,CAd9B,8CACE,GACE,gCAAW,CAAX,wBAAyB,CAG3B,GACE,gCAAW,CAAX,wBAAyB,CAG3B,IACE,iCAAW,CAAX,yBAA0B,CAG5B,KACE,iCAAW,CAAX,yBAA0B,CAAA,CAK9B,8CACE,GACE,kBAAmB,CACnB,4BAAoB,CAApB,oBAAoB,CACpB,SAAU,CAGZ,IACE,kBAAmB,CACnB,4BAAoB,CAApB,oBAAoB,CACpB,SAAU,CAGZ,IACE,kBAAmB,CACnB,6BAAW,CAAX,qBAAsB,CAGxB,KACE,YAAa,CACb,0BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CAAA,CArBd,sCACE,GACE,kBAAmB,CACnB,4BAAoB,CAApB,oBAAoB,CACpB,SAAU,CAGZ,IACE,kBAAmB,CACnB,4BAAoB,CAApB,oBAAoB,CACpB,SAAU,CAGZ,IACE,kBAAmB,CACnB,6BAAW,CAAX,qBAAsB,CAGxB,KACE,YAAa,CACb,0BAAmB,CAAnB,kBAAmB,CACnB,SAAU,CAAA,CAId,4CACE,GACE,iCAA0B,CAA1B,yBAA0B,CAC1B,SAAU,CAGZ,KACE,+BAAwB,CAAxB,uBAAwB,CACxB,SAAU,CAAA,CARd,oCACE,GACE,iCAA0B,CAA1B,yBAA0B,CAC1B,SAAU,CAGZ,KACE,+BAAwB,CAAxB,uBAAwB,CACxB,SAAU,CAAA,CC/Id,wCAGM,cAAe,CACf,4BAA6B,CAJnC,oDAOQ,4BAA6B,CAPrC,kDAWQ,KAAM,CACN,UAAW,CACX,WAAY,CACZ,QAAS,CACT,kCAAW,CAAX,0BAA2B,CAfnC,8GAoBQ,KAAM,CACN,OAAQ,CACR,WAAY,CACZ,SAAU,CAvBlB,+GA4BQ,KAAM,CACN,UAAW,CACX,WAAY,CACZ,MAAO,CA/Bf,qHAoCQ,OAAQ,CACR,UAAW,CACX,WAAY,CACZ,MAAO,CACP,kCAAW,CAAX,0BAA2B,CAxCnC,qDA4CQ,OAAQ,CACR,UAAW,CACX,WAAY,CACZ,QAAS,CACT,uCAAW,CAAX,+BAAgC,CAhDxC,oHAqDQ,OAAQ,CACR,OAAQ,CACR,WAAY,CACZ,SAAU,CACV,kCAAW,CAAX,0BAA2B,CAzDnC,qHA8DQ,QAAS,CACT,UAAW,CACX,QAAS,CACT,MAAO,CAjEf,qDAqEQ,QAAS,CACT,UAAW,CACX,QAAS,CACT,QAAS,CACT,kCAAW,CAAX,0BAA2B,CAzEnC,oHA8EQ,QAAS,CACT,OAAQ,CACR,QAAS,CACT,SAAU,CAjFlB,qCAwFM,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,yBAAa,CAAb,sBAAa,CAAb,mBAAoB,CAzF1B,oDA4FQ,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,2BAAmB,CAAnB,kBAAmB,CACnB,YAAa,CACb,kBAAmB,CA/F3B,oDAmGQ,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CAnG/B,kDAuGQ,UAAW,CACX,mBAAoB,CACpB,aC+ByB,CDxIjC,+DA6GQ,aC4B8B,CD3B/B,yBAQH,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,UCYoB,CDXpB,cCYwB,CDXxB,sCCnHsB,CDmHtB,8BCnHsB,CDoHtB,iBAAkB,CAPtB,uCAUM,6BAAgB,CAAhB,4BAAgB,CAAhB,sBAAgB,CAAhB,kBAAmB,CAVzB,sCAcM,kBAAY,CAAZ,mBAAY,CAAZ,WAAY,CACZ,sBAA2B,CAA3B,mBAA2B,CAA3B,0BAA2B,CAC3B,aAAc,CACd,aCC2B,CDlBjC,uCAqBM,gBCE8B,CDD9B,iBCC8B,CDA9B,cCE6B,CDzBnC,sCA2BM,gBAAiB,CACjB,UCf+B,CDgB/B,WCfgC,CDgBhC,cCfmC,CDfzC,wCAkCM,sBAA2B,CAA3B,mBAA2B,CAA3B,0BAA2B,CAC3B,aChB6B,CDnBnC,qCAuCM,SAAU,CACV,aAAc,CACd,UAAW,CACX,QAAS,CA1Cf,0CA6CQ,aAAc,CACd,gBAAiB,CACjB,eAAgB,CA/CxB,uEAqDU,SAAU,CACV,UAAW,CAtDrB,8EA6DU,UAAW,CACX,aAAc,CA9DxB,6FAiEY,YAAa,CAjEzB,8FAqEY,aAAc,CArE1B,wCA4EM,WAAY,CACZ,gBAAiB,CA7EvB,uCAiFM,gBAAiB,CACjB,sBAAuB,CACvB,aC7D6B,CDtBnC,6CAsFQ,yEC5EqB,CD4ErB,iEC5E2C,CDVnD,wCA2FM,oBCxLiB,CD6FvB,+EA+FQ,iBAAkB,CAClB,SAAU,CACV,eAAgB,CAChB,+BAAwB,CAAxB,uBAAwB,CACxB,iBAAkB,CAnG1B,8FAsGU,UAAW,CACX,aAAc,CACd,gCAAyB,CAAzB,wBAAyB,CACzB,gCAAyB,CAAzB,wBAAyB,CACzB,yBAA0B,CA1GpC,+FA8GU,UAAW,CACX,YAAa,CACb,8BAAuB,CAAvB,sBAAuB,CACvB,yBAA0B,CAjHpC,4DAsHQ,SAAU,CACV,UAAW,CAvHnB,2DA2HQ,KAAM,CACN,YAAa,CACb,aAAc,CACd,eAAgB,CA9HxB,sEAkIQ,cAAe,CAlIvB,oFAqIU,WAAY,CACZ,YAAa,CACb,WAAY,CAvItB,qFA2IU,WAAY,CACZ,aAAc,CACd,aAAc,CA7IxB,oCAmJM,oCAAW,CAAX,4BAA6B,CAnJnC,oCAuJM,6CAAW,CAAX,qCAAsC,CAvJ5C,6EA4JQ,gDAAW,CAAX,wCAAyC,CA5JjD,8EAgKQ,iDAAW,CAAX,yCAA0C,CAC3C,kCAOL,GACE,oDAA4C,CAA5C,4CAA4C,CAC5C,SAAU,CAGZ,IACE,8CAAuC,CAAvC,sCAAuC,CACvC,UAAW,CAGb,IACE,oDAA4C,CAA5C,4CAA4C,CAC5C,UAAW,CAGb,KACE,0CAAmC,CAAnC,kCAAmC,CACnC,SAAU,CAAA,CAxBP,0BAOL,GACE,oDAA4C,CAA5C,4CAA4C,CAC5C,SAAU,CAGZ,IACE,8CAAuC,CAAvC,sCAAuC,CACvC,UAAW,CAGb,IACE,oDAA4C,CAA5C,4CAA4C,CAC5C,UAAW,CAGb,KACE,0CAAmC,CAAnC,kCAAmC,CACnC,SAAU,CAAA,CAId,kCACE,GACE,SAAU,CAGZ,IACE,UAAW,CAGb,KACE,+BAAwB,CAAxB,uBAAwB,CACxB,SAAU,CAAA,CAXd,0BACE,GACE,SAAU,CAGZ,IACE,UAAW,CAGb,KACE,+BAAwB,CAAxB,uBAAwB,CACxB,SAAU,CAAA,CAId,6CACE,GACE,WAAY,CACZ,YAAa,CACb,OAAQ,CAGV,IACE,UAAW,CACX,WAAY,CACZ,OAAQ,CAGV,IACE,UAAW,CACX,WAAY,CACZ,aAAc,CAGhB,IACE,YAAa,CACb,UAAW,CACX,UAAW,CAGb,KACE,WAAY,CACZ,YAAa,CACb,WAAY,CAAA,CA5BhB,qCACE,GACE,WAAY,CACZ,YAAa,CACb,OAAQ,CAGV,IACE,UAAW,CACX,WAAY,CACZ,OAAQ,CAGV,IACE,UAAW,CACX,WAAY,CACZ,aAAc,CAGhB,IACE,YAAa,CACb,UAAW,CACX,UAAW,CAGb,KACE,WAAY,CACZ,YAAa,CACb,WAAY,CAAA,CAIhB,8CACE,GACE,WAAY,CACZ,aAAc,CACd,OAAQ,CAGV,IACE,UAAW,CACX,aAAc,CACd,OAAQ,CAGV,IACE,WAAY,CACZ,OAAQ,CACR,aAAc,CAGhB,KACE,WAAY,CACZ,aAAc,CACd,aAAc,CAAA,CAtBlB,sCACE,GACE,WAAY,CACZ,aAAc,CACd,OAAQ,CAGV,IACE,UAAW,CACX,aAAc,CACd,OAAQ,CAGV,IACE,WAAY,CACZ,OAAQ,CACR,aAAc,CAGhB,KACE,WAAY,CACZ,aAAc,CACd,aAAc,CAAA,CE9WlB,iEAMM,eAAgB,CANtB,uBAWI,sBAAuB,CAX3B,oCAgBM,QAAS,CACT,UAAW,CACX,WAAY,CACZ,SAAU,CACV,4BAA6B,CApBnC,iDAuBQ,2CD9BM,CC8BN,mCD9BU,CCOlB,8CA2BQ,KAAM,CACN,QAAS,CACT,kCAAW,CAAX,0BAA2B,CA7BnC,uGAkCQ,KAAM,CACN,MAAO,CAnCf,sGAwCQ,KAAM,CACN,OAAQ,CAzChB,iDA6CQ,OAAQ,CACR,QAAS,CACT,uCAAW,CAAX,+BAAgC,CA/CxC,6GAoDQ,OAAQ,CACR,MAAO,CACP,kCAAW,CAAX,0BAA2B,CAtDnC,4GA2DQ,OAAQ,CACR,OAAQ,CACR,kCAAW,CAAX,0BAA2B,CA7DnC,iDAiEQ,QAAS,CACT,QAAS,CACT,kCAAW,CAAX,0BAA2B,CAnEnC,6GAwEQ,QAAS,CACT,MAAO,CAzEf,4GA8EQ,OAAQ,CACR,QAAS,CACV,iBAOL,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,cAAe,CACf,KAAM,CACN,OAAQ,CACR,QAAS,CACT,MAAO,CACP,6BAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CACnB,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,YAAa,CAGb,4BAA6B,CAE7B,YAAa,CACb,iBAAkB,CAGlB,gCAAiC,CApBnC,2BAuBI,uBAAa,CAAb,oBAAa,CAAb,sBAAuB,CAvB3B,iEA4BI,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CA7B/B,gEAkCI,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,oBAAiB,CAAjB,iBAAiB,CAAjB,wBAAyB,CAnC7B,8BAuCI,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CAvCvB,uEA4CI,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CA7C/B,sEAkDI,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,oBAAiB,CAAjB,iBAAiB,CAAjB,wBAAyB,CAnD7B,8BAuDI,qBAAa,CAAb,kBAAa,CAAb,oBAAqB,CAvDzB,uEA4DI,qBAAqB,CAArB,kBAAqB,CAArB,oBAAqB,CACrB,sBAAiB,CAAjB,mBAAiB,CAAjB,0BAA2B,CA7D/B,sEAkEI,qBAAqB,CAArB,kBAAqB,CAArB,oBAAqB,CACrB,oBAAiB,CAAjB,iBAAiB,CAAjB,wBAAyB,CAnE7B,oDAuEI,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,2BAAmB,CAAnB,kBAAmB,CACnB,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CA1E3B,6CA8EI,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,yBAAqB,CAArB,oBAAqB,CACrB,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CAjF3B,mCAqFI,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,2BAAgB,CAAhB,4BAAgB,CAAhB,yBAAgB,CAAhB,qBAAsB,CAtF1B,6IA2FM,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CA3FzB,2TAoGM,uBAAa,CAAb,oBAAa,CAAb,sBAAuB,CApG7B,wTA6GM,qBAAa,CAAb,kBAAa,CAAb,oBAAqB,CA7G3B,gDAiHM,8BAAwB,CAAxB,8BAAwB,CAAxB,uBAAwB,CACxB,kBAAO,CAAP,UAAO,CAAP,MAAO,CACP,yBAAqB,CAArB,oBAAqB,CACrB,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CApH7B,oXA0IM,WAAY,CACb,sEA3IL,8BAgJM,mBAAoB,CACrB,CAjJL,4BAqJI,uCAAY,CAAZ,+BAAgC,CArJpC,6BAyJI,gCDrPc,CCsPf,aAKD,YAAa,CACb,iBAAkB,CAClB,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,UD5PgB,CC6PhB,cAAe,CACf,cD7PoB,CC8PpB,qBD7P2B,CC8P3B,eDpQgB,CCqQhB,mBDxPkB,CCyPlB,cDxPoB,CCyPpB,6BAAY,CAAZ,qBAAsB,CAZxB,mBAeI,YAAa,CAfjB,2BAmBI,iBAAkB,CAnBtB,2BAuBI,mBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,2BAAsB,CAAtB,4BAAsB,CAAtB,yBAAsB,CAAtB,qBAAsB,CACtB,wBAAa,CAAb,qBAAa,CAAb,kBAAmB,CAzBvB,0BA6BI,aAAc,CACd,iBAAkB,CAClB,cAAe,CACf,gBDzPyB,CC0PzB,SAAU,CACV,aD1PyC,CC2PzC,iBD1P2B,CC2P3B,eAAgB,CAChB,iBAAkB,CAClB,mBAAoB,CACpB,oBAAqB,CAvCzB,4BA2CI,kBAAe,CAAf,cAAe,CACf,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,oBD9LgC,CC+LhC,SAAU,CA/Cd,wEAoDU,UAAW,CApDrB,oEAwDU,4GAAkB,CAAlB,kEAAyF,CAxDnG,qEA4DU,4GAAkB,CAAlB,kEAA2F,CA5DrG,sEAoEU,WAAY,CACZ,YAAa,CACb,eAAgB,CAChB,SAAU,CACV,8BAA+B,CAC/B,kBAAmB,CACnB,wBAAyB,CACzB,uCAAwC,CACxC,iBAAkB,CAClB,cAAe,CACf,6BAAsB,CAAtB,qBAAsB,CACtB,qEAA8D,CAA9D,6DAA8D,CAC9D,wBAAa,CAAb,qBAAa,CAAb,oBAAa,CAAb,gBAAiB,CAhF3B,qEAoFU,iBAAkB,CAClB,gBAAiB,CArF3B,mFA4FY,oBAAqB,CACrB,UAAW,CACX,WAAY,CACZ,eAAgB,CAChB,qBAA2C,CAC3C,iBAAkB,CAClB,8BAA+B,CAC/B,mCD9VM,CC8VN,2BD9VM,CC+VN,UAAW,CACX,qEAAW,CAAX,6DAA8D,CArG1E,2BA6GI,cAAe,CACf,kBAAmB,CACnB,eAAgB,CAChB,uBAAY,CAAZ,eAAgB,CAhHpB,2CAmHM,cAAe,CAnHrB,yCAuHM,QDpQyB,CCqQzB,mBDpQoC,CCqQpC,kBAAmB,CACnB,wBDrQyC,CCsQzC,UDtXY,CCuXZ,kBDrQmC,CCyIzC,wCAgIM,QDtQwB,CCuQxB,mBDtQmC,CCuQnC,kBAAmB,CACnB,qBDvQqC,CCwQrC,UD/XY,CCgYZ,kBDvQkC,CCkIxC,iCAyIM,YAAa,CACb,gEDvQuB,CCuQvB,wDDvQ6C,CC6HnD,6CA8IM,QAAS,CA9If,2BAmJI,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,iBDtU4B,CCuU5B,eDtU0B,CCuU1B,yBDtU4B,CCuU5B,aDtU0C,CCuU1C,aDtUwB,CC8K5B,0BA4JI,cAAe,CACf,kBDzX4B,CC4NhC,0BAiKI,iBDhUkC,CCiUlC,KDhUsB,CCiUtB,ODjUsB,CCkUtB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,WDvU4B,CCwU5B,YDvU6B,CCwU7B,SAAU,CACV,sCDrU8C,CCqU9C,8BDrU8C,CCsU9C,WDrU4B,CCsU5B,eDrUgC,CCsUhC,eDrUgC,CCsUhC,wBDrUuC,CCsUvC,UDrUgD,CCsUhD,iBAAkB,CAClB,eDtUgC,CCuUhC,eDhVgC,CCiVhC,cAAe,CACf,eAAgB,CAlLpB,gCAqLM,sBDzUmC,CCyUnC,cDzUmC,CC0UnC,aDxZe,CCkOrB,kKAgMI,YAAa,CAhMjB,4BAoMI,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,QAAS,CACT,SAAU,CACV,aD3Z2C,CC4Z3C,iBD3Z6B,CC4Z7B,eAAgB,CAChB,kBAAmB,CACnB,SAAU,CACV,oBAAqB,CA5MzB,4BAgNI,iBAAkB,CAhNtB,kKAyNI,eDzayB,CCgN7B,gFA+NI,UAAW,CACX,2DAA4C,CAA5C,mDAA4C,CAA5C,2CAA4C,CAA5C,mEAA4C,CAC5C,wBD9a0C,CC+a1C,qBD9a+B,CC+a/B,iBD3a2B,CC4a3B,mDD9dc,CC8dd,2CD9dc,CC+dd,6BAAY,CAAZ,qBAAsB,CArO1B,mIAwOM,+BAAqC,CACrC,6CAAiC,CAAjC,qCAA2C,CAzOjD,kGA6OM,wBDxb4B,CCyb5B,YAAa,CACb,kCDxbyB,CCwbzB,0BDxbgC,CCyMtC,mKAmPM,UAAgC,CAnPtC,wIAmPM,UAAgC,CAnPtC,oJAmPM,UAAgC,CAnPtC,uJAmPM,UAAgC,CAnPtC,uHAmPM,UAAgC,CAnPtC,gCAyPM,SAAU,CAzPhB,iCA6PM,SAAU,CACV,eAAgB,CAChB,iBAAkB,CA/PxB,iEAoQM,cDndsB,CCodtB,eDrduB,CCsdvB,SAAU,CACV,iBD/cyB,CCgdzB,mBDvdsB,CC+M5B,0BA6QI,cD5dwB,CC6dxB,gBD5dyB,CC8M7B,yCAiRM,cAAe,CAjRrB,yBAsRI,iBD9d2B,CCwM/B,6BA0RI,aD/d0B,CCge1B,aD/d0B,CCoM9B,2BA+RI,aAAc,CACd,cAAe,CACf,qBAAsB,CACtB,aAAgC,CAChC,iBD3e2B,CCwM/B,uDAwSI,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBAAiB,CAAjB,oBAAiB,CAAjB,sBAAuB,CAzS3B,mEA4SM,aAAc,CACd,iBDrfyB,CCwM/B,mEAiTM,aAAc,CAjTpB,uCAsTI,YAAa,CACb,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,uBDzf0C,CCyf1C,oBDzf0C,CCyf1C,sBDzf0C,CC0f1C,cDzfkC,CC0flC,kBDzfwD,CC0fxD,UDzfmD,CC0fnD,aDzfiC,CC0fjC,eDzfmC,CC0fnC,eAAgB,CA9TpB,+CAiUM,oBAAqB,CACrB,WAAY,CACZ,eAAgB,CAChB,YAAa,CACb,eAAgB,CAChB,iBAAkB,CAClB,wBDziBe,CC0iBf,UDnkBY,CCokBZ,eAAgB,CAChB,iBAAkB,CAClB,iBAAkB,CAClB,WAAY,CACZ,WDtgBkC,CCugBnC,kCAMH,mBAEI,qBAAsB,CAF1B,oBAMI,YAAa,CACd,CAKL,sEAEE,mBAEI,qBAAsB,CAF1B,oBAMI,YAAa,CACd,CAKL,4BACE,mBAEI,sCD9e6C,CC+e9C,CAIL,YACE,iBAAkB,CAClB,uBAAuB,CAAvB,oBAAuB,CAAvB,sBAAuB,CACvB,SDhmBmB,CCimBnB,UDjmBmB,CCkmBnB,0BDjmBqC,CCkmBrC,8BAA+B,CAC/B,iBAAkB,CAClB,eDrmBmB,CCsmBnB,cAAe,CACf,8BAAuB,CAAvB,sBAAuB,CACvB,wBAAiB,CAAjB,qBAAiB,CAAjB,oBAAiB,CAAjB,gBAAiB,CACjB,WDvmBsB,CCymBtB,iBACE,gBAAiB,CAfrB,wBAmBI,oBD3mBiB,CCwlBrB,sCAsBM,iBAAkB,CAClB,kBAAW,CAAX,mBAAW,CAAX,WAAY,CAvBlB,qDA2BM,aAAc,CACd,iBAAkB,CAClB,YAAa,CACb,cAAe,CACf,cAAe,CACf,oBAAqB,CACrB,wBDznBe,CCwlBrB,oEAoCQ,aAAc,CACd,+BAAW,CAAX,uBAAwB,CArChC,qEAyCQ,SAAU,CACV,gCAAW,CAAX,wBAAyB,CA1CjC,0BAgDI,oBAAwC,CACxC,aDxoBmB,CCulBvB,uBAqDI,oBAAsC,CACtC,aD5oBgB,CCslBpB,2BA0DI,oBAA0C,CAC1C,aDhpBoB,CCqlBxB,0BA+DI,oBDzpBmB,CC0lBvB,iEAmEM,iBAAkB,CAClB,YAAa,CACb,YAAa,CACb,+BAAwB,CAAxB,uBAAwB,CACxB,iBAAkB,CAvExB,gFA0EQ,YAAa,CACb,cAAe,CACf,gCAAyB,CAAzB,wBAAyB,CACzB,sCAA+B,CAA/B,8BAA+B,CAC/B,6BAA8B,CA9EtC,iFAkFQ,YAAa,CACb,YAAa,CACb,gCAAyB,CAAzB,wBAAyB,CACzB,iCAA0B,CAA1B,yBAA0B,CAC1B,6BAA8B,CAtFtC,8CA4FM,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,UAAW,CACX,WAAY,CACZ,yCD3rBiB,CC4rBjB,iBAAkB,CAClB,SAAU,CACV,8BAAY,CAAZ,sBAAuB,CApG7B,6CAyGM,iBAAkB,CAClB,QAAS,CACT,YAAa,CACb,aAAc,CACd,cAAe,CACf,gCAAyB,CAAzB,wBAAyB,CACzB,SAAU,CA/GhB,wDAmHM,aAAc,CACd,iBAAkB,CAClB,cAAe,CACf,oBAAqB,CACrB,wBDjtBiB,CCktBjB,SAAU,CAxHhB,sEA2HQ,WAAY,CACZ,WAAY,CACZ,cAAe,CACf,+BAAW,CAAX,uBAAwB,CA9HhC,uEAkIQ,WAAY,CACZ,UAAW,CACX,cAAe,CACf,gCAAW,CAAX,wBAAyB,CAC1B,qBASL,wBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,iBD5rBsC,CC6rBtC,SAAU,CACV,eAAgB,CAPlB,wBAUI,oBAAqB,CACrB,iBAAkB,CAXtB,2CAeI,SAAU,CACV,UAAW,CACX,iBAAkB,CAClB,kBAhBY,CAiBZ,UD/wBc,CCgxBd,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CAtBf,uDAyBM,aAAc,CAzBpB,sDA6BM,cAAe,CA7BrB,oEAiCM,kBA/BU,CAFhB,4FAoCQ,kBAnCa,CADrB,0FAwCQ,kBAvCa,CADrB,yCA8CI,WDpuBiC,CCquBjC,WAAY,CACZ,aAAc,CACd,kBA/CY,CAgDZ,UAAW,CACZ,iBAMD,uCAAwC,CACzC,YAGC,iCDzuBgC,CCyuBhC,yBDzuBmC,CCwuBrC,8BAII,sBAAW,CAAX,cAAe,CAChB,YAID,2CDhvBqC,CCgvBrC,mCDhvB6C,CC+uB/C,8BAII,sBAAW,CAAX,cAAe,CAChB,wBAOC,UAAW,CACX,MDhvBsB,CCivBvB,oDAOC,sDDnwB8D,CCmwB9D,8CDnwBkE,CCiwBtE,qDAMI,uDDtwBgE,CCswBhE,+CDtwBoE,CCgwBxE,+DAUI,kEDzwB6E,CCywB7E,0DDzwBoF,CC0wBrF,0BAKD,+CD9wBoD,CC8wBpD,uCD9wBuD,CC6wBzD,wCAII,iDDhxBsD,CCgxBtD,yCDhxByD,CCixB1D,wCAID,GACE,8BAAW,CAAX,sBAAuB,CAGzB,KACE,gCAAW,CAAX,wBAAyB,CAAA,CAT1B,gCAID,GACE,8BAAW,CAAX,sBAAuB,CAGzB,KACE,gCAAW,CAAX,wBAAyB,CAAA,CAI7B,aACE,iEAMM,4BAA6B,CANnC,sFASQ,YAAa,CATrB,kFAaQ,2BAA4B,CAC7B", "file": "vendors/sweetalert2.css", "sourcesContent": ["// Appearance animation\n@keyframes swal2-show {\n  0% {\n    transform: scale(.7);\n  }\n\n  45% {\n    transform: scale(1.05);\n  }\n\n  80% {\n    transform: scale(.95);\n  }\n\n  100% {\n    transform: scale(1);\n  }\n}\n\n// Disppearance animation\n@keyframes swal2-hide {\n  0% {\n    transform: scale(1);\n    opacity: 1;\n  }\n\n  100% {\n    transform: scale(.5);\n    opacity: 0;\n  }\n}\n\n// Success icon animations\n@keyframes swal2-animate-success-line-tip {\n  0% {\n    top: 1.1875em;\n    left: .0625em;\n    width: 0;\n  }\n\n  54% {\n    top: 1.0625em;\n    left: .125em;\n    width: 0;\n  }\n\n  70% {\n    top: 2.1875em;\n    left: -.375em;\n    width: 3.125em;\n  }\n\n  84% {\n    top: 3em;\n    left: 1.3125em;\n    width: 1.0625em;\n  }\n\n  100% {\n    top: 2.8125em;\n    left: .875em;\n    width: 1.5625em;\n  }\n}\n\n@keyframes swal2-animate-success-line-long {\n  0% {\n    top: 3.375em;\n    right: 2.875em;\n    width: 0;\n  }\n\n  65% {\n    top: 3.375em;\n    right: 2.875em;\n    width: 0;\n  }\n\n  84% {\n    top: 2.1875em;\n    right: 0;\n    width: 3.4375em;\n  }\n\n  100% {\n    top: 2.375em;\n    right: .5em;\n    width: 2.9375em;\n  }\n}\n\n@keyframes swal2-rotate-success-circular-line {\n  0% {\n    transform: rotate(-45deg);\n  }\n\n  5% {\n    transform: rotate(-45deg);\n  }\n\n  12% {\n    transform: rotate(-405deg);\n  }\n\n  100% {\n    transform: rotate(-405deg);\n  }\n}\n\n// Error icon animations\n@keyframes swal2-animate-error-x-mark {\n  0% {\n    margin-top: 1.625em;\n    transform: scale(.4);\n    opacity: 0;\n  }\n\n  50% {\n    margin-top: 1.625em;\n    transform: scale(.4);\n    opacity: 0;\n  }\n\n  80% {\n    margin-top: -.375em;\n    transform: scale(1.15);\n  }\n\n  100% {\n    margin-top: 0;\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes swal2-animate-error-icon {\n  0% {\n    transform: rotateX(100deg);\n    opacity: 0;\n  }\n\n  100% {\n    transform: rotateX(0deg);\n    opacity: 1;\n  }\n}", "body {\n  &.swal2-toast-shown {\n    .swal2-container {\n      position: fixed;\n      background-color: transparent;\n\n      &.swal2-shown {\n        background-color: transparent;\n      }\n\n      &.swal2-top {\n        top: 0;\n        right: auto;\n        bottom: auto;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n\n      &.swal2-top-end,\n      &.swal2-top-right {\n        top: 0;\n        right: 0;\n        bottom: auto;\n        left: auto;\n      }\n\n      &.swal2-top-start,\n      &.swal2-top-left {\n        top: 0;\n        right: auto;\n        bottom: auto;\n        left: 0;\n      }\n\n      &.swal2-center-start,\n      &.swal2-center-left {\n        top: 50%;\n        right: auto;\n        bottom: auto;\n        left: 0;\n        transform: translateY(-50%);\n      }\n\n      &.swal2-center {\n        top: 50%;\n        right: auto;\n        bottom: auto;\n        left: 50%;\n        transform: translate(-50%, -50%);\n      }\n\n      &.swal2-center-end,\n      &.swal2-center-right {\n        top: 50%;\n        right: 0;\n        bottom: auto;\n        left: auto;\n        transform: translateY(-50%);\n      }\n\n      &.swal2-bottom-start,\n      &.swal2-bottom-left {\n        top: auto;\n        right: auto;\n        bottom: 0;\n        left: 0;\n      }\n\n      &.swal2-bottom {\n        top: auto;\n        right: auto;\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n\n      &.swal2-bottom-end,\n      &.swal2-bottom-right {\n        top: auto;\n        right: 0;\n        bottom: 0;\n        left: auto;\n      }\n    }\n  }\n\n  &.swal2-toast-column {\n    .swal2-toast {\n      flex-direction: column;\n      align-items: stretch;\n\n      .swal2-actions {\n        flex: 1;\n        align-self: stretch;\n        height: 2.2em;\n        margin-top: .3125em;\n      }\n\n      .swal2-loading {\n        justify-content: center;\n      }\n\n      .swal2-input {\n        height: 2em;\n        margin: .3125em auto;\n        font-size: $swal2-toast-input-font-size;\n      }\n\n      .swal2-validation-message {\n        font-size: $swal2-toast-validation-font-size;\n      }\n    }\n  }\n\n}\n\n.swal2-popup {\n  &.swal2-toast {\n    flex-direction: row;\n    align-items: center;\n    width: $swal2-toast-width;\n    padding: $swal2-toast-padding;\n    box-shadow: 0 0 .625em $swal2-box-shadow;\n    overflow-y: hidden;\n\n    .swal2-header {\n      flex-direction: row;\n    }\n\n    .swal2-title {\n      flex-grow: 1;\n      justify-content: flex-start;\n      margin: 0 .6em;\n      font-size: $swal2-toast-title-font-size;\n    }\n\n    .swal2-footer {\n      margin: $swal2-toast-footer-margin;\n      padding: $swal2-toast-footer-margin;\n      font-size: $swal2-toast-footer-font-size;\n    }\n\n    .swal2-close {\n      position: initial;\n      width: $swal2-toast-close-button-width;\n      height: $swal2-toast-close-button-height;\n      line-height: $swal2-toast-close-button-line-height;\n    }\n\n    .swal2-content {\n      justify-content: flex-start;\n      font-size: $swal2-toast-content-font-size;\n    }\n\n    .swal2-icon {\n      width: 2em;\n      min-width: 2em;\n      height: 2em;\n      margin: 0;\n\n      &-text {\n        font-size: 2em;\n        font-weight: bold;\n        line-height: 1em;\n      }\n\n      &.swal2-success {\n\n        .swal2-success-ring {\n          width: 2em;\n          height: 2em;\n        }\n      }\n\n      &.swal2-error {\n\n        [class^='swal2-x-mark-line'] {\n          top: .875em;\n          width: 1.375em;\n\n          &[class$='left'] {\n            left: .3125em;\n          }\n\n          &[class$='right'] {\n            right: .3125em;\n          }\n        }\n      }\n    }\n\n    .swal2-actions {\n      height: auto;\n      margin: 0 .3125em;\n    }\n\n    .swal2-styled {\n      margin: 0 .3125em;\n      padding: .3125em .625em;\n      font-size: $swal2-toast-buttons-font-size;\n\n      &:focus {\n        box-shadow: 0 0 0 .0625em $swal2-white, 0 0 0 .125em $swal2-button-focus-outline;\n      }\n    }\n\n    .swal2-success {\n      border-color: $swal2-success;\n\n      [class^='swal2-success-circular-line'] {\n        // Emulate moving circular line\n        position: absolute;\n        width: 2em;\n        height: 2.8125em;\n        transform: rotate(45deg);\n        border-radius: 50%;\n\n        &[class$='left'] {\n          top: -.25em;\n          left: -.9375em;\n          transform: rotate(-45deg);\n          transform-origin: 2em 2em;\n          border-radius: 4em 0 0 4em;\n        }\n\n        &[class$='right'] {\n          top: -.25em;\n          left: .9375em;\n          transform-origin: 0 2em;\n          border-radius: 0 4em 4em 0;\n        }\n      }\n\n      .swal2-success-ring {\n        width: 2em;\n        height: 2em;\n      }\n\n      .swal2-success-fix {\n        top: 0;\n        left: .4375em;\n        width: .4375em;\n        height: 2.6875em;\n      }\n\n      [class^='swal2-success-line'] {\n        height: .3125em;\n\n        &[class$='tip'] {\n          top: 1.125em;\n          left: .1875em;\n          width: .75em;\n        }\n\n        &[class$='long'] {\n          top: .9375em;\n          right: .1875em;\n          width: 1.375em;\n        }\n      }\n    }\n\n    &.swal2-show {\n      animation: showSweetToast .5s;\n    }\n\n    &.swal2-hide {\n      animation: hideSweetToast .2s forwards;\n    }\n\n    .swal2-animate-success-icon {\n      .swal2-success-line-tip {\n        animation: animate-toast-success-tip .75s;\n      }\n\n      .swal2-success-line-long {\n        animation: animate-toast-success-long .75s;\n      }\n    }\n  }\n}\n\n// Animations\n@keyframes showSweetToast {\n  0% {\n    transform: translateY(-.625em) rotateZ(2deg);\n    opacity: 0;\n  }\n\n  33% {\n    transform: translateY(0) rotateZ(-2deg);\n    opacity: .5;\n  }\n\n  66% {\n    transform: translateY(.3125em) rotateZ(2deg);\n    opacity: .7;\n  }\n\n  100% {\n    transform: translateY(0) rotateZ(0);\n    opacity: 1;\n  }\n}\n\n@keyframes hideSweetToast {\n  0% {\n    opacity: 1;\n  }\n\n  33% {\n    opacity: .5;\n  }\n\n  100% {\n    transform: rotateZ(1deg);\n    opacity: 0;\n  }\n}\n\n@keyframes animate-toast-success-tip {\n  0% {\n    top: .5625em;\n    left: .0625em;\n    width: 0;\n  }\n\n  54% {\n    top: .125em;\n    left: .125em;\n    width: 0;\n  }\n\n  70% {\n    top: .625em;\n    left: -.25em;\n    width: 1.625em;\n  }\n\n  84% {\n    top: 1.0625em;\n    left: .75em;\n    width: .5em;\n  }\n\n  100% {\n    top: 1.125em;\n    left: .1875em;\n    width: .75em;\n  }\n}\n\n@keyframes animate-toast-success-long {\n  0% {\n    top: 1.625em;\n    right: 1.375em;\n    width: 0;\n  }\n\n  65% {\n    top: 1.25em;\n    right: .9375em;\n    width: 0;\n  }\n\n  84% {\n    top: .9375em;\n    right: 0;\n    width: 1.125em;\n  }\n\n  100% {\n    top: .9375em;\n    right: .1875em;\n    width: 1.375em;\n  }\n}", "$swal2-white: #fff !default;\n$swal2-black: #000 !default;\n\n// BOX MODEL\n$swal2-width: 32em !default;\n$swal2-padding: 1.25em !default;\n$swal2-border-radius: .3125em !default;\n$swal2-box-shadow: #d9d9d9 !default;\n\n// BACKGROUND\n$swal2-background: $swal2-white !default;\n\n// TYPOGRAPHY\n$swal2-font: inherit !default;\n$swal2-font-size: 1rem !default;\n\n// BACKDROP\n$swal2-backdrop: rgba($swal2-black, .4) !default;\n\n// ICONS\n$swal2-icon-size: 5em !default;\n$swal2-icon-margin: 1.25em auto 1.875em !default;\n$swal2-icon-zoom: normal !default;\n$swal2-success: #a5dc86 !default;\n$swal2-success-border: rgba($swal2-success, .3) !default;\n$swal2-error: #f27474 !default;\n$swal2-warning: #f8bb86 !default;\n$swal2-info: #3fc3ee !default;\n$swal2-question: #87adbd !default;\n\n// IMAGE\n$swal2-image-margin: 1.25em auto !default;\n\n// TITLE\n$swal2-title-margin: 0 0 .4em !default;\n$swal2-title-color: lighten($swal2-black, 35) !default;\n$swal2-title-font-size: 1.875em !default;\n\n// CONTENT\n$swal2-content-color: lighten($swal2-black, 33) !default;\n$swal2-content-font-size: 1.125em !default;\n\n// INPUT\n$swal2-input-margin: 1em auto !default;\n$swal2-input-height: 2.625em !default;\n$swal2-input-padding: 0 .75em !default;\n$swal2-input-border: lighten($swal2-black, 85) !default;\n$swal2-input-border-radius: .1875em !default;\n$swal2-input-border-focus: #b4dbed !default;\n$swal2-input-box-shadow: rgba($swal2-black, .06) !default;\n$swal2-input-box-shadow-focus: #c4e6f5 !default;\n$swal2-input-font-size: 1.125em !default;\n\n// TEXTAREA SPECIFIC VARIABLES\n$swal2-textarea-height: 6.75em !default;\n$swal2-textarea-padding: .75em !default;\n\n// VALIDATION MESSAGE\n$swal2-validationerror-justify-content: center !default;\n$swal2-validationerror-padding: .625em !default;\n$swal2-validationerror-background: lighten($swal2-black, 94) !default;\n$swal2-validationerror-color: lighten($swal2-black, 40) !default;\n$swal2-validationerror-font-size: 1em !default;\n$swal2-validationerror-font-weight: 300 !default;\n$swal2-validationerror-icon-background: $swal2-error !default;\n$swal2-validationerror-icon-color: $swal2-white !default;\n$swal2-validationerror-icon-zoom: normal !default;\n\n// PROGRESS STEPS\n$swal2-progress-steps-margin: 0 0 1.25em !default;\n$swal2-progress-steps-distance: 2.5em !default;\n\n// FOOTER\n$swal2-footer-margin: 1.25em 0 0 !default;\n$swal2-footer-padding: 1em 0 0 !default;\n$swal2-footer-border-color: #eee !default;\n$swal2-footer-color: lighten($swal2-black, 33) !default;\n$swal2-footer-font-size: 1em !default;\n\n// ANIMATIONS\n$swal2-show-animation: swal2-show .3s !default;\n$swal2-hide-animation: swal2-hide .15s forwards !default;\n$swal2-success-line-tip-animation: swal2-animate-success-line-tip .75s !default;\n$swal2-success-line-long-animation: swal2-animate-success-line-long .75s !default;\n$swal2-success-circular-line-animation: swal2-rotate-success-circular-line 4.25s ease-in !default;\n$swal2-error-icon-animation: swal2-animate-error-icon .5s !default;\n$swal2-error-x-mark-animation: swal2-animate-error-x-mark .5s !default;\n\n// CLOSE BUTTON\n$swal2-close-button-width: 1.2em !default;\n$swal2-close-button-height: 1.2em !default;\n$swal2-close-button-line-height: 1.2 !default;\n$swal2-close-button-position: absolute !default;\n$swal2-close-button-gap: 0 !default;\n$swal2-close-button-transition: color .1s ease-out !default;\n$swal2-close-button-border: none !default;\n$swal2-close-button-border-radius: 0 !default;\n$swal2-close-button-outline: initial !default;\n$swal2-close-button-background: transparent !default;\n$swal2-close-button-color: lighten($swal2-black, 80) !default;\n$swal2-close-button-font-size: 2.5em !default;\n\n// CLOSE BUTTON:HOVER\n$swal2-close-button-hover-transform: none !default;\n$swal2-close-button-hover-color: $swal2-error !default;\n\n// ACTIONS\n$swal2-actions-margin: 1.25em auto 0 !default;\n\n// CONFIRM BUTTON\n$swal2-confirm-button-border: 0 !default;\n$swal2-confirm-button-border-radius: .25em !default;\n$swal2-confirm-button-background-color: #3085d6 !default;\n$swal2-confirm-button-color: $swal2-white !default;\n$swal2-confirm-button-font-size: 1.0625em !default;\n\n// CANCEL BUTTON\n$swal2-cancel-button-border: 0 !default;\n$swal2-cancel-button-border-radius: .25em !default;\n$swal2-cancel-button-background-color: #aaa !default;\n$swal2-cancel-button-color: $swal2-white !default;\n$swal2-cancel-button-font-size: 1.0625em !default;\n\n// COMMON VARIABLES FOR CONFIRM AND CANCEL BUTTONS\n$swal2-button-darken-hover: rgba($swal2-black, .1) !default;\n$swal2-button-darken-active: rgba($swal2-black, .2) !default;\n$swal2-button-focus-outline: rgba(50, 100, 150, .4) !default;\n\n// TOASTS\n$swal2-toast-close-button-width: .8em !default;\n$swal2-toast-close-button-height: .8em !default;\n$swal2-toast-close-button-line-height: .8 !default;\n$swal2-toast-width: auto !default;\n$swal2-toast-padding: .625em !default;\n$swal2-toast-title-font-size: 1em !default;\n$swal2-toast-content-font-size: 1em !default;\n$swal2-toast-input-font-size: 1em !default;\n$swal2-toast-validation-font-size: 1em !default;\n$swal2-toast-buttons-font-size: 1em !default;\n$swal2-toast-footer-margin: .5em 0 0 !default;\n$swal2-toast-footer-padding: .5em 0 0 !default;\n$swal2-toast-footer-font-size: .8em !default;", "// SweetAlert2\n// github.com/sweetalert2/sweetalert2\n\n@import 'sweetalert2-master/variables';\n@import 'sweetalert2-master/animations';\n@import 'sweetalert2-master/mixins';\n@import 'sweetalert2-master/toasts';\n\nbody {\n  &.swal2-shown {\n    @include not('.swal2-no-backdrop',\n      '.swal2-toast-shown'\n\n    ) {\n      overflow: hidden; // not overflow-y because of <PERSON><PERSON>, #1253\n    }\n  }\n\n  &.swal2-height-auto {\n    height: auto !important; // #781 #1107\n  }\n\n  &.swal2-no-backdrop {\n    .swal2-shown {\n      top: auto;\n      right: auto;\n      bottom: auto;\n      left: auto;\n      background-color: transparent;\n\n      &>.swal2-modal {\n        box-shadow: 0 0 10px $swal2-backdrop;\n      }\n\n      &.swal2-top {\n        top: 0;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n\n      &.swal2-top-start,\n      &.swal2-top-left {\n        top: 0;\n        left: 0;\n      }\n\n      &.swal2-top-end,\n      &.swal2-top-right {\n        top: 0;\n        right: 0;\n      }\n\n      &.swal2-center {\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n      }\n\n      &.swal2-center-start,\n      &.swal2-center-left {\n        top: 50%;\n        left: 0;\n        transform: translateY(-50%);\n      }\n\n      &.swal2-center-end,\n      &.swal2-center-right {\n        top: 50%;\n        right: 0;\n        transform: translateY(-50%);\n      }\n\n      &.swal2-bottom {\n        bottom: 0;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n\n      &.swal2-bottom-start,\n      &.swal2-bottom-left {\n        bottom: 0;\n        left: 0;\n      }\n\n      &.swal2-bottom-end,\n      &.swal2-bottom-right {\n        right: 0;\n        bottom: 0;\n      }\n    }\n  }\n}\n\n.swal2-container {\n  // centering\n  display: flex;\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  padding: 10px;\n\n  // backdrop\n  background-color: transparent;\n\n  z-index: 1060;\n  overflow-x: hidden;\n\n  // sweetalert2/issues/905\n  -webkit-overflow-scrolling: touch;\n\n  &.swal2-top {\n    align-items: flex-start;\n  }\n\n  &.swal2-top-start,\n  &.swal2-top-left {\n    align-items: flex-start;\n    justify-content: flex-start;\n  }\n\n  &.swal2-top-end,\n  &.swal2-top-right {\n    align-items: flex-start;\n    justify-content: flex-end;\n  }\n\n  &.swal2-center {\n    align-items: center;\n  }\n\n  &.swal2-center-start,\n  &.swal2-center-left {\n    align-items: center;\n    justify-content: flex-start;\n  }\n\n  &.swal2-center-end,\n  &.swal2-center-right {\n    align-items: center;\n    justify-content: flex-end;\n  }\n\n  &.swal2-bottom {\n    align-items: flex-end;\n  }\n\n  &.swal2-bottom-start,\n  &.swal2-bottom-left {\n    align-items: flex-end;\n    justify-content: flex-start;\n  }\n\n  &.swal2-bottom-end,\n  &.swal2-bottom-right {\n    align-items: flex-end;\n    justify-content: flex-end;\n  }\n\n  &.swal2-grow-fullscreen>.swal2-modal {\n    display: flex !important;\n    flex: 1;\n    align-self: stretch;\n    justify-content: center;\n  }\n\n  &.swal2-grow-row>.swal2-modal {\n    display: flex !important;\n    flex: 1;\n    align-content: center;\n    justify-content: center;\n  }\n\n  &.swal2-grow-column {\n    flex: 1;\n    flex-direction: column;\n\n    &.swal2-top,\n    &.swal2-center,\n    &.swal2-bottom {\n      align-items: center;\n    }\n\n    &.swal2-top-start,\n    &.swal2-center-start,\n    &.swal2-bottom-start,\n    &.swal2-top-left,\n    &.swal2-center-left,\n    &.swal2-bottom-left {\n      align-items: flex-start;\n    }\n\n    &.swal2-top-end,\n    &.swal2-center-end,\n    &.swal2-bottom-end,\n    &.swal2-top-right,\n    &.swal2-center-right,\n    &.swal2-bottom-right {\n      align-items: flex-end;\n    }\n\n    &>.swal2-modal {\n      display: flex !important;\n      flex: 1;\n      align-content: center;\n      justify-content: center;\n    }\n  }\n\n  @include not('.swal2-top',\n    '.swal2-top-start',\n    '.swal2-top-end',\n    '.swal2-top-left',\n    '.swal2-top-right',\n    '.swal2-center-start',\n    '.swal2-center-end',\n    '.swal2-center-left',\n    '.swal2-center-right',\n    '.swal2-bottom',\n    '.swal2-bottom-start',\n    '.swal2-bottom-end',\n    '.swal2-bottom-left',\n    '.swal2-bottom-right',\n    '.swal2-grow-fullscreen'\n\n  ) {\n    &>.swal2-modal {\n      margin: auto;\n    }\n  }\n\n  @include ie {\n    .swal2-modal {\n      margin: 0 !important;\n    }\n  }\n\n  &.swal2-fade {\n    transition: background-color .1s;\n  }\n\n  &.swal2-shown {\n    background-color: $swal2-backdrop;\n  }\n}\n\n\n.swal2-popup {\n  display: none;\n  position: relative;\n  flex-direction: column;\n  justify-content: center;\n  width: $swal2-width;\n  max-width: 100%;\n  padding: $swal2-padding;\n  border-radius: $swal2-border-radius;\n  background: $swal2-background;\n  font-family: $swal2-font;\n  font-size: $swal2-font-size;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n  }\n\n  &.swal2-loading {\n    overflow-y: hidden;\n  }\n\n  .swal2-header {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .swal2-title {\n    display: block;\n    position: relative;\n    max-width: 100%;\n    margin: $swal2-title-margin;\n    padding: 0;\n    color: $swal2-title-color;\n    font-size: $swal2-title-font-size;\n    font-weight: 600;\n    text-align: center;\n    text-transform: none;\n    word-wrap: break-word;\n  }\n\n  .swal2-actions {\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: center;\n    margin: $swal2-actions-margin;\n    z-index: 1; // prevent sucess icon overlapping buttons\n\n    &:not(.swal2-loading) {\n      .swal2-styled {\n        &[disabled] {\n          opacity: .4;\n        }\n\n        &:hover {\n          background-image: linear-gradient($swal2-button-darken-hover, $swal2-button-darken-hover);\n        }\n\n        &:active {\n          background-image: linear-gradient($swal2-button-darken-active, $swal2-button-darken-active);\n        }\n      }\n    }\n\n    &.swal2-loading {\n      .swal2-styled {\n        &.swal2-confirm {\n          width: 2.5em;\n          height: 2.5em;\n          margin: .46875em;\n          padding: 0;\n          border: .25em solid transparent;\n          border-radius: 100%;\n          border-color: transparent;\n          background-color: transparent !important;\n          color: transparent;\n          cursor: default;\n          box-sizing: border-box;\n          animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n          user-select: none;\n        }\n\n        &.swal2-cancel {\n          margin-right: 30px;\n          margin-left: 30px;\n        }\n      }\n\n      :not(.swal2-styled) {\n        &.swal2-confirm {\n          &::after {\n            display: inline-block;\n            width: 15px;\n            height: 15px;\n            margin-left: 5px;\n            border: 3px solid lighten($swal2-black, 60);\n            border-radius: 50%;\n            border-right-color: transparent;\n            box-shadow: 1px 1px 1px $swal2-white;\n            content: '';\n            animation: swal2-rotate-loading 1.5s linear 0s infinite normal;\n          }\n        }\n      }\n    }\n  }\n\n  .swal2-styled {\n    margin: .3125em;\n    padding: .625em 2em;\n    font-weight: 500;\n    box-shadow: none;\n\n    &:not([disabled]) {\n      cursor: pointer;\n    }\n\n    &.swal2-confirm {\n      border: $swal2-confirm-button-border;\n      border-radius: $swal2-confirm-button-border-radius;\n      background: initial;\n      background-color: $swal2-confirm-button-background-color;\n      color: $swal2-confirm-button-color;\n      font-size: $swal2-confirm-button-font-size;\n    }\n\n    &.swal2-cancel {\n      border: $swal2-cancel-button-border;\n      border-radius: $swal2-cancel-button-border-radius;\n      background: initial;\n      background-color: $swal2-cancel-button-background-color;\n      color: $swal2-cancel-button-color;\n      font-size: $swal2-cancel-button-font-size;\n    }\n\n    &:focus {\n      outline: none;\n      box-shadow: 0 0 0 2px $swal2-white, 0 0 0 4px $swal2-button-focus-outline;\n    }\n\n    &::-moz-focus-inner {\n      border: 0;\n    }\n  }\n\n  .swal2-footer {\n    justify-content: center;\n    margin: $swal2-footer-margin;\n    padding: $swal2-footer-padding;\n    border-top: 1px solid $swal2-footer-border-color;\n    color: $swal2-footer-color;\n    font-size: $swal2-footer-font-size;\n  }\n\n  .swal2-image {\n    max-width: 100%;\n    margin: $swal2-image-margin;\n  }\n\n  .swal2-close {\n    position: $swal2-close-button-position;\n    top: $swal2-close-button-gap;\n    right: $swal2-close-button-gap;\n    justify-content: center;\n    width: $swal2-close-button-width;\n    height: $swal2-close-button-height;\n    padding: 0;\n    transition: $swal2-close-button-transition;\n    border: $swal2-close-button-border;\n    border-radius: $swal2-close-button-border-radius;\n    outline: $swal2-close-button-outline;\n    background: $swal2-close-button-background;\n    color: $swal2-close-button-color;\n    font-family: serif;\n    font-size: $swal2-close-button-font-size;\n    line-height: $swal2-close-button-line-height;\n    cursor: pointer;\n    overflow: hidden;\n\n    &:hover {\n      transform: $swal2-close-button-hover-transform;\n      color: $swal2-close-button-hover-color;\n    }\n  }\n\n  >.swal2-input,\n  >.swal2-file,\n  >.swal2-textarea,\n  >.swal2-select,\n  >.swal2-radio,\n  >.swal2-checkbox {\n    display: none;\n  }\n\n  .swal2-content {\n    justify-content: center;\n    margin: 0;\n    padding: 0;\n    color: $swal2-content-color;\n    font-size: $swal2-content-font-size;\n    font-weight: 300;\n    line-height: normal;\n    z-index: 1; // prevent sucess icon overlapping the content\n    word-wrap: break-word;\n  }\n\n  #swal2-content {\n    text-align: center;\n  }\n\n  .swal2-input,\n  .swal2-file,\n  .swal2-textarea,\n  .swal2-select,\n  .swal2-radio,\n  .swal2-checkbox {\n    margin: $swal2-input-margin;\n  }\n\n  .swal2-input,\n  .swal2-file,\n  .swal2-textarea {\n    width: 100%;\n    transition: border-color .3s, box-shadow .3s;\n    border: 1px solid $swal2-input-border;\n    border-radius: $swal2-input-border-radius;\n    font-size: $swal2-input-font-size;\n    box-shadow: inset 0 1px 1px $swal2-input-box-shadow;\n    box-sizing: border-box;\n\n    &.swal2-inputerror {\n      border-color: $swal2-error !important;\n      box-shadow: 0 0 2px $swal2-error !important;\n    }\n\n    &:focus {\n      border: 1px solid $swal2-input-border-focus;\n      outline: none;\n      box-shadow: 0 0 3px $swal2-input-box-shadow-focus;\n    }\n\n    &::placeholder {\n      color: lighten($swal2-black, 80);\n    }\n  }\n\n  .swal2-range {\n    input {\n      width: 80%;\n    }\n\n    output {\n      width: 20%;\n      font-weight: 600;\n      text-align: center;\n    }\n\n    input,\n    output {\n      height: $swal2-input-height;\n      margin: $swal2-input-margin;\n      padding: 0;\n      font-size: $swal2-input-font-size;\n      line-height: $swal2-input-height;\n    }\n  }\n\n  .swal2-input {\n    height: $swal2-input-height;\n    padding: $swal2-input-padding;\n\n    &[type='number'] {\n      max-width: 10em;\n    }\n  }\n\n  .swal2-file {\n    font-size: $swal2-input-font-size;\n  }\n\n  .swal2-textarea {\n    height: $swal2-textarea-height;\n    padding: $swal2-textarea-padding;\n  }\n\n  .swal2-select {\n    min-width: 50%;\n    max-width: 100%;\n    padding: .375em .625em;\n    color: lighten($swal2-black, 33);\n    font-size: $swal2-input-font-size;\n  }\n\n  .swal2-radio,\n  .swal2-checkbox {\n    align-items: center;\n    justify-content: center;\n\n    label {\n      margin: 0 .6em;\n      font-size: $swal2-input-font-size;\n    }\n\n    input {\n      margin: 0 .4em;\n    }\n  }\n\n  .swal2-validation-message {\n    display: none;\n    align-items: center;\n    justify-content: $swal2-validationerror-justify-content;\n    padding: $swal2-validationerror-padding;\n    background: $swal2-validationerror-background;\n    color: $swal2-validationerror-color;\n    font-size: $swal2-validationerror-font-size;\n    font-weight: $swal2-validationerror-font-weight;\n    overflow: hidden;\n\n    &::before {\n      display: inline-block;\n      width: 1.5em;\n      min-width: 1.5em;\n      height: 1.5em;\n      margin: 0 .625em;\n      border-radius: 50%;\n      background-color: $swal2-validationerror-icon-background;\n      color: $swal2-validationerror-icon-color;\n      font-weight: 600;\n      line-height: 1.5em;\n      text-align: center;\n      content: '!';\n      zoom: $swal2-validationerror-icon-zoom;\n    }\n  }\n}\n\n// Microsoft Edge\n@supports (-ms-accelerator: true) {\n  .swal2-range {\n    input {\n      width: 100% !important;\n    }\n\n    output {\n      display: none;\n    }\n  }\n}\n\n// IE11\n@media all and (-ms-high-contrast: none),\n(-ms-high-contrast: active) {\n  .swal2-range {\n    input {\n      width: 100% !important;\n    }\n\n    output {\n      display: none;\n    }\n  }\n}\n\n// Firefox\n@-moz-document url-prefix() {\n  .swal2-close {\n    &:focus {\n      outline: 2px solid $swal2-button-focus-outline;\n    }\n  }\n}\n\n.swal2-icon {\n  position: relative;\n  justify-content: center;\n  width: $swal2-icon-size;\n  height: $swal2-icon-size;\n  margin: $swal2-icon-margin;\n  border: .25em solid transparent;\n  border-radius: 50%;\n  line-height: $swal2-icon-size;\n  cursor: default;\n  box-sizing: content-box;\n  user-select: none;\n  zoom: $swal2-icon-zoom;\n\n  &-text {\n    font-size: 3.75em;\n  }\n\n  &.swal2-error {\n    border-color: $swal2-error;\n\n    .swal2-x-mark {\n      position: relative;\n      flex-grow: 1;\n    }\n\n    [class^='swal2-x-mark-line'] {\n      display: block;\n      position: absolute;\n      top: 2.3125em;\n      width: 2.9375em;\n      height: .3125em;\n      border-radius: .125em;\n      background-color: $swal2-error;\n\n      &[class$='left'] {\n        left: 1.0625em;\n        transform: rotate(45deg);\n      }\n\n      &[class$='right'] {\n        right: 1em;\n        transform: rotate(-45deg);\n      }\n    }\n  }\n\n  &.swal2-warning {\n    border-color: lighten($swal2-warning, 7);\n    color: $swal2-warning;\n  }\n\n  &.swal2-info {\n    border-color: lighten($swal2-info, 20);\n    color: $swal2-info;\n  }\n\n  &.swal2-question {\n    border-color: lighten($swal2-question, 20);\n    color: $swal2-question;\n  }\n\n  &.swal2-success {\n    border-color: $swal2-success;\n\n    [class^='swal2-success-circular-line'] {\n      // Emulate moving circular line\n      position: absolute;\n      width: 3.75em;\n      height: 7.5em;\n      transform: rotate(45deg);\n      border-radius: 50%;\n\n      &[class$='left'] {\n        top: -.4375em;\n        left: -2.0635em;\n        transform: rotate(-45deg);\n        transform-origin: 3.75em 3.75em;\n        border-radius: 7.5em 0 0 7.5em;\n      }\n\n      &[class$='right'] {\n        top: -.6875em;\n        left: 1.875em;\n        transform: rotate(-45deg);\n        transform-origin: 0 3.75em;\n        border-radius: 0 7.5em 7.5em 0;\n      }\n    }\n\n    .swal2-success-ring {\n      // Ring\n      position: absolute;\n      top: -.25em;\n      left: -.25em;\n      width: 100%;\n      height: 100%;\n      border: .25em solid $swal2-success-border;\n      border-radius: 50%;\n      z-index: 2;\n      box-sizing: content-box;\n    }\n\n    .swal2-success-fix {\n      // Hide corners left from animation\n      position: absolute;\n      top: .5em;\n      left: 1.625em;\n      width: .4375em;\n      height: 5.625em;\n      transform: rotate(-45deg);\n      z-index: 1;\n    }\n\n    [class^='swal2-success-line'] {\n      display: block;\n      position: absolute;\n      height: .3125em;\n      border-radius: .125em;\n      background-color: $swal2-success;\n      z-index: 2;\n\n      &[class$='tip'] {\n        top: 2.875em;\n        left: .875em;\n        width: 1.5625em;\n        transform: rotate(45deg);\n      }\n\n      &[class$='long'] {\n        top: 2.375em;\n        right: .5em;\n        width: 2.9375em;\n        transform: rotate(-45deg);\n      }\n    }\n  }\n}\n\n.swal2-progresssteps {\n  $lightblue: #add8e6;\n  $blue: #3085d6;\n\n  align-items: center;\n  margin: $swal2-progress-steps-margin;\n  padding: 0;\n  font-weight: 600;\n\n  li {\n    display: inline-block;\n    position: relative;\n  }\n\n  .swal2-progresscircle {\n    width: 2em;\n    height: 2em;\n    border-radius: 2em;\n    background: $blue;\n    color: $swal2-white;\n    line-height: 2em;\n    text-align: center;\n    z-index: 20;\n\n    &:first-child {\n      margin-left: 0;\n    }\n\n    &:last-child {\n      margin-right: 0;\n    }\n\n    &.swal2-activeprogressstep {\n      background: $blue;\n\n      ~.swal2-progresscircle {\n        background: $lightblue;\n      }\n\n      ~.swal2-progressline {\n        background: $lightblue;\n      }\n    }\n  }\n\n  .swal2-progressline {\n    width: $swal2-progress-steps-distance;\n    height: .4em;\n    margin: 0 -1px;\n    background: $blue;\n    z-index: 10;\n  }\n}\n\n\n// github.com/sweetalert2/sweetalert2/issues/268\n[class^='swal2'] {\n  -webkit-tap-highlight-color: transparent;\n}\n\n.swal2-show {\n  animation: $swal2-show-animation;\n\n  &.swal2-noanimation {\n    animation: none;\n  }\n}\n\n.swal2-hide {\n  animation: $swal2-hide-animation;\n\n  &.swal2-noanimation {\n    animation: none;\n  }\n}\n\n\n// Right-to-left support\n.swal2-rtl {\n  .swal2-close {\n    right: auto;\n    left: $swal2-close-button-gap;\n  }\n}\n\n\n// Success icon animation\n.swal2-animate-success-icon {\n  .swal2-success-line-tip {\n    animation: $swal2-success-line-tip-animation;\n  }\n\n  .swal2-success-line-long {\n    animation: $swal2-success-line-long-animation;\n  }\n\n  .swal2-success-circular-line-right {\n    animation: $swal2-success-circular-line-animation;\n  }\n}\n\n// Error icon animation\n.swal2-animate-error-icon {\n  animation: $swal2-error-icon-animation;\n\n  .swal2-x-mark {\n    animation: $swal2-error-x-mark-animation;\n  }\n}\n\n@keyframes swal2-rotate-loading {\n  0% {\n    transform: rotate(0deg);\n  }\n\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n@media print {\n  body {\n    &.swal2-shown {\n      @include not('.swal2-no-backdrop',\n        '.swal2-toast-shown'\n\n      ) {\n        overflow-y: scroll !important;\n\n        >[aria-hidden='true'] {\n          display: none;\n        }\n\n        .swal2-container {\n          position: initial !important;\n        }\n      }\n    }\n  }\n}"]}