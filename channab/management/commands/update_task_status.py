from django.core.management.base import BaseCommand
from django.utils import timezone
from crops.models import TaskInstance


class Command(BaseCommand):
    help = 'Update status of task instances whose scheduled date has passed'

    def handle(self, *args, **kwargs):
        now = timezone.now().date()
        instances = TaskInstance.objects.filter(scheduled_date__lt=now, status='pending')
        updated_count = instances.update(status='unattended')
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} task instances'))
