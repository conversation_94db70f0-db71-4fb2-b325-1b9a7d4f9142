from django import template
import math

register = template.Library()

@register.filter
def divide(value, arg):
    try:
        # Attempt to convert value and arg to numbers
        num_value = float(value)
        num_arg = float(arg)
        if num_arg == 0:
            return None  # Avoid division by zero
        if math.isinf(num_value) or math.isinf(num_arg):
             return None # Handle infinity gracefully, can't divide by/into infinity for this use case
        return int(num_value / num_arg) # Return as int for month calculation
    except (ValueError, TypeError):
        return None

@register.filter
def is_infinity(value):
    try:
        return math.isinf(float(value))
    except (ValueError, TypeError):
        return False
