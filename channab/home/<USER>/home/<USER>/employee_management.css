/* Employee Management Page Specific Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern-overlay.png');
    opacity: 0.1;
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-title {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: var(--h1-size);
    line-height: 1.2;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.hero-image {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.5s ease;
}

.hero-image:hover {
    transform: perspective(1000px) rotateY(0deg);
}

/* Feature Cards */
.feature-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(13, 164, 135, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

/* Statistics Section */
.stats-section {
    background: var(--white);
    padding: 4rem 0;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Dashboard Preview */
.dashboard-preview {
    position: relative;
    padding: 4rem 0;
}

.dashboard-image {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-image:hover {
    transform: translateY(-10px);
}

/* Solution Cards */
.solution-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.solution-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.solution-card:hover .solution-image {
    transform: scale(1.05);
}

.solution-content {
    padding: 2rem;
    background: var(--white);
}

.solution-content h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

/* Benefits List */
.benefits-list {
    list-style: none;
    padding: 0;
}

.benefits-list li {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
    position: relative;
}

.benefits-list li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    padding: 5rem 0;
    color: var(--white);
    text-align: center;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Payroll System Visualization */
.payroll-system {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.payroll-flow {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 3rem 0;
}

.payroll-flow::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    z-index: 1;
}

.payroll-step {
    width: 120px;
    height: 120px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.payroll-step:hover {
    transform: scale(1.1);
}

.payroll-step h4 {
    font-size: 0.9rem;
    margin: 0;
    font-weight: 600;
}

/* Task Management Demo */
.task-management-demo {
    background-color: rgba(13, 164, 135, 0.1);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
}

.task-list {
    margin-top: 1.5rem;
}

.task-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.task-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 1rem;
}

.status-pending {
    background-color: #ffc107;
}

.status-progress {
    background-color: #17a2b8;
}

.status-completed {
    background-color: #28a745;
}

.task-details {
    flex-grow: 1;
}

.task-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.task-meta {
    display: flex;
    font-size: 0.8rem;
    color: #6c757d;
}

.task-meta span {
    margin-right: 1rem;
}

/* Leave Management Calendar */
.leave-calendar {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    margin-top: 2rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 0.9rem;
    position: relative;
}

.calendar-day.has-leave::after {
    content: '';
    position: absolute;
    bottom: 5px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
}

.calendar-day.weekend {
    color: #dc3545;
}

.calendar-day.today {
    background: rgba(13, 164, 135, 0.1);
    font-weight: bold;
}

/* Process Steps */
.process-steps {
    counter-reset: step-counter;
    position: relative;
    margin: 3rem 0;
}

.process-step {
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    padding-left: 4rem;
}

.process-step::before {
    counter-increment: step-counter;
    content: counter(step-counter);
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
    z-index: 2;
}

.process-steps::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 5px;
    width: 2px;
    background: var(--primary-color);
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 991px) {
    .hero-image {
        margin-top: 2rem;
        transform: none;
    }
    
    .solution-image {
        height: 200px;
    }
    
    .feature-card {
        margin-bottom: 1rem;
    }
    
    .payroll-flow {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }
    
    .payroll-flow::after {
        width: 4px;
        height: 100%;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .process-step {
        padding-left: 3rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 4rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .solution-image {
        height: 180px;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .calendar-grid {
        grid-template-columns: repeat(7, 1fr);
        gap: 0.25rem;
    }
    
    .calendar-day {
        font-size: 0.8rem;
    }
} 