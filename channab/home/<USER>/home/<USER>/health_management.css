/* Health Management Page Specific Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern-overlay.png');
    opacity: 0.1;
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-title {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: var(--h1-size);
    line-height: 1.2;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.hero-image {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.5s ease;
}

.hero-image:hover {
    transform: perspective(1000px) rotateY(0deg);
}

/* Feature Cards */
.feature-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(13, 164, 135, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

/* Statistics Section */
.stats-section {
    background: var(--white);
    padding: 4rem 0;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Dashboard Preview */
.dashboard-preview {
    position: relative;
    padding: 4rem 0;
}

.dashboard-image {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-image:hover {
    transform: translateY(-10px);
}

/* Solution Cards */
.solution-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.solution-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.solution-card:hover .solution-image {
    transform: scale(1.05);
}

.solution-content {
    padding: 2rem;
    background: var(--white);
}

.solution-content h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

/* Benefits List */
.benefits-list {
    list-style: none;
    padding: 0;
}

.benefits-list li {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
    position: relative;
}

.benefits-list li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    padding: 5rem 0;
    color: var(--white);
    text-align: center;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Timeline for Vaccination Schedule */
.timeline {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 6px;
    background-color: var(--primary-color);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
    border-radius: 10px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
}

.timeline-item:nth-child(odd) {
    left: 0;
}

.timeline-item:nth-child(even) {
    left: 50%;
}

.timeline-content {
    padding: 20px 30px;
    background-color: white;
    position: relative;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 25px;
    height: 25px;
    right: -12px;
    background-color: white;
    border: 4px solid var(--primary-color);
    top: 15px;
    border-radius: 50%;
    z-index: 1;
}

.timeline-item:nth-child(even)::after {
    left: -12px;
}

/* Notification Alert Demo */
.notification-demo {
    background-color: rgba(13, 164, 135, 0.1);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    position: relative;
    overflow: hidden;
}

.notification-icon {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    opacity: 0.2;
}

/* Responsive Design */
@media (max-width: 991px) {
    .hero-image {
        margin-top: 2rem;
        transform: none;
    }
    
    .solution-image {
        height: 200px;
    }
    
    .feature-card {
        margin-bottom: 1rem;
    }
    
    .timeline::after {
        left: 31px;
    }
    
    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .timeline-item:nth-child(even) {
        left: 0;
    }
    
    .timeline-item::after {
        left: 18px;
    }
    
    .timeline-item:nth-child(even)::after {
        left: 18px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 4rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .solution-image {
        height: 180px;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
} 