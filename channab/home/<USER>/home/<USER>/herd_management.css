/* Herd Management Page Specific Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern-overlay.png');
    opacity: 0.1;
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-title {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: var(--h1-size);
    line-height: 1.2;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.hero-image {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.5s ease;
}

.hero-image:hover {
    transform: perspective(1000px) rotateY(0deg);
}

/* Feature Cards */
.feature-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(13, 164, 135, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

/* Statistics Section */
.stats-section {
    background: var(--white);
    padding: 4rem 0;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Dashboard Preview */
.dashboard-preview {
    position: relative;
    padding: 4rem 0;
}

.dashboard-image {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dashboard-image:hover {
    transform: translateY(-10px);
}

/* Solution Cards */
.solution-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.solution-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.solution-card:hover .solution-image {
    transform: scale(1.05);
}

.solution-content {
    padding: 2rem;
    background: var(--white);
}

.solution-content h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

/* Benefits List */
.benefits-list {
    list-style: none;
    padding: 0;
}

.benefits-list li {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
    position: relative;
}

.benefits-list li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    padding: 5rem 0;
    color: var(--white);
    text-align: center;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Herd Visualization */
.herd-visualization {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.herd-groups {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
    margin: 3rem 0;
}

.herd-group {
    width: 150px;
    height: 150px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.herd-group:hover {
    transform: scale(1.1);
}

.herd-group h4 {
    font-size: 1rem;
    margin: 0;
    font-weight: 600;
}

.herd-group p {
    font-size: 0.8rem;
    margin: 0.5rem 0 0;
    color: var(--primary-color);
}

/* Animal Tracking Demo */
.animal-tracking-demo {
    background-color: rgba(13, 164, 135, 0.1);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
}

.animal-list {
    margin-top: 1.5rem;
}

.animal-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.animal-status {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 1rem;
}

.status-healthy {
    background-color: #28a745;
}

.status-attention {
    background-color: #ffc107;
}

.status-treatment {
    background-color: #dc3545;
}

.animal-details {
    flex-grow: 1;
}

.animal-id {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.animal-meta {
    display: flex;
    font-size: 0.8rem;
    color: #6c757d;
}

.animal-meta span {
    margin-right: 1rem;
}

/* Productivity Chart */
.productivity-chart {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    margin-top: 2rem;
}

.chart-container {
    height: 300px;
    position: relative;
    margin-top: 1.5rem;
}

.chart-bar {
    position: absolute;
    bottom: 0;
    width: 40px;
    background: var(--primary-color);
    border-radius: 5px 5px 0 0;
    transition: height 0.5s ease;
}

.chart-label {
    position: absolute;
    bottom: -25px;
    text-align: center;
    font-size: 0.8rem;
    width: 40px;
}

/* Process Steps */
.process-steps {
    counter-reset: step-counter;
    position: relative;
    margin: 3rem 0;
}

.process-step {
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    padding-left: 4rem;
}

.process-step::before {
    counter-increment: step-counter;
    content: counter(step-counter);
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
    z-index: 2;
}

.process-steps::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 5px;
    width: 2px;
    background: var(--primary-color);
    z-index: 1;
}

/* Lifecycle Visualization */
.lifecycle-visualization {
    position: relative;
    margin: 4rem 0;
}

.lifecycle-circle {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    border: 4px solid var(--primary-color);
    position: relative;
    margin: 0 auto;
}

.lifecycle-stage {
    position: absolute;
    width: 100px;
    height: 100px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

.lifecycle-stage:hover {
    transform: scale(1.1);
    z-index: 10;
}

.lifecycle-stage h5 {
    font-size: 0.9rem;
    margin: 0;
}

.stage-birth {
    top: -50px;
    left: 100px;
}

.stage-calf {
    top: 50px;
    right: -50px;
}

.stage-heifer {
    bottom: -50px;
    left: 100px;
}

.stage-adult {
    top: 50px;
    left: -50px;
}

/* Responsive Design */
@media (max-width: 991px) {
    .hero-image {
        margin-top: 2rem;
        transform: none;
    }
    
    .solution-image {
        height: 200px;
    }
    
    .feature-card {
        margin-bottom: 1rem;
    }
    
    .herd-groups {
        flex-direction: column;
        align-items: center;
    }
    
    .process-step {
        padding-left: 3rem;
    }
    
    .lifecycle-circle {
        width: 250px;
        height: 250px;
    }
    
    .lifecycle-stage {
        width: 80px;
        height: 80px;
    }
    
    .stage-birth {
        top: -40px;
        left: 85px;
    }
    
    .stage-calf {
        top: 85px;
        right: -40px;
    }
    
    .stage-heifer {
        bottom: -40px;
        left: 85px;
    }
    
    .stage-adult {
        top: 85px;
        left: -40px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 4rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .solution-image {
        height: 180px;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .lifecycle-circle {
        width: 200px;
        height: 200px;
    }
    
    .lifecycle-stage {
        width: 70px;
        height: 70px;
    }
    
    .stage-birth {
        top: -35px;
        left: 65px;
    }
    
    .stage-calf {
        top: 65px;
        right: -35px;
    }
    
    .stage-heifer {
        bottom: -35px;
        left: 65px;
    }
    
    .stage-adult {
        top: 65px;
        left: -35px;
    }
} 