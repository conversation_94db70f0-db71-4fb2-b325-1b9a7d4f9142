/* Modern Variables */
:root {
    --primary-color: #0da487;
    --primary-dark: #0b8c73;
    --primary-light: #11c09e;
    --text-dark: #2d3436;
    --text-light: #636e72;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --section-gap: 100px;
    --card-gap: 30px;
    --transition: all 0.3s ease-in-out;
    --box-shadow: 0 10px 30px rgba(13, 164, 135, 0.1);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

/* Global Styles */
body {
    color: var(--text-dark);
    line-height: 1.7;
    font-family: 'Inter', sans-serif;
}

.section-spacing {
    padding: var(--section-gap) 0;
}

.section-header {
    margin-bottom: 60px;
    text-align: center;
}

.section-header h2 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1.2rem;
    color: var(--text-dark);
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.lead {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #0da487 0%, #0b8c73 100%);
    color: var(--white);
    position: relative;
    padding: 120px 0;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.1;
    animation: moveBackground 20s linear infinite;
}

.hero-title {
    font-size: 3.8rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    background: linear-gradient(to right, #fff, rgba(255,255,255,0.9));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-image img {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: all 0.5s ease;
    transform: perspective(1000px) rotateY(-5deg);
}

.hero-image:hover img {
    transform: perspective(1000px) rotateY(0deg) translateY(-10px);
}

/* Feature Cards */
.feature-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2.5rem;
    height: 100%;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(13, 164, 135, 0.1);
    border: 1px solid rgba(13, 164, 135, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #0da487, #0b8c73);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(13, 164, 135, 0.15);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0da487, #0b8c73);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    transform: rotate(-5deg);
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon {
    transform: rotate(0deg) scale(1.1);
}

.feature-icon i {
    font-size: 2rem;
    color: var(--white);
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon i {
    transform: scale(1.2);
}

.feature-card img {
    border-radius: 15px;
    margin-bottom: 1.5rem;
    transition: all 0.5s ease;
}

.feature-card:hover img {
    transform: scale(1.05);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0 0;
}

.feature-list li {
    padding-left: 1.8rem;
    position: relative;
    margin-bottom: 0.75rem;
    color: var(--text-light);
}

.feature-list li::before {
    content: '✓';
    color: var(--primary-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Solution Cards */
.solution-card {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(13, 164, 135, 0.1);
    height: 100%;
    position: relative;
}

.solution-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 50%, rgba(0,0,0,0.7));
    opacity: 0;
    transition: all 0.4s ease;
}

.solution-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(13, 164, 135, 0.15);
}

.solution-card:hover::after {
    opacity: 1;
}

.solution-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.solution-card:hover .solution-image {
    transform: scale(1.1);
}

.solution-content {
    padding: 2rem;
    border-top: 4px solid #0da487;
    background: white;
    position: relative;
    z-index: 1;
}

.solution-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
    position: relative;
    padding-bottom: 1rem;
}

.solution-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: #0da487;
    transition: all 0.4s ease;
}

.solution-card:hover .solution-content h3::after {
    width: 100px;
}

/* Integration List */
.integration-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.integration-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1.2rem;
    padding: 1.2rem;
    background: var(--white);
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid rgba(13, 164, 135, 0.1);
}

.integration-list li:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(13, 164, 135, 0.1);
    border-color: #0da487;
}

.integration-list i {
    color: #0da487;
    margin-right: 1rem;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.integration-list li:hover i {
    transform: scale(1.2);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #0da487 0%, #0b8c73 100%);
    color: var(--white);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.1;
    animation: moveBackground 20s linear infinite;
}

/* Buttons */
.btn {
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.4s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: #0da487;
    border-color: #0da487;
}

.btn-primary:hover {
    background: #0b8c73;
    border-color: #0b8c73;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(13, 164, 135, 0.2);
}

.btn-light:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255, 255, 255, 0.1);
}

/* Animations */
@keyframes moveBackground {
    0% { background-position: 0 0; }
    100% { background-position: 100% 100%; }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #0da487;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0b8c73;
}

/* Responsive Design */
@media (max-width: 991px) {
    :root {
        --section-gap: 80px;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .section-header h2 {
        font-size: 2.4rem;
    }
    
    .hero-section {
        padding: 80px 0;
    }
}

@media (max-width: 768px) {
    :root {
        --section-gap: 60px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .feature-card, .solution-card {
        margin-bottom: var(--card-gap);
    }
    
    .solution-image {
        height: 200px;
    }
}

/* Animations */
[data-aos] {
    opacity: 0;
    transition-property: transform, opacity;
}

[data-aos].aos-animate {
    opacity: 1;
}
