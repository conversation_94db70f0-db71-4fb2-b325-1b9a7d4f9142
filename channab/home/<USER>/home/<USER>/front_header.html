{% load static %}
<!-- Navbar -->
<nav class="navbar navbar-expand-lg sticky-top bg-white shadow-sm">
    <div class="container">
        <a class="navbar-brand" href="{% url 'home:home' %}">
            <PERSON><PERSON>b
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto align-items-center">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'home:home' %}">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'services' %}active{% endif %}" href="{% url 'home:services' %}">Services</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'pricing' %}active{% endif %}" href="{% url 'home:pricing' %}">Pricing</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#contact">Contact</a>
                </li>
                {% if user.is_authenticated %}
                <li class="nav-item ms-lg-3">
                    <a class="btn btn-primary" href="{% url 'home:dashboard' %}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                {% else %}
                <li class="nav-item ms-lg-3">
                    <a class="btn btn-outline-primary me-2" href="/accounts/login/">Login</a>
                </li>
                <li class="nav-item">
                    <a class="btn btn-primary" href="{% url 'home:dashboard' %}">Start Free Trial</a>
                </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
