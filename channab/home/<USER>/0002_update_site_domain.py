from django.db import migrations
from django.contrib.sites.models import Site

def update_site_domain(apps, schema_editor):
    Site.objects.update_or_create(
        id=1,
        defaults={
            'domain': 'farm.channab.com',
            'name': 'Channab Farm'
        }
    )

def reverse_site_domain(apps, schema_editor):
    Site.objects.update_or_create(
        id=1,
        defaults={
            'domain': 'example.com',
            'name': 'example.com'
        }
    )

class Migration(migrations.Migration):
    dependencies = [
        ('sites', '0002_alter_domain_unique'),
    ]

    operations = [
        migrations.RunPython(update_site_domain, reverse_site_domain),
    ]
