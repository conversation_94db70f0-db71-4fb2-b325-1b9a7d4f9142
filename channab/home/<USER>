from django.urls import path
from . import views

app_name = 'home'


urlpatterns = [
    path('', views.about, name='home'),  # Making about view the home page
    path('dashboard/', views.home_view, name='dashboard'),  # Moving home view to dashboard
    path('services/', views.services, name='services'),
    path('about/', views.about, name='about'),
    path('privacy-policy/', views.privacy_policy, name='privacy_policy'),
    path('api/dashboard/', views.DashboardAPIView.as_view(), name='api_dashboard'),
    path('api/mobile-dashboard/', views.MobileDashboardAPIView.as_view(), name='api_mobile_dashboard'),
    path('prices/', views.pricing, name='pricing'),
    path('contact/', views.contact, name='contact'),  # Added contact page URL
    path('services/milk-record/', views.milking_detail, name='milking_detail'),  # Added milking detail page URL
    path('services/health-management/', views.health_management, name='health_management'),  # Added health management page URL
    path('services/breeding-management/', views.breeding_management, name='breeding_management'),  # Added breeding management page URL
    path('services/employee-management/', views.employee_management, name='employee_management'),  # Added employee management page URL
    path('services/herd-management/', views.herd_management, name='herd_management'),  # Added herd management page URL
]
