from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from django.utils import timezone

class HomeSitemap(Sitemap):
    changefreq = "weekly"
    priority = 0.8
    protocol = 'https'

    def items(self):
        return [
            'home:home',           # Home page
            'home:services',       # Services
            'home:about',         # About
            'home:privacy_policy', # Privacy Policy
            'home:pricing',       # Pricing
            'home:contact',       # Contact
            'home:milking_detail', # Milk Record
            'home:health_management',  # Health Management
            'home:breeding_management', # Breeding Management
            'home:employee_management', # Employee Management
            'home:herd_management',    # Herd Management
        ]

    def location(self, item):
        return reverse(item)

    def lastmod(self, item):
        # Return the current date as the last modification date
        return timezone.now()
