# views.py
from django.shortcuts import render
from dairy.models import Animal, MilkRecord, MilkSale, MilkPayment, AnimalEvent, AnimalScore
from farm_finances.models import Income, Expense, IncomeCategory, ExpenseCategory
from crops.models import TaskInstance, Crop, Field, Task
from accounts.models import Profile
from health.models import VaccinationRecord
from django.contrib.auth.decorators import login_required
from datetime import timedelta, date, datetime
from django.utils import timezone
from django.db.models import Count, Sum, F, Case, When, DecimalField
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from .serializers import AnimalSerializer, MobileDashboardSerializer
from django.utils.dateparse import parse_date


from django.utils import timezone
import pytz

riyadh_timezone = pytz.timezone('Asia/Riyadh')
now = timezone.now().astimezone(riyadh_timezone)





def services(request):
    return render(request, 'home/services.html')

@login_required
def home_view(request):
    farm = request.user.farm
    now = timezone.now()

    # Define default time ranges for filters
    time_ranges = {
        "this_month": (now.replace(day=1), now),
        "last_month": ((now.replace(day=1) - timedelta(days=1)).replace(day=1), now.replace(day=1) - timedelta(seconds=1)),
        "last_3_months": ((now - timedelta(days=90)).replace(day=1), now),
        "this_year": (now.replace(month=1, day=1), now),
        "last_year": ((now.replace(month=1, day=1) - timedelta(days=365)).replace(month=1, day=1), now.replace(month=1, day=1) - timedelta(seconds=1)),
    }

    # Determine the time filter from request
    time_filter = request.GET.get('time_filter', 'this_month')
    start_date_str = request.GET.get('start_date') # Renamed to avoid conflict with datetime object
    end_date_str = request.GET.get('end_date')     # Renamed to avoid conflict

    if start_date_str and end_date_str:
        # Custom date range
        start_datetime = timezone.make_aware(datetime.strptime(start_date_str, '%Y-%m-%d'))
        end_datetime = timezone.make_aware(datetime.strptime(end_date_str, '%Y-%m-%d') + timedelta(days=1) - timedelta(seconds=1))
    else:
        # Predefined time range
        start_datetime, end_datetime = time_ranges[time_filter]

    # Use the determined dates to filter your queries for income/expense
    total_income = Income.objects.filter(farm=farm, date__gte=start_datetime, date__lte=end_datetime).aggregate(Sum('amount'))['amount__sum'] or 0
    total_expense = Expense.objects.filter(farm=farm, date__gte=start_datetime, date__lte=end_datetime).aggregate(Sum('amount'))['amount__sum'] or 0
    financial_status = total_income - total_expense # Renamed 'status' to avoid conflict

    # Aggregate data for income and expense summaries
    income_summary = Income.objects.filter(farm=farm, date__gte=start_datetime, date__lte=end_datetime).values('category__name').annotate(total_amount=Sum('amount'))
    expense_summary = Expense.objects.filter(farm=farm, date__gte=start_datetime, date__lte=end_datetime).values('category__name').annotate(total_amount=Sum('amount'))

    # --- Animal Dashboard Data ---
    all_farm_animals = Animal.objects.filter(farm=farm)
    total_animals_count = all_farm_animals.count()

    status_choices_dict = dict(Animal.STATUS_CHOICES)
    category_choices_dict = dict(Animal.CATEGORY_CHOICES)
    type_choices_dict = dict(Animal.TYPE_CHOICES)

    # Animal summary by category
    animal_summary_by_category = {
        cat_key: {stat_key: 0 for stat_key, _ in Animal.STATUS_CHOICES}
        for cat_key, _ in Animal.CATEGORY_CHOICES
    }
    for cat_key in list(animal_summary_by_category.keys()):
        animal_summary_by_category[cat_key]['display_name'] = category_choices_dict.get(cat_key, cat_key)
        animal_summary_by_category[cat_key]['total_category'] = 0
    
    raw_category_summary = all_farm_animals.values('category', 'status').annotate(count=Count('id')).order_by('category')
    for item in raw_category_summary:
        cat_key = item['category']
        stat_key = item['status']
        count = item['count']
        if cat_key in animal_summary_by_category and stat_key in animal_summary_by_category[cat_key]:
            animal_summary_by_category[cat_key][stat_key] = count
            animal_summary_by_category[cat_key]['total_category'] += count

    # Animal summary by type
    animal_summary_by_type = {
        type_key: {stat_key: 0 for stat_key, _ in Animal.STATUS_CHOICES}
        for type_key, _ in Animal.TYPE_CHOICES
    }
    for type_key in list(animal_summary_by_type.keys()):
        animal_summary_by_type[type_key]['display_name'] = type_choices_dict.get(type_key, type_key)
        animal_summary_by_type[type_key]['total_type'] = 0

    raw_type_summary = all_farm_animals.values('animal_type', 'status').annotate(count=Count('id')).order_by('animal_type')
    for item in raw_type_summary:
        type_key = item['animal_type']
        stat_key = item['status']
        count = item['count']
        if type_key in animal_summary_by_type and stat_key in animal_summary_by_type[type_key]:
            animal_summary_by_type[type_key][stat_key] = count
            animal_summary_by_type[type_key]['total_type'] += count

    # Animal summary by age
    age_groups_config = [
        {'name': '<1 year', 'min_days': 0, 'max_days': 365},
        {'name': '1-2 years', 'min_days': 365, 'max_days': 365 * 2},
        {'name': '2-3 years', 'min_days': 365 * 2, 'max_days': 365 * 3},
        {'name': '3-5 years', 'min_days': 365 * 3, 'max_days': 365 * 5},
        {'name': '>5 years', 'min_days': 365 * 5, 'max_days': float('inf')}
    ]
    animal_summary_by_age = {
        group['name']: {stat_key: 0 for stat_key, _ in Animal.STATUS_CHOICES}
        for group in age_groups_config
    }
    for group_name in animal_summary_by_age:
        animal_summary_by_age[group_name]['total_age_group'] = 0

    today_date = now.date()
    for animal in all_farm_animals.only('dob', 'status'):
        if animal.dob:
            age_delta_days = (today_date - animal.dob).days
            age_group_name_found = None
            for group_config in age_groups_config:
                if group_config['min_days'] <= age_delta_days < group_config['max_days']:
                    age_group_name_found = group_config['name']
                    break
            
            if age_group_name_found and animal.status in animal_summary_by_age[age_group_name_found]:
                animal_summary_by_age[age_group_name_found][animal.status] += 1
                animal_summary_by_age[age_group_name_found]['total_age_group'] += 1
    # --- End of Animal Dashboard Data ---

    # Load animals list for display (potentially filtered by sex)
    sex_filter = request.GET.get('sex', 'all')
    animals_list_for_display = Animal.objects.filter(farm=farm)
    if sex_filter != 'all':
        animals_list_for_display = animals_list_for_display.filter(sex=sex_filter)

    # Milk records for the selected period
    filtered_milk_records = MilkRecord.objects.filter(
        animal__farm=farm, 
        date__gte=start_datetime.date(), 
        date__lte=end_datetime.date()
    ).annotate(
        milk_total=Case(
            When(first_time__isnull=True, then=0), default=F('first_time'), output_field=DecimalField()) +
            Case(When(second_time__isnull=True, then=0), default=F('second_time'), output_field=DecimalField()) +
            Case(When(third_time__isnull=True, then=0), default=F('third_time'), output_field=DecimalField())
    )
    total_milk_filtered_period = filtered_milk_records.aggregate(total_milk=Sum('milk_total'))['total_milk'] or 0

    context = {
        'total_income': total_income,
        'total_expense': total_expense,
        'financial_status': financial_status, # Renamed from 'status'
        'income_summary': list(income_summary), # Renamed from 'summary'
        'expense_summary': list(expense_summary),
        'time_filter': time_filter,
        'start_date': start_date_str, # Use original string for template
        'end_date': end_date_str,     # Use original string for template
        
        'animals': animals_list_for_display, # Original animal list, renamed for clarity
        'total_milk_filtered_period': total_milk_filtered_period,

        # New animal dashboard context
        'total_animals_count': total_animals_count,
        'animal_summary_by_category': animal_summary_by_category,
        'animal_summary_by_type': animal_summary_by_type,
        'animal_summary_by_age': animal_summary_by_age,
        'animal_status_choices_dict': status_choices_dict,
        'animal_category_choices': Animal.CATEGORY_CHOICES, 
        'animal_type_choices': Animal.TYPE_CHOICES, 
        'animal_age_groups_config': age_groups_config,
    }

    return render(request, 'home/index.html', context)

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from django.utils import timezone
from datetime import datetime, timedelta

class DashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get_date_range(self, time_filter, start_date=None, end_date=None):
        today = timezone.now().date()
        
        if time_filter == 'today':
            return today, today
        elif time_filter == 'this_week':
            # Get the start of the week (Monday)
            start = today - timedelta(days=today.weekday())
            return start, today
        elif time_filter == 'this_month':
            start = today.replace(day=1)
            return start, today
        elif time_filter == 'custom_range':
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d').date()
                end = datetime.strptime(end_date, '%Y-%m-%d').date()
                return start, end
            except (ValueError, TypeError):
                return today, today
        else:  # Default to today
            return today, today

    def get(self, request):
        farm = request.user.farm
        time_filter = request.GET.get('time_filter', 'today')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        start_date, end_date = self.get_date_range(time_filter, start_date, end_date)
        
        # Get income and expense data
        current_income = Income.objects.filter(
            farm=farm,
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(total=Sum('amount'))['total'] or 0

        current_expense = Expense.objects.filter(
            farm=farm,
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Get milk data
        milk_records = MilkRecord.objects.filter(
            animal__farm=farm,
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(
            first_time_total=Sum('first_time') or 0,
            second_time_total=Sum('second_time') or 0,
            third_time_total=Sum('third_time') or 0,
            total_milk=Sum(
                F('first_time') + F('second_time') + F('third_time')
            ) or 0
        )

        data = {
            'period': {
                'time_filter': time_filter,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            },
            'finances': {
                'income': current_income,
                'expense': current_expense,
                'balance': current_income - current_expense
            },
            'milk': {
                'first_time': float(milk_records['first_time_total'] or 0),
                'second_time': float(milk_records['second_time_total'] or 0),
                'third_time': float(milk_records['third_time_total'] or 0),
                'total': float(milk_records['total_milk'] or 0)
            }
        }

        return Response(data)

class MobileDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get_date_range(self, time_filter, start_date=None, end_date=None):
        today = timezone.now().date()
        
        if time_filter == 'today':
            return today, today
        elif time_filter == 'this_week':
            start = today - timedelta(days=today.weekday())
            return start, today
        elif time_filter == 'this_month':
            start = today.replace(day=1)
            return start, today
        elif time_filter == 'custom_range':
            try:
                start = datetime.strptime(start_date, '%Y-%m-%d').date()
                end = datetime.strptime(end_date, '%Y-%m-%d').date()
                return start, end
            except (ValueError, TypeError):
                return today, today
        else:
            return today, today

    def get_animal_summary(self, farm, start_date, end_date):
        
        # Only work with active animals
        active_animals = Animal.objects.filter(farm=farm, status='active')
        
        # Get average score for active animals only
        latest_scores = AnimalScore.objects.filter(
            animal__farm=farm,
            animal__status='active'
        ).order_by('animal', '-last_calculated').distinct('animal')
        
        avg_score = 0
        low_score_count = 0
        if latest_scores.exists():
            scores = [score.total_score for score in latest_scores if score.total_score]
            if scores:
                avg_score = sum(scores) / len(scores)
                low_score_count = len([s for s in scores if s < 60])

        # Get top performers with today's milk (active animals only)
        today_milk = MilkRecord.objects.filter(
            animal__farm=farm,
            animal__status='active',
            date=start_date
        ).values('animal__id', 'animal__tag').annotate(
            total_milk=Sum(F('first_time') + F('second_time') + F('third_time'))
        ).order_by('-total_milk')[:5]

        top_performers = []
        for record in today_milk:
            animal_id = record['animal__id']
            latest_score_obj = AnimalScore.objects.filter(
                animal_id=animal_id
            ).order_by('-last_calculated').first()
            
            top_performers.append({
                'animal_id': animal_id,
                'tag': record['animal__tag'],
                'latest_score': float(latest_score_obj.total_score) if latest_score_obj else 0.0,
                'total_milk_today': float(record['total_milk'] or 0)
            })

        # Count by animal type (active animals only)
        type_counts = {}
        type_data = active_animals.values('animal_type').annotate(count=Count('id'))
        for item in type_data:
            type_counts[item['animal_type']] = item['count']

        # Count by category (active animals only)
        category_counts = {}
        category_data = active_animals.values('category').annotate(count=Count('id'))
        for item in category_data:
            category_counts[item['category']] = item['count']

        # Count by age groups (active animals only)
        from django.utils import timezone
        today_date = timezone.now().date()
        
        age_groups = {
            'calf': 0,           # 0-6 months
            'heifer_young': 0,   # 6-18 months  
            'breeding_heifer': 0, # 18-30 months
            'adult_cow': 0       # 30+ months
        }
        
        for animal in active_animals.filter(dob__isnull=False):
            age_days = (today_date - animal.dob).days
            age_months = age_days / 30.44  # Average days per month
            
            if age_months <= 6:
                age_groups['calf'] += 1
            elif age_months <= 18:
                age_groups['heifer_young'] += 1
            elif age_months <= 30:
                age_groups['breeding_heifer'] += 1
            else:
                age_groups['adult_cow'] += 1

        return {
            'total_count': active_animals.count(),
            'average_score': round(avg_score, 2),
            'top_performers': top_performers,
            'low_score_alerts': low_score_count,
            'by_type': type_counts,
            'by_category': category_counts,
            'by_age_group': {
                'calf_0_6_months': age_groups['calf'],
                'heifer_young_6_18_months': age_groups['heifer_young'],
                'breeding_heifer_18_30_months': age_groups['breeding_heifer'],
                'adult_cow_30_plus_months': age_groups['adult_cow']
            }
        }

    def get_milk_summary(self, farm, start_date, end_date):
        
        # Current period milk
        current_milk = MilkRecord.objects.filter(
            animal__farm=farm,
            date__gte=start_date,
            date__lte=end_date
        ).aggregate(
            first_time=Sum('first_time') or 0,
            second_time=Sum('second_time') or 0,
            third_time=Sum('third_time') or 0,
            total=Sum(F('first_time') + F('second_time') + F('third_time')) or 0
        )

        # Yesterday's milk for comparison
        yesterday = start_date - timedelta(days=1)
        yesterday_milk = MilkRecord.objects.filter(
            animal__farm=farm,
            date=yesterday
        ).aggregate(
            total=Sum(F('first_time') + F('second_time') + F('third_time')) or 0
        )

        # Calculate daily average
        days_in_period = (end_date - start_date).days + 1
        daily_avg = float(current_milk['total'] or 0) / days_in_period if days_in_period > 0 else 0

        # Calculate vs yesterday
        vs_yesterday = float(current_milk['total'] or 0) - float(yesterday_milk['total'] or 0)

        return {
            'first_time': float(current_milk['first_time'] or 0),
            'second_time': float(current_milk['second_time'] or 0),
            'third_time': float(current_milk['third_time'] or 0),
            'total': float(current_milk['total'] or 0),
            'daily_average': round(daily_avg, 2),
            'vs_yesterday': round(vs_yesterday, 2)
        }

    def get_financial_summary(self, farm, start_date, end_date):
        
        # Income and expenses
        income = Income.objects.filter(
            farm=farm, date__gte=start_date, date__lte=end_date
        ).aggregate(total=Sum('amount'))['total'] or 0

        expense = Expense.objects.filter(
            farm=farm, date__gte=start_date, date__lte=end_date
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Milk sales revenue (using total_price field)
        milk_sales = MilkSale.objects.filter(
            customer__farm=farm, date__gte=start_date, date__lte=end_date
        ).aggregate(total=Sum('total_price'))['total'] or 0

        # Pending customer payments (calculated from MilkPayment remaining amounts)
        pending_payments = MilkPayment.objects.filter(
            customer__farm=farm
        ).aggregate(total=Sum('remaining_payment'))['total'] or 0

        # Employee salaries due (this month's expected vs paid)
        # For now, let's set to 0 and implement proper salary tracking later
        salaries_due = 0

        return {
            'income': float(income),
            'expense': float(expense),
            'balance': float(income) - float(expense),
            'milk_sales_revenue': float(milk_sales),
            'pending_payments': float(pending_payments),
            'employee_salaries_due': float(salaries_due)
        }

    def get_today_activity(self, farm, today):
        
        # Today's activities
        milk_records = MilkRecord.objects.filter(
            animal__farm=farm, date=today
        ).count()

        print(f"Farm type: {type(farm)}")
        print(f"Farm ID: {farm.id}")
        
        # Try step by step to debug the relationship
        try:
            # First, let's see if we can get tasks directly
            all_task_instances = TaskInstance.objects.all().count()
            print(f"Total TaskInstance count: {all_task_instances}")
            
            # Let's try a simpler query first
            tasks_completed = TaskInstance.objects.filter(
                completed_date=today,
                status='completed'
            ).count()
            print(f"All completed tasks today (no farm filter): {tasks_completed}")
            
            # Now try with farm filter but break it down
            farm_fields = Field.objects.filter(farm=farm)
            print(f"Farm fields count: {farm_fields.count()}")
            
            farm_crops = Crop.objects.filter(fields__farm=farm)
            print(f"Farm crops count: {farm_crops.count()}")
            
            farm_tasks = Task.objects.filter(crop__fields__farm=farm)
            print(f"Farm tasks count: {farm_tasks.count()}")
            
            # Finally try the full query with the correct relationship
            tasks_completed = TaskInstance.objects.filter(
                task__crop__fields__farm=farm, 
                completed_date=today,
                status='completed'
            ).count()
            print(f"Completed tasks today for farm: {tasks_completed}")
            
        except Exception as e:
            print(f"Error in task query: {e}")
            tasks_completed = 0

        try:
            pending_tasks = TaskInstance.objects.filter(
                task__crop__fields__farm=farm,
                status='pending'
            ).count()
        except Exception as e:
            print(f"Error in pending tasks query: {e}")
            pending_tasks = 0

        try:
            overdue_tasks = TaskInstance.objects.filter(
                task__crop__fields__farm=farm,
                status='pending',
                scheduled_date__lt=today
            ).count()
        except Exception as e:
            print(f"Error in overdue tasks query: {e}")
            overdue_tasks = 0

        animal_events = AnimalEvent.objects.filter(
            animal__farm=farm, date=today
        ).count()

        # Vaccinations due (animals not vaccinated in last 6 months - simplified logic)
        six_months_ago = today - timedelta(days=180)
        recent_vaccinated_animals = VaccinationRecord.objects.filter(
            animal__farm=farm,
            date__gte=six_months_ago
        ).values('animal').distinct()
        
        total_farm_animals = Animal.objects.filter(farm=farm, status='active').count()
        vaccinations_due = max(0, total_farm_animals - recent_vaccinated_animals.count())

        return {
            'milk_records_count': milk_records,
            'tasks_completed': tasks_completed,
            'pending_tasks': pending_tasks,
            'overdue_tasks': overdue_tasks,
            'animal_events': animal_events,
            'vaccinations_due': vaccinations_due
        }

    def get_performance_kpis(self, farm, start_date, end_date):
        
        # Milk per animal average
        active_animals_count = Animal.objects.filter(farm=farm, status='active').count()
        total_milk = MilkRecord.objects.filter(
            animal__farm=farm, date__gte=start_date, date__lte=end_date
        ).aggregate(total=Sum(F('first_time') + F('second_time') + F('third_time')))['total'] or 0
        
        milk_per_animal = float(total_milk) / active_animals_count if active_animals_count > 0 else 0

        # Employee count
        employee_count = Profile.objects.filter(user__farm=farm, end_date__isnull=True).count()

        # Active fields and crops
        active_fields = Field.objects.filter(farm=farm).count()
        crops_in_progress = Crop.objects.filter(
            fields__farm=farm, stage__in=['planning', 'planting', 'growing', 'flowering']
        ).count()

        # Vaccination compliance (animals vaccinated in last year)
        total_animals = Animal.objects.filter(farm=farm, status='active').count()
        vaccinated_animals = VaccinationRecord.objects.filter(
            animal__farm=farm,
            date__gte=start_date - timedelta(days=365)
        ).values('animal').distinct().count()
        
        compliance = (vaccinated_animals / total_animals * 100) if total_animals > 0 else 0

        return {
            'milk_per_animal_avg': round(milk_per_animal, 2),
            'employee_count': employee_count,
            'active_fields': active_fields,
            'crops_in_progress': crops_in_progress,
            'vaccination_compliance': round(compliance, 2)
        }

    def get_alerts(self, farm, today):
        alerts = []
        
        # Low scoring animals
        low_score_animals = AnimalScore.objects.filter(
            animal__farm=farm,
            animal__status='active',
            total_score__lt=60
        ).values('animal').distinct().count()
        
        if low_score_animals > 0:
            alerts.append({
                'type': 'low_score_animals',
                'message': f'{low_score_animals} animals have low performance scores',
                'count': low_score_animals,
                'priority': 'high'
            })

        # Overdue tasks
        overdue_tasks = TaskInstance.objects.filter(
            task__crop__fields__farm=farm,
            status='pending',
            scheduled_date__lt=today
        ).count()
        
        if overdue_tasks > 0:
            alerts.append({
                'type': 'overdue_tasks',
                'message': f'{overdue_tasks} tasks are overdue',
                'count': overdue_tasks,
                'priority': 'medium'
            })

        # Overdue vaccinations (animals not vaccinated in last 6 months)
        six_months_ago = today - timedelta(days=180)
        recent_vaccinated_animals = VaccinationRecord.objects.filter(
            animal__farm=farm,
            date__gte=six_months_ago
        ).values('animal').distinct()
        
        total_farm_animals = Animal.objects.filter(farm=farm, status='active').count()
        overdue_vaccinations = max(0, total_farm_animals - recent_vaccinated_animals.count())
        
        if overdue_vaccinations > 0:
            alerts.append({
                'type': 'overdue_vaccinations',
                'message': f'{overdue_vaccinations} vaccinations are overdue',
                'count': overdue_vaccinations,
                'priority': 'high'
            })

        return alerts

    def get_recent_activities(self, farm, today):
        activities = []
        
        # Recent milk records
        recent_milk = MilkRecord.objects.filter(
            animal__farm=farm, date=today
        ).order_by('-id')[:3]
        
        for record in recent_milk:
            total_milk = (record.first_time or 0) + (record.second_time or 0) + (record.third_time or 0)
            activities.append({
                'type': 'milk_record',
                'message': f'Animal {record.animal.tag} produced {total_milk}L today',
                'timestamp': timezone.now(),
                'related_id': record.id
            })

        # Recent tasks completed
        recent_tasks = TaskInstance.objects.filter(
            task__crop__fields__farm=farm,
            completed_date=today,
            status='completed'
        ).order_by('-completed_date')[:2]
        
        for task in recent_tasks:
            activities.append({
                'type': 'task_completed',
                'message': f'Task "{task.task.title}" completed',
                'timestamp': task.completed_date or timezone.now(),
                'related_id': task.id
            })

        # Recent animal events
        recent_events = AnimalEvent.objects.filter(
            animal__farm=farm, date=today
        ).order_by('-id')[:2]
        
        for event in recent_events:
            activities.append({
                'type': 'animal_event',
                'message': f'Animal {event.animal.tag}: {event.event_type}',
                'timestamp': timezone.now(),
                'related_id': event.id
            })

        return sorted(activities, key=lambda x: x['timestamp'], reverse=True)[:5]

    def get(self, request):
        
        print("=== Mobile Dashboard API Debug ===")
        print(f"User: {request.user}")
        print(f"User farm: {hasattr(request.user, 'farm')}")
        
        try:
            farm = request.user.farm
            print(f"Farm: {farm}")
        except Exception as e:
            print(f"Error getting farm: {e}")
            return Response(
                {"error": f"Farm access error: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        time_filter = request.GET.get('time_filter', 'today')
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        
        print(f"Time filter: {time_filter}")
        print(f"Start date: {start_date_str}")
        print(f"End date: {end_date_str}")
        
        try:
            start_date, end_date = self.get_date_range(time_filter, start_date_str, end_date_str)
            today = timezone.now().date()
            print(f"Calculated dates - Start: {start_date}, End: {end_date}, Today: {today}")

            print("Getting animal summary...")
            animals_data = self.get_animal_summary(farm, start_date, end_date)
            print(f"Animals data: {animals_data}")

            print("Getting milk summary...")
            milk_data = self.get_milk_summary(farm, start_date, end_date)
            print(f"Milk data: {milk_data}")

            print("Getting financial summary...")
            finances_data = self.get_financial_summary(farm, start_date, end_date)
            print(f"Finances data: {finances_data}")

            print("Getting today activity...")
            activity_data = self.get_today_activity(farm, today)
            print(f"Activity data: {activity_data}")

            print("Getting performance KPIs...")
            performance_data = self.get_performance_kpis(farm, start_date, end_date)
            print(f"Performance data: {performance_data}")

            print("Getting alerts...")
            alerts_data = self.get_alerts(farm, today)
            print(f"Alerts data: {alerts_data}")

            print("Getting recent activities...")
            recent_activities_data = self.get_recent_activities(farm, today)
            print(f"Recent activities data: {recent_activities_data}")

            dashboard_data = {
                'period': {
                    'time_filter': time_filter,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d')
                },
                'animals': animals_data,
                'milk': milk_data,
                'finances': finances_data,
                'today_activity': activity_data,
                'performance': performance_data,
                'alerts': alerts_data,
                'recent_activities': recent_activities_data
            }

            print("Creating serializer...")
            serializer = MobileDashboardSerializer(dashboard_data)
            print("Serializer created successfully")
            
            print("Returning response...")
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            print(f"ERROR in mobile dashboard: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return Response(
                {"error": f"Failed to fetch dashboard data: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

def about(request):
    """
    View function for the About page.
    Includes SEO-friendly meta information and company details.
    """
    context = {
        'title': 'About Channab Dairy Farm Management System',
        'meta_description': 'Learn about Channab Dairy Farm Management System - A comprehensive solution for dairy farm operations, animal management, milk production tracking, and financial oversight.',
        'meta_keywords': 'dairy farm management, animal tracking, milk production, farm operations, dairy software, livestock management',
    }
    return render(request, 'home/about.html', context)

def pricing(request):
    """
    View function for the pricing page
    """
    return render(request, 'home/pricing.html')

def privacy_policy(request):
    return render(request, 'home/privacy-policy.html')

def test_ui(request):
    """
    View function for the test UI page with dummy data
    """
    return render(request, 'home/test_ui.html')

def contact(request):
    """
    View function for the contact page.
    Displays contact information and support details.
    """
    return render(request, 'home/contact.html')

def milking_detail(request):
    """
    View function for the milking detail page.
    Displays comprehensive information about milk recording features.
    """
    return render(request, 'home/milking_detail.html')

def health_management(request):
    """
    View function for the dairy farm health management page.
    Displays comprehensive information about animal health management, vaccination records,
    health monitoring, and notification alerts.
    """
    return render(request, 'home/health_management.html')

def breeding_management(request):
    """
    View function for the breeding management page.
    Displays comprehensive information about estrus detection, AI records,
    pregnancy tracking, calving management, and breeding analytics.
    """
    return render(request, 'home/breeding_management.html')

def employee_management(request):
    """
    View function for the employee management page.
    Displays comprehensive information about salary management, payroll calculation,
    leave tracking, task assignments, and employee record-keeping.
    """
    return render(request, 'home/employee_management.html')

def herd_management(request):
    """
    View function for the herd management page.
    Displays comprehensive information about animal tagging, movement tracking,
    grouping, productivity monitoring, and historical records analysis.
    """
    return render(request, 'home/herd_management.html')

def custom_404(request, exception):
    return render(request, '404.html', status=404)
