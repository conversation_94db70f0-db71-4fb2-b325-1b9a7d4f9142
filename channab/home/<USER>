from rest_framework import serializers
from dairy.models import Animal, MilkR<PERSON>ord, AnimalScore, AnimalEvent, Customer, MilkSale
from farm_finances.models import Income, Expense, IncomeCategory, ExpenseCategory
from accounts.models import Profile, SalaryTransaction
from crops.models import TaskInstance, Crop, Field
from health.models import VaccinationRecord
from feeds.models import FeedActivity


class IncomeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Income
        fields = '__all__'

class ExpenseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Expense
        fields = '__all__'

class IncomeCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeCategory
        fields = '__all__'

class ExpenseCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpenseCategory
        fields = '__all__'

class AnimalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Animal
        fields = '__all__'

class MilkRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = <PERSON>R<PERSON>ord
        fields = '__all__'

class TopAnimalSerializer(serializers.Serializer):
    animal_id = serializers.IntegerField()
    tag = serializers.CharField()
    latest_score = serializers.DecimalField(max_digits=5, decimal_places=2)
    total_milk_today = serializers.DecimalField(max_digits=10, decimal_places=2)

class AlertSerializer(serializers.Serializer):
    type = serializers.CharField()
    message = serializers.CharField()
    count = serializers.IntegerField()
    priority = serializers.CharField()

class ActivitySerializer(serializers.Serializer):
    type = serializers.CharField()
    message = serializers.CharField()
    timestamp = serializers.DateTimeField()
    related_id = serializers.IntegerField(required=False)

class FinancialSummarySerializer(serializers.Serializer):
    income = serializers.DecimalField(max_digits=12, decimal_places=2)
    expense = serializers.DecimalField(max_digits=12, decimal_places=2)
    balance = serializers.DecimalField(max_digits=12, decimal_places=2)
    milk_sales_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    pending_payments = serializers.DecimalField(max_digits=12, decimal_places=2)
    employee_salaries_due = serializers.DecimalField(max_digits=12, decimal_places=2)

class AnimalSummarySerializer(serializers.Serializer):
    total_count = serializers.IntegerField()
    average_score = serializers.DecimalField(max_digits=5, decimal_places=2)
    top_performers = TopAnimalSerializer(many=True)
    low_score_alerts = serializers.IntegerField()
    by_type = serializers.DictField()
    by_category = serializers.DictField()
    by_age_group = serializers.DictField()

class MilkSummarySerializer(serializers.Serializer):
    first_time = serializers.DecimalField(max_digits=10, decimal_places=2)
    second_time = serializers.DecimalField(max_digits=10, decimal_places=2)
    third_time = serializers.DecimalField(max_digits=10, decimal_places=2)
    total = serializers.DecimalField(max_digits=10, decimal_places=2)
    daily_average = serializers.DecimalField(max_digits=10, decimal_places=2)
    vs_yesterday = serializers.DecimalField(max_digits=10, decimal_places=2)

class TodayActivitySerializer(serializers.Serializer):
    milk_records_count = serializers.IntegerField()
    tasks_completed = serializers.IntegerField()
    pending_tasks = serializers.IntegerField()
    overdue_tasks = serializers.IntegerField()
    animal_events = serializers.IntegerField()
    vaccinations_due = serializers.IntegerField()

class PerformanceKPISerializer(serializers.Serializer):
    milk_per_animal_avg = serializers.DecimalField(max_digits=10, decimal_places=2)
    employee_count = serializers.IntegerField()
    active_fields = serializers.IntegerField()
    crops_in_progress = serializers.IntegerField()
    vaccination_compliance = serializers.DecimalField(max_digits=5, decimal_places=2)

class MobileDashboardSerializer(serializers.Serializer):
    period = serializers.DictField()
    animals = AnimalSummarySerializer()
    milk = MilkSummarySerializer()
    finances = FinancialSummarySerializer()
    today_activity = TodayActivitySerializer()
    performance = PerformanceKPISerializer()
    alerts = AlertSerializer(many=True)
    recent_activities = ActivitySerializer(many=True)
