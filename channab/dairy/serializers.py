from rest_framework import serializers
from .models import Animal, AnimalWeight, MilkRecord, Farm, AnimalEvent, Customer, MilkSale, MilkPayment, AnimalScore
from django.db import IntegrityError
from django.utils import timezone
from datetime import date

class AnimalWeightSerializer(serializers.ModelSerializer):
    animal_tag = serializers.CharField(source='animal.tag', read_only=True)
    previous_weight = serializers.SerializerMethodField()
    weight_change_percent = serializers.SerializerMethodField()
    weight_gain_per_day = serializers.SerializerMethodField()

    class Meta:
        model = AnimalWeight
        fields = ['id', 'weight_kg', 'date', 'description', 'animal_tag', 'previous_weight', 'weight_change_percent', 'weight_gain_per_day']

    def get_previous_weight(self, obj):
        # Get the previous weight record for this animal
        prev_weight = AnimalWeight.objects.filter(
            animal=obj.animal,
            date__lt=obj.date
        ).order_by('-date').first()
        
        return float(prev_weight.weight_kg) if prev_weight else None

    def get_weight_change_percent(self, obj):
        prev_weight = self.get_previous_weight(obj)
        if prev_weight is None:
            return None
        
        try:
            change_percent = ((float(obj.weight_kg) - prev_weight) / prev_weight) * 100
            return round(change_percent, 2)
        except (ZeroDivisionError, TypeError):
            return None

    def get_weight_gain_per_day(self, obj):
        prev_weight = self.get_previous_weight(obj)
        if prev_weight is None:
            return None
        
        # Get the previous weight record for this animal
        prev_weight_record = AnimalWeight.objects.filter(
            animal=obj.animal,
            date__lt=obj.date
        ).order_by('-date').first()
        
        if not prev_weight_record:
            return None
            
        try:
            days_passed = (obj.date - prev_weight_record.date).days
            if days_passed == 0:
                return None
            gain_per_day = (float(obj.weight_kg) - prev_weight) / days_passed
            return round(gain_per_day, 2)
        except (ZeroDivisionError, TypeError):
            return None

class MilkRecordSerializer(serializers.ModelSerializer):
    animal = serializers.PrimaryKeyRelatedField(queryset=Animal.objects.all(), write_only=True)
    animal_tag = serializers.CharField(source='animal.tag', read_only=True)
    total_milk = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = MilkRecord
        fields = ['id', 'animal', 'animal_tag', 'date', 'first_time', 'second_time', 'third_time', 'total_milk']

    def validate(self, data):
        # Validate that at least one milk time is provided
        milk_times = [
            data.get('first_time'),
            data.get('second_time'),
            data.get('third_time')
        ]
        if not any(milk_times):
            raise serializers.ValidationError(
                "At least one milk time (first_time, second_time, or third_time) must be provided"
            )

        # Validate that the animal is female
        animal = data.get('animal')
        if animal and animal.sex != 'female':
            raise serializers.ValidationError(
                "Milk records can only be created for female animals"
            )

        # Validate that the animal is of type 'milking' or 'preg_milking'
        valid_types = ['milking', 'preg_milking']
        if animal and animal.animal_type not in valid_types:
            raise serializers.ValidationError(
                f"Milk records can only be created for animals of type: {', '.join(valid_types)}"
            )

        # Validate date is not in the future
        date = data.get('date')
        if date:
            # Get current date in UTC
            current_utc = timezone.now()
            # Convert to Asia/Karachi timezone (UTC+5)
            pk_tz = timezone.get_fixed_timezone(5 * 60)  # 5 hours ahead of UTC
            current_date = timezone.localtime(current_utc, pk_tz).date()
            
            if date > current_date:
                raise serializers.ValidationError(
                    "Milk record date cannot be in the future"
                )

        return data

    def create(self, validated_data):
        animal = validated_data.get('animal')
        date = validated_data.get('date')
        
        try:
            milk_record, created = MilkRecord.objects.get_or_create(
                animal=animal,
                date=date,
                defaults=validated_data
            )
            
            if not created:
                # Update existing record
                for attr, value in validated_data.items():
                    setattr(milk_record, attr, value)
                milk_record.save()
            
            return milk_record
            
        except IntegrityError as e:
            raise serializers.ValidationError(f"Database integrity error: {str(e)}")

class MilkAnimalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Animal
        fields = ['id', 'tag', 'sex', 'animal_type']

class AnimalSerializer(serializers.ModelSerializer):
    weights = AnimalWeightSerializer(many=True, read_only=True)
    milk_records = MilkRecordSerializer(many=True, read_only=True)
    farm = serializers.PrimaryKeyRelatedField(
        queryset=Farm.objects.all(),
        required=True
    )

    class Meta:
        model = Animal
        fields = [
            'id', 'tag', 'farm', 'dob', 'image', 'status', 
            'purchase_cost', 'sex', 'category', 'animal_type', 
            'weights', 'milk_records', 'mother', 'father'
        ]

    def validate(self, data):
        mother = data.get('mother')
        father = data.get('father')
        
        if mother is not None and father is not None and mother == father:
            raise serializers.ValidationError("Mother and father cannot be the same animal")
        
        if mother is not None and mother.sex != 'female':
            raise serializers.ValidationError("Mother must be female")
        
        if father is not None and father.sex != 'male':
            raise serializers.ValidationError("Father must be male")
        
        return data

    def create(self, validated_data):
        try:
            return super().create(validated_data)
        except IntegrityError:
            raise serializers.ValidationError("An animal with this tag already exists in this farm")

class AnimalTypeSerializer(serializers.Serializer):
    name = serializers.CharField()

class AnimalEventSerializer(serializers.ModelSerializer):
    animal_tag = serializers.CharField(source='animal.tag', read_only=True)
    
    class Meta:
        model = AnimalEvent
        fields = ['id', 'animal', 'animal_tag', 'title', 'description', 'date', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at', 'animal_tag']

    def validate_date(self, value):
        """
        Check that the date is not in the future
        """
        if value > date.today():
            raise serializers.ValidationError("Event date cannot be in the future")
        return value

class MinimalAnimalSerializer(serializers.ModelSerializer):
    class Meta:
        model = Animal
        fields = ['id', 'tag']

class CustomerSerializer(serializers.ModelSerializer):
    total_milk = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = Customer
        fields = ['id', 'name', 'mobile_number', 'created_date', 'total_milk', 'total_amount']

class MilkSaleSerializer(serializers.ModelSerializer):
    total_liters = serializers.SerializerMethodField()
    
    class Meta:
        model = MilkSale
        fields = ['id', 'date', 'first_sale', 'second_sale', 'third_sale', 
                 'price_per_liter', 'total_price', 'total_liters']
    
    def get_total_liters(self, obj):
        total = 0
        if obj.first_sale:
            total += float(obj.first_sale)
        if obj.second_sale:
            total += float(obj.second_sale)
        if obj.third_sale:
            total += float(obj.third_sale)
        return total

class AnimalScoreSerializer(serializers.ModelSerializer):
    animal_tag = serializers.CharField(source='animal.tag', read_only=True)
    animal_id = serializers.IntegerField(source='animal.id', read_only=True)
    grade = serializers.SerializerMethodField()
    score_color = serializers.CharField(source='get_score_color', read_only=True)
    last_photo_date = serializers.SerializerMethodField()
    last_weight_date = serializers.SerializerMethodField()
    days_since_photo = serializers.SerializerMethodField()
    days_since_weight = serializers.SerializerMethodField()
    milk_compliance_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = AnimalScore
        fields = [
            'id', 'animal_id', 'animal_tag', 'photo_score', 'weight_score', 
            'milk_score', 'total_score', 'grade', 'score_color', 'last_calculated',
            'last_photo_date', 'last_weight_date', 'days_since_photo', 
            'days_since_weight', 'milk_compliance_rate'
        ]
        read_only_fields = ['id', 'last_calculated']
    
    def get_last_photo_date(self, obj):
        if obj.animal.image and obj.animal.image_updated:
            return obj.animal.image_updated.date()
        return None
    
    def get_last_weight_date(self, obj):
        latest_weight = obj.animal.weights.order_by('-date').first()
        return latest_weight.date if latest_weight else None
    
    def get_days_since_photo(self, obj):
        if obj.animal.image and obj.animal.image_updated:
            return (timezone.now().date() - obj.animal.image_updated.date()).days
        return None
    
    def get_days_since_weight(self, obj):
        last_weight_date = self.get_last_weight_date(obj)
        if last_weight_date:
            return (timezone.now().date() - last_weight_date).days
        return None
    
    def get_milk_compliance_rate(self, obj):
        if obj.animal.animal_type in ['milking', 'preg_milking'] and obj.animal.sex == 'female':
            # Calculate compliance rate based on recent records
            from datetime import timedelta
            
            days_to_check = min(30, (timezone.now().date() - obj.animal.type_changed_date.date()).days) if obj.animal.type_changed_date else 30
            start_date = timezone.now().date() - timedelta(days=days_to_check)
            
            expected_records = max(1, int(days_to_check * 0.85))
            actual_records = obj.animal.milkrecord_set.filter(date__gte=start_date).count()
            
            return round((actual_records / expected_records) * 100, 1) if expected_records > 0 else 0
        return None
    
    def get_grade(self, obj):
        return obj.get_grade()

class AnimalWithScoreSerializer(serializers.ModelSerializer):
    score = AnimalScoreSerializer(read_only=True)
    
    class Meta:
        model = Animal
        fields = [
            'id', 'tag', 'dob', 'sex', 'category', 'animal_type', 
            'status', 'image', 'score'
        ]

class FarmScoreStatsSerializer(serializers.Serializer):
    total_active_animals = serializers.IntegerField()
    average_score = serializers.FloatField()
    excellent_count = serializers.IntegerField()  # 80+
    good_count = serializers.IntegerField()       # 60-79
    fair_count = serializers.IntegerField()       # 40-59
    poor_count = serializers.IntegerField()       # 0-39
    top_performers = AnimalWithScoreSerializer(many=True)
    needs_attention = AnimalWithScoreSerializer(many=True)
