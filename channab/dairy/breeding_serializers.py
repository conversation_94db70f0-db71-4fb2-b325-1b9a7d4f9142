from rest_framework import serializers
from .models import Breeding, Pregnancy<PERSON><PERSON><PERSON>, Calving<PERSON><PERSON>ord, BreedingCycle, Animal
from .serializers import AnimalSerializer
from django.core.exceptions import ValidationError

class BreedingCycleSerializer(serializers.ModelSerializer):
    class Meta:
        model = BreedingCycle
        fields = ['id', 'animal', 'start_date', 'end_date', 'status', 'lactation_number', 'notes']
        read_only_fields = ['id']

class PregnancyCheckSerializer(serializers.ModelSerializer):
    class Meta:
        model = PregnancyCheck
        fields = ['id', 'breeding', 'check_date', 'status', 'notes']
        read_only_fields = ['id']

class CalvingRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = CalvingRecord
        fields = ['id', 'breeding', 'calving_date', 'calf', 'difficulty', 'complications', 'notes']
        read_only_fields = ['id']

class BreedingSerializer(serializers.ModelSerializer):
    female_animal = AnimalSerializer(read_only=True)
    female_animal_id = serializers.PrimaryKeyRelatedField(
        source='female_animal',
        queryset=Animal.objects.filter(sex='female'),
        write_only=True
    )
    bull = AnimalSerializer(read_only=True)
    bull_id = serializers.PrimaryKeyRelatedField(
        source='bull',
        queryset=Animal.objects.filter(sex='male', animal_type='breeder'),
        write_only=True,
        required=False
    )
    pregnancy_checks = PregnancyCheckSerializer(many=True, read_only=True)
    calving = CalvingRecordSerializer(read_only=True)
    breeding_cycle = BreedingCycleSerializer(read_only=True)

    class Meta:
        model = Breeding
        fields = [
            'id', 'female_animal', 'female_animal_id', 'date_of_insemination',
            'breeding_method', 'bull', 'bull_id', 'semen_tag', 'comment',
            'lactation_number', 'attempt', 'expected_calving_date', 'dry_off_date',
            'breeding_cycle', 'status', 'pregnancy_checks', 'calving'
        ]
        read_only_fields = [
            'id', 'lactation_number', 'attempt', 'expected_calving_date',
            'dry_off_date', 'breeding_cycle', 'status'
        ]

    def validate(self, data):
        # First validate breeding method specific requirements
        breeding_method = data.get('breeding_method')
        bull = data.get('bull')
        semen_tag = data.get('semen_tag')

        if breeding_method == 'natural' and not bull:
            raise serializers.ValidationError("A bull must be selected for natural breeding.")
        if breeding_method == 'artificial' and not semen_tag:
            raise serializers.ValidationError("A semen tag number must be provided for artificial breeding.")
        if breeding_method == 'natural' and semen_tag:
            raise serializers.ValidationError("Semen tag number must be empty for natural breeding.")
        if breeding_method == 'artificial' and bull:
            raise serializers.ValidationError("Bull must not be selected for artificial breeding.")

        # Then run model validation
        instance = Breeding(**data)
        try:
            instance.clean()
        except ValidationError as e:
            raise serializers.ValidationError(str(e))
            
        return data
