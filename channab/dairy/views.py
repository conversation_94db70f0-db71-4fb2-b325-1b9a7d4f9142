from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.urls import reverse, reverse_lazy
from .models import Animal, Breeding, Customer, MilkPayment, MilkSale, AnimalEvent, PregnancyCheck, CalvingRecord, BreedingCycle
from .forms import AnimalForm, AnimalWeightForm, CustomerForm, MilkPaymentForm, MilkRecordForm, MilkSaleForm, BreedingForm, PregnancyCheckForm, CalvingRecordForm, HeatDetectionForm, SemenInventoryForm
from accounts.models import Farm
from .models import MilkRecord, Animal, AnimalWeight
from django.db.models import F, Avg, Count, Max, Min
from datetime import timedelta, date, datetime
import decimal
from django.http import JsonResponse
from calendar import monthrange
from django.db.models import Subquery, OuterRef
from django.utils import timezone
from django.db.models import Sum
from datetime import timedelta
from django.db.models import F, Q, Value, DecimalField
from django.db.models.functions import Lag
from django.http import HttpResponseForbidden, JsonResponse
from django.core import serializers
from django.core.exceptions import ValidationError
from django.db.models.functions import Coalesce
from farm_finances.models import IncomeCategory, Income
from dateutil.relativedelta import relativedelta
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.contrib import messages
import json

@login_required
def search(request):
    q = request.GET.get('q', '')

    # Get the farm of the logged in user
    user_farm = request.user.farm

    animals = Animal.objects.filter(
        Q(tag__icontains=q) |
        Q(category__icontains=q) |
        Q(sex__icontains=q) |
        Q(status__icontains=q),
        farm=user_farm  # Limit search to the user's farm
    )

    data = serializers.serialize('json', animals)
    return JsonResponse(data, safe=False)

from django.core.paginator import Paginator
# just a comment 

@login_required
def animal_list(request):
    farm = request.user.farm
    
    # Get sorting parameter
    sort_by = request.GET.get('sort_by', 'tag')  # Default to tag
    
    animals = Animal.objects.filter(farm=farm).prefetch_related('score')
    animals = animals.annotate(latest_weight=Subquery(
        AnimalWeight.objects.filter(animal=OuterRef('pk')).order_by('-date')[:1].values('weight_kg')
    ))
    
    animal_types = dict(Animal.TYPE_CHOICES)
    animal_categories = dict(Animal.CATEGORY_CHOICES)
    
    # Get all filter parameters
    filter_params = {}
    
    # Add sort_by to filter_params for URL preservation
    filter_params['sort_by'] = sort_by
    
    # Filter by category (from both form and direct URL parameter)
    selected_category = request.GET.get('categorySelect') or request.GET.get('category')
    if selected_category and selected_category != 'all':
        animals = animals.filter(category=selected_category)
        filter_params['categorySelect'] = selected_category
        filter_params['category'] = selected_category

    # Filter by type (from both form and direct URL parameter)
    selected_type = request.GET.get('animalTypeSelect') or request.GET.get('type')
    if selected_type and selected_type != 'all':
        animals = animals.filter(animal_type=selected_type)
        filter_params['animalTypeSelect'] = selected_type
        filter_params['type'] = selected_type

    # Filter by status - default to 'active' if not specified
    selected_status = request.GET.get('animalStatusSelect')
    
    # If no status filter is explicitly set, default to active
    if selected_status is None:
        selected_status = 'active'
        animals = animals.filter(status='active')
        filter_params['animalStatusSelect'] = 'active'
    elif selected_status != 'all':
        animals = animals.filter(status=selected_status)
        filter_params['animalStatusSelect'] = selected_status
    else:
        # User explicitly selected 'all', don't filter
        filter_params['animalStatusSelect'] = 'all'

    # Filter by age
    min_age = request.GET.get('minAge')
    max_age = request.GET.get('maxAge')
    if min_age or max_age:
        today = timezone.now().date()
        if min_age:
            max_date = today - timedelta(days=int(min_age) * 30)  # Convert months to days
            animals = animals.filter(dob__lte=max_date)
            filter_params['minAge'] = min_age
        if max_age:
            min_date = today - timedelta(days=int(max_age) * 30)  # Convert months to days
            animals = animals.filter(dob__gte=min_date)
            filter_params['maxAge'] = max_age

    # Filter by sex
    male_selected = request.GET.get('maleCheckbox') == 'Male' or request.GET.get('sex') == 'male'
    female_selected = request.GET.get('femaleCheckbox') == 'Female' or request.GET.get('sex') == 'female'
    
    if male_selected and not female_selected:
        animals = animals.filter(sex='male')
        filter_params['maleCheckbox'] = 'Male'
        filter_params['sex'] = 'male'
    elif female_selected and not male_selected:
        animals = animals.filter(sex='female')
        filter_params['femaleCheckbox'] = 'Female'
        filter_params['sex'] = 'female'
    elif male_selected and female_selected:
        animals = animals.filter(sex__in=['male', 'female'])
        filter_params['maleCheckbox'] = 'Male'
        filter_params['femaleCheckbox'] = 'Female'

    # Filter by scoring
    selected_score = request.GET.get('scoringSelect')
    if selected_score and selected_score != 'all':
        # First ensure all animals have scores
        from .models import AnimalScore
        animal_ids = animals.values_list('id', flat=True)
        
        # Create scores for animals that don't have them
        for animal_id in animal_ids:
            score, created = AnimalScore.objects.get_or_create(animal_id=animal_id)
            if created or (timezone.now() - score.last_calculated).total_seconds() > 3600:
                score.update_scores()
        
        # Apply score filter
        if selected_score == 'excellent':
            animals = animals.filter(score__total_score__gte=80)
        elif selected_score == 'good':
            animals = animals.filter(score__total_score__gte=60, score__total_score__lt=80)
        elif selected_score == 'fair':
            animals = animals.filter(score__total_score__gte=40, score__total_score__lt=60)
        elif selected_score == 'poor':
            animals = animals.filter(score__total_score__lt=40)
        
        filter_params['scoringSelect'] = selected_score

    # Apply sorting to animals queryset
    if sort_by == 'tag':
        animals = animals.order_by('tag')
    elif sort_by == 'age':
        animals = animals.order_by('-dob')  # Younger animals first (more recent DOB)
    elif sort_by == 'type':
        animals = animals.order_by('animal_type')
    elif sort_by == 'category':
        animals = animals.order_by('category')
    elif sort_by == 'scoring':
        # Ensure animals have scores before sorting
        from .models import AnimalScore
        animal_ids = animals.values_list('id', flat=True)
        for animal_id in animal_ids:
            score, created = AnimalScore.objects.get_or_create(animal_id=animal_id)
            if created or (timezone.now() - score.last_calculated).total_seconds() > 3600:
                score.update_scores()
        animals = animals.order_by('-score__total_score')  # Highest score first
    else:
        animals = animals.order_by('tag')  # Default fallback

    # Save the base filtered animals (without type filter) for counting
    base_filtered_animals = Animal.objects.filter(farm=farm).order_by('id').prefetch_related('score')
    base_filtered_animals = base_filtered_animals.annotate(latest_weight=Subquery(
        AnimalWeight.objects.filter(animal=OuterRef('pk')).order_by('-date')[:1].values('weight_kg')
    ))
    
    # Apply all filters EXCEPT type filter for counting purposes
    if selected_category and selected_category != 'all':
        base_filtered_animals = base_filtered_animals.filter(category=selected_category)
    # Apply the same status filter logic
    if selected_status == 'active' or (selected_status is None):
        base_filtered_animals = base_filtered_animals.filter(status='active')
    elif selected_status != 'all':
        base_filtered_animals = base_filtered_animals.filter(status=selected_status)
    if min_age:
        max_date = timezone.now().date() - timedelta(days=int(min_age) * 30)
        base_filtered_animals = base_filtered_animals.filter(dob__lte=max_date)
    if max_age:
        min_date = timezone.now().date() - timedelta(days=int(max_age) * 30)
        base_filtered_animals = base_filtered_animals.filter(dob__gte=min_date)
    if male_selected and not female_selected:
        base_filtered_animals = base_filtered_animals.filter(sex='male')
    elif female_selected and not male_selected:
        base_filtered_animals = base_filtered_animals.filter(sex='female')
    elif male_selected and female_selected:
        base_filtered_animals = base_filtered_animals.filter(sex__in=['male', 'female'])
    
    # Apply scoring filter to base_filtered_animals as well
    if selected_score and selected_score != 'all':
        if selected_score == 'excellent':
            base_filtered_animals = base_filtered_animals.filter(score__total_score__gte=80)
        elif selected_score == 'good':
            base_filtered_animals = base_filtered_animals.filter(score__total_score__gte=60, score__total_score__lt=80)
        elif selected_score == 'fair':
            base_filtered_animals = base_filtered_animals.filter(score__total_score__gte=40, score__total_score__lt=60)
        elif selected_score == 'poor':
            base_filtered_animals = base_filtered_animals.filter(score__total_score__lt=40)

    # Get counts by category (using base filtered animals) - only categories with animals
    counts_by_category = {}
    for category_id, _ in Animal.CATEGORY_CHOICES:
        count = base_filtered_animals.filter(category=category_id).count()
        if count > 0:  # Only include categories with animals
            counts_by_category[category_id] = count

    # Get counts by type (using base filtered animals)
    animals_by_type = {}
    counts_by_type = {}
    paginators_by_type = {}
    page_objs_by_type = {}

    for animal_type in animal_types:
        animals_of_type = base_filtered_animals.filter(animal_type=animal_type)
        animals_by_type[animal_type] = animals_of_type
        counts_by_type[animal_type] = animals_of_type.count()
        paginators_by_type[animal_type] = Paginator(animals_of_type, 14)
        page_number = request.GET.get('page')
        page_obj = paginators_by_type[animal_type].get_page(page_number)
        # Add filter parameters to page_obj for template use
        page_obj.filter_params = filter_params
        page_objs_by_type[animal_type] = page_obj

    # Add paginator for all animals (or filtered by type if selected)
    paginator_all = Paginator(animals, 10)
    page_number = request.GET.get('page')
    page_obj_all = paginator_all.get_page(page_number)
    # Add filter parameters to page_obj for template use
    page_obj_all.filter_params = filter_params
    page_objs_by_type['all'] = page_obj_all

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        animals_data = list(animals.values('id', 'name', 'dob', 'latest_weight', 'animal_type'))
        return JsonResponse({'animals': animals_data})

    return render(request, 'dairy/animal_list.html', {
        'animals': animals,
        'animals_by_type': animals_by_type,
        'counts_by_type': counts_by_type,
        'page_objs_by_type': page_objs_by_type,
        'selected_type': selected_type or 'all',
        'selected_category': selected_category or 'all',
        'selected_status': selected_status,  # Don't default to 'all', let it be 'active' or the selected value
        'selected_score': selected_score or 'all',
        'sort_by': sort_by,
        'animal_categories': animal_categories,
        'counts_by_category': counts_by_category,
        'filter_params': filter_params,  # Pass filter params to template
    })

@login_required
def animal_create(request):
    farm = get_object_or_404(Farm, admin=request.user)
    edit_mode = False
    if request.method == 'POST':
        form = AnimalForm(request.POST, request.FILES, farm=farm)
        if form.is_valid():
            animal = form.save(commit=False)
            animal.farm = farm
            animal.save()
            return redirect('dairy:animal_list')
        else:
            pass  # Form errors will be displayed in template
    else:
        form = AnimalForm(farm=farm)
    
    # Get category choices from the model
    category_choices = dict(Animal.CATEGORY_CHOICES)
    
    return render(request, 'dairy/animal_form.html', {
        'form': form, 
        'edit_mode': edit_mode,
        'category_choices': category_choices
    })




@login_required
def animal_edit(request, pk):
    animal = get_object_or_404(Animal, pk=pk, farm__admin=request.user)
    edit_mode = True
    if request.method == 'POST':
        form = AnimalForm(request.POST, request.FILES, instance=animal)
        if form.is_valid():
            try:
                # Check if the tag already exists for another animal in the same farm
                new_tag = form.cleaned_data['tag']
                if Animal.objects.filter(farm=request.user.farm, tag=new_tag).exclude(pk=animal.pk).exists():
                    form.add_error('tag', f'An animal with tag "{new_tag}" already exists in your farm.')
                else:
                    # Save the form if tag is unique
                    updated_animal = form.save()
                    messages.success(request, f'Animal {updated_animal.tag} has been successfully updated.')
                    return redirect('dairy:animal_detail', pk=animal.pk)
            except Exception as e:
                messages.error(request, f'Error updating animal: {str(e)}')
    else:
        form = AnimalForm(instance=animal)
    
    # Get category choices from the model
    category_choices = dict(Animal.CATEGORY_CHOICES)
    
    return render(request, 'dairy/animal_form.html', {
        'form': form, 
        'edit_mode': edit_mode, 
        'animal': animal,
        'category_choices': category_choices
    })


from django.http import HttpResponseRedirect, JsonResponse

import logging
logger = logging.getLogger(__name__)

@login_required
def animal_detail(request, pk):
    farm = request.user.farm
    animal = get_object_or_404(Animal, pk=pk, farm=farm)

    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')

    if sort_order == 'asc':
        weights = AnimalWeight.objects.filter(animal=animal).select_related('animal').order_by(sort_by)
    else:
        weights = AnimalWeight.objects.filter(animal=animal).select_related('animal').order_by(F(sort_by).desc(nulls_last=True))

    prev_weights = {}
    for weight in weights:
        prev_weight = AnimalWeight.objects.filter(animal=weight.animal, date__lt=weight.date).order_by('-date').first()
        prev_weights[weight.pk] = prev_weight 

    # Assume time_filter is passed as a parameter in the GET request
    time_filter = request.GET.get('time_filter', 'all')

    # Filter MilkRecord instances based on the time_filter
    if time_filter == 'all':
        # For 'all', we'll use a very old start date
        start_date = date.today() - timedelta(days=3650)  # Approximately 10 years ago
        milk_records = MilkRecord.objects.filter(animal=animal, date__gte=start_date)
    elif time_filter == 'last_day':
        one_day_ago = timezone.now() - timedelta(days=1)
        milk_records = MilkRecord.objects.filter(animal=animal, date__gte=one_day_ago)
    elif time_filter == 'last_7_days':
        seven_days_ago = timezone.now() - timedelta(days=7)
        milk_records = MilkRecord.objects.filter(animal=animal, date__gte=seven_days_ago)
    elif time_filter == 'one_month':
        one_month_ago = timezone.now() - timedelta(days=30)
        milk_records = MilkRecord.objects.filter(animal=animal, date__gte=one_month_ago)
    elif time_filter == 'four_months':
        four_months_ago = timezone.now() - timedelta(days=120)
        milk_records = MilkRecord.objects.filter(animal=animal, date__gte=four_months_ago)
    elif time_filter == 'one_year':
        one_year_ago = timezone.now() - timedelta(days=365)
        milk_records = MilkRecord.objects.filter(animal=animal, date__gte=one_year_ago)

    # Summarize milk_records to obtain total quantities
    total_first_time = milk_records.aggregate(Sum('first_time'))['first_time__sum']
    total_second_time = milk_records.aggregate(Sum('second_time'))['second_time__sum']
    total_third_time = milk_records.aggregate(Sum('third_time'))['third_time__sum']
    total_milk = milk_records.aggregate(total_milk=Sum(F('first_time')+F('second_time')+F('third_time')))['total_milk']


    # Calculate total milk for each record as the sum of the three time fields
    milk_records = milk_records.annotate(computed_total_milk=F('first_time')+F('second_time')+F('third_time'))


    # Then aggregate over these calculated total_milk values for all records
    total_milk = milk_records.aggregate(total_milk_sum=Sum('computed_total_milk'))['total_milk_sum']
    logger.debug(f'Animal father: {animal.father}')
    active_tab = request.GET.get('active_tab', 'events')
    
    # Get animal score data
    try:
        animal_score = animal.score
        # Update if score is older than 1 hour
        if (timezone.now() - animal_score.last_calculated).total_seconds() > 3600:
            animal_score.update_scores()
    except AnimalScore.DoesNotExist:
        animal_score = AnimalScore.objects.create(animal=animal)
        animal_score.update_scores()
    
    # Get milk record count for scoring context
    # Calculate days since animal became a milking type
    days_as_milking = 30  # Default to 30 days
    if animal.type_changed_date and animal.animal_type in ['milking', 'preg_milking']:
        days_since_type_change = (timezone.now() - animal.type_changed_date).days
        days_as_milking = min(30, days_since_type_change)
    
    start_date = timezone.now().date() - timedelta(days=days_as_milking)
    milk_record_count = MilkRecord.objects.filter(
        animal=animal,
        date__gte=start_date
    ).count()
    
    # Calculate expected records for context
    expected_milk_records = int(days_as_milking * 0.85) if days_as_milking >= 7 else 0
    
    # Get last milk record
    last_milk_record = MilkRecord.objects.filter(animal=animal).order_by('-date').first()
    
    # Get type changes for the events tab
    type_changes = animal.type_changes.all()

    return render(request, 'dairy/animal_detail.html', {'animal': animal, 'milk_records': milk_records, 
                                                        'active_tab': active_tab,
                                                        'total_first_time': total_first_time,'total_second_time': total_second_time,'total_third_time': total_third_time,'total_milk': total_milk,'weights': weights, 'prev_weights': prev_weights, 'sort_by': sort_by, 'sort_order': sort_order,
                                                        'animal_score': animal_score,
                                                        'milk_record_count': milk_record_count,
                                                        'last_milk_record': last_milk_record,
                                                        'days_as_milking': days_as_milking,
                                                        'expected_milk_records': expected_milk_records,
                                                        'type_changes': type_changes})


@login_required
def create_family(request, pk):
    animal = get_object_or_404(Animal, pk=pk)

    if request.method == 'POST':
        father_id = request.POST.get('father')
        mother_id = request.POST.get('mother')


        if father_id:
            father = get_object_or_404(Animal, pk=father_id)
            animal.father = father
        if mother_id:
            mother = get_object_or_404(Animal, pk=mother_id)
            animal.mother = mother

        try:
            animal.save()
            messages.success(request, "Family created successfully.")
            return redirect('dairy:animal_detail', pk=animal.pk)
        except ValidationError as e:
            messages.error(request, str(e))
            # render the form again, possibly with error messages
    
    # Fetch all animals, excluding the current one
    animals = Animal.objects.exclude(pk=animal.pk)
    
    return render(request, 'dairy/create_family.html', {'animal': animal, 'animals': animals})


@login_required
def update_family(request, pk):
    animal = get_object_or_404(Animal, pk=pk)

    if request.method == 'POST':
        father_id = request.POST.get('father')
        mother_id = request.POST.get('mother')

        if father_id:
            father = get_object_or_404(Animal, pk=father_id)
            animal.father = father
        if mother_id:
            mother = get_object_or_404(Animal, pk=mother_id)
            animal.mother = mother

        animal.save()
        messages.success(request, "Family updated successfully.")
        return redirect('animal_detail', pk=animal.pk)

    return render(request, 'dairy/update_parents.html', {'animal': animal})

@login_required
def delete_family(request, pk):
    animal = get_object_or_404(Animal, pk=pk)
    relation_id = request.POST.get('relation_id')

    if request.method == 'POST':
        if relation_id == "father" and animal.father:
            animal.father.children_father.remove(animal)
            animal.father = None
        elif relation_id == "mother" and animal.mother:
            animal.mother.children_mother.remove(animal)
            animal.mother = None
        else:
            messages.error(request, "Invalid relation specified.")
            return redirect('dairy:animal_detail', pk=animal.pk)

        animal.save()
        messages.success(request, "Family relation deleted successfully.")
        return redirect('dairy:animal_detail', pk=animal.pk)

    return render(request, 'confirm_delete_family.html', {'animal': animal})



@login_required
def delete_animal(request, pk):
    animal = get_object_or_404(Animal, pk=pk, farm=request.user.farm)
    
    if request.method == "POST":
        # Delete the animal
        tag = animal.tag
        animal.delete()
        messages.success(request, f'Animal {tag} was successfully deleted.')
        return redirect('dairy:animal_list')
    
    # Check for related records
    related_data = {
        'children': animal.all_children.count(),
        'milk_records': MilkRecord.objects.filter(animal=animal).count(),
        'weight_records': animal.weights.count(),
        'events': animal.events.count(),
        'breeding_events': getattr(animal, 'breeding_events', []).count() if hasattr(animal, 'breeding_events') else 0,
        'breeding_cycles': getattr(animal, 'breeding_cycles', []).count() if hasattr(animal, 'breeding_cycles') else 0
    }
    
    # Get parent relationships
    parent_info = {}
    if animal.mother:
        parent_info['mother'] = animal.mother.tag
    if animal.father:
        parent_info['father'] = animal.father.tag
    
    return render(request, 'dairy/animal_delete.html', {
        'animal': animal,
        'related_data': related_data,
        'parent_info': parent_info
    })


def milk_create_for_animal(request, animal_pk):
    animal = get_object_or_404(Animal, pk=animal_pk)

    if request.method == 'POST':
        form = MilkRecordForm(request.POST)
        if form.is_valid():
            milk_record = form.save(commit=False)
            milk_record.animal = animal
            milk_record.save()
            return redirect('dairy:milk-list')
    else:
        form = MilkRecordForm()

    return render(request, 'dairy/milk_records/create_update.html', {'form': form, 'edit_mode': False})

@login_required
def add_milk_record(request, animal_id):
    animal = get_object_or_404(Animal, pk=animal_id)
    if request.method == 'POST':
        form = MilkRecordForm(request.POST)
        if form.is_valid():
            milk_record = form.save(commit=False)  # Modify this line
            milk_record.animal = animal  # Add this line
            milk_record.save()  # Add this line
            return redirect('dairy/animal_detail.html', pk=animal_id)
    else:
        form = MilkRecordForm()
    return render(request, 'dairy/milk_records/add_milk_record.html', {'form': form, 'animal_id': animal_id, 'animal':animal})

@login_required
def get_milk_record(request, animal_id, date):
    animal = get_object_or_404(Animal, pk=animal_id)
    record = MilkRecord.objects.filter(animal=animal, date=date).first()
    
    if record:
        data = {
            'first_time': float(record.first_time),
            'second_time': float(record.second_time),
            'third_time': float(record.third_time)
        }
    else:
        data = {
            'first_time': '',
            'second_time': '',
            'third_time': ''
        }
    
    return JsonResponse(data)

@login_required
def edit_milk_record(request, milk_record_id):
    milk_record = get_object_or_404(MilkRecord, pk=milk_record_id)
    if request.method == 'POST':
        form = MilkRecordForm(request.POST, instance=milk_record)
        if form.is_valid():
            form.save(animal=milk_record.animal)  # Modify this line
            return redirect('dairy/animal_detail.html', pk=milk_record.animal.id)
    else:
        form = MilkRecordForm(instance=milk_record)
    return render(request, 'dairy/milk_records/edit_milk_record.html', {'form': form, 'milk_record':milk_record})

@login_required
def delete_milk_record(request, milk_record_id):
    milk_record = get_object_or_404(MilkRecord, pk=milk_record_id)
    animal_id = milk_record.animal.id
    milk_record.delete()
    return redirect('dairy/animal_detail.html', pk=animal_id)

from datetime import timedelta
from django.utils import timezone
from django.db import models
from django.core.paginator import Paginator


from datetime import datetime, timedelta
from calendar import monthrange
from django.db.models import F
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.shortcuts import render, redirect
from django.contrib import messages
from django.utils import timezone
from django.db import models

def calculate_totals(milk_records):
    total_first_time = milk_records.aggregate(total=models.Sum('first_time'))['total'] or 0
    total_second_time = milk_records.aggregate(total=models.Sum('second_time'))['total'] or 0
    total_third_time = milk_records.aggregate(total=models.Sum('third_time'))['total'] or 0
    total_milk = total_first_time + total_second_time + total_third_time

    return total_first_time, total_second_time, total_third_time, total_milk
def get_date_range(time_filter):
    """
    Returns a tuple of start and end dates for a specific time filter.
    """
    # Get current time in UTC
    current_utc = timezone.now()
    # Convert to Asia/Karachi timezone (UTC+5)
    pk_tz = timezone.get_fixed_timezone(5 * 60)  # 5 hours ahead of UTC
    end_date = timezone.localtime(current_utc, pk_tz).date()
    
    if time_filter == 'all':
        # For 'all', we'll use a very old start date
        start_date = end_date - timedelta(days=3650)  # Approximately 10 years ago
    elif time_filter == 'last_7_days':
        start_date = end_date - timedelta(days=7)
    elif time_filter in ['last_day', 'today']:
        start_date = end_date
    elif time_filter == 'yesterday':
        end_date = end_date - timedelta(days=1)
        start_date = end_date
    elif time_filter == 'four_months':
        start_date = end_date - timedelta(days=120)
    elif time_filter == 'one_year':
        start_date = end_date - timedelta(days=365)
    elif time_filter == 'this_month':
        start_date = end_date.replace(day=1)
    elif time_filter == 'last_month':
        end_date = end_date.replace(day=1) - timedelta(days=1)
        start_date = end_date.replace(day=1)
    else:
        raise ValueError('Invalid time filter')

    return start_date, end_date

@login_required
def animal_milk_list(request):
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    time_filter = request.GET.get('time_filter', 'all')  # Default to 'all' instead of 'today'

    sort_by_mapping = {
        'tag': 'animal__tag',
        'date': 'date',
        'first_time': 'first_time',
        'second_time': 'second_time',
        'third_time': 'third_time',
        'total_milk': '',
    }
    sort_by_field = sort_by_mapping.get(sort_by, 'date')

    farm = request.user.farm
    milk_records = MilkRecord.objects.filter(animal__farm=farm).select_related('animal')

    # Apply time filter
    start_date, end_date = get_date_range(time_filter)
    milk_records = milk_records.filter(date__range=[start_date, end_date])

    # Apply sorting
    if sort_order == 'asc':
        milk_records = milk_records.order_by(sort_by_field)
    else:
        milk_records = milk_records.order_by(F(sort_by_field).desc(nulls_last=True))

    # Get the date range for the current and previous periods
    if time_filter in ['today', 'yesterday']:
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = prev_end_date

    elif time_filter in ['this_month', 'last_month']:
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = prev_end_date.replace(day=1)
    else:
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = start_date - (end_date - start_date + timedelta(days=1))

    # Apply the time filter to the querysets
    prev_milk_records = MilkRecord.objects.filter(animal__farm=farm, date__range=[prev_start_date, prev_end_date])

    # Calculate totals
    total_first_time, total_second_time, total_third_time, total_milk = calculate_totals(milk_records)
    prev_total_first_time, prev_total_second_time, prev_total_third_time, prev_total_milk = calculate_totals(prev_milk_records)

    if sort_by == 'total_milk':
        milk_records = sorted(milk_records, key=lambda x: x.total_milk, reverse=sort_order == 'desc')

    paginator = Paginator(milk_records, 50)
    page_number = request.GET.get('page', 1)
    page = paginator.get_page(page_number)

    # Create a dictionary to store milk records with the combination of animal's tag and date as the key
    milk_records_dict = {(record.animal.tag, record.date): record for record in milk_records}

    # Initialize the difference attributes for each milk_record
    for milk_record in page:
        milk_record.first_time_diff = 0
        milk_record.second_time_diff = 0
        milk_record.third_time_diff = 0
        milk_record.total_milk_diff = 0

        # Calculate differences based on the previous day's record
        previous_date = milk_record.date - timedelta(days=1)
        prev_record_key = (milk_record.animal.tag, previous_date)
        prev_record = milk_records_dict.get(prev_record_key)
        if prev_record:
            milk_record.first_time_diff = (milk_record.first_time or 0) - (prev_record.first_time or 0)
            milk_record.second_time_diff = (milk_record.second_time or 0) - (prev_record.second_time or 0)
            milk_record.third_time_diff = (milk_record.third_time or 0) - (prev_record.third_time or 0)
            milk_record.total_milk_diff = (milk_record.total_milk or 0) - (prev_record.total_milk or 0)


    return render(request, 'dairy/animal_milk_list.html', {
        'page': page,
        'milk_records': milk_records,
        'sort_by': sort_by,
        'sort_order': sort_order,
        'time_filter': time_filter,
        'total_first_time': total_first_time,
        'total_second_time': total_second_time,
        'total_third_time': total_third_time,
        'total_milk': total_milk,
        'prev_total_first_time': prev_total_first_time,
        'prev_total_second_time': prev_total_second_time,
        'prev_total_third_time': prev_total_third_time,
        'prev_total_milk': prev_total_milk,
    })


@login_required
def total_milk_list(request):
    # Sorting, filtering and pagination logic remains similar
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    time_filter = request.GET.get('time_filter', 'today')  # Default to last 7 days

    farm = request.user.farm

    # Get the date range for the current and previous periods
    start_date, end_date = get_date_range(time_filter)

    if time_filter in ['today', 'yesterday']:
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = prev_end_date
    elif time_filter in ['this_month', 'last_month']:
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = prev_end_date.replace(day=1)
    else:
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = start_date - (end_date - start_date + timedelta(days=1))

    # Aggregate records by date
    aggregated_records = (
        MilkRecord.objects.filter(animal__farm=farm, date__range=[start_date, end_date])
        .values('date')
        .annotate(
            total_first_time=models.Sum('first_time'),
            total_second_time=models.Sum('second_time'),
            total_third_time=models.Sum('third_time'),
        )
        .annotate(
            total_milk=models.F('total_first_time') + models.F('total_second_time') + models.F('total_third_time')
        )
        .order_by(sort_by)
    )

    prev_aggregated_records = (
        MilkRecord.objects.filter(animal__farm=farm, date__range=[prev_start_date, prev_end_date])
        .values('date')
        .annotate(
            total_first_time=models.Sum('first_time'),
            total_second_time=models.Sum('second_time'),
            total_third_time=models.Sum('third_time'),
        )
        .annotate(
            total_milk=models.F('total_first_time') + models.F('total_second_time') + models.F('total_third_time')
        )
    )

    # Create a dictionary for easy lookup
    prev_totals_dict = {record['date']: record for record in prev_aggregated_records}
    # Get the date range for the current and previous periods
    milk_records = MilkRecord.objects.filter(animal__farm=farm)

    # Filter the milk_records based on the date range
    milk_records = milk_records.filter(date__range=[start_date, end_date])
    filtered_milk_records = milk_records.filter(date__range=[start_date, end_date])

    # Calculate totals
    total_first_time, total_second_time, total_third_time, total_milk = calculate_totals(filtered_milk_records)



    # Calculate the difference
   
    aggregated_list = list(aggregated_records)
    aggregated_list.sort(key=lambda x: x['date'])

    prev_record = None
    for record in aggregated_list:
        if prev_record:
            record['first_time_diff'] = (record['total_first_time'] or 0) - (prev_record['total_first_time'] or 0)
            record['second_time_diff'] = (record['total_second_time'] or 0) - (prev_record['total_second_time'] or 0)
            record['third_time_diff'] = (record['total_third_time'] or 0) - (prev_record['total_third_time'] or 0)
            record['total_diff'] = (record['total_milk'] or 0) - (prev_record['total_milk'] or 0)

           

        prev_record = record

    paginator = Paginator(aggregated_records, 50)
    page_number = request.GET.get('page', 1)
    page = paginator.get_page(page_number)
   
    return render(request, 'dairy/total_milk_list.html', {
        'page': page,
        'sort_by': sort_by,
        'sort_order': sort_order,
        'time_filter': time_filter,
        'total_first_time': total_first_time,
        'total_second_time': total_second_time,
        'total_third_time': total_third_time,
        'total_milk': total_milk,
    })


@login_required
def animal_milk_delete(request, pk):
    milk_record = get_object_or_404(MilkRecord, pk=pk)
    milk_record.delete()
    return redirect('dairy:animal_milk_list')

@login_required
def animal_milk_new(request):
    edit_mode = False
    milk_record = None
    farm = request.user.farm

    if request.method == "POST":
        form = MilkRecordForm(request.POST, farm=farm)
        if form.is_valid():
            animal = form.cleaned_data['animal']
            date = form.cleaned_data['date']
            milk_record, created = MilkRecord.objects.get_or_create(animal=animal, date=date)

            if not created:
                edit_mode = True

            form = MilkRecordForm(request.POST, instance=milk_record, farm=farm)
            if form.is_valid():
                form.save()
                return redirect('dairy:animal_milk_list')
    else:
        animal_id = request.GET.get('animal')
        date = request.GET.get('date')

        if animal_id and date:
            animal = get_object_or_404(Animal, pk=animal_id)
            milk_record, _ = MilkRecord.objects.get_or_create(animal=animal, date=date)
            edit_mode = True

        form = MilkRecordForm(instance=milk_record, farm=farm)

    return render(request, 'dairy/animal_milk_edit.html', {'form': form, 'edit_mode': edit_mode, 'milk_record': milk_record})


from datetime import timedelta

@login_required
def animal_weight_list(request):
    farm = request.user.farm
    # Default sort is by date in descending order
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    time_filter = request.GET.get('time_filter', 'all')
    search_query = request.GET.get('search', '')
    page = request.GET.get('page', 1)

    # Base queryset
    base_queryset = AnimalWeight.objects.filter(animal__farm=farm).select_related('animal')
    
    # Apply time filter
    if time_filter == '7days':
        start_date = timezone.now().date() - timedelta(days=7)
        base_queryset = base_queryset.filter(date__gte=start_date)
    elif time_filter == '30days':
        start_date = timezone.now().date() - timedelta(days=30)
        base_queryset = base_queryset.filter(date__gte=start_date)
    elif time_filter == '90days':
        start_date = timezone.now().date() - timedelta(days=90)
        base_queryset = base_queryset.filter(date__gte=start_date)
    
    # Apply search filter
    if search_query:
        base_queryset = base_queryset.filter(animal__tag__icontains=search_query)

    # We'll use a different approach to get previous weights
    # First, get the sorted weights without Window function
    annotated_queryset = base_queryset
    
    # Map sort_by parameter to the correct field
    sort_by_mapping = {
        'tag': 'animal__tag',
        'date': 'date',
        'weight_kg': 'weight_kg',
        'weight_change_kg': '',  # This will be handled separately
        'weight_change_percent': '',  # This will be handled separately
    }
    sort_by_field = sort_by_mapping.get(sort_by, 'date')
    
    # Apply sorting
    if sort_order == 'asc':
        weights_queryset = annotated_queryset.order_by(sort_by_field)
    else:
        weights_queryset = annotated_queryset.order_by(F(sort_by_field).desc(nulls_last=True))
    
    # Dashboard statistics based on filtered data
    # Get unique animals in the filtered queryset
    total_animals = base_queryset.values('animal').distinct().count()
    avg_weight = base_queryset.aggregate(avg=Avg('weight_kg'))['avg'] or 0
    
    # Calculate average daily gain based on filtered data
    avg_daily_gain = 0
    daily_gains = []
    
    # Get animals with weights in the filtered queryset
    animal_ids = base_queryset.values_list('animal_id', flat=True).distinct()
    
    # For each animal in the filtered set, calculate their daily gain
    for animal_id in animal_ids:
        # Get all weights for this animal (even outside filter to calculate proper gain)
        animal_weights = AnimalWeight.objects.filter(animal_id=animal_id).order_by('date')
        
        # If time filter is applied, use only weights within that period for the calculation
        if time_filter != 'all':
            if time_filter == '7days':
                start_date = timezone.now().date() - timedelta(days=7)
            elif time_filter == '30days':
                start_date = timezone.now().date() - timedelta(days=30)
            elif time_filter == '90days':
                start_date = timezone.now().date() - timedelta(days=90)
            
            # Filter weights by date
            animal_weights = animal_weights.filter(date__gte=start_date)
        
        if len(animal_weights) >= 2:
            first_weight = animal_weights.first()
            last_weight = animal_weights.last()
            days_passed = (last_weight.date - first_weight.date).days
            if days_passed > 0:
                gain_per_day = (last_weight.weight_kg - first_weight.weight_kg) / days_passed
                daily_gains.append(gain_per_day)
    
    if daily_gains:
        avg_daily_gain = sum(daily_gains) / len(daily_gains)
    
    # Recent records count based on the current filter
    if time_filter == 'all':
        # If no time filter, count records from the last 7 days
        recent_date = timezone.now().date() - timedelta(days=7)
        recent_records = AnimalWeight.objects.filter(animal__farm=farm, date__gte=recent_date).count()
    else:
        # If time filter is applied, use the count from the filtered queryset
        recent_records = base_queryset.count()
    
    # Process weights for display
    paginator = Paginator(weights_queryset, 12)  # Show 12 weights per page
    weights_paginated = paginator.get_page(page) # Use this for the loop
    try:
        weights = paginator.page(page)
    except PageNotAnInteger:
        weights = paginator.page(1)
    except EmptyPage:
        weights = paginator.page(paginator.num_pages)
    
    # Calculate previous weight and weight gain per day for each weight record
    for weight in weights_paginated:
        # Find the previous weight record for this animal
        previous_record = AnimalWeight.objects.filter(
            animal_id=weight.animal_id,
            date__lt=weight.date
        ).order_by('-date').first()
        
        # Set previous weight value and calculate daily gain
        if previous_record:
            weight.previous_weight_value = previous_record.weight_kg
            days_difference = (weight.date - previous_record.date).days
            if days_difference > 0:
                weight_diff = weight.weight_kg - previous_record.weight_kg
                weight.weight_gain_per_day = round(weight_diff / decimal.Decimal(days_difference), 2)
            else:
                weight.weight_gain_per_day = None
        else:
            weight.previous_weight_value = None
            weight.weight_gain_per_day = None

    context = {
        'weights': weights_paginated, # Pass paginated weights to template
        'sort_by': sort_by,
        'sort_order': sort_order,
        'time_filter': time_filter,
        'search_query': search_query,
        # Dashboard stats
        'total_animals': total_animals,
        'avg_weight': avg_weight,
        'avg_daily_gain': avg_daily_gain,
        'recent_records': recent_records,
    }
    
    return render(request, 'dairy/animal_weight_list.html', context)



@login_required
def animal_milk_edit(request, pk):
    milk_record = get_object_or_404(MilkRecord, pk=pk)
    edit_mode = True
    if request.method == "POST":
        form = MilkRecordForm(request.POST, instance=milk_record)
        if form.is_valid():
            milk_record = form.save(commit=False)
            milk_record.save()
            return redirect('dairy:animal_milk_list')
    else:
        form = MilkRecordForm(instance=milk_record, initial={'animal': milk_record.animal.pk})
    return render(request, 'dairy/animal_milk_edit.html', {'form': form, 'edit_mode': edit_mode, 'milk_record': milk_record})





@login_required
def animal_weight_detail(request, pk):
    animal = get_object_or_404(Animal, pk=pk)
    weights = AnimalWeight.objects.filter(animal=animal)
    return render(request, 'dairy/animal_weight_detail.html', {'weights': weights, 'animal': animal})


@login_required
def animal_detail_weight_new(request, pk):
    """Add a new weight record for a specific animal from the animal detail page"""
    edit_mode = False
    animal = get_object_or_404(Animal, pk=pk, farm=request.user.farm)
    
    if request.method == "POST":
        form = AnimalWeightForm(request.POST, user=request.user)
        if form.is_valid():
            weight = form.save(commit=False)
            weight.animal = animal  # Set the animal attribute of the weight instance
            weight.save()
            messages.success(request, f"Weight record for {animal.tag} added successfully.")
            return redirect('dairy:animal_detail', pk=animal.pk)
    else:
        # Set default date to today
        form = AnimalWeightForm(
            initial={
                'animal': animal,
                'date': timezone.now().date()
            }, 
            user=request.user
        )
        # Disable the animal field since we're adding from animal detail
        form.fields['animal'].widget.attrs['disabled'] = True
        form.fields['animal'].widget.attrs['readonly'] = True

    return render(
        request, 
        'dairy/animal_weight_edit.html', 
        {
            'form': form, 
            'edit_mode': edit_mode,
            'animal': animal
        }
    )

@login_required
def animal_weight_new(request):
    """Add a new weight record from the weight dashboard"""
    edit_mode = False
    
    if request.method == "POST":
        form = AnimalWeightForm(request.POST, user=request.user)
        if form.is_valid():
            weight = form.save(commit=False)
            # Ensure the animal belongs to the user's farm
            if weight.animal.farm != request.user.farm:
                messages.error(request, "You can only add weight records for animals in your farm.")
                return redirect('dairy:animal_weight_list')
            
            weight.save()
            messages.success(request, f"Weight record for {weight.animal.tag} added successfully.")
            return redirect('dairy:animal_weight_list')
    else:
        # Set default date to today
        form = AnimalWeightForm(
            initial={'date': timezone.now().date()}, 
            user=request.user
        )
    
    return render(
        request, 
        'dairy/animal_weight_new.html', 
        {
            'form': form, 
            'edit_mode': edit_mode
        }
    )

@login_required
def animal_weight_edit(request, pk):
    """Edit an existing weight record"""
    # Get the weight record and ensure it belongs to the user's farm
    weight = get_object_or_404(AnimalWeight, pk=pk, animal__farm=request.user.farm)
    edit_mode = True
    
    if request.method == "POST":
        form = AnimalWeightForm(request.POST, instance=weight, user=request.user)
        if form.is_valid():
            # Ensure the animal belongs to the user's farm
            if form.cleaned_data['animal'].farm != request.user.farm:
                messages.error(request, "You can only add weight records for animals in your farm.")
                return redirect('dairy:animal_weight_list')
                
            weight = form.save()
            messages.success(request, f"Weight record for {weight.animal.tag} updated successfully.")
            return redirect('dairy:animal_weight_list')
    else:
        form = AnimalWeightForm(instance=weight, user=request.user)
    
    return render(
        request, 
        'dairy/animal_weight_edit.html', 
        {
            'form': form, 
            'edit_mode': edit_mode, 
            'animal_weight_record': weight
        }
    )


@login_required
def animal_weight_delete(request, pk):
    # Get the weight record and ensure it belongs to the user's farm
    weight = get_object_or_404(AnimalWeight, pk=pk, animal__farm=request.user.farm)
    animal_tag = weight.animal.tag
    weight_value = weight.weight_kg
    weight_date = weight.date
    
    # Delete the weight record
    weight.delete()
    
    # Add success message
    messages.success(request, f"Weight record ({weight_value} kg) for {animal_tag} on {weight_date} deleted successfully.")
    
    return redirect('dairy:animal_weight_list')




@login_required
def customer_new(request, pk=None):
    edit_mode = False
    customer = None
    if pk:
        customer = get_object_or_404(Customer, pk=pk)
        edit_mode = True

    if request.method == "POST":
        form = CustomerForm(request.POST, instance=customer)
        if form.is_valid():
            customer = form.save(commit=False)  # Don't save to the database yet
            customer.farm = request.user.farm  # Associate customer with the logged-in user's farm
            customer.save()  # Now save to the database
            return redirect('dairy:customer_list')
    else:
        form = CustomerForm(instance=customer)

    return render(request, 'dairy/customer/customer_edit.html', {'form': form, 'edit_mode': edit_mode, 'customer': customer})

@login_required
def customer_list(request):
    customers = Customer.objects.filter(farm=request.user.farm).annotate(
        total_milk=Sum(
            Coalesce(F('milksale__first_sale'), Value(0)) + 
            Coalesce(F('milksale__second_sale'), Value(0)) + 
            Coalesce(F('milksale__third_sale'), Value(0)),
            output_field=DecimalField()
        ),
        total_amount=Sum('milksale__total_price')
    )
    return render(request, 'dairy/customer/customer_list.html', {'customers': customers})

from datetime import timedelta, datetime
from dateutil.relativedelta import relativedelta
from django.db.models import Sum, F, Value, DecimalField
from django.db.models.functions import Coalesce

@login_required
def customer_detail(request, pk):
    customer = get_object_or_404(Customer, pk=pk)
    
    # Get filter parameters
    time_filter = request.GET.get('time_filter', 'this_month')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Initialize date range based on filter
    today = datetime.now().date()
    
    # Handle custom range dates first
    if time_filter == "custom_range" and start_date and end_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except (ValueError, TypeError):
            # If date parsing fails, default to this month
            start_date = today.replace(day=1)
            end_date = (start_date + relativedelta(months=1, days=-1))
            time_filter = "this_month"
    else:
        # Handle other filter options
        if time_filter == "this_month":
            start_date = today.replace(day=1)
            end_date = (start_date + relativedelta(months=1, days=-1))
        elif time_filter == "last_month":
            start_date = (today.replace(day=1) - relativedelta(months=1))
            end_date = (today.replace(day=1) - timedelta(days=1))
        elif time_filter == "four_months":
            start_date = (today - relativedelta(months=4))
            end_date = today
        elif time_filter == "this_year":
            start_date = today.replace(month=1, day=1)
            end_date = today
        else:
            # Default to this month if no valid filter
            start_date = today.replace(day=1)
            end_date = (start_date + relativedelta(months=1, days=-1))
            time_filter = "this_month"

    # Filter milk sales and payments based on date range
    milk_sales = MilkSale.objects.filter(
        customer=customer,
        date__range=[start_date, end_date]
    ).order_by('-date')

    milk_payments = MilkPayment.objects.filter(
        customer=customer,
        date__range=[start_date, end_date]
    ).order_by('-date')

    # Calculate totals for filtered data
    totals = milk_sales.aggregate(
        total_first=Coalesce(Sum('first_sale'), Value(0), output_field=DecimalField()),
        total_second=Coalesce(Sum('second_sale'), Value(0), output_field=DecimalField()),
        total_third=Coalesce(Sum('third_sale'), Value(0), output_field=DecimalField())
    )
    
    # Calculate total liters
    totals['total_liters'] = totals['total_first'] + totals['total_second'] + totals['total_third']

    # Get the latest price per liter or use a default price
    latest_price = milk_sales.exclude(price_per_liter__isnull=True).order_by('-date').values_list('price_per_liter', flat=True).first() or 0

    # Calculate filtered totals for status cards
    filtered_total_milk_sale = totals['total_liters'] * latest_price
    filtered_total_paid = milk_payments.aggregate(
        total=Coalesce(Sum('received_payment'), Value(0), output_field=DecimalField())
    )['total']
    filtered_remaining_amount = filtered_total_milk_sale - filtered_total_paid

    # Calculate all-time totals
    all_time_milk_sales = MilkSale.objects.filter(customer=customer)
    all_time_totals = all_time_milk_sales.aggregate(
        total_first=Coalesce(Sum('first_sale'), Value(0), output_field=DecimalField()),
        total_second=Coalesce(Sum('second_sale'), Value(0), output_field=DecimalField()),
        total_third=Coalesce(Sum('third_sale'), Value(0), output_field=DecimalField())
    )
    
    all_time_total_liters = all_time_totals['total_first'] + all_time_totals['total_second'] + all_time_totals['total_third']
    total_milk_sale = all_time_total_liters * latest_price
    
    total_paid = MilkPayment.objects.filter(customer=customer).aggregate(
        total=Coalesce(Sum('received_payment'), Value(0), output_field=DecimalField())
    )['total']
    remaining_amount = total_milk_sale - total_paid


    context = {
        'customer': customer,
        'milk_sales': milk_sales,
        'milk_payments': milk_payments,
        'time_filter': time_filter,
        'start_date': start_date,
        'end_date': end_date,
        'totals': totals,
        'filtered_total_milk_sale': filtered_total_milk_sale,
        'filtered_total_paid': filtered_total_paid,
        'filtered_remaining_amount': filtered_remaining_amount,
        'total_milk_sale': total_milk_sale,
        'total_paid': total_paid,
        'remaining_amount': remaining_amount,
        'price_per_liter': latest_price,
    }

    return render(request, 'dairy/customer/customer_detail.html', context)



@login_required
def customer_delete(request, pk):
    customer = get_object_or_404(Customer, pk=pk)
    customer.delete()
    return redirect('dairy:customer_list')  # 'dairy' should be replaced with your actual app's name

@login_required
def milk_sale_create(request):
    # Ensure the user has an associated farm
    if not hasattr(request.user, 'farm'):
        messages.error(request, 'User does not have an associated farm.')
        return redirect('your_redirect_location')  # Change 'your_redirect_location' to where you want to redirect in this case

    if request.method == "POST":
        form = MilkSaleForm(request.POST, user=request.user)  # Adjusting the form to consider the user's farm
        if form.is_valid():
            milk_sale = form.save(commit=False)
            milk_sale.farm = request.user.farm
            milk_sale.save()
            return redirect('dairy:milk_sale_list')
    else:
        form = MilkSaleForm(user=request.user)  # Adjusting the form to consider the user's farm
    context = {
        'form': form
    }
    return render(request, 'dairy/milk_records/milk_sale_form.html', context)


@login_required
def milk_sale_list(request):
    # Get filter and sort parameters
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    time_filter = request.GET.get('time_filter', 'all')

    # Get sales for the user's farm
    sales = MilkSale.objects.filter(farm=request.user.farm)

    # Apply time filter
    today = timezone.now().date()
    
    if time_filter == 'custom':
        try:
            start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
            end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
            sales = sales.filter(date__range=[start_date, end_date])
        except (ValueError, TypeError):
            # If dates are invalid or not provided, default to showing all records
            time_filter = 'all'
            start_date = None
            end_date = None
    else:
        start_date = None
        end_date = None
        if time_filter == 'last_day':
            sales = sales.filter(date=today)
        elif time_filter == 'yesterday':
            sales = sales.filter(date=today - timedelta(days=1))
        elif time_filter == 'last_7_days':
            sales = sales.filter(date__gte=today - timedelta(days=7))
        elif time_filter == 'this_month':
            sales = sales.filter(date__year=today.year, date__month=today.month)
        elif time_filter == 'last_month':
            last_month = today.replace(day=1) - timedelta(days=1)
            sales = sales.filter(date__year=last_month.year, date__month=last_month.month)
        elif time_filter == 'four_months':
            sales = sales.filter(date__gte=today - timedelta(days=120))
        elif time_filter == 'one_year':
            sales = sales.filter(date__gte=today - timedelta(days=365))
        # 'all' filter doesn't need any date filtering

    # Add calculated fields
    sales = sales.annotate(
        total_liters=F('first_sale') + F('second_sale') + F('third_sale')
    )

    # Apply sorting with proper field mapping
    sort_mapping = {
        'date': 'date',
        'customer': 'customer__name',
        'first_sale': 'first_sale',
        'second_sale': 'second_sale',
        'third_sale': 'third_sale',
        'total_liters': 'total_liters',  # Now using the annotated field
        'price_per_liter': 'price_per_liter',
        'total_price': 'total_price'
    }

    # Get the correct sort field from mapping, default to 'date' if not found
    sort_field = sort_mapping.get(sort_by, 'date')
    
    # Apply sort order
    if sort_order == 'desc':
        sort_field = f'-{sort_field}'
    
    sales = sales.order_by(sort_field)

    # Calculate totals for the filtered data using DecimalField for consistent types
    totals = sales.aggregate(
        total_first=Coalesce(Sum('first_sale', output_field=DecimalField()), Value(0, output_field=DecimalField())),
        total_second=Coalesce(Sum('second_sale', output_field=DecimalField()), Value(0, output_field=DecimalField())),
        total_third=Coalesce(Sum('third_sale', output_field=DecimalField()), Value(0, output_field=DecimalField())),
        total_price=Coalesce(Sum('total_price', output_field=DecimalField()), Value(0, output_field=DecimalField()))
    )
    
    # Calculate total liters
    totals['total_liters'] = totals['total_first'] + totals['total_second'] + totals['total_third']

    # Paginate the results
    paginator = Paginator(sales, 50)  # Show 50 sales per page
    page_number = request.GET.get('page')
    page = paginator.get_page(page_number)

    context = {
        'sales': page,  # Use paginated results
        'page': page,  # Add page object for pagination template
        'sort_by': sort_by,
        'sort_order': sort_order,
        'time_filter': time_filter,
        'totals': totals,
        'start_date': start_date,
        'end_date': end_date
    }
    return render(request, 'dairy/milk_records/milk_sale_list.html', context)

@login_required
def milk_sale_edit(request, pk):
    milk_sale = get_object_or_404(MilkSale, id=pk)

    # Ensure the logged-in user has permission to edit this MilkSale (assuming by farm ownership)
    if milk_sale.farm != request.user.farm:
        return HttpResponseForbidden("You don't have permission to edit this MilkSale.")

    if request.method == "POST":
        form = MilkSaleForm(request.POST, instance=milk_sale, user=request.user)
        if form.is_valid():
            form.save()
            return redirect('dairy:milk_sale_list')
    else:
        form = MilkSaleForm(instance=milk_sale, user=request.user)

    context = {
        'form': form,
        'milk_sale': milk_sale
    }
    return render(request, 'dairy/milk_records/milk_sale_form.html', context)

@login_required
def milk_sale_delete(request, sale_id):
    milk_sale = get_object_or_404(MilkSale, id=sale_id)
    milk_sale.delete()
    return redirect('dairy:milk_sale_list')

@login_required
def add_milk_payment(request):
    
    # Ensure the user has an associated farm
    if not hasattr(request.user, 'farm'):
        messages.error(request, 'User does not have an associated farm.')
        return redirect('your_redirect_location')

    if request.method == "POST":
        form = MilkPaymentForm(request.POST, user=request.user)
        if form.is_valid():
            milk_payment = form.save(commit=False)
            milk_payment.farm = request.user.farm
            milk_payment.save()

            # Create or retrieve the 'Milk Sale' income category for the farm
            milk_sale_category, created = IncomeCategory.objects.get_or_create(farm=request.user.farm, name='Milk Sale')

            # As we're adding a new milk payment, there shouldn't be an associated income entry already. 
            # Hence, we create a new income instance directly.
            income = Income(user=request.user, farm=request.user.farm)
            income.date = milk_payment.date
            income.description = f'Milk payment from {milk_payment.customer.name}'
            income.amount = milk_payment.received_payment
            income.category = milk_sale_category
            income.milk_payment = milk_payment
            income.save()

            messages.success(request, 'Milk payment added successfully!')
            return redirect('dairy:customer_detail', pk=milk_payment.customer.pk)
    else:
        form = MilkPaymentForm(user=request.user)

    context = {
        'form': form
    }
    return render(request, 'dairy/customer/add_milk_payment.html', context)


@login_required
def update_milk_payment(request, milk_payment_id):
    farm = request.user.farm

    try:
        milk_payment = MilkPayment.objects.get(pk=milk_payment_id)
        edit_mode = True
    except MilkPayment.DoesNotExist:
        return HttpResponseNotFound("MilkPayment not found")

    if request.method == 'POST':
        form = MilkPaymentForm(request.POST, instance=milk_payment, user=request.user)
        if form.is_valid():
            milk_payment = form.save(commit=False)
            milk_payment.farm = request.user.farm
            milk_payment.save()

            # Code for auto-creating Income entry (same as in add_milk_payment view)
            milk_sale_category, created = IncomeCategory.objects.get_or_create(farm=request.user.farm, name='Milk Sale')
            try:
                income = Income.objects.get(milk_payment=milk_payment)
            except Income.DoesNotExist:
                income = Income(user=request.user, farm=request.user.farm)

            income.date = milk_payment.date
            income.description = f'Milk payment from {milk_payment.customer.name}'
            income.amount = milk_payment.received_payment
            income.category = milk_sale_category
            income.milk_payment = milk_payment
            income.save()

            messages.success(request, 'Milk Payment updated successfully!')
            return redirect('dairy:customer_detail', pk=milk_payment.customer.pk)
    else:
        form = MilkPaymentForm(instance=milk_payment, user=request.user)

    context = {
        'form': form,
        'edit_mode': edit_mode,
        'milk_payment': milk_payment
    }
    return render(request, 'dairy/customer/add_milk_payment.html', context)

@login_required
def delete_milk_payment(request, pk):
    milk_payment = MilkPayment.objects.get(pk=pk)
    milk_payment_pk = milk_payment.pk

    incomes = Income.objects.filter(milk_payment=milk_payment)
    
    if incomes.exists():
        for income in incomes:
            income_pk = income.pk
            income.delete()
    else:
        pass

    milk_payment.delete()

    return redirect('dairy:customer_detail', pk=milk_payment.customer.pk)

@login_required
def add_event(request, animal_id):
    if request.method == 'POST':
        animal = get_object_or_404(Animal, id=animal_id)
        title = request.POST.get('title')
        description = request.POST.get('description')
        date = request.POST.get('date')
        
        AnimalEvent.objects.create(
            animal=animal,
            title=title,
            description=description,
            date=date
        )
        messages.success(request, 'Event added successfully')
        return redirect('dairy:animal_detail', pk=animal_id)
    return redirect('dairy:animal_detail', pk=animal_id)

def edit_event(request, event_id):
    event = get_object_or_404(AnimalEvent, id=event_id)
    if request.method == 'POST':
        event.title = request.POST.get('title')
        event.description = request.POST.get('description')
        event.date = request.POST.get('date')
        event.save()
        messages.success(request, 'Event updated successfully')
        return redirect('dairy:animal_detail', pk=event.animal.id)
    return redirect('dairy:animal_detail', pk=event.animal.id)

def delete_event(request, event_id):
    event = get_object_or_404(AnimalEvent, id=event_id)
    animal_id = event.animal.id
    event.delete()
    messages.success(request, 'Event deleted successfully')
    return redirect('dairy:animal_detail', pk=animal_id)

# Animal Scoring Views
from .models import AnimalScore

@login_required
def update_animal_scores(request):
    """Update scores for all active animals in the user's farm"""
    farm = request.user.farm
    animals = Animal.objects.filter(farm=farm, status='active')
    
    updated_count = 0
    for animal in animals:
        # Get or create AnimalScore for this animal
        score, created = AnimalScore.objects.get_or_create(animal=animal)
        score.update_scores()
        updated_count += 1
    
    messages.success(request, f'Successfully updated scores for {updated_count} active animals.')
    return redirect('dairy:animal_list')

@login_required
def update_animal_score(request, pk):
    """Update score for a specific animal"""
    animal = get_object_or_404(Animal, pk=pk, farm=request.user.farm)
    
    # Get or create AnimalScore for this animal
    score, created = AnimalScore.objects.get_or_create(animal=animal)
    score.update_scores()
    
    messages.success(request, f'Score updated for {animal.tag}.')
    return redirect('dairy:animal_detail', pk=pk)

@login_required
def animal_score_detail(request, pk):
    """Redirect to animal detail page with scores tab active"""
    return redirect(f'{reverse("dairy:animal_detail", kwargs={"pk": pk})}?active_tab=scores')

@login_required 
def farm_score_dashboard(request):
    """Display scoring dashboard for all active animals in the farm"""
    farm = request.user.farm
    
    # Get only active animals with their scores
    animals = Animal.objects.filter(farm=farm, status='active').prefetch_related('score')
    
    # Calculate statistics
    total_animals = animals.count()
    scored_animals = 0
    total_score = 0
    score_distribution = {
        'excellent': 0,  # 80-100
        'good': 0,       # 60-79
        'fair': 0,       # 40-59
        'poor': 0        # 0-39
    }
    
    animals_with_scores = []
    for animal in animals:
        try:
            score = animal.score
            # Update if score is older than 1 hour
            if (timezone.now() - score.last_calculated).total_seconds() > 3600:
                score.update_scores()
        except AnimalScore.DoesNotExist:
            score = AnimalScore.objects.create(animal=animal)
            score.update_scores()
        
        if score:
            scored_animals += 1
            total_score += score.total_score
            
            # Categorize score
            if score.total_score >= 80:
                score_distribution['excellent'] += 1
            elif score.total_score >= 60:
                score_distribution['good'] += 1
            elif score.total_score >= 40:
                score_distribution['fair'] += 1
            else:
                score_distribution['poor'] += 1
                
            animals_with_scores.append({
                'animal': animal,
                'score': score,
                'percentage': score.total_score,
                'color': score.get_score_color(),
                'grade': score.get_grade()
            })
    
    # Sort animals by score (highest first)
    animals_with_scores.sort(key=lambda x: x['score'].total_score, reverse=True)
    
    # Calculate average score
    avg_score = total_score / scored_animals if scored_animals > 0 else 0
    
    context = {
        'total_animals': total_animals,
        'scored_animals': scored_animals,
        'avg_score': avg_score,
        'score_distribution': score_distribution,
        'animals_with_scores': animals_with_scores[:10],  # Top 10 animals
        'low_scoring_animals': animals_with_scores[-10:] if len(animals_with_scores) > 10 else [],  # Bottom 10
    }
    
    return render(request, 'dairy/farm_score_dashboard.html', context)

# Breeding Management Views
@login_required
def breeding_dashboard(request):
    """Main breeding dashboard with overview statistics"""
    farm = request.user.farm
    today = timezone.now().date()
    
    # Get breeding statistics
    total_breedings = Breeding.objects.filter(female_animal__farm=farm).count()
    pending_breedings = Breeding.objects.filter(female_animal__farm=farm, status='pending').count()
    successful_breedings = Breeding.objects.filter(female_animal__farm=farm, status='successful').count()
    
    # Calculate success rate
    success_rate = (successful_breedings / total_breedings * 100) if total_breedings > 0 else 0
    
    # Get upcoming expected calvings
    upcoming_calvings = Breeding.objects.filter(
        female_animal__farm=farm,
        status='successful',
        expected_calving_date__gte=today,
        expected_calving_date__lte=today + timedelta(days=30)
    ).select_related('female_animal').order_by('expected_calving_date')
    
    # Get animals due for drying off
    dry_off_due = Breeding.objects.filter(
        female_animal__farm=farm,
        status='successful',
        dry_off_date__gte=today,
        dry_off_date__lte=today + timedelta(days=14)
    ).select_related('female_animal').order_by('dry_off_date')
    
    # Get recent breedings
    recent_breedings = Breeding.objects.filter(
        female_animal__farm=farm
    ).select_related('female_animal', 'bull').order_by('-date_of_insemination')[:10]
    
    # Get breeding by method stats
    breeding_methods = Breeding.objects.filter(female_animal__farm=farm).values('breeding_method').annotate(
        count=Count('id')
    )
    
    # Get monthly breeding trend for last 6 months
    six_months_ago = today - timedelta(days=180)
    monthly_breedings = Breeding.objects.filter(
        female_animal__farm=farm,
        date_of_insemination__gte=six_months_ago
    ).extra(
        select={'month': "TO_CHAR(date_of_insemination, 'YYYY-MM')"}
    ).values('month').annotate(count=Count('id')).order_by('month')
    
    # Animals ready for breeding (open cycles, not recently bred)
    ready_for_breeding = Animal.objects.filter(
        farm=farm,
        sex='female',
        animal_type__in=['breeder', 'dry', 'milking'],
        status='active'
    ).exclude(
        breeding_events__date_of_insemination__gte=today - timedelta(days=21)
    ).distinct()[:10]
    
    context = {
        'total_breedings': total_breedings,
        'pending_breedings': pending_breedings,
        'successful_breedings': successful_breedings,
        'success_rate': round(success_rate, 1),
        'upcoming_calvings': upcoming_calvings,
        'dry_off_due': dry_off_due,
        'recent_breedings': recent_breedings,
        'breeding_methods': list(breeding_methods),
        'monthly_breedings': list(monthly_breedings),
        'ready_for_breeding': ready_for_breeding,
    }
    
    return render(request, 'dairy/breeding/dashboard.html', context)

@login_required
def breeding_list(request):
    """List all breeding records with filtering options"""
    farm = request.user.farm
    
    # Get filter parameters
    status_filter = request.GET.get('status', 'all')
    method_filter = request.GET.get('method', 'all')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    animal_filter = request.GET.get('animal')
    
    # Base queryset
    breedings = Breeding.objects.filter(female_animal__farm=farm).select_related(
        'female_animal', 'bull', 'breeding_cycle'
    ).prefetch_related('pregnancy_checks', 'calving_record')
    
    # Apply filters
    if status_filter != 'all':
        breedings = breedings.filter(status=status_filter)
    
    if method_filter != 'all':
        breedings = breedings.filter(breeding_method=method_filter)
    
    if date_from:
        breedings = breedings.filter(date_of_insemination__gte=date_from)
    
    if date_to:
        breedings = breedings.filter(date_of_insemination__lte=date_to)
    
    if animal_filter:
        breedings = breedings.filter(female_animal__tag__icontains=animal_filter)
    
    # Order by date
    breedings = breedings.order_by('-date_of_insemination')
    
    # Pagination
    paginator = Paginator(breedings, 20)
    page = request.GET.get('page')
    try:
        breedings_page = paginator.page(page)
    except PageNotAnInteger:
        breedings_page = paginator.page(1)
    except EmptyPage:
        breedings_page = paginator.page(paginator.num_pages)
    
    context = {
        'breedings': breedings_page,
        'status_filter': status_filter,
        'method_filter': method_filter,
        'date_from': date_from,
        'date_to': date_to,
        'animal_filter': animal_filter,
    }
    
    return render(request, 'dairy/breeding/list.html', context)

@login_required
def breeding_create(request):
    """Create a new breeding record"""
    if request.method == 'POST':
        form = BreedingForm(request.POST, farm=request.user.farm)
        if form.is_valid():
            breeding = form.save(commit=False)
            
            # Auto-calculate expected dates
            breeding.expected_calving_date = breeding.date_of_insemination + timedelta(days=280)
            breeding.dry_off_date = breeding.expected_calving_date - timedelta(days=60)
            
            # Get or create breeding cycle
            cycle = BreedingCycle.objects.filter(
                animal=breeding.female_animal,
                status='open'
            ).first()
            
            if not cycle:
                # Create new cycle
                cycle = BreedingCycle.objects.create(
                    animal=breeding.female_animal,
                    lactation_number=1,  # Will be incremented properly
                    start_date=breeding.date_of_insemination,
                    status='bred'
                )
            else:
                cycle.status = 'bred'
                cycle.save()
            
            breeding.breeding_cycle = cycle
            breeding.save()
            
            messages.success(request, f'Breeding record created for {breeding.female_animal.tag}')
            return redirect('dairy:breeding_detail', pk=breeding.pk)
    else:
        form = BreedingForm(farm=request.user.farm)
    
    return render(request, 'dairy/breeding/form.html', {'form': form, 'title': 'New Breeding'})

@login_required
def breeding_detail(request, pk):
    """View breeding record details"""
    breeding = get_object_or_404(Breeding, pk=pk, female_animal__farm=request.user.farm)
    
    # Get related records
    pregnancy_checks = breeding.pregnancy_checks.all().order_by('-check_date')
    calving_record = hasattr(breeding, 'calving_record') and breeding.calving_record or None
    
    context = {
        'breeding': breeding,
        'pregnancy_checks': pregnancy_checks,
        'calving_record': calving_record,
    }
    
    return render(request, 'dairy/breeding/detail.html', context)

@login_required
def breeding_calendar(request):
    """Calendar view for breeding events and schedules"""
    farm = request.user.farm
    
    # Get current month or from request
    today = timezone.now().date()
    month = int(request.GET.get('month', today.month))
    year = int(request.GET.get('year', today.year))
    
    # Calculate calendar bounds
    first_day = date(year, month, 1)
    if month == 12:
        last_day = date(year + 1, 1, 1) - timedelta(days=1)
    else:
        last_day = date(year, month + 1, 1) - timedelta(days=1)
    
    # Get events for this month
    events = []
    
    # Breeding events
    breedings = Breeding.objects.filter(
        female_animal__farm=farm,
        date_of_insemination__gte=first_day,
        date_of_insemination__lte=last_day
    ).select_related('female_animal')
    
    for breeding in breedings:
        events.append({
            'date': breeding.date_of_insemination,
            'type': 'breeding',
            'title': f'Breeding: {breeding.female_animal.tag}',
            'url': reverse('dairy:breeding_detail', args=[breeding.pk]),
            'color': '#0da487'
        })
    
    # Expected calvings
    expected_calvings = Breeding.objects.filter(
        female_animal__farm=farm,
        status='successful',
        expected_calving_date__gte=first_day,
        expected_calving_date__lte=last_day
    ).select_related('female_animal')
    
    for breeding in expected_calvings:
        events.append({
            'date': breeding.expected_calving_date,
            'type': 'calving',
            'title': f'Expected Calving: {breeding.female_animal.tag}',
            'url': reverse('dairy:breeding_detail', args=[breeding.pk]),
            'color': '#dc3545'
        })
    
    # Dry-off dates
    dry_offs = Breeding.objects.filter(
        female_animal__farm=farm,
        status='successful',
        dry_off_date__gte=first_day,
        dry_off_date__lte=last_day
    ).select_related('female_animal')
    
    for breeding in dry_offs:
        events.append({
            'date': breeding.dry_off_date,
            'type': 'dry_off',
            'title': f'Dry-off: {breeding.female_animal.tag}',
            'url': reverse('dairy:breeding_detail', args=[breeding.pk]),
            'color': '#ffc107'
        })
    
    # Pregnancy checks
    pregnancy_checks = PregnancyCheck.objects.filter(
        breeding__female_animal__farm=farm,
        check_date__gte=first_day,
        check_date__lte=last_day
    ).select_related('breeding__female_animal')
    
    for check in pregnancy_checks:
        events.append({
            'date': check.check_date,
            'type': 'pregnancy_check',
            'title': f'Pregnancy Check: {check.breeding.female_animal.tag}',
            'url': reverse('dairy:breeding_detail', args=[check.breeding.pk]),
            'color': '#17a2b8'
        })
    
    # Calculate previous and next month
    if month == 1:
        prev_month, prev_year = 12, year - 1
    else:
        prev_month, prev_year = month - 1, year
    
    if month == 12:
        next_month, next_year = 1, year + 1
    else:
        next_month, next_year = month + 1, year
    
    context = {
        'events': events,
        'month': month,
        'year': year,
        'month_name': first_day.strftime('%B'),
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'today': today,
    }
    
    return render(request, 'dairy/breeding/calendar.html', context)

@login_required
def pregnancy_check_create(request, breeding_id):
    """Record a pregnancy check for a breeding"""
    breeding = get_object_or_404(Breeding, pk=breeding_id, female_animal__farm=request.user.farm)
    
    if request.method == 'POST':
        form = PregnancyCheckForm(request.POST, farm=request.user.farm)
        if form.is_valid():
            check = form.save(commit=False)
            check.breeding = breeding
            check.save()
            
            # Update breeding status based on result
            if check.status == 'pregnant':
                breeding.status = 'successful'
                breeding.breeding_cycle.status = 'pregnant'
                breeding.breeding_cycle.save()
            elif check.status == 'not_pregnant':
                breeding.status = 'failed'
                breeding.breeding_cycle.status = 'open'
                breeding.breeding_cycle.save()
            
            breeding.save()
            
            messages.success(request, 'Pregnancy check recorded successfully')
            return redirect('dairy:breeding_detail', pk=breeding.pk)
    else:
        form = PregnancyCheckForm(
            initial={'breeding': breeding},
            farm=request.user.farm
        )
    
    context = {
        'form': form,
        'breeding': breeding,
        'title': f'Pregnancy Check for {breeding.female_animal.tag}'
    }
    
    return render(request, 'dairy/breeding/pregnancy_check_form.html', context)

@login_required
def calving_record_create(request, breeding_id):
    """Record calving for a breeding"""
    breeding = get_object_or_404(Breeding, pk=breeding_id, female_animal__farm=request.user.farm)
    
    if request.method == 'POST':
        form = CalvingRecordForm(request.POST, farm=request.user.farm)
        if form.is_valid():
            calving = form.save(commit=False)
            calving.breeding = breeding
            calving.save()
            
            # Create calf record
            calf = Animal.objects.create(
                farm=request.user.farm,
                tag=form.cleaned_data['calf_tag'],
                sex=form.cleaned_data['calf_sex'],
                dob=calving.calving_date,
                mother=breeding.female_animal,
                father=breeding.bull,
                category='calf',
                animal_type='calf',
                status='active'
            )
            
            calving.calf = calf
            calving.save()
            
            # Update breeding cycle
            breeding.breeding_cycle.status = 'calved'
            breeding.breeding_cycle.end_date = calving.calving_date
            breeding.breeding_cycle.save()
            
            # Create new cycle for the mother
            new_cycle = BreedingCycle.objects.create(
                animal=breeding.female_animal,
                lactation_number=breeding.lactation_number + 1,
                start_date=calving.calving_date,
                status='open'
            )
            
            messages.success(request, f'Calving recorded successfully. Calf {calf.tag} created.')
            return redirect('dairy:breeding_detail', pk=breeding.pk)
    else:
        form = CalvingRecordForm(
            initial={'breeding': breeding},
            farm=request.user.farm
        )
    
    context = {
        'form': form,
        'breeding': breeding,
        'title': f'Record Calving for {breeding.female_animal.tag}'
    }
    
    return render(request, 'dairy/breeding/calving_form.html', context)

@login_required
def heat_detection(request):
    """Heat detection tracking"""
    farm = request.user.farm
    
    if request.method == 'POST':
        form = HeatDetectionForm(request.POST, farm=farm)
        if form.is_valid():
            # Store heat detection in AnimalEvent
            animal = form.cleaned_data['animal']
            heat_signs = form.cleaned_data.get('heat_signs', [])
            if isinstance(heat_signs, list):
                heat_signs_text = ', '.join(heat_signs)
            else:
                heat_signs_text = str(heat_signs)
            
            notes = form.cleaned_data.get('notes', '')
            description = f"Heat signs: {heat_signs_text}"
            if notes:
                description += f"\nNotes: {notes}"
                
            event = AnimalEvent.objects.create(
                animal=animal,
                title='Heat Detection',
                date=form.cleaned_data['detection_date'],
                description=description
            )
            
            messages.success(request, f'Heat detection recorded for {animal.tag}')
            return redirect('dairy:heat_detection')
    else:
        form = HeatDetectionForm(farm=farm)
    
    # Get recent heat detections
    recent_detections = AnimalEvent.objects.filter(
        animal__farm=farm,
        title__icontains='heat'
    ).select_related('animal').order_by('-date')[:20]
    
    # Get animals potentially in heat (21-day cycle)
    today = timezone.now().date()
    potential_heat = []
    
    females = Animal.objects.filter(
        farm=farm,
        sex='female',
        animal_type__in=['breeder', 'dry', 'milking'],
        status='active'
    )
    
    for animal in females:
        # Check last heat or breeding
        last_heat = AnimalEvent.objects.filter(
            animal=animal,
            title__icontains='heat'
        ).order_by('-date').first()
        
        last_breeding = Breeding.objects.filter(
            female_animal=animal,
            status='failed'
        ).order_by('-date_of_insemination').first()
        
        # Calculate expected heat date
        if last_heat:
            expected_heat = last_heat.date + timedelta(days=21)
            if today - timedelta(days=3) <= expected_heat <= today + timedelta(days=3):
                potential_heat.append({
                    'animal': animal,
                    'expected_date': expected_heat,
                    'last_event': f'Heat on {last_heat.date}'
                })
        elif last_breeding:
            expected_heat = last_breeding.date_of_insemination + timedelta(days=21)
            if today - timedelta(days=3) <= expected_heat <= today + timedelta(days=3):
                potential_heat.append({
                    'animal': animal,
                    'expected_date': expected_heat,
                    'last_event': f'Failed breeding on {last_breeding.date_of_insemination}'
                })
    
    context = {
        'form': form,
        'recent_detections': recent_detections,
        'potential_heat': potential_heat,
    }
    
    return render(request, 'dairy/breeding/heat_detection.html', context)

@login_required
def breeding_analytics(request):
    """Breeding analytics and reports"""
    farm = request.user.farm
    
    # Date range filter
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=365)
    
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
    if date_to:
        end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
    
    # Overall statistics
    total_breedings = Breeding.objects.filter(
        female_animal__farm=farm,
        date_of_insemination__gte=start_date,
        date_of_insemination__lte=end_date
    ).count()
    
    successful_breedings = Breeding.objects.filter(
        female_animal__farm=farm,
        status='successful',
        date_of_insemination__gte=start_date,
        date_of_insemination__lte=end_date
    ).count()
    
    conception_rate = (successful_breedings / total_breedings * 100) if total_breedings > 0 else 0
    
    # Average services per conception
    avg_services = Breeding.objects.filter(
        female_animal__farm=farm,
        status='successful',
        date_of_insemination__gte=start_date,
        date_of_insemination__lte=end_date
    ).aggregate(avg=Avg('attempt'))['avg'] or 0
    
    # Bull performance
    bull_stats = Breeding.objects.filter(
        female_animal__farm=farm,
        breeding_method='natural',
        date_of_insemination__gte=start_date,
        date_of_insemination__lte=end_date
    ).values('bull__tag').annotate(
        total=Count('id'),
        successful=Count('id', filter=Q(status='successful')),
    ).order_by('-total')
    
    # Calculate success rate for each bull
    for bull in bull_stats:
        bull['success_rate'] = (bull['successful'] / bull['total'] * 100) if bull['total'] > 0 else 0
    
    # AI vs Natural breeding comparison
    breeding_method_stats = Breeding.objects.filter(
        female_animal__farm=farm,
        date_of_insemination__gte=start_date,
        date_of_insemination__lte=end_date
    ).values('breeding_method').annotate(
        total=Count('id'),
        successful=Count('id', filter=Q(status='successful')),
    )
    
    for method in breeding_method_stats:
        method['success_rate'] = (method['successful'] / method['total'] * 100) if method['total'] > 0 else 0
    
    # Monthly trend
    monthly_stats = Breeding.objects.filter(
        female_animal__farm=farm,
        date_of_insemination__gte=start_date,
        date_of_insemination__lte=end_date
    ).extra(
        select={'month': "TO_CHAR(date_of_insemination, 'YYYY-MM')"}
    ).values('month').annotate(
        total=Count('id'),
        successful=Count('id', filter=Q(status='successful')),
    ).order_by('month')
    
    # Calving interval analysis
    calving_intervals = []
    animals_with_multiple_calvings = CalvingRecord.objects.filter(
        breeding__female_animal__farm=farm
    ).values('breeding__female_animal').annotate(
        calving_count=Count('id')
    ).filter(calving_count__gt=1)
    
    for animal_data in animals_with_multiple_calvings:
        calvings = CalvingRecord.objects.filter(
            breeding__female_animal_id=animal_data['breeding__female_animal']
        ).order_by('calving_date')
        
        for i in range(1, len(calvings)):
            interval = (calvings[i].calving_date - calvings[i-1].calving_date).days
            calving_intervals.append(interval)
    
    avg_calving_interval = sum(calving_intervals) / len(calving_intervals) if calving_intervals else 0
    
    context = {
        'date_from': start_date,
        'date_to': end_date,
        'total_breedings': total_breedings,
        'successful_breedings': successful_breedings,
        'conception_rate': round(conception_rate, 1),
        'avg_services': round(avg_services, 1),
        'bull_stats': bull_stats,
        'breeding_method_stats': list(breeding_method_stats),
        'monthly_stats': list(monthly_stats),
        'avg_calving_interval': round(avg_calving_interval),
    }
    
    return render(request, 'dairy/breeding/analytics.html', context)
