from django.urls import path,include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from django.http import HttpResponse
import json
from .views_api import AnimalDetailView
from . import views
from . import views_api
from . import breeding_views

app_name = 'dairy'

router = DefaultRouter()
router.register(r'milk_records', views_api.MilkRecordViewSet, basename='milk_record')
router.register(r'breeding', breeding_views.BreedingViewSet, basename='breeding')
router.register(r'pregnancy-checks', breeding_views.PregnancyCheckViewSet, basename='pregnancy-check')
router.register(r'calving-records', breeding_views.CalvingRecordViewSet, basename='calving-record')
router.register(r'breeding-cycles', breeding_views.BreedingCycleViewSet, basename='breeding-cycle')

urlpatterns = [
    # Debug catch-all URL pattern
   
    
    path('api/milk_records/create/', views_api.api_milk_record_create, name='api_milk_record_create'),
    path('api/milking_animals/', views_api.api_get_milking_animals, name='api_get_milking_animals'),
    path('api/animal_milk_record/', views_api.api_get_animal_milk_record, name='api_get_animal_milk_record'),
    path('api/', include(router.urls)),
    path('animals/', views.animal_list, name='animal_list'),
   
    path('animal/<int:pk>/', views.animal_detail, name='animal_detail'),
    path('api/animals/<int:pk>/', AnimalDetailView.as_view(), name='api_animal-detail'),
    path('api/animals/<int:pk>/update/', views_api.api_animal_update, name='api_animal_update'),
 
    path('animals/create/', views.animal_create, name='animal_create'),
    path('animals/edit/<int:pk>/', views.animal_edit, name='animal_edit'),
    path('animals/<int:pk>/delete/', views.delete_animal, name='delete_animal'),
    

    path('animal/<int:pk>/create_family/', views.create_family, name='create_family'),
    path('animal/<int:pk>/update_family/', views.update_family, name='update_family'),
    path('animal/<int:pk>/delete_family/', views.delete_family, name='delete_family'),


    path('search/', views.search, name='search'),


    path('total_animal_milk/', views.total_milk_list, name='total_animal_milk_list'),
    path('animal_milk/', views.animal_milk_list, name='animal_milk_list'),
    path('animal_milks/new/', views.animal_milk_new, name='animal_milk_new'),
    path('animal_milks/<int:pk>/edit/', views.animal_milk_edit, name='animal_milk_edit'),
    path('animal_milks/<int:pk>/delete/', views.animal_milk_delete, name='animal_milk_delete'),

    

    path('milk_record/<int:milk_record_id>/edit/', views.edit_milk_record, name='milk_edit'),
    path('milk_record/<int:milk_record_id>/delete/', views.delete_milk_record, name='milk_delete'),

    path('animal/<int:animal_id>/add_milk_record/', views.add_milk_record, name='add_milk_record'),
    path('animal/<int:animal_id>/get_milk_record/<str:date>/', views.get_milk_record, name='get_milk_record'),


    path('animal_weights/', views.animal_weight_list, name='animal_weight_list'),
    path('animal_weights/<int:pk>/', views.animal_weight_detail, name='animal_weight_detail'),
    path('animal_weights/new/<int:pk>/', views.animal_detail_weight_new, name='animal_detail_weight_new'),
    path('animal_weights/new/', views.animal_weight_new, name='animal_weight_new'),
    path('animal_weights/<int:pk>/edit/', views.animal_weight_edit, name='animal_weight_edit'),
    path('animal_weights/<int:pk>/delete/', views.animal_weight_delete, name='animal_weight_delete'),





    path('customers/', views.customer_list, name='customer_list'),
    path('customers/create/', views.customer_new, name='customer_create'),
    path('customers/<int:pk>/', views.customer_detail, name='customer_detail'),
    path('customers/<int:pk>/edit/', views.customer_new, name='customer_edit'),
    path('customers/<int:pk>/delete/', views.customer_delete, name='customer_delete'),

    path('add-milk-payment/', views.add_milk_payment, name='add_milk_payment'),
    path('update-milk-payment/<int:milk_payment_id>/', views.update_milk_payment, name='update_milk_payment'),
    path('delete-milk-payment/<int:pk>/', views.delete_milk_payment, name='delete_milk_payment'),



    path('api/animal_milk/', views_api.get_milk_records, name='api_animal_milk_list'),
    path('api/animal-milk/', views_api.api_animal_milk_create_update, name='api_animal_milk_create_update'),


  

    path('api/total_milk_records/', views_api.api_total_milk_list, name='api_total_milk_list'),
   
    
    path('api/animals/filtered/', views_api.list_filtered_milk_animals, name='list_milk_animals'),
    path('api/animals/create/', views_api.api_animal_create, name='api_animal_create'),

    path('api/animal-weights/', views_api.AnimalWeightListView.as_view(), name='api_animal_weights'),
    path('api/animal-weight/', views_api.api_animal_weight_create_update, name='api_animal_weight_create_update'),
    path('api/animal-weight/<int:weight_id>/update/', views_api.api_animal_weight_update, name='api_animal_weight_update'),
    path('api/animal-weight/<int:weight_id>/delete/', views_api.api_animal_weight_delete, name='api_animal_weight_delete'),
    path('api/animals-with-weights/', views_api.api_animals_with_weights, name='api_animals_with_weights'),
    path('api/animal/<int:animal_id>/weights/', views_api.api_animal_weight_history, name='api_animal_weight_history'),
    path('milk_sale/', views.milk_sale_list, name='milk_sale_list'),
    path('milk_sale/new/', views.milk_sale_create, name='milk_sale_create'),
    path('milk_sale/edit/<int:pk>/', views.milk_sale_edit, name='milk_sale_edit'),
    path('milk_sale/<int:sale_id>/delete/', views.milk_sale_delete, name='milk_sale_delete'),
    path('api/types/', views_api.get_animal_types, name='get-animal-types'),
    path('api/animals/', views_api.get_animals, name='get-animals'),
    path('api/animals/add/', views_api.create_animal, name='create_animal'),
    path('api/animal_types/', views_api.get_animal_types, name='get_animal_types'),
    
    path('api/milking-animals/', views_api.get_milking_animals, name='get_milking_animals'),
    path('api/milk_records/<int:animal_id>/', views_api.get_milk_record_by_animal_id, name='get_milk_record_by_animal_id'),

    # Milk Sale APIs
    path('api/milk_sale/create/', views_api.api_milk_sale_create, name='api_milk_sale_create'),
    path('api/milk_sale/check/', views_api.api_check_milk_sale_exists, name='api_check_milk_sale_exists'),
    path('api/milk_sale/cleanup_duplicates/', views_api.api_cleanup_duplicate_milk_sales, name='api_cleanup_duplicate_milk_sales'),
    path('api/milk_sale/<int:milk_sale_id>/', views_api.api_milk_sale_detail, name='api_milk_sale_detail'),
    path('api/milk_sale/<int:milk_sale_id>/update/', views_api.api_milk_sale_update, name='api_milk_sale_update'),
    path('api/milk_sale/<int:milk_sale_id>/delete/', views_api.api_milk_sale_delete, name='api_milk_sale_delete'),
    path('api/customers/<int:customer_id>/milk_sales/', views_api.api_customer_milk_sales, name='api_customer_milk_sales'),

    # Event URLs
    path('animal/<int:animal_id>/add-event/', views.add_event, name='add_event'),
    path('event/<int:event_id>/edit/', views.edit_event, name='edit_event'),
    path('event/<int:event_id>/delete/', views.delete_event, name='delete_event'),

    # Event API URLs
    path('api/animal/<int:animal_id>/events/', views_api.get_animal_events, name='api_get_animal_events'),
    path('api/animal/<int:animal_id>/events/create/', views_api.create_animal_event, name='api_create_animal_event'),
    path('api/events/<int:event_id>/', views_api.animal_event_detail, name='api_animal_event_detail'),
    path('api/farm/events/', views_api.get_farm_animal_events, name='api_get_farm_animal_events'),
    path('api/animals/by-type/', views_api.get_animals_by_type, name='get_animals_by_type'),
    path('api/milk_sale_totals/', views_api.milk_sale_totals, name='api_milk_sale_totals'),

    # API endpoints
    path('api/customers/', views_api.api_customer_list, name='api_customer_list'),
    path('api/customers/<int:pk>/', views_api.api_customer_detail, name='api_customer_detail'),
    path('api/milk_sale/create/', views_api.api_milk_sale_create, name='api_milk_sale_create'),
    path('api/customers/<int:customer_id>/milk_sales/', views_api.api_customer_milk_sales, name='api_customer_milk_sales'),
    
    # Animal Scoring URLs
    path('scores/update/', views.update_animal_scores, name='update_animal_scores'),
    path('scores/update/<int:pk>/', views.update_animal_score, name='update_animal_score'),
    path('scores/animal/<int:pk>/', views.animal_score_detail, name='animal_score_detail'),
    path('scores/dashboard/', views.farm_score_dashboard, name='farm_score_dashboard'),
    
    # Animal Scoring API URLs
    path('api/scores/dashboard/', views_api.api_farm_score_dashboard, name='api_farm_score_dashboard'),
    path('api/scores/animal/<int:animal_id>/', views_api.api_animal_score_detail, name='api_animal_score_detail'),
    path('api/scores/update/', views_api.api_update_animal_scores, name='api_update_animal_scores'),
    path('api/scores/list/', views_api.api_animal_scores_list, name='api_animal_scores_list'),
    
    # Breeding Web URLs
    path('breeding/', views.breeding_dashboard, name='breeding_dashboard'),
    path('breeding/list/', views.breeding_list, name='breeding_list'),
    path('breeding/create/', views.breeding_create, name='breeding_create'),
    path('breeding/<int:pk>/', views.breeding_detail, name='breeding_detail'),
    path('breeding/calendar/', views.breeding_calendar, name='breeding_calendar'),
    path('breeding/<int:breeding_id>/pregnancy-check/', views.pregnancy_check_create, name='pregnancy_check_create'),
    path('breeding/<int:breeding_id>/calving/', views.calving_record_create, name='calving_record_create'),
    path('breeding/heat-detection/', views.heat_detection, name='heat_detection'),
    path('breeding/analytics/', views.breeding_analytics, name='breeding_analytics'),
]
