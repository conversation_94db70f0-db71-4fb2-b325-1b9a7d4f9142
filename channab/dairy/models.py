from datetime import date, timedelta
from django.db import models
from datetime import datetime
from django.utils import timezone
from accounts.models import Farm
from PIL import Image, ExifTags
from io import BytesIO
from django.core.files import File
from image_cropping import ImageRatioField
from django.core.files.uploadedfile import SimpleUploadedFile
import io 
from django.utils.text import slugify
from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.core.exceptions import ValidationError

class Animal(models.Model):
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, related_name='animals')
    tag = models.CharField(max_length=100)
    
    image = models.ImageField(upload_to='animals/', blank=True, null=True)
    image_updated = models.DateTimeField(null=True, blank=True, help_text="Timestamp when image was last updated")
    dob = models.DateField(verbose_name='Date of Birth')
    purchase_cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('sold', 'Sold'),
    ]
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    SEX_CHOICES = [
        ('male', 'Male'),
        ('female', 'Female'),
    ]
    sex = models.CharField(max_length=6, choices=SEX_CHOICES)
    CATEGORY_CHOICES = [
        ('dairy', 'Dairy'),
        ('buffalo', 'Buffalo'),
        ('cow', 'Cow'),
        ('goat', 'Goat'),
        ('sheep', 'Sheep'),
        ('beef', 'Beef'),
        ('other', 'Other')
    ]
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES, default='dairy')
    TYPE_CHOICES = [
        ('breeder', 'Breeder'),
        ('pregnant', 'Pregnant'),
        ('dry', 'Dry'),
        ('milking', 'Milking'),
        ('preg_milking', 'Pre-Milk'),
        ('calf', 'Calf'),
        ('other', 'Other'),
    ]
    animal_type = models.CharField(max_length=200, choices=TYPE_CHOICES, default='other')
    TYPE_CHOICES_DICT = dict(TYPE_CHOICES)
    type_changed_date = models.DateTimeField(null=True, blank=True, help_text="Date when animal type was last changed")

    mother = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='children_mother')
    father = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='children_father')
    
    def _rotate_image(self, image):
        if hasattr(image, '_getexif'):
            exif_data = image._getexif()
            if exif_data is not None:
                for tag, value in list(exif_data.items()):
                    if tag in ExifTags.TAGS:
                        if ExifTags.TAGS[tag] == 'Orientation':
                            if value == 3:
                                image = image.rotate(180, expand=True)
                            elif value == 6:
                                image = image.rotate(-90, expand=True)
                            elif value == 8:
                                image = image.rotate(90, expand=True)
                            break
        return image
    
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['farm', 'tag'], name='unique_farm_tag')
        ]

    def save(self, *args, **kwargs):
        if self.mother == self or self.father == self:
            raise ValidationError('An animal cannot be its own parent.')
        if self.mother and self.mother.sex != 'female':
            raise ValidationError('Mother must be a female animal.')
        if self.father and self.father.sex != 'male':
            raise ValidationError('Father must be a male animal.')
        if self.mother and self in self.mother.get_descendants(include_children=False):
            raise ValidationError('Invalid parent-child relationship.')
        if self.father and self in self.father.get_descendants(include_children=False):
            raise ValidationError('Invalid parent-child relationship.')

        # Track image update timestamp and type changes
        if self.pk:  # Existing instance
            try:
                old_instance = Animal.objects.get(pk=self.pk)
                if old_instance.image != self.image:
                    self.image_updated = timezone.now()
                # Track animal type changes
                if old_instance.animal_type != self.animal_type:
                    self.type_changed_date = timezone.now()
                    # We'll create the type change record after save
            except Animal.DoesNotExist:
                pass
        else:
            # New instance
            if self.image:
                self.image_updated = timezone.now()
            # Set type_changed_date for new animals
            self.type_changed_date = timezone.now()

        if self.image:
            # Open the original image
            image = Image.open(self.image)

            # Rotate the image if necessary based on the EXIF orientation
            image = self._rotate_image(image)

            # Resize the image while maintaining the aspect ratio
            max_size = (800, 800)
            image.thumbnail(max_size)

            # If the image has transparency (an alpha channel), convert it to RGB
            if image.mode in ('RGBA', 'LA'):
                background = Image.new(image.mode[:-1], image.size, '#FFFFFF')
                background.paste(image, image.split()[-1]) # alpha channel is used as mask
                image = background

            # Create a BytesIO object to hold the compressed image data
            image_io = io.BytesIO()

            # Save the image to the BytesIO object with JPEG format and quality of 70
            image.save(image_io, format='JPEG', quality=70)

            # Calculate the size of the compressed image
            image_size = image_io.tell()

            # If the image size is larger than 50 KB, further compress it
            if image_size > 50000:
                # Calculate the desired compression ratio
                compression_ratio = 50000 / image_size

                # Create a new BytesIO object to hold the further compressed image data
                compressed_image_io = io.BytesIO()

                # Adjust the quality based on the compression ratio and save the image
                image.save(compressed_image_io, format='JPEG', quality=int(compression_ratio * 70))

                # Set the content of the image field to the further compressed image
                self.image = SimpleUploadedFile(self.image.name, compressed_image_io.getvalue())

        super().save(*args, **kwargs)


    def get_descendants(self, include_children=True):
        descendants = set()
        if include_children:
            for child in self.children_mother.all():
                descendants.add(child)
                descendants.update(child.get_descendants())
            for child in self.children_father.all():
                descendants.add(child)
                descendants.update(child.get_descendants())
        return descendants
    @property
    def all_children(self):
        return self.children_mother.all() | self.children_father.all()
    
    @property
    def siblings(self):
        mother_children = self.mother.children_mother.all() if self.mother else Animal.objects.none()
        father_children = self.father.children_father.all() if self.father else Animal.objects.none()
        return (mother_children | father_children).exclude(id=self.id)
    
    def __str__(self):
        return f'{self.tag} ({self.category})'



from django.core.exceptions import ValidationError



class MilkRecord(models.Model):
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, limit_choices_to={'sex': 'female'})
    date = models.DateField(default=timezone.now)
    first_time = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    second_time = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    third_time = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    
    @property
    def total_milk(self):
        first_time = self.first_time or 0
        second_time = self.second_time or 0
        third_time = self.third_time or 0
        return first_time + second_time + third_time

    
class AnimalWeight(models.Model):
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, related_name='weights')
    weight_kg = models.DecimalField(max_digits=5, decimal_places=2)
    date = models.DateField(default=timezone.now)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f'{self.animal.tag} - {self.weight_kg} kg on {self.date}'




class Breeding(models.Model):
    female_animal = models.ForeignKey(
        Animal,
        on_delete=models.CASCADE,
        related_name='breeding_events',
        limit_choices_to={'sex': 'female'},
        null=True
    )
    
    date_of_insemination = models.DateField(default=datetime.today)
    
    BREEDING_METHOD_CHOICES = [
        ('natural', 'Natural'),
        ('artificial', 'Artificial')
    ]
    breeding_method = models.CharField(max_length=10, choices=BREEDING_METHOD_CHOICES)
    
    bull = models.ForeignKey(
        Animal,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='natural_breeding_events',
        limit_choices_to={'sex': 'male', 'animal_type': 'breeder'}
    )
    
    semen_tag = models.CharField(max_length=100, blank=True, null=True)
    comment = models.TextField(blank=True, null=True)
    
    lactation_number = models.PositiveIntegerField(blank=True, null=True)
    attempt = models.PositiveIntegerField(default=1)
    
    expected_calving_date = models.DateField(null=True, blank=True)
    dry_off_date = models.DateField(null=True, blank=True)
    breeding_cycle = models.ForeignKey(
        'BreedingCycle', 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='breedings'
    )
    
    STATUS_CHOICES = [
        ('pending', 'Pending Check'),
        ('successful', 'Successful'),
        ('failed', 'Failed'),
        ('aborted', 'Aborted')
    ]
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')

    def save(self, *args, **kwargs):
        if not self.pk:  # New breeding record
            # Calculate expected calving date (approximately 280 days from insemination)
            self.expected_calving_date = self.date_of_insemination + timedelta(days=280)
            
            # Calculate dry-off date (approximately 60 days before expected calving)
            self.dry_off_date = self.expected_calving_date - timedelta(days=60)
            
            # Get or create a new breeding cycle
            cycle = BreedingCycle.objects.filter(
                animal=self.female_animal,
                status='open'
            ).first()
            
            if not cycle:
                last_cycle = self.female_animal.breeding_cycles.first()
                new_lactation = 1 if not last_cycle else last_cycle.lactation_number + 1
                cycle = BreedingCycle.objects.create(
                    animal=self.female_animal,
                    start_date=self.date_of_insemination,
                    lactation_number=new_lactation,
                    status='bred'
                )
            
            self.breeding_cycle = cycle
            self.lactation_number = cycle.lactation_number
        
        super(Breeding, self).save(*args, **kwargs)

    def mark_pregnancy_status(self, is_pregnant, check_date=None):
        """Update breeding status based on pregnancy check"""
        if check_date is None:
            check_date = timezone.now().date()
            
        status = 'pregnant' if is_pregnant else 'not_pregnant'
        PregnancyCheck.objects.create(
            breeding=self,
            check_date=check_date,
            status=status
        )
        
        if is_pregnant:
            self.status = 'successful'
            if self.breeding_cycle:
                self.breeding_cycle.status = 'pregnant'
                self.breeding_cycle.save()
        else:
            self.status = 'failed'
        
        self.save()

    def record_calving(self, calving_date, calf=None, difficulty='normal', complications=None, notes=None):
        """Record calving event"""
        CalvingRecord.objects.create(
            breeding=self,
            calving_date=calving_date,
            calf=calf,
            difficulty=difficulty,
            complications=complications,
            notes=notes
        )
        
        if self.breeding_cycle:
            self.breeding_cycle.status = 'calved'
            self.breeding_cycle.end_date = calving_date
            self.breeding_cycle.save()
            
            # Start a new breeding cycle
            BreedingCycle.objects.create(
                animal=self.female_animal,
                start_date=calving_date,
                lactation_number=self.lactation_number + 1,
                status='open'
            )

    def clean(self):
        # Existing validation
        if self.breeding_method == 'natural' and not self.bull:
            raise ValidationError('A bull must be selected for natural breeding.')
        if self.breeding_method == 'artificial' and not self.semen_tag:
            raise ValidationError('A semen tag number must be provided for artificial breeding.')
        if self.breeding_method == 'natural' and self.semen_tag:
            raise ValidationError('Semen tag number must be empty for natural breeding.')
        if self.breeding_method == 'artificial' and self.bull:
            raise ValidationError('Bull must not be selected for artificial breeding.')
        
        # New validation
        if self.female_animal and self.female_animal.animal_type not in ['breeder', 'dry', 'milking']:
            raise ValidationError('Animal must be of type breeder, dry, or milking for breeding.')

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['female_animal', 'date_of_insemination'], 
                name='unique_animal_insemination_date'
            )
        ]
        
    def __str__(self):
        return f"{self.female_animal.tag} bred on {self.date_of_insemination} ({self.status})"


class BreedingCycle(models.Model):
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, related_name='breeding_cycles')
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('bred', 'Bred'),
        ('pregnant', 'Pregnant'),
        ('dry', 'Dry'),
        ('calved', 'Calved')
    ]
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='open')
    lactation_number = models.PositiveIntegerField()
    notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.animal.tag} - Cycle {self.lactation_number} ({self.status})"


from django.db.models import Sum

class Customer(models.Model):
    name = models.CharField(max_length=255)
    mobile_number = models.CharField(max_length=15, blank=True, null=True)  # Making it optional
    created_date = models.DateField(default=date.today, editable=True)
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, blank=True, null=True)
    # Any other relevant details about the customer

    def total_sales_amount(self):
        total_sales = MilkSale.objects.filter(customer=self).aggregate(total=Sum('total_price'))['total']
        return total_sales or 0

    def __str__(self):
        return self.name


class MilkSale(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    first_sale = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    second_sale = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    third_sale = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    price_per_liter = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    total_price = models.DecimalField(max_digits=7, decimal_places=2, blank=True, null=True)
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, blank=True, null=True)
   
    def save(self, *args, **kwargs):
        total_liters = (self.first_sale or 0) + (self.second_sale or 0) + (self.third_sale or 0)
        # Only update total_price if it's not provided and price_per_liter is not None
        if self.total_price is None and self.price_per_liter is not None:
            self.total_price = total_liters * self.price_per_liter
        super().save(*args, **kwargs)



    @property
    def total_liters_sold(self):
        return (self.first_sale or 0) + (self.second_sale or 0) + (self.third_sale or 0)

    @property
    def is_valid_sale(self):
        return self.total_liters_sold <= self.milk_record.total_milk

    def __str__(self):
        return f"{self.total_liters_sold} liters to {self.customer.name} on {self.date}"

class MilkPayment(models.Model):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    total_milk_payment = models.DecimalField(max_digits=7, decimal_places=2)
    received_payment = models.DecimalField(max_digits=7, decimal_places=2, default=0)
    remaining_payment = models.DecimalField(max_digits=7, decimal_places=2, blank=True, null=True)
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, blank=True, null=True)
    note = models.TextField(blank=True, null=True)  # Optional: For any additional info or comments

    def calculate_remaining(self):
        return self.total_milk_payment - self.received_payment

    def save(self, *args, **kwargs):
        if not self.total_milk_payment:
            # Calculate balance from total sales for the customer
            self.total_milk_payment = self.customer.total_sales_amount()
            
            # Consider previous payments
            previous_payments = MilkPayment.objects.filter(customer=self.customer, date__lt=self.date)
            for payment in previous_payments:
                self.total_milk_payment -= payment.received_payment

            # Now consider the remaining amounts from previous payments
            self.total_milk_payment += previous_payments.aggregate(total_remaining=Sum('remaining_payment'))['total_remaining'] or 0
            
        # Calculate remaining payment
        self.remaining_payment = self.calculate_remaining()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Payment of {self.received_payment} by {self.customer.name} on {self.date}"

class PregnancyCheck(models.Model):
    breeding = models.ForeignKey(Breeding, on_delete=models.CASCADE, related_name='pregnancy_checks')
    check_date = models.DateField()
    
    STATUS_CHOICES = [
        ('pregnant', 'Pregnant'),
        ('not_pregnant', 'Not Pregnant'),
        ('uncertain', 'Uncertain')
    ]
    status = models.CharField(max_length=12, choices=STATUS_CHOICES)
    notes = models.TextField(blank=True, null=True)
    
    def __str__(self):
        return f"{self.breeding.female_animal.tag} - {self.status} on {self.check_date}"

class CalvingRecord(models.Model):
    breeding = models.OneToOneField(Breeding, on_delete=models.CASCADE, related_name='calving')
    calving_date = models.DateField()
    calf = models.ForeignKey(
        Animal, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='birth_record'
    )
    
    DIFFICULTY_CHOICES = [
        ('normal', 'Normal'),
        ('assisted', 'Assisted'),
        ('difficult', 'Difficult'),
        ('veterinary', 'Veterinary Assistance')
    ]
    difficulty = models.CharField(max_length=10, choices=DIFFICULTY_CHOICES, default='normal')
    complications = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.breeding.female_animal.tag} calving on {self.calving_date}"

class AnimalEvent(models.Model):
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, related_name='events')
    title = models.CharField(max_length=200)
    description = models.TextField()
    date = models.DateField(default=date.today)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-date', '-created_at']
        
    def __str__(self):
        return f"{self.animal.tag} - {self.title} ({self.date})"

class AnimalTypeChange(models.Model):
    """
    Model to track animal type change history
    """
    animal = models.ForeignKey(Animal, on_delete=models.CASCADE, related_name='type_changes')
    from_type = models.CharField(max_length=200, blank=True, null=True)
    to_type = models.CharField(max_length=200)
    changed_date = models.DateTimeField(default=timezone.now)
    changed_by = models.ForeignKey('accounts.CustomUser', on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        ordering = ['-changed_date']
    
    def get_from_type_display(self):
        if not self.from_type:
            return "Initial"
        return dict(Animal.TYPE_CHOICES).get(self.from_type, self.from_type)
    
    def get_to_type_display(self):
        return dict(Animal.TYPE_CHOICES).get(self.to_type, self.to_type)
        
    def __str__(self):
        return f"{self.animal.tag}: {self.get_from_type_display()} → {self.get_to_type_display()} on {self.changed_date.date()}"

class AnimalScore(models.Model):
    """
    Model to track and calculate animal health/management scores based on various criteria
    """
    animal = models.OneToOneField(Animal, on_delete=models.CASCADE, related_name='score')
    
    # Individual score components
    photo_score = models.IntegerField(default=0, help_text="Score based on photo recency (max 33)")
    weight_score = models.IntegerField(default=0, help_text="Score based on weight record recency (max 33)")
    milk_score = models.IntegerField(default=0, help_text="Score based on milk record consistency (max 34)")
    
    # Total score (out of 100)
    total_score = models.IntegerField(default=0, help_text="Total score out of 100")
    
    # Metadata
    last_calculated = models.DateTimeField(auto_now=True)
    
    # Additional tracking fields
    last_photo_update = models.DateTimeField(null=True, blank=True)
    last_weight_record = models.DateTimeField(null=True, blank=True)
    last_milk_record = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-total_score']
        
    def calculate_photo_score(self):
        """Calculate score based on photo recency (33 points if updated within 60 days)"""
        if not self.animal.image:
            return 0
            
        # Check if image was updated recently
        if self.animal.image_updated:
            days_since = (timezone.now() - self.animal.image_updated).days
            if days_since <= 60:
                return 33
            elif days_since <= 90:
                return 25
            elif days_since <= 120:
                return 17
            elif days_since <= 180:
                return 8
            return 0
        else:
            # If no timestamp, give partial credit for having an image
            return 17
    
    def calculate_weight_score(self):
        """Calculate score based on weight record recency (33 points if recorded within 30 days)"""
        latest_weight = self.animal.weights.order_by('-date').first()
        if not latest_weight:
            return 0
            
        days_since = (timezone.now().date() - latest_weight.date).days
        if days_since <= 30:
            return 33
        elif days_since <= 60:
            return 17
        elif days_since <= 90:
            return 8
        return 0
    
    def calculate_milk_score(self):
        """Calculate score based on milk recording consistency (for female animals)"""
        if self.animal.sex != 'female' or self.animal.animal_type not in ['milking', 'preg_milking']:
            return 34  # Full score for non-milking animals
            
        # Calculate days since animal became a milking type
        days_as_milking = 30  # Default to 30 days
        if self.animal.type_changed_date:
            # Check if the type was changed to milking/preg_milking recently
            days_since_type_change = (timezone.now() - self.animal.type_changed_date).days
            # Use the smaller of 30 days or days since type change
            days_as_milking = min(30, days_since_type_change)
        
        # If animal has been milking type for less than 7 days, give grace period
        if days_as_milking < 7:
            return 34  # Full score during grace period
            
        # Check milk recording frequency based on actual milking days
        start_date = timezone.now().date() - timedelta(days=days_as_milking)
        recent_records = MilkRecord.objects.filter(
            animal=self.animal,
            date__gte=start_date
        ).count()
        
        # Calculate expected records (allowing for 85% compliance)
        expected_records = int(days_as_milking * 0.85)
        
        # Score based on percentage of expected records
        if recent_records >= expected_records:
            return 34
        elif recent_records >= expected_records * 0.75:
            return 26
        elif recent_records >= expected_records * 0.50:
            return 17
        elif recent_records >= expected_records * 0.25:
            return 9
        return 0
    
    # Health and Breeding scores have been removed and redistributed to other components
    
    def update_scores(self):
        """Update all component scores and calculate total"""
        self.photo_score = self.calculate_photo_score()
        self.weight_score = self.calculate_weight_score()
        self.milk_score = self.calculate_milk_score()
        
        # Calculate total score
        self.total_score = (
            self.photo_score + 
            self.weight_score + 
            self.milk_score
        )
        
        # Update tracking fields
        if self.animal.image_updated:
            self.last_photo_update = self.animal.image_updated
            
        latest_weight = self.animal.weights.order_by('-date').first()
        if latest_weight:
            self.last_weight_record = timezone.make_aware(
                datetime.combine(latest_weight.date, datetime.min.time())
            )
            
        latest_milk = MilkRecord.objects.filter(animal=self.animal).order_by('-date').first()
        if latest_milk:
            self.last_milk_record = timezone.make_aware(
                datetime.combine(latest_milk.date, datetime.min.time())
            )
            
        latest_event = self.animal.events.order_by('-date').first()
        if latest_event:
            self.last_health_check = timezone.make_aware(
                datetime.combine(latest_event.date, datetime.min.time())
            )
        
        self.save()
        
    def get_score_color(self):
        """Return color class based on score range"""
        if self.total_score >= 80:
            return 'success'  # Green
        elif self.total_score >= 60:
            return 'warning'  # Yellow
        elif self.total_score >= 40:
            return 'orange'   # Orange
        else:
            return 'danger'   # Red
            
    def get_grade(self):
        """Return letter grade based on score"""
        if self.total_score >= 90:
            return 'A+'
        elif self.total_score >= 80:
            return 'A'
        elif self.total_score >= 70:
            return 'B'
        elif self.total_score >= 60:
            return 'C'
        elif self.total_score >= 50:
            return 'D'
        else:
            return 'F'
    
    def __str__(self):
        return f"{self.animal.tag} - Score: {self.total_score}/100"


# Signal to track animal type changes
@receiver(pre_save, sender=Animal)
def track_animal_type_change(sender, instance, **kwargs):
    if instance.pk:  # Existing instance
        try:
            old_instance = Animal.objects.get(pk=instance.pk)
            if old_instance.animal_type != instance.animal_type:
                # Store the old type for post_save signal
                instance._old_type = old_instance.animal_type
        except Animal.DoesNotExist:
            pass

@receiver(post_save, sender=Animal)
def create_type_change_record(sender, instance, created, **kwargs):
    if created:
        # Create initial type record for new animals
        AnimalTypeChange.objects.create(
            animal=instance,
            from_type=None,
            to_type=instance.animal_type,
            changed_date=timezone.now()
        )
    elif hasattr(instance, '_old_type'):
        # Create type change record
        AnimalTypeChange.objects.create(
            animal=instance,
            from_type=instance._old_type,
            to_type=instance.animal_type,
            changed_date=timezone.now()
        )
        # Clean up the temporary attribute
        delattr(instance, '_old_type')
