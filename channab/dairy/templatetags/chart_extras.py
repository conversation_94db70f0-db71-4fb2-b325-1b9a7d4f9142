from django import template

register = template.Library()

@register.filter
def divide(value, arg):
    try:
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError):
        return 0

@register.filter
def multiply(value, arg):
    try:
        return float(value) * float(arg)
    except ValueError:
        return 0

@register.filter
def filter_by_sex(queryset, sex):
    return queryset.filter(sex=sex)

@register.filter
def get_item(dictionary, key):
    return dictionary.get(key, '')
