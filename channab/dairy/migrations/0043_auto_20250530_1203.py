# Generated by Django 3.2.16 on 2025-05-30 12:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0042_animalevent'),
    ]

    operations = [
        migrations.AddField(
            model_name='animal',
            name='image_updated',
            field=models.DateTimeField(blank=True, help_text='Timestamp when image was last updated', null=True),
        ),
        migrations.CreateModel(
            name='AnimalScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo_score', models.IntegerField(default=0, help_text='Score based on photo recency (max 20)')),
                ('weight_score', models.IntegerField(default=0, help_text='Score based on weight record recency (max 20)')),
                ('milk_score', models.IntegerField(default=0, help_text='Score based on milk record consistency (max 20)')),
                ('health_score', models.IntegerField(default=0, help_text='Score based on health/vaccination records (max 20)')),
                ('breeding_score', models.IntegerField(default=0, help_text='Score based on breeding record completeness (max 20)')),
                ('total_score', models.IntegerField(default=0, help_text='Total score out of 100')),
                ('last_calculated', models.DateTimeField(auto_now=True)),
                ('last_photo_update', models.DateTimeField(blank=True, null=True)),
                ('last_weight_record', models.DateTimeField(blank=True, null=True)),
                ('last_milk_record', models.DateTimeField(blank=True, null=True)),
                ('last_health_check', models.DateTimeField(blank=True, null=True)),
                ('animal', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='score', to='dairy.animal')),
            ],
            options={
                'ordering': ['-total_score'],
            },
        ),
    ]
