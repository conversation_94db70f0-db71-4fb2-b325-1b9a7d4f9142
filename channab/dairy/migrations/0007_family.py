# Generated by Django 3.2.16 on 2023-05-15 10:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_salarytransaction'),
        ('dairy', '0006_auto_20230506_1258'),
    ]

    operations = [
        migrations.CreateModel(
            name='Family',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('child', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='family', to='dairy.animal')),
                ('farm', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='families', to='accounts.farm')),
                ('father', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fathered_families', to='dairy.animal')),
                ('mother', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mothered_families', to='dairy.animal')),
            ],
        ),
    ]
