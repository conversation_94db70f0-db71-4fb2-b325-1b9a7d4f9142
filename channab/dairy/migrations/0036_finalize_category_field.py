# Generated by Django 3.2.16 on 2024-11-25 23:40

from django.db import migrations

def copy_categories_forward(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    db_alias = schema_editor.connection.alias
    
    # Update all animals with their new categories
    for animal in Animal.objects.using(db_alias).all():
        if not animal.new_category:  # Only update if new_category is not set
            old_category = animal.category.title.lower() if animal.category else ''
            
            if old_category in ['buffalo', 'baffalo']:
                animal.new_category = 'buffalo'
            elif old_category in ['cow', 'cows']:
                animal.new_category = 'cow'
            elif old_category in ['bakri', 'bakra']:
                animal.new_category = 'goat'
            elif 'sheep' in old_category:
                animal.new_category = 'sheep'
            else:
                animal.new_category = 'others'
            
            animal.save()

class Migration(migrations.Migration):
    atomic = False  # This might help with the trigger issue
    
    dependencies = [
        ('dairy', '0035_migrate_to_new_categories'),
    ]

    operations = [
        migrations.RunPython(
            copy_categories_forward,
            migrations.RunPython.noop,
            atomic=False
        ),
    ]
