# Generated by Django 3.2.16 on 2023-04-27 05:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnimalCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('slug', models.SlugField(blank=True, unique=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='animal_categories/')),
                ('farm', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='animal_categories', to='accounts.farm')),
            ],
        ),
    ]
