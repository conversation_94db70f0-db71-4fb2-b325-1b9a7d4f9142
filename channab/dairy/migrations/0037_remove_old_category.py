# Generated by Django 3.2.16 on 2024-11-25 23:46

from django.db import migrations, models

def convert_categories(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    for animal in Animal.objects.all():
        # Map old categories to new ones
        old_category = str(animal.category).lower() if animal.category else ''
        if 'cow' in old_category or 'buffalo' in old_category:
            animal.category = 'dairy'
        elif 'goat' in old_category or 'sheep' in old_category:
            animal.category = 'beef'
        else:
            animal.category = 'other'
        animal.save()

class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0036_finalize_category_field'),
    ]

    operations = [
        # First convert the category data
        migrations.RunPython(convert_categories),

        # Then update the field
        migrations.AlterField(
            model_name='animal',
            name='category',
            field=models.CharField(
                choices=[('dairy', 'Dairy'), ('beef', 'Beef'), ('other', 'Other')],
                default='dairy',
                max_length=50
            ),
        ),

        # Finally remove the AnimalCategory model
        migrations.DeleteModel(
            name='AnimalCategory',
        ),
    ]
