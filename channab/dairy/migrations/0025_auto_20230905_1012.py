# Generated by Django 3.2.16 on 2023-09-05 07:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0012_alter_customuser_role'),
        ('dairy', '0024_remove_milksale_milk_record'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='farm',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.farm'),
        ),
        migrations.AddField(
            model_name='milksale',
            name='farm',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.farm'),
        ),
    ]
