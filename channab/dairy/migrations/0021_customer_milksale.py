# Generated by Django 3.2.16 on 2023-09-04 15:47

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0020_auto_20230904_1224'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('contact_number', models.CharField(blank=True, max_length=15, null=True)),
                ('address', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='MilkSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('first_sale', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('second_sale', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('third_sale', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('price_per_liter', models.DecimalField(decimal_places=2, max_digits=5)),
                ('total_price', models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dairy.customer')),
                ('milk_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dairy.milkrecord')),
            ],
        ),
    ]
