# Generated by Django 3.2.16 on 2023-06-08 05:04

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0013_breeding'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='breeding',
            name='due_date',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='insemination_date',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='insemination_method',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='semen_info',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='test',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='test_comments',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='test_date',
        ),
        migrations.AddField(
            model_name='breeding',
            name='ai_dose_name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='breeding',
            name='breeding_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='breeding',
            name='method',
            field=models.CharField(choices=[('NATURAL', 'Natural'), ('AI', 'Artificial Insemination')], default=django.utils.timezone.now, max_length=20),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='breeding',
            name='animal',
            field=models.ForeignKey(limit_choices_to={'sex': 'F'}, on_delete=django.db.models.deletion.CASCADE, to='dairy.animal'),
        ),
        migrations.AlterField(
            model_name='breeding',
            name='bull',
            field=models.ForeignKey(blank=True, limit_choices_to={'sex': 'M'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='dairy.animal'),
        ),
        migrations.AlterField(
            model_name='breeding',
            name='doctor_name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
    ]
