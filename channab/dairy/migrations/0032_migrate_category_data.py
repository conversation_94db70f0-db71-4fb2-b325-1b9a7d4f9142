# Generated by Django 3.2.16 on 2024-11-25 07:28

from django.db import migrations


def migrate_categories(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    AnimalCategory = apps.get_model('dairy', 'AnimalCategory')
    
    for animal in Animal.objects.all():
        try:
            category = AnimalCategory.objects.get(id=animal.category_id)
            category_title = category.title.lower()
            
            # Map Buffalo categories
            if category_title in ['buffalo', 'baffalo']:
                animal.new_category = 'buffalo'
            
            # Map Cow categories
            elif category_title in ['cows', 'cow']:
                animal.new_category = 'cow'
            
            # Map Goat categories
            elif category_title in ['bakri', 'bakra']:
                animal.new_category = 'goat'
            
            # Map Sheep categories (none in current list)
            elif 'sheep' in category_title:
                animal.new_category = 'sheep'
            
            # Map everything else to others
            else:
                # This will catch: 'test', 'weighting animals', 'imad', 'others'
                animal.new_category = 'others'
            
            animal.save()
        except AnimalCategory.DoesNotExist:
            # If category doesn't exist, set to others
            animal.new_category = 'others'
            animal.save()

def reverse_migrate_categories(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    for animal in Animal.objects.all():
        animal.new_category = None
        animal.save()


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0031_animal_new_category'),
    ]

    operations = [
        migrations.RunPython(migrate_categories, reverse_migrate_categories),
    ]
