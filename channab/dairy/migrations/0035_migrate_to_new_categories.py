# Generated by Django 3.2.16 on 2024-11-25 23:36

from django.db import migrations


def migrate_to_new_categories(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    AnimalCategory = apps.get_model('dairy', 'AnimalCategory')
    
    # Create a mapping dictionary for special cases
    category_mapping = {
        'cows': 'cow',
        'cow': 'cow',
        'baffalo': 'buffalo',
        'buffalo': 'buffalo',
        'bakri': 'goat',
        'bakra': 'goat',
        'sheep': 'sheep',
        'test': 'others',
        'weighting animals': 'others',
        'imad': 'others',
        'others': 'others'
    }
    
    # Process all animals
    for animal in Animal.objects.all():
        if animal.category:
            old_category_title = animal.category.title.lower().strip()
            
            # Use mapping dictionary with fallback to 'others'
            new_category = category_mapping.get(old_category_title, 'others')
            
            # Set the new category
            animal.new_category = new_category
            animal.save()
            
            print(f"Mapped: {animal.category.title} -> {new_category}")


def reverse_migrate_categories(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    for animal in Animal.objects.all():
        animal.new_category = None
        animal.save()


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0034_auto_20241125_2334'),
    ]

    operations = [
        migrations.RunPython(migrate_to_new_categories, reverse_migrate_categories),
    ]
