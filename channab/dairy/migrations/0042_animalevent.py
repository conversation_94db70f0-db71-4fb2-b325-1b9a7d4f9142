# Generated by Django 3.2.16 on 2025-01-31 06:29

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0041_auto_20241210_0044'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnimalEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('date', models.DateField(default=datetime.date.today)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('animal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='dairy.animal')),
            ],
            options={
                'ordering': ['-date', '-created_at'],
            },
        ),
    ]
