# Generated by Django 3.2.16 on 2023-09-13 06:14

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0012_alter_customuser_role'),
        ('dairy', '0026_alter_milksale_price_per_liter'),
    ]

    operations = [
        migrations.CreateModel(
            name='MilkPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('total_milk_payment', models.DecimalField(decimal_places=2, max_digits=7)),
                ('received_payment', models.DecimalField(decimal_places=2, default=0, max_digits=7)),
                ('remaining_payment', models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True)),
                ('note', models.TextField(blank=True, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dairy.customer')),
                ('farm', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.farm')),
            ],
        ),
    ]
