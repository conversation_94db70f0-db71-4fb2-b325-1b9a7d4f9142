# Generated by Django 3.2.16 on 2025-05-30 17:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dairy', '0045_animal_type_changed_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnimalTypeChange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_type', models.CharField(blank=True, max_length=200, null=True)),
                ('to_type', models.CharField(max_length=200)),
                ('changed_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('animal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='type_changes', to='dairy.animal')),
                ('changed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-changed_date'],
            },
        ),
    ]
