# Generated by Django 3.2.16 on 2024-11-25 07:50

from django.db import migrations, models

def forward_data(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    for animal in Animal.objects.all():
        # Copy new_category value to category
        if animal.new_category:
            animal.category = animal.new_category
            animal.save()

def reverse_data(apps, schema_editor):
    Animal = apps.get_model('dairy', 'Animal')
    for animal in Animal.objects.all():
        animal.new_category = animal.category
        animal.save()

class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0032_migrate_category_data'),
    ]

    operations = [
        # First copy data from new_category to category
        migrations.RunPython(forward_data, reverse_data),
        
        # Then alter the category field
        migrations.AlterField(
            model_name='animal',
            name='category',
            field=models.CharField(choices=[('buffalo', 'Buffalo'), ('cow', 'Cow'), ('goat', 'Goat'), ('sheep', 'Sheep'), ('others', 'Others')], max_length=10),
        ),
        
        # Finally remove the temporary field
        migrations.RemoveField(
            model_name='animal',
            name='new_category',
        ),
    ]
