# Generated by Django 3.2.16 on 2024-11-26 00:15

from django.db import migrations, models

def forward_func(apps, schema_editor):
    # Get the database cursor
    cursor = schema_editor.connection.cursor()
    
    # Simple safe operation for SQLite compatibility
    cursor.execute("SELECT 1;")

class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0038_update_animal_category'),
    ]

    operations = [
        migrations.RunPython(forward_func),
        
        # Ensure the field is properly defined
        migrations.AlterField(
            model_name='animal',
            name='category',
            field=models.CharField(
                choices=[('dairy', 'Dairy'), ('beef', 'Beef'), ('other', 'Other')],
                default='dairy',
                max_length=50
            ),
        ),
    ]
