# Generated by Django 3.2.16 on 2024-12-10 00:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0040_alter_animal_category'),
    ]

    operations = [
        migrations.AddField(
            model_name='breeding',
            name='dry_off_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='breeding',
            name='expected_calving_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='breeding',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending Check'), ('successful', 'Successful'), ('failed', 'Failed'), ('aborted', 'Aborted')], default='pending', max_length=10),
        ),
        migrations.CreateModel(
            name='PregnancyCheck',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_date', models.DateField()),
                ('status', models.CharField(choices=[('pregnant', 'Pregnant'), ('not_pregnant', 'Not Pregnant'), ('uncertain', 'Uncertain')], max_length=12)),
                ('notes', models.TextField(blank=True, null=True)),
                ('breeding', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pregnancy_checks', to='dairy.breeding')),
            ],
        ),
        migrations.CreateModel(
            name='CalvingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('calving_date', models.DateField()),
                ('difficulty', models.CharField(choices=[('normal', 'Normal'), ('assisted', 'Assisted'), ('difficult', 'Difficult'), ('veterinary', 'Veterinary Assistance')], default='normal', max_length=10)),
                ('complications', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('breeding', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='calving', to='dairy.breeding')),
                ('calf', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='birth_record', to='dairy.animal')),
            ],
        ),
        migrations.CreateModel(
            name='BreedingCycle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('open', 'Open'), ('bred', 'Bred'), ('pregnant', 'Pregnant'), ('dry', 'Dry'), ('calved', 'Calved')], default='open', max_length=10)),
                ('lactation_number', models.PositiveIntegerField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('animal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='breeding_cycles', to='dairy.animal')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.AddField(
            model_name='breeding',
            name='breeding_cycle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='breedings', to='dairy.breedingcycle'),
        ),
    ]
