# Generated by Django 3.2.16 on 2023-09-03 11:32

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('dairy', '0018_auto_20230823_1029'),
    ]

    operations = [
        migrations.RenameField(
            model_name='breeding',
            old_name='comments',
            new_name='comment',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='ai_dose_name',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='animal',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='attempt_number',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='breeding_date',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='doctor_name',
        ),
        migrations.RemoveField(
            model_name='breeding',
            name='method',
        ),
        migrations.AddField(
            model_name='breeding',
            name='attempt',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='breeding',
            name='breeding_method',
            field=models.CharField(choices=[('natural', 'Natural'), ('artificial', 'Artificial')], default='natural', max_length=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='breeding',
            name='date_of_insemination',
            field=models.DateField(default=datetime.datetime.today),
        ),
        migrations.AddField(
            model_name='breeding',
            name='female_animal',
            field=models.ForeignKey(limit_choices_to={'sex': 'female'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='breeding_events', to='dairy.animal'),
        ),
        migrations.AddField(
            model_name='breeding',
            name='semen_tag',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='breeding',
            name='bull',
            field=models.ForeignKey(blank=True, limit_choices_to={'animal_type': 'breeder', 'sex': 'male'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='natural_breeding_events', to='dairy.animal'),
        ),
        migrations.AlterField(
            model_name='breeding',
            name='lactation_number',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddConstraint(
            model_name='breeding',
            constraint=models.UniqueConstraint(fields=('female_animal', 'date_of_insemination'), name='unique_animal_insemination_date'),
        ),
    ]
