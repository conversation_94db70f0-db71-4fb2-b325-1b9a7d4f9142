import json
from .models import Animal, AnimalWeight
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.urls import reverse, reverse_lazy
from .models import Animal, Breeding, Customer, MilkPayment, MilkSale, AnimalEvent
from .forms import AnimalForm, AnimalWeightForm, CustomerForm, MilkPaymentForm, MilkRecordForm, MilkSaleForm
from accounts.models import Farm
from .models import MilkRecord, Animal, AnimalWeight
from django.db.models import F
from datetime import timedelta, date, datetime
from rest_framework import viewsets, status
from django.http import JsonResponse
from calendar import monthrange
from django.db.models import Subquery, OuterRef
from django.utils import timezone
from django.db.models import Sum, Avg, Count, Case, When, Value, IntegerField, DecimalField
from django.db.models.functions import Coalesce
from .serializers import AnimalSerializer, MilkRecordSerializer, MinimalAnimalSerializer, CustomerSerializer, MilkSaleSerializer, AnimalEventSerializer
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, parser_classes, authentication_classes
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from rest_framework.authentication import TokenAuthentication
from django.db import IntegrityError
from rest_framework.decorators import action
from django.utils.timezone import make_aware
from farm_finances.models import IncomeCategory, Income
from django.conf import settings
from urllib.parse import urljoin
from django.core.exceptions import ObjectDoesNotExist
from django.core.paginator import Paginator
from django.core.serializers.json import DjangoJSONEncoder
from django.views.decorators.csrf import csrf_exempt
from rest_framework import generics, permissions
from django.db import models
from django.db.models import Q

@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_animal_update(request, pk):
    """
    Update an existing animal record.
    
    URL Parameters:
    - pk: ID of the animal to update
    
    Request Body can include:
    - tag: Animal tag/identifier
    - dob: Date of birth (YYYY-MM-DD)
    - purchase_cost: Cost of purchase
    - status: Animal status (active, expired, sold)
    - sex: Gender of animal (male, female)
    - category: Animal category (dairy, buffalo, cow, goat, sheep, beef, other)
    - animal_type: Type of animal (breeder, pregnant, dry, milking, preg_milking, calf, other)
    - mother: ID of mother animal (optional)
    - father: ID of father animal (optional)
    - image: Animal image file (optional)
    """
    try:
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the animal
        try:
            animal = Animal.objects.get(id=pk, farm=farm)
        except Animal.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Animal not found or does not belong to your farm'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get the data from the request
        data = request.data.copy()
        
        # Check for parent relationships
        if 'mother' in data and data['mother']:
            try:
                mother = Animal.objects.get(id=data['mother'], farm=farm)
                if mother.sex != 'female':
                    return Response({
                        'success': False,
                        'message': 'Mother must be a female animal'
                    }, status=status.HTTP_400_BAD_REQUEST)
            except Animal.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Mother animal not found or does not belong to your farm'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        if 'father' in data and data['father']:
            try:
                father = Animal.objects.get(id=data['father'], farm=farm)
                if father.sex != 'male':
                    return Response({
                        'success': False,
                        'message': 'Father must be a male animal'
                    }, status=status.HTTP_400_BAD_REQUEST)
            except Animal.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Father animal not found or does not belong to your farm'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create a serializer with the animal instance and data
        serializer = AnimalSerializer(animal, data=data, partial=True)
        
        if serializer.is_valid():
            # Save the updated animal
            updated_animal = serializer.save()
            
            return Response({
                'success': True,
                'message': 'Animal updated successfully',
                'data': AnimalSerializer(updated_animal).data
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': 'An unexpected error occurred',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_total_milk_list(request):
    time_filter = request.GET.get('filter', 'this_year')
    today = timezone.now().date()
    
    # Pass request to api_get_date_range
    start_date, end_date = api_get_date_range(time_filter, today, request)  # Add request parameter
    
    # Get the user's farm
    user_farm = request.user.farm
    
    milk_records = (
        MilkRecord.objects.filter(
            date__range=(start_date, end_date),
            animal__farm=user_farm  # Filter by the user's farm through animal relationship
        )
        .values('date')  
        .annotate(
            first_time_total=Coalesce(Sum('first_time', output_field=models.DecimalField(max_digits=10, decimal_places=2)), 0, output_field=models.DecimalField(max_digits=10, decimal_places=2)),
            second_time_total=Coalesce(Sum('second_time', output_field=models.DecimalField(max_digits=10, decimal_places=2)), 0, output_field=models.DecimalField(max_digits=10, decimal_places=2)),
            third_time_total=Coalesce(Sum('third_time', output_field=models.DecimalField(max_digits=10, decimal_places=2)), 0, output_field=models.DecimalField(max_digits=10, decimal_places=2)),
        )
        .order_by('date')
    )

    records_list = []
    for record in milk_records:
        date_str = record['date'].strftime('%Y-%m-%d')
        first_time_total = record['first_time_total']
        second_time_total = record['second_time_total']
        third_time_total = record['third_time_total']
        total_milk = first_time_total + second_time_total + third_time_total
        
        records_list.append({
            'date': date_str,
            'first_time': first_time_total,
            'second_time': second_time_total,
            'third_time': third_time_total,
            'total_milk': total_milk,
        })

    return Response({
        'records': records_list,
        'time_filter': time_filter
    })


from rest_framework import generics, permissions
from .models import AnimalWeight
from .serializers import AnimalWeightSerializer

from rest_framework.response import Response
from rest_framework import status

class AnimalWeightListView(generics.ListAPIView):
    serializer_class = AnimalWeightSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return AnimalWeight.objects.filter(
            animal__farm=self.request.user.farm
        ).select_related('animal').order_by('-date')

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        response_data = serializer.data
        
        return Response(response_data, status=status.HTTP_200_OK)


@csrf_exempt
@api_view(['POST'])
@authentication_classes([TokenAuthentication])
@permission_classes([IsAuthenticated])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def create_or_update_milk_record(request):
    try:
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get the data from the request
        data = request.data.copy()

        # Validate that the animal belongs to the user's farm
        animal_id = data.get('animal')
        if not animal_id:
            return Response({
                'success': False,
                'message': 'Animal ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            animal = Animal.objects.get(id=animal_id, farm=farm)
        except Animal.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Invalid animal ID or animal does not belong to your farm'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate required fields
        required_fields = ['date']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return Response({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Convert milk values to float if present
        milk_fields = ['first_time', 'second_time', 'third_time']
        for field in milk_fields:
            if field in data:
                try:
                    data[field] = float(data[field])
                except (ValueError, TypeError):
                    return Response({
                        'success': False,
                        'message': f'Invalid value for {field}. Must be a number.'
                    }, status=status.HTTP_400_BAD_REQUEST)

        serializer = MilkRecordSerializer(data=data)
        if serializer.is_valid():
            milk_record = serializer.save()
            
            return Response({
                'success': True,
                'message': 'Milk record created/updated successfully',
                'data': MilkRecordSerializer(milk_record).data
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'message': 'Validation failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'An unexpected error occurred',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_filtered_milk_animals(request):
    user = request.user
    animals = Animal.objects.filter(sex='female', animal_type__in=['milking', 'preg_milking'])
    serializer = AnimalSerializer(animals, many=True)
    return Response(serializer.data)

def api_get_date_range(filter_key, today, request):
    if filter_key == 'custom_range':
        # Get start_date and end_date from request.GET
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        
        if start_date_str and end_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                return start_date, end_date
            except ValueError as e:
                # Fall back to default dates if parsing fails
                return today, today
    elif filter_key == 'this_year':
        start_date = today.replace(month=1, day=1)
        end_date = today.replace(month=12, day=31)
    elif filter_key == 'last_7_days':
        end_date = today
        start_date = today - timedelta(days=7)
    elif filter_key == 'this_month':
        start_date = today.replace(day=1)
        if today.month == 12:
            end_date = today.replace(day=31)
        else:
            next_month = today.replace(month=today.month + 1, day=1)
            end_date = next_month - timedelta(days=1)
    else:
        # Default case
        start_date = today
        end_date = today
        
    return start_date, end_date



def calculate_totals(milk_records):
    totals = {}
    for record in milk_records:
        # Ensure the date is converted to a string here
        date_str = record['date'].isoformat() if isinstance(record['date'], date) else record['date']
        totals[date_str] = {
            'total_first_time': record['first_time_total'] or 0,
            'total_second_time': record['second_time_total'] or 0,
            'third_time_total': record['third_time_total'] or 0,
            'total_milk': record['total_milk'] or 0,
        }
    return totals


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_animal_types(request):
    farm = request.user.farm
    animal_types = Animal.objects.filter(farm=farm).values('animal_type').annotate(count=Count('animal_type'))
    return Response(animal_types)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_animals(request):
    try:
        farm = request.user.farm

        # Extract filter parameters
        animal_type_query = request.query_params.get('animal_type', None)
        status_query = request.query_params.get('status', None)
        min_months_query = request.query_params.get('minMonths', None)
        max_months_query = request.query_params.get('maxMonths', None)
        sex_query = request.query_params.get('sex', None)

        # Filter animals based on the queries
        animals = Animal.objects.filter(farm=farm)

        if animal_type_query and animal_type_query != 'All':
            animals = animals.filter(animal_type__iexact=animal_type_query)

        if status_query:
            animals = animals.filter(status__iexact=status_query)

        if min_months_query or max_months_query:
            min_months = int(min_months_query) if min_months_query else 0
            max_months = int(max_months_query) if max_months_query else 240
            age_min_date = datetime.now() - timedelta(days=max_months * 30)
            age_max_date = datetime.now() - timedelta(days=min_months * 30)
            animals = animals.filter(dob__gte=age_min_date, dob__lte=age_max_date)

        if sex_query:
            if sex_query == 'both':
                animals = animals.filter(sex__in=['male', 'female'])
            else:
                animals = animals.filter(sex__iexact=sex_query)

        serializer = AnimalSerializer(animals, many=True)
        return Response(serializer.data)

    except ObjectDoesNotExist:
        return Response({"error": "Farm not found for the user"}, status=404)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_animal(request):
    try:
        farm = request.user.farm  # Ensure the user has an associated farm
        serializer = AnimalSerializer(data=request.data)
        if serializer.is_valid():
            # Directly save the animal with the farm from the validated serializer data
            animal = serializer.save(farm=farm)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except AttributeError:
        # Handle cases where the user might not have a farm associated
        return Response({"error": "No farm associated with the user"}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_animal_types(request):
    try:
        farm = request.user.farm

        # Getting the count of each animal type
        animal_types_with_count = (
            Animal.objects.filter(farm=farm)
            .values('animal_type')
            .annotate(count=Count('animal_type'))
            .order_by('animal_type')
        )

        return Response(animal_types_with_count)
    except ObjectDoesNotExist:
        return Response({"error": "Farm not found for the user"}, status=404)    



from datetime import datetime, timedelta
from django.utils.timezone import make_aware


class AnimalDetailView(APIView):
    def get(self, request, pk):
        farm = request.user.farm
        try:
            animal = Animal.objects.get(pk=pk, farm=farm)
            serializer = AnimalSerializer(animal)

            # Time filter handling
            time_filter = request.GET.get('time_filter', 'this_month')
            start_date, end_date = self.get_date_range(time_filter, request)  # Pass request here

            # Fetch milk records based on the time filter
            milk_records = MilkRecord.objects.filter(animal=animal, date__range=(start_date, end_date))
            milk_records = milk_records.annotate(total_milk_annotation=F('first_time') + F('second_time') + F('third_time'))

            # Aggregations
            totals = milk_records.aggregate(
                total_first_time=Sum('first_time'),
                total_second_time=Sum('second_time'),
                total_third_time=Sum('third_time'),
                total_milk=Sum('total_milk_annotation')
            )

            # Calculate the number of days in the selected range
            days = (end_date - start_date).days + 1

            # Calculate average milk per day
            average_milk_per_day = 0
            if totals['total_milk'] is not None and days > 0:
                average_milk_per_day = totals['total_milk'] / days

            # Serialize milk records and prepare response data
            serialized_milk_records = MilkRecordSerializer(milk_records, many=True).data
            response_data = serializer.data
            response_data['milk_records'] = serialized_milk_records
            response_data['totals'] = totals
            response_data['average_milk_per_day'] = average_milk_per_day

            return Response(response_data)
        except Animal.DoesNotExist:
            return Response({"error": "Animal not found"}, status=status.HTTP_404_NOT_FOUND)

    # ... rest of your class code ...

    def get_date_range(self, time_filter, request):
            today = timezone.now().date()
            if time_filter == 'all':
                start_date = Animal.objects.earliest('dob').dob
                end_date = today
            elif time_filter == 'today':
                start_date = end_date = today
            elif time_filter == 'last_7_days':
                start_date = today - timedelta(days=6)
                end_date = today
            elif time_filter == 'last_30_days':
                start_date = today - timedelta(days=29)
                end_date = today
            elif time_filter == 'last_1_year':
                start_date = today - timedelta(days=364)
                end_date = today
            elif time_filter == 'this_month':
                start_date = today.replace(day=1)
                if today.month == 12:
                    end_date = today.replace(day=31)
                else:
                    next_month = today.replace(month=today.month + 1, day=1)
                    end_date = next_month - timedelta(days=1)
            elif time_filter == 'custom':
                start_date_str = request.GET.get('start_date', today.replace(day=1).isoformat())
                end_date_str = request.GET.get('end_date', today.isoformat())

                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            else:
                start_date = today.replace(day=1)  # Default to this month
                end_date = today
            return start_date, end_date


            
        
            farm = request.user.farm
            try:
                animal = Animal.objects.get(pk=pk, farm=farm)
                serializer = AnimalSerializer(animal)

                return Response(serializer.data)
            except Animal.DoesNotExist:
                return Response({"error": "Animal not found"}, status=status.HTTP_404_NOT_FOUND)
        



@api_view(['POST'])
@permission_classes([IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def api_animal_create(request):
    try:
        
        # Get the farm
        farm = get_object_or_404(Farm, admin=request.user)
        
        # Create a mutable dict from request.data
        data = {}
        for key in request.data:
            if key != 'image':
                data[key] = request.data[key]

        # Explicitly set the farm ID in the data
        data['farm'] = farm.id

        # Convert fields to lowercase
        data['status'] = data.get('status', '').lower()
        data['sex'] = data.get('sex', '').lower()
        data['animal_type'] = data.get('animal_type', '').lower()

        # Create serializer with farm context
        serializer = AnimalSerializer(data=data, context={'farm': farm})
        if serializer.is_valid():
            animal = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        import traceback
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_milk_records(request):
    try:

        if not request.user.is_authenticated:
            return Response({'error': 'User is not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)

        sort_by = request.GET.get('sort_by', 'date')
        sort_order = request.GET.get('sort_order', 'desc')
        time_filter = request.GET.get('time_filter', 'today')  # Default to last 7 days

        sort_by_mapping = {
            'tag': 'animal__tag',
            'date': 'date',
            'first_time': 'first_time',
            'second_time': 'second_time',
            'third_time': 'third_time',
            'total_milk': '',
        }
        sort_by_field = sort_by_mapping.get(sort_by, 'date')

        farm = request.user.farm
        if sort_order == 'asc':
            milk_records = MilkRecord.objects.filter(animal__farm=farm).select_related('animal').order_by(sort_by_field)
        else:
            milk_records = MilkRecord.objects.filter(animal__farm=farm).select_related('animal').order_by(F(sort_by_field).desc(nulls_last=True))

        start_date, end_date = get_date_range(time_filter)
        milk_records = milk_records.filter(date__range=[start_date, end_date])

        if sort_by == 'total_milk':
            milk_records = sorted(milk_records, key=lambda x: x.total_milk, reverse=sort_order == 'desc')

        serializer = MilkRecordSerializer(milk_records, many=True)
        return Response(serializer.data)
    except ObjectDoesNotExist:
        return Response({"error": "Farm not found for the user"}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_milking_animals(request):
    try:
        # Get animals from user's farm that are either milking or pre-milk
        animals = Animal.objects.filter(
            farm=request.user.farm,
            animal_type__in=['milking', 'preg_milking'],
            status='active'  # Only get active animals
        ).values('id', 'tag', 'animal_type')  # Only send necessary fields
        
        response_data = {
            'animals': list(animals)
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
from rest_framework.decorators import action

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import MilkRecord
from .serializers import MilkRecordSerializer

class MilkRecordViewSet(viewsets.ModelViewSet):
    serializer_class = MilkRecordSerializer
    permission_classes = [IsAuthenticated]
    queryset = MilkRecord.objects.none()  # Default empty queryset

    def get_queryset(self):
        """Filter queryset to only return records from user's farm"""
        return MilkRecord.objects.filter(animal__farm=self.request.user.farm)

    def list(self, request):
        # Get the filter parameter (support both 'filter' and 'date_filter')
        date_filter = request.query_params.get('filter') or request.query_params.get('date_filter', 'today')
        summary = request.query_params.get('summary', '').lower() == 'true'
        animal_id = request.query_params.get('animal_id', None)
        
        try:
            start_date, end_date = get_date_range(date_filter, request)
            queryset = self.get_queryset().filter(date__range=[start_date, end_date])
            
            if animal_id:
                queryset = queryset.filter(animal_id=animal_id)
            
            if summary:
                decimal_field = models.DecimalField(max_digits=10, decimal_places=2)
                
                # Get daily summaries
                daily_summaries = queryset.values('date').annotate(
                    first_time=Coalesce(Sum('first_time'), 0, output_field=decimal_field),
                    second_time=Coalesce(Sum('second_time'), 0, output_field=decimal_field),
                    third_time=Coalesce(Sum('third_time'), 0, output_field=decimal_field),
                    record_count=Count('id')
                ).order_by('date')
                
                # Format daily summaries with calculated total
                daily_data = []
                for record in daily_summaries:
                    first_time = float(record['first_time'] or 0)
                    second_time = float(record['second_time'] or 0)
                    third_time = float(record['third_time'] or 0)
                    total_milk = first_time + second_time + third_time
                    
                    daily_data.append({
                        'date': record['date'].strftime('%Y-%m-%d'),
                        'first_time': round(first_time, 2),
                        'second_time': round(second_time, 2),
                        'third_time': round(third_time, 2),
                        'total_milk': round(total_milk, 2),
                        'record_count': record['record_count']
                    })
                
                # Get overall totals
                totals = queryset.aggregate(
                    total_first_time=Coalesce(Sum('first_time'), 0, output_field=decimal_field),
                    total_second_time=Coalesce(Sum('second_time'), 0, output_field=decimal_field),
                    total_third_time=Coalesce(Sum('third_time'), 0, output_field=decimal_field),
                    record_count=Count('id')
                )
                
                # Calculate total milk
                total_first = float(totals['total_first_time'] or 0)
                total_second = float(totals['total_second_time'] or 0)
                total_third = float(totals['total_third_time'] or 0)
                total_milk = total_first + total_second + total_third
                
                # Get unique animals count
                unique_animals = queryset.values('animal').distinct().count()
                
                response_data = {
                    'daily_summary': daily_data,
                    'total_summary': {
                        'total_first_time': round(total_first, 2),
                        'total_second_time': round(total_second, 2),
                        'total_third_time': round(total_third, 2),
                        'total_milk': round(total_milk, 2),
                        'record_count': totals['record_count'],
                        'unique_animals': unique_animals
                    },
                    'date_range': {
                        'start_date': start_date.strftime('%Y-%m-%d'),
                        'end_date': end_date.strftime('%Y-%m-%d')
                    }
                }
            else:
                # Return detailed records
                serializer = self.serializer_class(queryset, many=True)
                response_data = serializer.data
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except ValueError as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": "Failed to fetch milk records"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request):
        serializer = MilkRecordSerializer(data=request.data)
        if serializer.is_valid():
            # Verify the animal belongs to user's farm
            animal = serializer.validated_data['animal']
            if animal.farm != request.user.farm:
                return Response(
                    {"error": "You can only create milk records for animals in your farm"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None):
        try:
            record = self.get_queryset().get(pk=pk)
            serializer = MilkRecordSerializer(record, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except MilkRecord.DoesNotExist:
            return Response(
                {"error": "Milk record not found or does not belong to your farm"}, 
                status=status.HTTP_404_NOT_FOUND
            )

    def destroy(self, request, pk=None):
        try:
            record = self.get_queryset().get(pk=pk)
            record.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except MilkRecord.DoesNotExist:
            return Response(
                {"error": "Milk record not found or does not belong to your farm"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_milk_record_by_animal_id(request, animal_id):
    date = request.query_params.get('date')
    if not date:
        return Response({"error": "Date parameter is required"}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        animal = Animal.objects.get(pk=animal_id)
        try:
            milk_record = MilkRecord.objects.get(animal=animal, date=date)
            serializer = MilkRecordSerializer(milk_record)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except MilkRecord.DoesNotExist:
            return Response({"error": "Milk record not found"}, status=status.HTTP_404_NOT_FOUND)
    except Animal.DoesNotExist:
        return Response({"error": "Animal not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_get_animal_milk_record(request):
    """Get milk record for a specific animal and date if it exists"""
    animal_id = request.GET.get('animal_id')
    date_str = request.GET.get('date')
    
    if not animal_id or not date_str:
        return Response(
            {"error": "Both animal_id and date are required"}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        # Verify the animal belongs to user's farm
        animal = Animal.objects.get(
            id=animal_id, 
            farm=request.user.farm
        )
        
        # Try to get existing milk record
        milk_record = MilkRecord.objects.filter(
            animal=animal,
            date=date_str
        ).values(
            'id', 
            'first_time', 
            'second_time', 
            'third_time'
        ).first()
        
        response_data = milk_record if milk_record else {
            'first_time': None,
            'second_time': None,
            'third_time': None
        }
        return Response(response_data, status=status.HTTP_200_OK)
            
    except Animal.DoesNotExist:
        return Response(
            {"error": "Animal not found or does not belong to your farm"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to fetch milk record"}, 
            status=status.HTTP_400_BAD_REQUEST
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def api_get_milking_animals(request):
    """Get list of animals that are in milking or pre-milk stage"""
    try:
        # Get animals from user's farm that are either milking or pre-milk
        animals = Animal.objects.filter(
            farm=request.user.farm,
            animal_type__in=['milking', 'preg_milking'],
            status='active'  # Only get active animals
        ).order_by('tag')
        
        animals_data = []
        for animal in animals:
            animals_data.append({
                'id': animal.id,
                'tag': animal.tag,
                'animal_type': animal.animal_type
            })
        
        return Response({'animals': animals_data})
    except Exception as e:
        return Response(
            {"error": "Failed to fetch milking animals"}, 
            status=status.HTTP_400_BAD_REQUEST
        )

from rest_framework.decorators import action

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import MilkRecord
from .serializers import MilkRecordSerializer

def get_date_range(time_filter, request=None):
    """
    Returns a tuple of start and end dates for a specific time filter.
    Uses client's timezone offset if provided in the request headers.
    """
    # Get current time in UTC
    current_utc = timezone.now()
    
    # Get client's timezone offset from request headers (in minutes)
    client_tz_offset = None
    if request and request.headers.get('X-Timezone-Offset'):
        try:
            client_tz_offset = int(request.headers.get('X-Timezone-Offset'))
        except (ValueError, TypeError):
            pass
    
    # If client timezone is provided, use it; otherwise fallback to UTC
    if client_tz_offset is not None:
        client_tz = timezone.get_fixed_timezone(client_tz_offset)
        current_date = timezone.localtime(current_utc, client_tz).date()
    else:
        # Fallback to UTC if no client timezone provided
        current_date = current_utc.date()
    
    # Default end_date to current date unless modified by specific filters
    end_date = current_date
    
    if time_filter == 'custom':
        if not request or not request.query_params.get('start_date') or not request.query_params.get('end_date'):
            raise ValueError("Custom date range requires both start_date and end_date parameters")
        try:
            start_date = datetime.strptime(request.query_params['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(request.query_params['end_date'], '%Y-%m-%d').date()
            if start_date > end_date:
                raise ValueError("Start date cannot be after end date")
            if end_date > current_date:
                raise ValueError("End date cannot be in the future")
            return start_date, end_date
        except ValueError as e:
            if str(e) in ["Start date cannot be after end date", "End date cannot be in the future"]:
                raise
            raise ValueError("Invalid date format. Use YYYY-MM-DD")
    
    if time_filter == 'all':
        # For 'all', we'll use a very old start date
        start_date = end_date - timedelta(days=3650)  # Approximately 10 years ago
    elif time_filter == 'last_7_days':
        start_date = end_date - timedelta(days=7)
    elif time_filter in ['last_day', 'today']:
        start_date = end_date
    elif time_filter == 'yesterday':
        end_date = end_date - timedelta(days=1)
        start_date = end_date
    elif time_filter == 'this_week':
        # Get the start of the week (Monday)
        start_date = end_date - timedelta(days=end_date.weekday())
    elif time_filter == 'four_months':
        start_date = end_date - timedelta(days=120)
    elif time_filter == 'one_year':
        start_date = end_date - timedelta(days=365)
    elif time_filter == 'this_month':
        start_date = end_date.replace(day=1)
    elif time_filter == 'last_month':
        end_date = end_date.replace(day=1) - timedelta(days=1)
        start_date = end_date.replace(day=1)
    elif time_filter == 'this_year':
        start_date = end_date.replace(month=1, day=1)
    else:
        raise ValueError(f"Invalid time filter: {time_filter}. Valid options are: all, last_7_days, today, yesterday, this_week, this_year, four_months, one_year, this_month, last_month, custom")

    return start_date, end_date

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_milk_record_create(request):
    if request.method != 'POST':
        return Response({"error": "Only POST method is allowed"}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
        
    serializer = MilkRecordSerializer(data=request.data)
    if serializer.is_valid():
        # Validate that the animal belongs to the user's farm
        animal = serializer.validated_data['animal']
        if animal.farm != request.user.farm:
            return Response(
                {"error": "You can only create milk records for animals in your farm"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        try:
            milk_record = serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST', 'GET'])
@permission_classes([IsAuthenticated])
@parser_classes([MultiPartParser, FormParser])
def api_animal_milk_create_update(request):
    farm = request.user.farm

    if request.method == "POST":
        serializer = MilkRecordSerializer(data=request.data)
        if serializer.is_valid():
            animal = serializer.validated_data['animal']
            date = serializer.validated_data['date']
            
            # Ensure the animal belongs to the user's farm
            if animal.farm != farm:
                return Response({"error": "This animal does not belong to your farm."}, status=status.HTTP_403_FORBIDDEN)
            
            milk_record, created = MilkRecord.objects.get_or_create(animal=animal, date=date)
            
            serializer = MilkRecordSerializer(milk_record, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == "GET":
        animal_id = request.GET.get('animal')
        date = request.GET.get('date')
        
        if animal_id and date:
            animal = get_object_or_404(Animal, pk=animal_id, farm=farm)
            milk_record, created = MilkRecord.objects.get_or_create(animal=animal, date=date)
            serializer = MilkRecordSerializer(milk_record)
            return Response(serializer.data)
        else:
            return Response({"error": "Both animal_id and date are required for GET request."}, 
                            status=status.HTTP_400_BAD_REQUEST)

    return Response({"error": "Invalid request method."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_animal_events(request, animal_id):
    """
    Get all events for a specific animal
    """
    events = AnimalEvent.objects.filter(animal_id=animal_id)
    from .serializers import AnimalEventSerializer
    serializer = AnimalEventSerializer(events, many=True)
    return Response(serializer.data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_farm_animal_events(request):
    """
    Get all animal events for the logged-in farm
    """
    # Get the farm of the logged-in user
    farm = request.user.farm
    
    # Get all animals belonging to the farm
    farm_animals = Animal.objects.filter(farm=farm).values_list('id', flat=True)
    
    # Get all events for these animals
    events = AnimalEvent.objects.filter(animal_id__in=farm_animals)
    
    # Optional filtering by date range
    start_date = request.query_params.get('start_date', None)
    end_date = request.query_params.get('end_date', None)
    
    if start_date:
        events = events.filter(date__gte=start_date)
    if end_date:
        events = events.filter(date__lte=end_date)
    
    # Optional filtering by event title
    event_title = request.query_params.get('title', None)
    if event_title:
        events = events.filter(title__icontains=event_title)
    
    # Order by date (most recent first)
    events = events.order_by('-date')
    
    from .serializers import AnimalEventSerializer
    serializer = AnimalEventSerializer(events, many=True)
    
    return Response({
        'success': True,
        'count': events.count(),
        'events': serializer.data
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_animal_event(request, animal_id):
    """
    Create a new event for a specific animal
    """
    data = request.data.copy()
    data['animal'] = animal_id
    
    serializer = AnimalEventSerializer(data=data)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def animal_event_detail(request, event_id):
    """
    Retrieve, update or delete an animal event
    """
    event = get_object_or_404(AnimalEvent, id=event_id)
    
    if request.method == 'GET':
        serializer = AnimalEventSerializer(event)
        return Response(serializer.data)
    
    elif request.method == 'PUT':
        serializer = AnimalEventSerializer(event, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    elif request.method == 'DELETE':
        event.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_animals_by_type(request):
    """
    Get animals filtered by category, type, and age.
    Query parameters:
    - category: optional, filters animals by category
    - min_age: optional, minimum age in months
    - max_age: optional, maximum age in months
    - animal_type: optional, specific animal type to filter
    """
    try:
        # Get the user's farm
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get query parameters
        category = request.GET.get('category', None)
        min_age = request.GET.get('min_age', None)
        max_age = request.GET.get('max_age', None)
        specific_type = request.GET.get('animal_type', None)

        # Base queryset filtered by farm and active status
        queryset = Animal.objects.filter(farm=farm, status='active')

        # Apply category filter if provided
        if category:
            queryset = queryset.filter(category=category)

        # Apply age filters if provided
        today = timezone.now().date()
        if min_age:
            try:
                min_age_months = int(min_age)
                min_date = today - timedelta(days=min_age_months * 30)
                queryset = queryset.filter(dob__lte=min_date)
            except ValueError:
                pass

        if max_age:
            try:
                max_age_months = int(max_age)
                max_date = today - timedelta(days=max_age_months * 30)
                queryset = queryset.filter(dob__gte=max_date)
            except ValueError:
                pass

        # Apply specific animal type filter if provided
        if specific_type:
            queryset = queryset.filter(animal_type=specific_type)

        # Get unique animal types from the filtered queryset
        animal_types = queryset.values_list('animal_type', flat=True).distinct()
        
        # Create response data
        response_data = []
        for animal_type in animal_types:
            animals = queryset.filter(animal_type=animal_type)
            animals_data = MinimalAnimalSerializer(animals, many=True).data
            
            response_data.append({
                'type': animal_type,
                'type_display': Animal.TYPE_CHOICES_DICT.get(animal_type, animal_type),
                'animals': animals_data
            })

        return Response({
            'success': True,
            'data': response_data,
            'filters_applied': {
                'category': category,
                'min_age': min_age,
                'max_age': max_age,
                'animal_type': specific_type,
                'status': 'active'
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def milk_sale_totals(request):
    time_filter = request.GET.get('time_filter', 'all')
    
    try:
        # Get the date range based on the filter
        start_date, end_date = get_date_range(time_filter)
        
        # Get the user's farm
        user_farm = request.user.farm
        
        # Create a decimal field with appropriate precision
        decimal_field = models.DecimalField(max_digits=10, decimal_places=2)
        zero_decimal = models.Value(0, output_field=decimal_field)
        
        # Query milk sales for the date range with explicit output fields
        milk_sales = MilkSale.objects.filter(
            date__gte=start_date,
            date__lte=end_date,
            farm=user_farm
        ).aggregate(
            total_first=Coalesce(Sum('first_sale', output_field=decimal_field), zero_decimal),
            total_second=Coalesce(Sum('second_sale', output_field=decimal_field), zero_decimal),
            total_third=Coalesce(Sum('third_sale', output_field=decimal_field), zero_decimal),
            total_price=Coalesce(Sum('total_price', output_field=decimal_field), zero_decimal)
        )
        
        # Calculate total liters
        total_liters = (
            milk_sales['total_first'] +
            milk_sales['total_second'] +
            milk_sales['total_third']
        )
        
        return Response({
            'total_first': float(milk_sales['total_first']),
            'total_second': float(milk_sales['total_second']),
            'total_third': float(milk_sales['total_third']),
            'total_liters': float(total_liters),
            'total_price': float(milk_sales['total_price'])
        })
    except ValueError as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_customer_list(request):
    """
    Get list of customers with their total milk and payment details.
    Query parameters:
    - search: Search by customer name
    - sort_by: Sort by field (name, total_milk, total_amount)
    - sort_order: asc or desc
    - time_filter: Filter by time period (this_month, 7_days, last_month, this_year, custom)
    - start_date: Start date for custom filter (YYYY-MM-DD)
    - end_date: End date for custom filter (YYYY-MM-DD)
    """
    try:
        # Get query parameters
        search_query = request.GET.get('search', '')
        sort_by = request.GET.get('sort_by', 'name')
        sort_order = request.GET.get('sort_order', 'asc')
        time_filter = request.GET.get('time_filter', 'this_month')
        start_date = request.GET.get('start_date', None)
        end_date = request.GET.get('end_date', None)

        # Base queryset filtered by user's farm
        customers = Customer.objects.filter(farm=request.user.farm)

        # Apply search filter
        if search_query:
            customers = customers.filter(name__icontains=search_query)

        # Calculate date range based on time_filter
        today = timezone.now().date()
        
        if time_filter == 'this_month':
            start_date = today.replace(day=1)
            # Get the last day of the current month
            next_month = today.replace(day=28) + timedelta(days=4)
            end_date = next_month - timedelta(days=next_month.day)
        elif time_filter == '7_days':
            start_date = today - timedelta(days=7)
            end_date = today
        elif time_filter == 'last_month':
            # First day of previous month
            first_day_current_month = today.replace(day=1)
            last_day_previous_month = first_day_current_month - timedelta(days=1)
            start_date = last_day_previous_month.replace(day=1)
            end_date = last_day_previous_month
        elif time_filter == 'this_year':
            start_date = today.replace(month=1, day=1)
            end_date = today
        elif time_filter == 'custom':
            # Use the provided start_date and end_date
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # Filter milk sales by date range if applicable
        date_filter = {}
        if start_date:
            date_filter['milksale__date__gte'] = start_date
        if end_date:
            date_filter['milksale__date__lte'] = end_date

        # Add annotations for total milk and amount with date filtering
        if date_filter:
            customers = customers.annotate(
                total_milk=Coalesce(Sum(
                    Case(
                        When(
                            milksale__date__gte=start_date,
                            milksale__date__lte=end_date,
                            then=F('milksale__first_sale') + 
                                F('milksale__second_sale') + 
                                F('milksale__third_sale')
                        ),
                        default=Value(0),
                        output_field=DecimalField()
                    )
                ), Value(0), output_field=DecimalField()),
                total_amount=Coalesce(Sum(
                    Case(
                        When(
                            milksale__date__gte=start_date,
                            milksale__date__lte=end_date,
                            then=F('milksale__total_price')
                        ),
                        default=Value(0),
                        output_field=DecimalField()
                    )
                ), Value(0), output_field=DecimalField()),
                price_per_liter=Avg('milksale__price_per_liter'),
                filtered_sales_count=Count(
                    Case(
                        When(
                            milksale__date__gte=start_date,
                            milksale__date__lte=end_date,
                            then=1
                        ),
                        output_field=IntegerField()
                    )
                )
            )
        else:
            # Default annotations without date filtering
            customers = customers.annotate(
                total_milk=Coalesce(Sum(
                    F('milksale__first_sale') + 
                    F('milksale__second_sale') + 
                    F('milksale__third_sale')
                ), Value(0), output_field=DecimalField()),
                total_amount=Coalesce(Sum('milksale__total_price'), Value(0), output_field=DecimalField()),
                price_per_liter=Avg('milksale__price_per_liter'),
                filtered_sales_count=Count('milksale')
            )

        # Apply sorting
        sort_field = {
            'name': 'name',
            'total_milk': 'total_milk',
            'total_amount': 'total_amount',
            'created_date': 'created_date'
        }.get(sort_by, 'name')

        if sort_order == 'desc':
            sort_field = f'-{sort_field}'
        
        customers = customers.order_by(sort_field)

        # Serialize the data
        serializer = CustomerSerializer(customers, many=True)

        return Response({
            'success': True,
            'data': serializer.data,
            'meta': {
                'time_filter': time_filter,
                'start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                'end_date': end_date.strftime('%Y-%m-%d') if end_date else None,
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_customer_detail(request, pk):
    """
    Get detailed information about a specific customer with milk sales data.
    """
    try:
        # Get the customer
        try:
            customer = Customer.objects.get(id=pk, farm=request.user.farm)
        except Customer.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Customer not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get query parameters
        time_filter = request.GET.get('time_filter', 'this_month')
        start_date = request.GET.get('start_date', None)
        end_date = request.GET.get('end_date', None)

        # Calculate date range based on time_filter
        today = timezone.now().date()
        
        if time_filter == 'this_month':
            start_date = today.replace(day=1)
            # Get the last day of the current month
            next_month = today.replace(day=28) + timedelta(days=4)
            end_date = next_month - timedelta(days=next_month.day)
        elif time_filter == '7_days':
            start_date = today - timedelta(days=7)
            end_date = today
        elif time_filter == 'last_month':
            # First day of previous month
            first_day_current_month = today.replace(day=1)
            last_day_previous_month = first_day_current_month - timedelta(days=1)
            start_date = last_day_previous_month.replace(day=1)
            end_date = last_day_previous_month
        elif time_filter == 'this_year':
            start_date = today.replace(month=1, day=1)
            end_date = today
        elif time_filter == 'custom':
            # Use the provided start_date and end_date
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # Get milk sales within the date range
        milk_sales = MilkSale.objects.filter(customer=customer)
        
        if start_date:
            milk_sales = milk_sales.filter(date__gte=start_date)
        if end_date:
            milk_sales = milk_sales.filter(date__lte=end_date)
            
        # Sort by date in descending order (newest first)
        milk_sales = milk_sales.order_by('-date')
        
        # Calculate totals
        total_liters = milk_sales.aggregate(
            total=Coalesce(Sum(
                F('first_sale') + F('second_sale') + F('third_sale')
            ), Value(0), output_field=DecimalField())
        )['total'] or 0
        
        total_amount = milk_sales.aggregate(
            total=Coalesce(Sum('total_price'), Value(0), output_field=DecimalField())
        )['total'] or 0
        
        avg_price_per_liter = milk_sales.aggregate(
            avg=Avg('price_per_liter')
        )['avg'] or 0
        
        # Serialize the customer data
        customer_serializer = CustomerSerializer(customer)
        
        # Serialize the milk sales data
        milk_sales_serializer = MilkSaleSerializer(milk_sales, many=True)
        
        # Prepare response data
        response_data = {
            'success': True,
            'customer': customer_serializer.data,
            'milk_sales': milk_sales_serializer.data,
            'summary': {
                'filtered_sales_count': milk_sales.count(),
                'total_liters': float(total_liters),
                'price_per_liter': float(avg_price_per_liter),
                'filtered_total_sale': float(total_amount)
            },
            'meta': {
                'time_filter': time_filter,
                'start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                'end_date': end_date.strftime('%Y-%m-%d') if end_date else None,
            }
        }
        
        # Log response data for debugging
        
        if milk_sales.count() > 0:
            for i, sale in enumerate(milk_sales_serializer.data):
                first_sale = float(sale.get('first_sale', 0)) if sale.get('first_sale') is not None else 0
                second_sale = float(sale.get('second_sale', 0)) if sale.get('second_sale') is not None else 0
                third_sale = float(sale.get('third_sale', 0)) if sale.get('third_sale') is not None else 0
                total_liters = first_sale + second_sale + third_sale
                total_price = sale.get('total_price', 0) or 0
        
        
        return Response(response_data)
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_milk_sale_create(request):
    """
    Create a new milk sale record for a customer.
    If a record already exists for the customer on the given date, it will be updated.
    
    Required fields:
    - customer_id: ID of the customer
    - date: Date of the sale (YYYY-MM-DD)
    - first_sale: First sale amount in liters (optional)
    - second_sale: Second sale amount in liters (optional)
    - third_sale: Third sale amount in liters (optional)
    - price_per_liter: Price per liter
    """
    try:
        # Log the request data for debugging
        
        # Ensure the user has an associated farm
        if not hasattr(request.user, 'farm'):
            return Response({
                'success': False,
                'message': 'User does not have an associated farm.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get data from request
        customer_id = request.data.get('customer_id')
        
        # Also check for 'customer' field if customer_id is not provided
        if not customer_id and request.data.get('customer'):
            customer_val = request.data.get('customer')
            if isinstance(customer_val, dict) and 'id' in customer_val:
                customer_id = customer_val['id']
            elif isinstance(customer_val, int) or (isinstance(customer_val, str) and customer_val.isdigit()):
                customer_id = int(customer_val)
        
        date = request.data.get('date')
        first_sale = request.data.get('first_sale')
        second_sale = request.data.get('second_sale')
        third_sale = request.data.get('third_sale')
        price_per_liter = request.data.get('price_per_liter')
        
        # Log extracted values
        
        # Validate required fields
        if not customer_id:
            return Response({
                'success': False,
                'message': 'Customer ID is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not date:
            return Response({
                'success': False,
                'message': 'Date is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if price_per_liter is None:
            return Response({
                'success': False,
                'message': 'Price per liter is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if at least one sale amount is provided
        if first_sale in (None, '') and second_sale in (None, '') and third_sale in (None, ''):
            return Response({
                'success': False,
                'message': 'At least one sale amount (first_sale, second_sale, or third_sale) is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the customer
        try:
            customer = Customer.objects.get(id=customer_id, farm=request.user.farm)
        except Customer.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Customer not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Parse date
        try:
            if isinstance(date, str):
                sale_date = datetime.strptime(date, '%Y-%m-%d').date()
            else:
                sale_date = date  # Assume it's already a date object
        except ValueError:
            return Response({
                'success': False,
                'message': 'Invalid date format. Use YYYY-MM-DD.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Convert sale amounts to decimal or None
        try:
            first_sale_decimal = float(first_sale) if first_sale not in (None, '') else None
            second_sale_decimal = float(second_sale) if second_sale not in (None, '') else None
            third_sale_decimal = float(third_sale) if third_sale not in (None, '') else None
            price_per_liter_decimal = float(price_per_liter)
        except ValueError:
            return Response({
                'success': False,
                'message': 'Invalid numeric value.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Calculate total price
        total_liters = 0
        if first_sale_decimal:
            total_liters += first_sale_decimal
        if second_sale_decimal:
            total_liters += second_sale_decimal
        if third_sale_decimal:
            total_liters += third_sale_decimal
        
        total_price = total_liters * price_per_liter_decimal
        
        # Check if a milk sale record already exists for this customer and date
        existing_milk_sale = MilkSale.objects.filter(
            customer=customer,
            date=sale_date,
            farm=request.user.farm
        )
        
        # Log the existing records for debugging
        if existing_milk_sale.exists():
            pass
        is_update = False
        if existing_milk_sale.exists():
            # If multiple records exist for the same date (which shouldn't happen),
            # update the first one and delete the others
            if existing_milk_sale.count() > 1:
                # Keep the first record and update it
                milk_sale = existing_milk_sale.first()
                # Delete all other records for this customer and date
                existing_milk_sale.exclude(id=milk_sale.id).delete()
            else:
                milk_sale = existing_milk_sale.first()
            
            # Update existing record
            is_update = True
            milk_sale.first_sale = first_sale_decimal
            milk_sale.second_sale = second_sale_decimal
            milk_sale.third_sale = third_sale_decimal
            milk_sale.price_per_liter = price_per_liter_decimal
            milk_sale.total_price = total_price
            milk_sale.save()
            
        else:
            # Create new milk sale record
            milk_sale = MilkSale.objects.create(
                customer=customer,
                date=sale_date,
                first_sale=first_sale_decimal,
                second_sale=second_sale_decimal,
                third_sale=third_sale_decimal,
                price_per_liter=price_per_liter_decimal,
                total_price=total_price,
                farm=request.user.farm
            )
        
        # Serialize and return the created/updated milk sale
        serializer = MilkSaleSerializer(milk_sale)
        
        return Response({
            'success': True,
            'message': 'Milk sale updated successfully.' if is_update else 'Milk sale created successfully.',
            'is_update': is_update,
            'data': serializer.data
        }, status=status.HTTP_200_OK if is_update else status.HTTP_201_CREATED)
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_customer_milk_sales(request, customer_id):
    """
    Get all milk sales for a specific customer with filtering options.
    
    Query parameters:
    - time_filter: Filter by time period (this_month, 7_days, last_month, this_year, custom)
    - start_date: Start date for custom filter (YYYY-MM-DD)
    - end_date: End date for custom filter (YYYY-MM-DD)
    - sort_by: Sort by field (date, total_price)
    - sort_order: Sort order (asc, desc)
    """
    try:
        # Get query parameters
        time_filter = request.GET.get('time_filter', 'this_month')
        start_date = request.GET.get('start_date', None)
        end_date = request.GET.get('end_date', None)
        sort_by = request.GET.get('sort_by', 'date')
        sort_order = request.GET.get('sort_order', 'desc')
        
        # Get the customer
        try:
            customer = Customer.objects.get(id=customer_id, farm=request.user.farm)
        except Customer.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Customer not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Calculate date range based on time_filter
        today = timezone.now().date()
        
        if time_filter == 'this_month':
            start_date = today.replace(day=1)
            # Get the last day of the current month
            next_month = today.replace(day=28) + timedelta(days=4)
            end_date = next_month - timedelta(days=next_month.day)
        elif time_filter == '7_days':
            start_date = today - timedelta(days=7)
            end_date = today
        elif time_filter == 'last_month':
            # First day of previous month
            first_day_current_month = today.replace(day=1)
            last_day_previous_month = first_day_current_month - timedelta(days=1)
            start_date = last_day_previous_month.replace(day=1)
            end_date = last_day_previous_month
        elif time_filter == 'this_year':
            start_date = today.replace(month=1, day=1)
            end_date = today
        elif time_filter == 'custom':
            # Use the provided start_date and end_date
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # Get milk sales for the customer with date filtering
        milk_sales = MilkSale.objects.filter(customer=customer)
        
        if start_date:
            milk_sales = milk_sales.filter(date__gte=start_date)
        if end_date:
            milk_sales = milk_sales.filter(date__lte=end_date)
        
        # Apply sorting
        sort_field = {
            'date': 'date',
            'total_price': 'total_price',
            'first_sale': 'first_sale',
            'second_sale': 'second_sale',
            'third_sale': 'third_sale',
        }.get(sort_by, 'date')
        
        if sort_order.lower() != 'asc':  # Default to descending order if not explicitly set to 'asc'
            sort_field = f'-{sort_field}'
        
        milk_sales = milk_sales.order_by(sort_field)
        
        # Calculate summary statistics
        total_liters = milk_sales.aggregate(
            total=Coalesce(Sum(
                F('first_sale') + F('second_sale') + F('third_sale')
            ), Value(0), output_field=DecimalField())
        )['total'] or 0
        
        total_amount = milk_sales.aggregate(
            total=Coalesce(Sum('total_price'), Value(0), output_field=DecimalField())
        )['total'] or 0
        
        avg_price_per_liter = milk_sales.aggregate(
            avg=Avg('price_per_liter')
        )['avg'] or 0
        
        # Serialize the milk sales
        serializer = MilkSaleSerializer(milk_sales, many=True)
        
        # Prepare response data
        response_data = {
            'success': True,
            'data': serializer.data,
            'summary': {
                'customer_id': customer.id,
                'customer_name': customer.name,
                'filtered_sales_count': milk_sales.count(),
                'total_liters': float(total_liters),
                'price_per_liter': float(avg_price_per_liter),
                'filtered_total_sale': float(total_amount)
            },
            'meta': {
                'time_filter': time_filter,
                'start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                'end_date': end_date.strftime('%Y-%m-%d') if end_date else None,
            }
        }
        
        # Log response data for debugging
        
        if milk_sales.count() > 0:
            for i, sale in enumerate(serializer.data):
                first_sale = float(sale.get('first_sale', 0)) if sale.get('first_sale') is not None else 0
                second_sale = float(sale.get('second_sale', 0)) if sale.get('second_sale') is not None else 0
                third_sale = float(sale.get('third_sale', 0)) if sale.get('third_sale') is not None else 0
                total_liters = first_sale + second_sale + third_sale
                total_price = sale.get('total_price', 0) or 0
        
        
        return Response(response_data)
        
    except Customer.DoesNotExist:
        return Response({
            'success': False,
            'message': 'Customer not found.'
        }, status=status.HTTP_404_NOT_FOUND)
    except ValueError as e:
        return Response({
            'success': False,
            'message': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_check_milk_sale_exists(request):
    """
    Check if milk sale data exists for a customer on a specific date.
    
    Query parameters:
    - customer_id: ID of the customer
    - date: Date to check (YYYY-MM-DD)
    
    Returns:
    - exists: Boolean indicating if data exists
    - data: Milk sale data if exists, null otherwise
    """
    try:
        # Get query parameters
        customer_id = request.GET.get('customer_id')
        date = request.GET.get('date')
        
        # Log request data for debugging
        
        # Validate required parameters
        if not customer_id:
            return Response({
                'success': False,
                'message': 'Customer ID is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not date:
            return Response({
                'success': False,
                'message': 'Date is required.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Parse date
        try:
            sale_date = datetime.strptime(date, '%Y-%m-%d').date()
        except ValueError:
            return Response({
                'success': False,
                'message': 'Invalid date format. Use YYYY-MM-DD.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the customer
        try:
            customer = Customer.objects.get(id=customer_id, farm=request.user.farm)
        except Customer.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Customer not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check if milk sale exists for the customer on the given date
        milk_sale = MilkSale.objects.filter(
            customer=customer,
            date=sale_date,
            farm=request.user.farm
        ).first()
        
        if milk_sale:
            # Milk sale exists, return the data
            serializer = MilkSaleSerializer(milk_sale)
            return Response({
                'success': True,
                'exists': True,
                'message': 'Milk sale data found for the specified date.',
                'data': serializer.data
            })
        else:
            # No milk sale exists
            return Response({
                'success': True,
                'exists': False,
                'message': 'No milk sale data found for the specified date.',
                'data': None
            })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_milk_sale_update(request, milk_sale_id):
    """
    Update an existing milk sale record.
    
    URL Parameters:
    - milk_sale_id: ID of the milk sale to update
    
    Request Body:
    - date: Date of the sale (YYYY-MM-DD)
    - first_sale: First sale amount in liters
    - second_sale: Second sale amount in liters
    - third_sale: Third sale amount in liters
    - price_per_liter: Price per liter
    """
    try:
        # Log the request data for debugging
        
        # Get the milk sale record
        try:
            milk_sale = MilkSale.objects.get(id=milk_sale_id, farm=request.user.farm)
        except MilkSale.DoesNotExist:
            return Response({
                'success': False,
                'message': f'Milk sale with ID {milk_sale_id} not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get data from request
        data = request.data
        date = data.get('date')
        first_sale = data.get('first_sale')
        second_sale = data.get('second_sale')
        third_sale = data.get('third_sale')
        price_per_liter = data.get('price_per_liter')
        
        # Update fields if provided
        if date:
            try:
                if isinstance(date, str):
                    sale_date = datetime.strptime(date, '%Y-%m-%d').date()
                else:
                    sale_date = date  # Assume it's already a date object
                milk_sale.date = sale_date
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid date format. Use YYYY-MM-DD.'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Update numeric fields if provided
        try:
            if first_sale is not None:
                milk_sale.first_sale = float(first_sale) if first_sale != '' else None
            
            if second_sale is not None:
                milk_sale.second_sale = float(second_sale) if second_sale != '' else None
            
            if third_sale is not None:
                milk_sale.third_sale = float(third_sale) if third_sale != '' else None
            
            if price_per_liter is not None:
                milk_sale.price_per_liter = float(price_per_liter)
        except ValueError:
            return Response({
                'success': False,
                'message': 'Invalid numeric value.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Calculate total price
        total_liters = 0
        if milk_sale.first_sale:
            total_liters += milk_sale.first_sale
        if milk_sale.second_sale:
            total_liters += milk_sale.second_sale
        if milk_sale.third_sale:
            total_liters += milk_sale.third_sale
        
        milk_sale.total_price = total_liters * milk_sale.price_per_liter
        
        # Save the updated milk sale
        milk_sale.save()
        
        # Serialize and return the updated milk sale
        serializer = MilkSaleSerializer(milk_sale)
        
        return Response({
            'success': True,
            'message': 'Milk sale updated successfully.',
            'data': serializer.data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_milk_sale_delete(request, milk_sale_id):
    """
    Delete a milk sale record.
    
    URL Parameters:
    - milk_sale_id: ID of the milk sale to delete
    """
    try:
        # Log the request
        
        # Get the milk sale record
        try:
            milk_sale = MilkSale.objects.get(id=milk_sale_id, farm=request.user.farm)
        except MilkSale.DoesNotExist:
            return Response({
                'success': False,
                'message': f'Milk sale with ID {milk_sale_id} not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Store some info for the response
        customer_id = milk_sale.customer.id
        customer_name = milk_sale.customer.name
        sale_date = milk_sale.date
        
        # Delete the milk sale
        milk_sale.delete()
        
        return Response({
            'success': True,
            'message': f'Milk sale for customer {customer_name} on {sale_date} deleted successfully.',
            'data': {
                'milk_sale_id': milk_sale_id,
                'customer_id': customer_id,
                'customer_name': customer_name,
                'date': sale_date
            }
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_milk_sale_detail(request, milk_sale_id):
    """
    Get details of a specific milk sale.
    
    URL Parameters:
    - milk_sale_id: ID of the milk sale to retrieve
    """
    try:
        # Log the request
        
        # Get the milk sale record
        try:
            milk_sale = MilkSale.objects.get(id=milk_sale_id, farm=request.user.farm)
        except MilkSale.DoesNotExist:
            return Response({
                'success': False,
                'message': f'Milk sale with ID {milk_sale_id} not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Serialize and return the milk sale
        serializer = MilkSaleSerializer(milk_sale)
        
        return Response({
            'success': True,
            'data': serializer.data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_cleanup_duplicate_milk_sales(request):
    """
    Clean up duplicate milk sale records.
    This API will find and fix any duplicate milk sale records for the same customer and date.
    It will keep the first record and delete the others.
    """
    try:
        # Get all customers for the user's farm
        customers = Customer.objects.filter(farm=request.user.farm)
        
        total_duplicates_found = 0
        total_duplicates_deleted = 0
        cleanup_results = []
        
        for customer in customers:
            # Get all dates for this customer
            dates = MilkSale.objects.filter(
                customer=customer,
                farm=request.user.farm
            ).values_list('date', flat=True).distinct()
            
            for date in dates:
                # Get all milk sales for this customer and date
                milk_sales = MilkSale.objects.filter(
                    customer=customer,
                    date=date,
                    farm=request.user.farm
                ).order_by('id')
                
                # If there are duplicates, keep the first one and delete the rest
                if milk_sales.count() > 1:
                    total_duplicates_found += milk_sales.count() - 1
                    
                    # Keep the first record
                    first_record = milk_sales.first()
                    
                    # Delete all other records
                    duplicates = milk_sales.exclude(id=first_record.id)
                    duplicate_ids = list(duplicates.values_list('id', flat=True))
                    
                    # Log the duplicates
                    
                    # Delete the duplicates
                    duplicates.delete()
                    total_duplicates_deleted += len(duplicate_ids)
                    
                    # Add to results
                    cleanup_results.append({
                        'customer_id': customer.id,
                        'customer_name': customer.name,
                        'date': date.strftime('%Y-%m-%d'),
                        'kept_record_id': first_record.id,
                        'deleted_record_ids': duplicate_ids
                    })
        
        return Response({
            'success': True,
            'message': f"Cleanup completed. Found {total_duplicates_found} duplicates and deleted {total_duplicates_deleted} records.",
            'total_duplicates_found': total_duplicates_found,
            'total_duplicates_deleted': total_duplicates_deleted,
            'cleanup_results': cleanup_results
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_animal_weight_create_update(request):
    """
    Create or update an animal weight record.
    
    For POST (Create):
    Required fields:
    - animal_id: ID of the animal
    - weight_kg: Weight in kilograms
    - date: Date of measurement (YYYY-MM-DD)
    
    Optional fields:
    - description: Notes about the measurement
    
    For PUT/PATCH (Update):
    URL Parameters:
    - weight_id: ID of the weight record to update
    
    Request Body can include:
    - weight_kg: New weight in kilograms
    - date: New date of measurement
    - description: New notes about the measurement
    """
    try:
        # Log the request data for debugging
        
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if request.method == 'POST':
            # Create new weight record
            animal_id = request.data.get('animal_id')
            if not animal_id:
                return Response({
                    'success': False,
                    'message': 'Animal ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                animal = Animal.objects.get(id=animal_id, farm=farm)
            except Animal.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Animal not found or does not belong to your farm'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Validate required fields
            weight_kg = request.data.get('weight_kg')
            date = request.data.get('date')
            
            if not weight_kg or not date:
                return Response({
                    'success': False,
                    'message': 'Weight and date are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Parse date
            try:
                if isinstance(date, str):
                    weight_date = datetime.strptime(date, '%Y-%m-%d').date()
                else:
                    weight_date = date
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Convert weight to decimal
            try:
                weight_kg_decimal = float(weight_kg)
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid weight value'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Create the weight record
            weight_record = AnimalWeight.objects.create(
                animal=animal,
                weight_kg=weight_kg_decimal,
                date=weight_date,
                description=request.data.get('description', '')
            )
            
            # Serialize and return the created weight record
            serializer = AnimalWeightSerializer(weight_record)
            
            return Response({
                'success': True,
                'message': 'Weight record created successfully',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)
            
        elif request.method in ['PUT', 'PATCH']:
            # Update existing weight record
            weight_id = request.data.get('weight_id')
            if not weight_id:
                return Response({
                    'success': False,
                    'message': 'Weight record ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                weight_record = AnimalWeight.objects.get(id=weight_id, animal__farm=farm)
            except AnimalWeight.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Weight record not found or does not belong to your farm'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Update fields if provided
            if 'weight_kg' in request.data:
                try:
                    weight_record.weight_kg = float(request.data['weight_kg'])
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid weight value'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if 'date' in request.data:
                try:
                    if isinstance(request.data['date'], str):
                        weight_record.date = datetime.strptime(request.data['date'], '%Y-%m-%d').date()
                    else:
                        weight_record.date = request.data['date']
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if 'description' in request.data:
                weight_record.description = request.data['description']
            
            # Save the updated weight record
            weight_record.save()
            
            # Serialize and return the updated weight record
            serializer = AnimalWeightSerializer(weight_record)
            
            return Response({
                'success': True,
                'message': 'Weight record updated successfully',
                'data': serializer.data
            })
            
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_animals_with_weights(request):
    """
    Get all animals for the logged-in farm with their latest weight information.
    
    Returns:
    - List of animals with their tags and latest weights
    - If no weight record exists, weight will be 0
    """
    try:
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get all animals for the farm
        animals = Animal.objects.filter(farm=farm)
        
        # Get the latest weight for each animal using a subquery
        latest_weights = AnimalWeight.objects.filter(
            animal=OuterRef('pk')
        ).order_by('-date').values('weight_kg')[:1]
        
        # Create a decimal field for the weight
        decimal_field = models.DecimalField(max_digits=10, decimal_places=2)
        
        # Annotate animals with their latest weight
        animals_with_weights = animals.annotate(
            latest_weight=Coalesce(Subquery(latest_weights), 0, output_field=decimal_field)
        ).values('id', 'tag', 'latest_weight')
        
        # Format the response
        response_data = {
            'success': True,
            'data': list(animals_with_weights)
        }
        
        # Log the response data for debugging
        
        return Response(response_data)
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_animal_weight_history(request, animal_id):
    """
    Get weight history for a specific animal.
    
    Parameters:
    - animal_id: ID of the animal
    
    Returns:
    - List of weight records for the specified animal
    - Includes previous weight, weight change percentage, and daily weight gain
    """
    try:
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the animal and verify it belongs to the user's farm
        try:
            animal = Animal.objects.get(id=animal_id, farm=farm)
        except Animal.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Animal not found or does not belong to your farm'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get all weight records for this animal, ordered by date (newest first)
        weight_records = AnimalWeight.objects.filter(
            animal=animal
        ).order_by('-date')
        
        # Serialize the weight records
        serializer = AnimalWeightSerializer(weight_records, many=True)
        
        # Log the response data for debugging
        
        return Response({
            'success': True,
            'animal_tag': animal.tag,
            'data': serializer.data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_animal_weight_delete(request, weight_id):
    """
    Delete an animal weight record.
    
    URL Parameters:
    - weight_id: ID of the weight record to delete
    """
    try:
        # Log the request
        
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the weight record and verify it belongs to the user's farm
        try:
            weight_record = AnimalWeight.objects.get(id=weight_id, animal__farm=farm)
            animal_tag = weight_record.animal.tag
            weight_date = weight_record.date
            weight_value = weight_record.weight_kg
            
            # Delete the weight record
            weight_record.delete()
            
            
            return Response({
                'success': True,
                'message': f'Weight record deleted successfully',
                'data': {
                    'weight_id': weight_id,
                    'animal_tag': animal_tag,
                    'date': weight_date,
                    'weight_kg': weight_value
                }
            })
            
        except AnimalWeight.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Weight record not found or does not belong to your farm'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
@parser_classes([JSONParser, FormParser, MultiPartParser])
def api_animal_weight_update(request, weight_id):
    """
    Update an existing animal weight record.
    
    URL Parameters:
    - weight_id: ID of the weight record to update
    
    Request Body can include:
    - weight_kg: New weight in kilograms
    - date: New date of measurement (YYYY-MM-DD)
    - description: New notes about the measurement
    """
    try:
        # Log the request data for debugging
        
        # Get the farm from the user
        farm = request.user.farm
        if not farm:
            return Response({
                'success': False,
                'message': 'User is not associated with any farm'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the weight record and verify it belongs to the user's farm
        try:
            weight_record = AnimalWeight.objects.get(id=weight_id, animal__farm=farm)
        except AnimalWeight.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Weight record not found or does not belong to your farm'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Update fields if provided
        if 'weight_kg' in request.data:
            try:
                weight_record.weight_kg = float(request.data['weight_kg'])
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid weight value'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        if 'date' in request.data:
            try:
                if isinstance(request.data['date'], str):
                    weight_record.date = datetime.strptime(request.data['date'], '%Y-%m-%d').date()
                else:
                    weight_record.date = request.data['date']
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        if 'description' in request.data:
            weight_record.description = request.data['description']
        
        # Save the updated weight record
        weight_record.save()
        
        # Serialize and return the updated weight record
        serializer = AnimalWeightSerializer(weight_record)
        
        return Response({
            'success': True,
            'message': 'Weight record updated successfully',
            'data': serializer.data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Animal Scoring API Views

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def api_farm_score_dashboard(request):
    """
    Get farm-wide scoring statistics and dashboard data.
    
    Response includes:
    - Total active animals count
    - Average farm score
    - Score distribution (excellent, good, fair, poor)
    - Top performing animals
    - Animals needing attention
    """
    try:
        user_identifier = getattr(request.user, 'mobile', None) or getattr(request.user, 'username', None) or str(request.user.id)
        farm_name = request.user.farm.name if hasattr(request.user, 'farm') and request.user.farm else 'No Farm'
    except Exception as e:
        pass
    try:
        from .models import AnimalScore, Animal
        from django.db.models import Avg, Count, Case, When, IntegerField
        
        farm = request.user.farm
        
        # Get all active animals with scores for this farm
        active_animals = Animal.objects.filter(
            farm=farm, 
            status='active'
        ).select_related('score')
        
        # Update scores for animals that don't have them or are outdated
        for animal in active_animals:
            score, created = AnimalScore.objects.get_or_create(animal=animal)
            if created or (timezone.now() - score.last_calculated).total_seconds() > 3600:  # 1 hour
                score.update_scores()
        
        # Refresh the queryset after score updates
        active_animals = active_animals.select_related('score')
        
        # Calculate statistics
        total_animals = active_animals.count()
        
        if total_animals == 0:
            return Response({
                'success': True,
                'data': {
                    'total_active_animals': 0,
                    'average_score': 0,
                    'excellent_count': 0,
                    'good_count': 0,
                    'fair_count': 0,
                    'poor_count': 0,
                    'top_performers': [],
                    'needs_attention': []
                }
            })
        
        # Score distribution
        score_stats = active_animals.aggregate(
            avg_score=Avg('score__total_score'),
            excellent_count=Count(Case(When(score__total_score__gte=80, then=1), output_field=IntegerField())),
            good_count=Count(Case(When(score__total_score__gte=60, score__total_score__lt=80, then=1), output_field=IntegerField())),
            fair_count=Count(Case(When(score__total_score__gte=40, score__total_score__lt=60, then=1), output_field=IntegerField())),
            poor_count=Count(Case(When(score__total_score__lt=40, then=1), output_field=IntegerField()))
        )
        
        # Top performers (score >= 80)
        top_performers = active_animals.filter(
            score__total_score__gte=80
        ).order_by('-score__total_score')[:10]
        
        # Animals needing attention (score < 60)
        needs_attention = active_animals.filter(
            score__total_score__lt=60
        ).order_by('score__total_score')[:20]
        
        from .serializers import FarmScoreStatsSerializer, AnimalWithScoreSerializer
        
        response_data = {
            'total_active_animals': total_animals,
            'average_score': round(score_stats['avg_score'] or 0, 1),
            'excellent_count': score_stats['excellent_count'],
            'good_count': score_stats['good_count'],
            'fair_count': score_stats['fair_count'],
            'poor_count': score_stats['poor_count'],
            'top_performers': AnimalWithScoreSerializer(top_performers, many=True).data,
            'needs_attention': AnimalWithScoreSerializer(needs_attention, many=True).data
        }
        
        
        return Response({
            'success': True,
            'data': response_data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def api_animal_score_detail(request, animal_id):
    """
    Get detailed scoring information for a specific animal.
    
    URL Parameters:
    - animal_id: ID of the animal
    
    Response includes:
    - Complete score breakdown
    - Component scores and criteria
    - Last update timestamps
    - Days since last photo/weight update
    - Milk compliance rate (for milking animals)
    """
    try:
        user_identifier = getattr(request.user, 'mobile', None) or getattr(request.user, 'username', None) or str(request.user.id)
    except Exception as e:
        pass
    try:
        from .models import AnimalScore, Animal
        
        farm = request.user.farm
        
        # Get the animal and ensure it belongs to user's farm
        try:
            animal = Animal.objects.get(id=animal_id, farm=farm)
        except Animal.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Animal not found or does not belong to your farm'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get or create the animal's score
        score, created = AnimalScore.objects.get_or_create(animal=animal)
        
        # Update score if it's outdated (older than 1 hour) or newly created
        if created or (timezone.now() - score.last_calculated).total_seconds() > 3600:
            score.update_scores()
        
        from .serializers import AnimalScoreSerializer
        serializer = AnimalScoreSerializer(score)
        
        
        return Response({
            'success': True,
            'data': serializer.data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def api_update_animal_scores(request):
    """
    Update scores for all active animals in the farm.
    
    Optional Parameters:
    - animal_ids: List of specific animal IDs to update (if not provided, updates all active animals)
    
    Response includes:
    - Number of animals updated
    - Update status
    """
    try:
        user_identifier = getattr(request.user, 'mobile', None) or getattr(request.user, 'username', None) or str(request.user.id)
    except Exception as e:
        pass
    try:
        from .models import AnimalScore, Animal
        
        farm = request.user.farm
        
        # Get list of specific animal IDs if provided
        animal_ids = request.data.get('animal_ids', [])
        
        if animal_ids:
            # Validate that all provided animal IDs belong to the user's farm
            animals = Animal.objects.filter(id__in=animal_ids, farm=farm)
            if len(animals) != len(animal_ids):
                return Response({
                    'success': False,
                    'message': 'Some animal IDs are invalid or do not belong to your farm'
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            # Get all active animals if no specific IDs provided
            animals = Animal.objects.filter(farm=farm, status='active')
        
        updated_count = 0
        
        for animal in animals:
            score, created = AnimalScore.objects.get_or_create(animal=animal)
            score.update_scores()
            updated_count += 1
        
        response_data = {
            'updated_count': updated_count,
            'total_requested': len(animal_ids) if animal_ids else animals.count()
        }
        
        
        return Response({
            'success': True,
            'message': f'Successfully updated scores for {updated_count} animals',
            'data': response_data
        })
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication])
def api_animal_scores_list(request):
    """
    Get list of all animals with scores and comprehensive filtering.
    
    IMPORTANT: By default returns ALL ACTIVE animals. Use page_size to limit results.
    
    Query Parameters:
    - animal_status: Animal status (default: 'active') - Options: active, expired, sold
    - animal_category: Animal category - Options: dairy, cow, buffalo, goat, sheep, beef, other
    - animal_type: Animal type - Options: breeder, pregnant, dry, milking, preg_milking, calf, other
    - animal_gender: Animal gender - Options: male, female
    - min_age_months: Minimum age in months (animals older than this)
    - max_age_months: Maximum age in months (animals younger than this)
    - score_filter: Predefined score ranges - Options: excellent, good, fair, poor
    - min_score: Custom minimum score (0-100)
    - max_score: Custom maximum score (0-100)
    - search: Search by animal tag
    - order_by: Sort order - Options: score_desc, score_asc, tag_asc, tag_desc
    - page: Page number (default: 1)
    - page_size: Items per page (default: 1000 to get all animals)
    
    Score Categories:
    - excellent: 80-100 points (Green)
    - good: 60-79 points (Yellow)
    - fair: 40-59 points (Orange)
    - poor: 0-39 points (Red)
    
    Default Behavior:
    - Returns ALL ACTIVE animals from the farm
    - To get expired/sold animals, explicitly set animal_status parameter
    - Use filters to narrow down results as needed
    """
    try:
        user_identifier = getattr(request.user, 'mobile', None) or getattr(request.user, 'username', None) or str(request.user.id)
    except Exception as e:
        pass
    try:
        from .models import AnimalScore, Animal
        from django.core.paginator import Paginator
        from django.db.models import Q
        
        farm = request.user.farm
        
        # Get query parameters
        score_filter = request.GET.get('score_filter', None)
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 1000))  # Default to large number to get all animals
        search = request.GET.get('search', '')
        order_by = request.GET.get('order_by', 'score_desc')
        
        # Age filtering parameters (in months)
        min_age_months = request.GET.get('min_age_months', None)
        max_age_months = request.GET.get('max_age_months', None)
        
        # New comprehensive filtering parameters
        animal_category = request.GET.get('animal_category', None)  # dairy, cow, buffalo, goat, sheep, beef, other
        animal_type = request.GET.get('animal_type', None)  # breeder, pregnant, dry, milking, preg_milking, calf, other
        animal_status = request.GET.get('animal_status', 'active')  # active (default), expired, sold
        animal_gender = request.GET.get('animal_gender', None)  # male, female
        
        # Score range filtering (alternative to score_filter)
        min_score = request.GET.get('min_score', None)  # Minimum score (0-100)
        max_score = request.GET.get('max_score', None)  # Maximum score (0-100)
        
        # Base queryset - animals with scores from user's farm
        # Default to active animals only, unless specific status is requested
        animals = Animal.objects.filter(
            farm=farm,
            status=animal_status  # Filter by status (default: active)
        ).select_related('score')
        
        # Apply search filter
        if search:
            animals = animals.filter(tag__icontains=search)
        
        # Apply animal category filter
        if animal_category:
            animals = animals.filter(category=animal_category)
        
        # Apply animal type filter
        if animal_type:
            animals = animals.filter(animal_type=animal_type)
        
        # Apply gender filter
        if animal_gender:
            animals = animals.filter(sex=animal_gender)
        
        # Apply age filter (in months)
        from datetime import date
        try:
            from dateutil.relativedelta import relativedelta
        except ImportError:
            # Fallback if dateutil is not available
            relativedelta = None
        
        if min_age_months and relativedelta:
            try:
                min_age_months = int(min_age_months)
                max_birth_date = date.today() - relativedelta(months=min_age_months)
                animals = animals.filter(dob__lte=max_birth_date)
            except (ValueError, TypeError):
                pass  # Invalid age, ignore filter
                
        if max_age_months and relativedelta:
            try:
                max_age_months = int(max_age_months)
                min_birth_date = date.today() - relativedelta(months=max_age_months)
                animals = animals.filter(dob__gte=min_birth_date)
            except (ValueError, TypeError):
                pass  # Invalid age, ignore filter
        
        # Apply score filtering (two options: predefined ranges or custom range)
        if score_filter:
            # Predefined score categories
            if score_filter == 'excellent':
                animals = animals.filter(score__total_score__gte=80)
            elif score_filter == 'good':
                animals = animals.filter(score__total_score__gte=60, score__total_score__lt=80)
            elif score_filter == 'fair':
                animals = animals.filter(score__total_score__gte=40, score__total_score__lt=60)
            elif score_filter == 'poor':
                animals = animals.filter(score__total_score__lt=40)
        else:
            # Custom score range filtering
            if min_score:
                try:
                    min_score_val = int(min_score)
                    animals = animals.filter(score__total_score__gte=min_score_val)
                except (ValueError, TypeError):
                    pass
            
            if max_score:
                try:
                    max_score_val = int(max_score)
                    animals = animals.filter(score__total_score__lte=max_score_val)
                except (ValueError, TypeError):
                    pass
        
        # Apply ordering
        if order_by == 'score_desc':
            animals = animals.order_by('-score__total_score')
        elif order_by == 'score_asc':
            animals = animals.order_by('score__total_score')
        elif order_by == 'tag_asc':
            animals = animals.order_by('tag')
        elif order_by == 'tag_desc':
            animals = animals.order_by('-tag')
        
        # Pagination
        paginator = Paginator(animals, page_size)
        page_obj = paginator.get_page(page)
        
        from .serializers import AnimalWithScoreSerializer
        serializer = AnimalWithScoreSerializer(page_obj, many=True)
        
        response_data = {
            'results': serializer.data,
            'pagination': {
                'current_page': page,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        }
        
        # Build filter info string for logging
        filter_info = []
        if score_filter:
            filter_info.append(f"Score: {score_filter}")
        if min_score or max_score:
            filter_info.append(f"Score Range: {min_score or 'no-min'}-{max_score or 'no-max'}")
        if animal_status != 'active':
            filter_info.append(f"Status: {animal_status}")
        if animal_category:
            filter_info.append(f"Category: {animal_category}")
        if animal_type:
            filter_info.append(f"Type: {animal_type}")
        if animal_gender:
            filter_info.append(f"Gender: {animal_gender}")
        if min_age_months or max_age_months:
            filter_info.append(f"Age: {min_age_months or 'no-min'}-{max_age_months or 'no-max'} months")
        
        filter_summary = ", ".join(filter_info) if filter_info else "No filters"
        
        # Debug prints for terminal output
        print("=== Animal Scores List API Debug ===")
        print(f"Farm: {farm}")
        print(f"User: {request.user}")
        print(f"Query parameters: {dict(request.GET)}")
        print(f"Applied filters: {filter_summary}")
        print(f"Total animals found: {paginator.count}")
        print(f"Page {page} of {paginator.num_pages} (showing {len(page_obj)} animals)")
        print(f"Order by: {order_by}")
        
        # Print details of returned animals
        print("Animals being returned:")
        for i, animal_data in enumerate(serializer.data, 1):
            print(f"  {i}. Tag: {animal_data.get('tag', 'N/A')}, "
                  f"Score: {animal_data.get('total_score', 'N/A')}, "
                  f"Category: {animal_data.get('category', 'N/A')}, "
                  f"Type: {animal_data.get('animal_type', 'N/A')}, "
                  f"Status: {animal_data.get('status', 'N/A')}")
        
        print(f"Response data keys: {list(response_data.keys())}")
        print(f"Results count: {len(response_data['results'])}")
        
        # Print the complete raw response being sent back
        final_response = {
            'success': True,
            'data': response_data
        }
        
        print("=== RAW RESPONSE DATA ===")
        import json
        try:
            print(json.dumps(final_response, indent=2, default=str))
        except Exception as e:
            print(f"Could not serialize response to JSON: {e}")
            print(f"Raw response object: {final_response}")
        print("=== END RAW RESPONSE DATA ===")
        
        return Response(final_response)
        
    except Exception as e:
        import traceback
        return Response({
            'success': False,
            'message': f"An error occurred: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)