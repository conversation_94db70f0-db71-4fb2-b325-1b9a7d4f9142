from django.contrib import admin
from .models import Animal, Customer, MilkPayment, MilkRecord, MilkSale, AnimalScore, AnimalTypeChange

class AnimalAdmin(admin.ModelAdmin):
    list_display = ('tag', 'farm', 'category', 'get_farm_admin')
    list_filter = ('farm', 'farm__admin')
    search_fields = ('tag',)
    
    def get_farm_admin(self, obj):
        return obj.farm.admin
    get_farm_admin.short_description = 'Farm Admin'

class AnimalScoreAdmin(admin.ModelAdmin):
    list_display = ('animal', 'total_score', 'photo_score', 'weight_score', 'milk_score', 'last_calculated')
    list_filter = ('total_score', 'last_calculated')
    search_fields = ('animal__tag',)
    readonly_fields = ('last_calculated', 'last_photo_update', 'last_weight_record', 'last_milk_record')
    
    def get_readonly_fields(self, request, obj=None):
        # Make all score fields readonly in admin
        if obj:
            return self.readonly_fields + ('photo_score', 'weight_score', 'milk_score', 'total_score')
        return self.readonly_fields

class AnimalTypeChangeAdmin(admin.ModelAdmin):
    list_display = ('animal', 'get_from_type_display', 'get_to_type_display', 'changed_date', 'changed_by')
    list_filter = ('changed_date', 'to_type')
    search_fields = ('animal__tag',)
    readonly_fields = ('changed_date',)

admin.site.register(Animal, AnimalAdmin)
admin.site.register(MilkRecord)
admin.site.register(Customer)
admin.site.register(MilkSale)
admin.site.register(MilkPayment)
admin.site.register(AnimalScore, AnimalScoreAdmin)
admin.site.register(AnimalTypeChange, AnimalTypeChangeAdmin)
