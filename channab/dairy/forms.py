from django import forms
from .models import Animal, Customer, MilkPayment, MilkSale, MilkRecord, AnimalWeight, Breeding, PregnancyCheck, CalvingRecord, BreedingCycle
from django.db.models import Q
from datetime import datetime, timedelta

class AnimalForm(forms.ModelForm):

    class Meta:
        model = Animal
        fields = ['tag', 'image', 'dob', 'purchase_cost', 'status', 'sex', 'category', 'animal_type']

    def __init__(self, *args, **kwargs):
        farm = kwargs.pop('farm', None)
        super(AnimalForm, self).__init__(*args, **kwargs)
        if farm:
            pass  # Category is now a CharField with choices

class MilkRecordForm(forms.ModelForm):
    first_time = forms.DecimalField(max_digits=5, decimal_places=2, required=False)
    second_time = forms.DecimalField(max_digits=5, decimal_places=2, required=False)
    third_time = forms.DecimalField(max_digits=5, decimal_places=2, required=False)

    class Meta:
        model = MilkRecord
        fields = ('animal', 'date', 'first_time', 'second_time', 'third_time')

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super(MilkRecordForm, self).__init__(*args, **kwargs)
        if self.farm:
            self.fields['animal'].queryset = Animal.objects.filter(farm=self.farm, sex='female', animal_type__in=['milking', 'preg_milking'])

class AnimalWeightForm(forms.ModelForm):
    class Meta:
        model = AnimalWeight
        fields = ['animal', 'weight_kg', 'date', 'description']

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super(AnimalWeightForm, self).__init__(*args, **kwargs)
        if self.user:
            self.fields['animal'].queryset = Animal.objects.filter(farm__admin=self.user)

class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = ['name', 'mobile_number', 'created_date']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'mobile_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional'}),
            'created_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }

class MilkSaleForm(forms.ModelForm):
    class Meta:
        model = MilkSale
        fields = ['customer', 'date', 'first_sale', 'second_sale', 'third_sale', 'price_per_liter']
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-control'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'first_sale': forms.NumberInput(attrs={'class': 'form-control'}),
            'second_sale': forms.NumberInput(attrs={'class': 'form-control'}),
            'third_sale': forms.NumberInput(attrs={'class': 'form-control'}),
            'price_per_liter': forms.NumberInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super(MilkSaleForm, self).__init__(*args, **kwargs)
        if self.user:
            self.fields['customer'].queryset = Customer.objects.filter(farm=self.user.farm)

class MilkPaymentForm(forms.ModelForm):
    class Meta:
        model = MilkPayment
        fields = ['customer', 'date', 'received_payment', 'note']
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-control'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'received_payment': forms.NumberInput(attrs={'class': 'form-control'}),
            'note': forms.Textarea(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super(MilkPaymentForm, self).__init__(*args, **kwargs)
        if self.user:
            self.fields['customer'].queryset = Customer.objects.filter(farm=self.user.farm)

class BreedingForm(forms.ModelForm):
    class Meta:
        model = Breeding
        fields = ['female_animal', 'date_of_insemination', 'breeding_method', 'bull', 'semen_tag', 'comment']
        widgets = {
            'female_animal': forms.Select(attrs={'class': 'form-control'}),
            'date_of_insemination': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'breeding_method': forms.Select(attrs={'class': 'form-control', 'id': 'breeding-method'}),
            'bull': forms.Select(attrs={'class': 'form-control', 'id': 'breeding-bull'}),
            'semen_tag': forms.TextInput(attrs={
                'class': 'form-control', 
                'id': 'semen-tag',
                'placeholder': 'e.g., AI001, SEM-2024-001, BUL123',
                'maxlength': '100'
            }),
            'comment': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super(BreedingForm, self).__init__(*args, **kwargs)
        if self.farm:
            # Only show female animals that can be bred
            self.fields['female_animal'].queryset = Animal.objects.filter(
                farm=self.farm,
                sex='female',
                animal_type__in=['breeder', 'dry', 'milking', 'preg_milking'],
                status='active'
            )
            # Only show male breeders for natural breeding
            self.fields['bull'].queryset = Animal.objects.filter(
                farm=self.farm,
                sex='male',
                animal_type='breeder',
                status='active'
            )
            self.fields['bull'].required = False
            
    def clean(self):
        cleaned_data = super().clean()
        breeding_method = cleaned_data.get('breeding_method')
        bull = cleaned_data.get('bull')
        semen_tag = cleaned_data.get('semen_tag')
        
        if breeding_method == 'natural':
            if not bull:
                self.add_error('bull', 'A bull must be selected for natural breeding.')
            if semen_tag:
                self.add_error('semen_tag', 'Semen tag should be empty for natural breeding.')
        elif breeding_method == 'artificial':
            if not semen_tag:
                self.add_error('semen_tag', 'Semen tag/ID is required for artificial insemination.')
            if bull:
                self.add_error('bull', 'Bull should not be selected for artificial insemination.')
                
        return cleaned_data

class PregnancyCheckForm(forms.ModelForm):
    class Meta:
        model = PregnancyCheck
        fields = ['breeding', 'check_date', 'status', 'notes']
        widgets = {
            'breeding': forms.Select(attrs={'class': 'form-control'}),
            'check_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super(PregnancyCheckForm, self).__init__(*args, **kwargs)
        if self.farm:
            # Only show pending breedings for pregnancy checks
            self.fields['breeding'].queryset = Breeding.objects.filter(
                female_animal__farm=self.farm,
                status='pending'
            ).select_related('female_animal')

class CalvingRecordForm(forms.ModelForm):
    calf_tag = forms.CharField(max_length=50, widget=forms.TextInput(attrs={'class': 'form-control'}))
    calf_sex = forms.ChoiceField(choices=Animal.SEX_CHOICES, widget=forms.Select(attrs={'class': 'form-control'}))
    
    class Meta:
        model = CalvingRecord
        fields = ['breeding', 'calving_date', 'difficulty', 'complications', 'notes', 'calf_tag', 'calf_sex']
        widgets = {
            'breeding': forms.Select(attrs={'class': 'form-control'}),
            'calving_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'difficulty': forms.Select(attrs={'class': 'form-control'}),
            'complications': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super(CalvingRecordForm, self).__init__(*args, **kwargs)
        if self.farm:
            # Only show successful breedings for calving
            self.fields['breeding'].queryset = Breeding.objects.filter(
                female_animal__farm=self.farm,
                status='successful'
            ).select_related('female_animal')

class HeatDetectionForm(forms.Form):
    animal = forms.ModelChoiceField(
        queryset=Animal.objects.none(),
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    detection_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    heat_signs = forms.MultipleChoiceField(
        choices=[
            ('mounting', 'Mounting behavior'),
            ('restless', 'Restlessness'),
            ('bellowing', 'Increased bellowing'),
            ('discharge', 'Clear discharge'),
            ('swelling', 'Vulva swelling'),
            ('standing', 'Standing to be mounted'),
        ],
        widget=forms.CheckboxSelectMultiple(),
        required=True
    )
    notes = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        required=False
    )

    def __init__(self, *args, **kwargs):
        self.farm = kwargs.pop('farm', None)
        super(HeatDetectionForm, self).__init__(*args, **kwargs)
        if self.farm:
            # Show only breedable females
            self.fields['animal'].queryset = Animal.objects.filter(
                farm=self.farm,
                sex='female',
                animal_type__in=['breeder', 'dry', 'milking'],
                status='active'
            )

class SemenInventoryForm(forms.Form):
    bull_name = forms.CharField(max_length=100, widget=forms.TextInput(attrs={'class': 'form-control'}))
    semen_code = forms.CharField(max_length=50, widget=forms.TextInput(attrs={'class': 'form-control'}))
    quantity = forms.IntegerField(widget=forms.NumberInput(attrs={'class': 'form-control', 'min': 0}))
    source = forms.CharField(max_length=100, widget=forms.TextInput(attrs={'class': 'form-control'}))
    purchase_date = forms.DateField(widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}))
    expiry_date = forms.DateField(widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}))
    storage_location = forms.CharField(max_length=100, widget=forms.TextInput(attrs={'class': 'form-control'}))
    price_per_straw = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )
