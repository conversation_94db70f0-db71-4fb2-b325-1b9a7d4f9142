from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import Breeding, Pregnancy<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, BreedingCycle, Animal
from .breeding_serializers import (
    BreedingSerializer, PregnancyCheckSerializer,
    CalvingRecordSerializer, BreedingCycleSerializer
)

class BreedingViewSet(viewsets.ModelViewSet):
    serializer_class = BreedingSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Breeding.objects.filter(
            female_animal__farm=self.request.user.farm
        ).select_related(
            'female_animal', 'bull', 'breeding_cycle'
        ).prefetch_related(
            'pregnancy_checks', 'calving'
        )

    @action(detail=True, methods=['post'])
    def mark_pregnancy(self, request, pk=None):
        breeding = self.get_object()
        is_pregnant = request.data.get('is_pregnant')
        check_date = request.data.get('check_date', timezone.now().date())
        notes = request.data.get('notes', '')

        if is_pregnant is None:
            return Response(
                {'error': 'is_pregnant field is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            breeding.mark_pregnancy_status(
                is_pregnant=is_pregnant,
                check_date=check_date
            )
            return Response({'status': 'success'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def record_calving(self, request, pk=None):
        breeding = self.get_object()
        calving_date = request.data.get('calving_date', timezone.now().date())
        difficulty = request.data.get('difficulty', 'normal')
        complications = request.data.get('complications')
        notes = request.data.get('notes')
        
        # Handle calf creation if details provided
        calf_data = request.data.get('calf')
        calf = None
        if calf_data:
            try:
                calf = Animal.objects.create(
                    farm=request.user.farm,
                    tag=calf_data.get('tag'),
                    dob=calving_date,
                    sex=calf_data.get('sex'),
                    category=breeding.female_animal.category,
                    animal_type='calf',
                    mother=breeding.female_animal,
                    father=breeding.bull if breeding.breeding_method == 'natural' else None
                )
            except Exception as e:
                return Response(
                    {'error': f'Failed to create calf: {str(e)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        try:
            breeding.record_calving(
                calving_date=calving_date,
                calf=calf,
                difficulty=difficulty,
                complications=complications,
                notes=notes
            )
            return Response({'status': 'success'})
        except Exception as e:
            if calf:
                calf.delete()  # Cleanup if calving record fails
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def breed_animal(self, request):
        """Register a breeding event for an animal"""
        data = request.data.copy()
        # Map animal_id to female_animal_id
        if 'animal_id' in data:
            data['female_animal_id'] = data.pop('animal_id')
        
        print("Processing data:", data)  # Debug print
        serializer = BreedingSerializer(data=data)
        if not serializer.is_valid():
            print("Serializer errors:", serializer.errors)  # Debug print
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            breeding = serializer.save()
            return Response(
                BreedingSerializer(breeding).data,
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            print("Exception:", str(e))  # Debug print
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['put', 'patch'])
    def update_breeding(self, request, pk=None):
        """Update a breeding record"""
        breeding = self.get_object()
        
        # Don't allow updates if breeding is successful and has pregnancy checks or calving
        if breeding.status == 'successful' and (breeding.pregnancy_checks.exists() or hasattr(breeding, 'calving')):
            return Response(
                {'error': 'Cannot update breeding record with confirmed pregnancy or calving'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Convert animal_id to female_animal_id if present
        data = request.data.copy()
        if 'animal_id' in data:
            data['female_animal_id'] = data.pop('animal_id')
            
        serializer = BreedingSerializer(breeding, data=data, partial=True)
        if not serializer.is_valid():
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            updated_breeding = serializer.save()
            return Response(
                BreedingSerializer(updated_breeding).data,
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['delete'])
    def delete_breeding(self, request, pk=None):
        """Delete a breeding record"""
        breeding = self.get_object()
        
        # Don't allow deletion if breeding is successful and has pregnancy checks or calving
        if breeding.status == 'successful' and (breeding.pregnancy_checks.exists() or hasattr(breeding, 'calving')):
            return Response(
                {'error': 'Cannot delete breeding record with confirmed pregnancy or calving'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # If this is the only breeding in the cycle, update cycle status back to 'open'
            cycle = breeding.breeding_cycle
            if cycle and cycle.breedings.count() == 1:
                cycle.status = 'open'
                cycle.save()
                
            breeding.delete()
            return Response(
                {'message': 'Breeding record deleted successfully'},
                status=status.HTTP_204_NO_CONTENT
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def animal_history(self, request):
        """Get complete breeding history for a specific animal"""
        animal_id = request.query_params.get('animal_id')
        if not animal_id:
            return Response(
                {'error': 'animal_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        animal = get_object_or_404(Animal, id=animal_id, farm=request.user.farm)
        
        # Get all breeding cycles
        breeding_cycles = BreedingCycle.objects.filter(
            animal=animal
        ).order_by('-start_date')
        
        # Get all breeding events
        breedings = self.get_queryset().filter(
            female_animal=animal
        ).order_by('-date_of_insemination')
        
        # Calculate breeding statistics
        total_breedings = breedings.count()
        successful_breedings = breedings.filter(status='successful').count()
        success_rate = (successful_breedings / total_breedings * 100) if total_breedings > 0 else 0
        
        # Get current breeding status
        current_cycle = breeding_cycles.first()
        latest_breeding = breedings.first()
        current_status = {
            'cycle_status': current_cycle.status if current_cycle else 'unknown',
            'lactation_number': current_cycle.lactation_number if current_cycle else 0,
            'breeding_status': latest_breeding.status if latest_breeding else 'none'
        }
        
        # Format the response
        response_data = {
            'animal': {
                'id': animal.id,
                'tag': animal.tag,
                'animal_type': animal.animal_type,
                'dob': animal.dob
            },
            'current_status': current_status,
            'statistics': {
                'total_breedings': total_breedings,
                'successful_breedings': successful_breedings,
                'success_rate': round(success_rate, 2),
                'total_calves': animal.children_mother.count(),
                'total_cycles': breeding_cycles.count()
            },
            'breeding_cycles': BreedingCycleSerializer(breeding_cycles, many=True).data,
            'breeding_history': []
        }
        
        # Add detailed breeding history
        for breeding in breedings:
            breeding_data = BreedingSerializer(breeding).data
            
            # Get pregnancy checks for this breeding
            pregnancy_checks = PregnancyCheck.objects.filter(
                breeding=breeding
            ).order_by('check_date')
            
            # Get calving record if exists
            try:
                calving_record = breeding.calving
                calving_data = CalvingRecordSerializer(calving_record).data if calving_record else None
            except CalvingRecord.DoesNotExist:
                calving_data = None
            
            breeding_event = {
                'breeding': breeding_data,
                'pregnancy_checks': PregnancyCheckSerializer(pregnancy_checks, many=True).data,
                'calving': calving_data
            }
            
            response_data['breeding_history'].append(breeding_event)
        
        return Response(response_data)

class PregnancyCheckViewSet(viewsets.ModelViewSet):
    serializer_class = PregnancyCheckSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return PregnancyCheck.objects.filter(
            breeding__female_animal__farm=self.request.user.farm
        )

class CalvingRecordViewSet(viewsets.ModelViewSet):
    serializer_class = CalvingRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return CalvingRecord.objects.filter(
            breeding__female_animal__farm=self.request.user.farm
        )

class BreedingCycleViewSet(viewsets.ModelViewSet):
    serializer_class = BreedingCycleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return BreedingCycle.objects.filter(
            animal__farm=self.request.user.farm
        )

    @action(detail=False, methods=['get'])
    def animal_cycles(self, request):
        animal_id = request.query_params.get('animal_id')
        if not animal_id:
            return Response(
                {'error': 'animal_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        animal = get_object_or_404(Animal, id=animal_id, farm=request.user.farm)
        cycles = self.get_queryset().filter(animal=animal)
        serializer = self.get_serializer(cycles, many=True)
        return Response(serializer.data)
