{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}Breeding Dashboard{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Breeding Dashboard Content -->
        
        <div class="row mb-3">
            <div class="col-auto ms-auto">
                <a href="{% url 'dairy:breeding_create' %}" class="btn btn-primary">
                    <i class="ri-add-line"></i> New Breeding
                </a>
            </div>
        </div>

        <!-- Key Metrics Row -->
    <div class="row">
        <div class="col-xl-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ total_breedings }}</h4>
                            <p class="mb-0 text-muted">Total Breedings</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-heart-line display-4 text-primary opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ success_rate }}%</h4>
                            <p class="mb-0 text-muted">Success Rate</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-bar-chart-line display-4 text-success opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ pending_breedings }}</h4>
                            <p class="mb-0 text-muted">Pending Checks</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-time-line display-4 text-warning opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ successful_breedings }}</h4>
                            <p class="mb-0 text-muted">Pregnant Animals</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-checkbox-circle-line display-4 text-info opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions and Alerts Row -->
    <div class="row">
        <!-- Upcoming Calvings -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Upcoming Calvings (Next 30 Days)</h6>
                    <a href="{% url 'dairy:breeding_calendar' %}" class="btn btn-sm btn-outline-primary float-end">
                        <i class="ri-calendar-line"></i> View Calendar
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_calvings %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Animal</th>
                                    <th>Expected Date</th>
                                    <th>Days Left</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for breeding in upcoming_calvings %}
                                <tr>
                                    <td>
                                        <a href="{% url 'dairy:animal_detail' breeding.female_animal.pk %}">
                                            {{ breeding.female_animal.tag }}
                                        </a>
                                    </td>
                                    <td>{{ breeding.expected_calving_date|date:"d M Y" }}</td>
                                    <td>
                                        {% with days_left=breeding.expected_calving_date|days_until %}
                                        <span class="badge {% if days_left <= 7 %}bg-danger{% elif days_left <= 14 %}bg-warning{% else %}bg-info{% endif %}">
                                            {{ days_left }} days
                                        </span>
                                        {% endwith %}
                                    </td>
                                    <td>
                                        <a href="{% url 'dairy:breeding_detail' breeding.pk %}" class="btn btn-sm btn-primary">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No upcoming calvings in the next 30 days</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Animals Due for Drying Off -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Due for Drying Off (Next 14 Days)</h6>
                </div>
                <div class="card-body">
                    {% if dry_off_due %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Animal</th>
                                    <th>Dry-off Date</th>
                                    <th>Days Left</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for breeding in dry_off_due %}
                                <tr>
                                    <td>
                                        <a href="{% url 'dairy:animal_detail' breeding.female_animal.pk %}">
                                            {{ breeding.female_animal.tag }}
                                        </a>
                                    </td>
                                    <td>{{ breeding.dry_off_date|date:"d M Y" }}</td>
                                    <td>
                                        {% with days_left=breeding.dry_off_date|days_until %}
                                        <span class="badge {% if days_left <= 3 %}bg-danger{% elif days_left <= 7 %}bg-warning{% else %}bg-info{% endif %}">
                                            {{ days_left }} days
                                        </span>
                                        {% endwith %}
                                    </td>
                                    <td>
                                        <a href="{% url 'dairy:breeding_detail' breeding.pk %}" class="btn btn-sm btn-primary">
                                            <i class="ri-eye-line"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No animals due for drying off</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Breedings and Ready for Breeding -->
    <div class="row">
        <!-- Recent Breeding Records -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Recent Breeding Records</h6>
                    <a href="{% url 'dairy:breeding_list' %}" class="btn btn-sm btn-outline-primary float-end">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Animal</th>
                                    <th>Method</th>
                                    <th>Bull/Semen</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for breeding in recent_breedings %}
                                <tr>
                                    <td>{{ breeding.date_of_insemination|date:"d M Y" }}</td>
                                    <td>
                                        <a href="{% url 'dairy:animal_detail' breeding.female_animal.pk %}">
                                            {{ breeding.female_animal.tag }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ breeding.get_breeding_method_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if breeding.breeding_method == 'natural' %}
                                            {{ breeding.bull.tag|default:"-" }}
                                        {% else %}
                                            {{ breeding.semen_tag|default:"-" }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge {% if breeding.status == 'successful' %}bg-success{% elif breeding.status == 'failed' %}bg-danger{% else %}bg-warning{% endif %}">
                                            {{ breeding.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{% url 'dairy:breeding_detail' breeding.pk %}" class="btn btn-sm btn-primary">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            {% if breeding.status == 'pending' %}
                                            <a href="{% url 'dairy:pregnancy_check_create' breeding.pk %}" class="btn btn-sm btn-warning">
                                                <i class="ri-stethoscope-line"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animals Ready for Breeding -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Ready for Breeding</h6>
                    <a href="{% url 'dairy:heat_detection' %}" class="btn btn-sm btn-outline-primary float-end">
                        Heat Detection
                    </a>
                </div>
                <div class="card-body">
                    {% if ready_for_breeding %}
                    <div class="list-group">
                        {% for animal in ready_for_breeding %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{% url 'dairy:animal_detail' animal.pk %}">
                                        {{ animal.tag }}
                                    </a>
                                </h6>
                                <small class="text-muted">{{ animal.get_animal_type_display }}</small>
                            </div>
                            <a href="{% url 'dairy:breeding_create' %}?animal={{ animal.pk }}" class="btn btn-sm btn-primary">
                                <i class="ri-add-line"></i> Breed
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No animals ready for breeding</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Breeding Methods Chart -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Methods</h6>
                </div>
                <div class="card-body">
                    <div id="breeding-methods-chart"></div>
                </div>
            </div>
        </div>

        <!-- Monthly Breeding Trend -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Monthly Breeding Trend</h6>
                </div>
                <div class="card-body">
                    <div id="monthly-breeding-chart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <a href="{% url 'dairy:breeding_calendar' %}" class="btn btn-light btn-sm mb-2 d-block">
                                <i class="ri-calendar-line"></i> Breeding Calendar
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'dairy:heat_detection' %}" class="btn btn-light btn-sm mb-2 d-block">
                                <i class="ri-temp-hot-line"></i> Heat Detection
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'dairy:breeding_analytics' %}" class="btn btn-light btn-sm mb-2 d-block">
                                <i class="ri-bar-chart-2-line"></i> Analytics
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'dairy:breeding_list' %}" class="btn btn-light btn-sm mb-2 d-block">
                                <i class="ri-list-check"></i> All Records
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Breeding Methods Pie Chart
    var methodsData = {{ breeding_methods|safe }};
    var methodsOptions = {
        series: methodsData.map(m => m.count),
        chart: {
            type: 'donut',
            height: 350
        },
        labels: methodsData.map(m => m.breeding_method === 'natural' ? 'Natural' : 'Artificial'),
        colors: ['#0da487', '#17a2b8'],
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };
    
    var methodsChart = new ApexCharts(document.querySelector("#breeding-methods-chart"), methodsOptions);
    methodsChart.render();
    
    // Monthly Breeding Trend Chart
    var monthlyData = {{ monthly_breedings|safe }};
    var monthlyOptions = {
        series: [{
            name: 'Breedings',
            data: monthlyData.map(m => m.count)
        }],
        chart: {
            height: 350,
            type: 'area',
            toolbar: {
                show: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: monthlyData.map(m => {
                var date = new Date(m.month + '-01');
                return date.toLocaleDateString('en', { month: 'short', year: 'numeric' });
            })
        },
        yaxis: {
            title: {
                text: 'Number of Breedings'
            }
        },
        colors: ['#0da487'],
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        }
    };
    
    var monthlyChart = new ApexCharts(document.querySelector("#monthly-breeding-chart"), monthlyOptions);
    monthlyChart.render();
});
</script>
{% endblock %}