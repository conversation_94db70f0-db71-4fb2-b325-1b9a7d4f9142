{% extends 'main/base.html' %}
{% load dairy_extras %}
{% load static %}

{% block title %}Breeding Calendar{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Breeding Calendar Content -->

        <div class="row mb-3">
            <div class="col-auto ms-auto">
                <a href="{% url 'dairy:breeding_create' %}" class="btn btn-primary btn-sm">
                    <i class="ri-add-line"></i> New Breeding
                </a>
            </div>
        </div>
    <!-- Calendar Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="?month={{ prev_month }}&year={{ prev_year }}" class="btn btn-outline-primary btn-sm">
                                <i class="ri-arrow-left-line"></i> Previous
                            </a>
                        </div>
                        <div>
                            <h4 class="mb-0">{{ month_name }} {{ year }}</h4>
                        </div>
                        <div>
                            <a href="?month={{ next_month }}&year={{ next_year }}" class="btn btn-outline-primary btn-sm">
                                Next <i class="ri-arrow-right-line"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Legend -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6>Event Legend:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <span class="badge" style="background-color: #0da487;">●</span>
                            Breeding Events
                        </div>
                        <div class="col-md-3">
                            <span class="badge" style="background-color: #dc3545;">●</span>
                            Expected Calvings
                        </div>
                        <div class="col-md-3">
                            <span class="badge" style="background-color: #ffc107;">●</span>
                            Dry-off Dates
                        </div>
                        <div class="col-md-3">
                            <span class="badge" style="background-color: #17a2b8;">●</span>
                            Pregnancy Checks
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade" id="eventModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="eventModalBody">
                    <!-- Event details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
<script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendar');
    
    // Convert Django events to FullCalendar format
    var events = [
        {% for event in events %}
        {
            title: '{{ event.title|escapejs }}',
            date: '{{ event.date|date:"Y-m-d" }}',
            color: '{{ event.color }}',
            url: '{{ event.url }}',
            extendedProps: {
                type: '{{ event.type }}'
            }
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    var calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        initialDate: '{{ year }}-{{ month|stringformat:"02d" }}-01',
        headerToolbar: false, // We have our own navigation
        events: events,
        eventClick: function(info) {
            info.jsEvent.preventDefault();
            
            // Show event details in modal
            showEventDetails(info.event);
        },
        eventDidMount: function(info) {
            // Add tooltip
            info.el.setAttribute('title', info.event.title);
        },
        height: 'auto',
        dayMaxEvents: 3,
        moreLinkClick: 'popover',
        eventDisplay: 'block'
    });
    
    calendar.render();
});

function showEventDetails(event) {
    var modalBody = document.getElementById('eventModalBody');
    var eventType = event.extendedProps.type;
    var eventTitle = event.title;
    var eventDate = event.startStr;
    
    var detailsHTML = `
        <div class="mb-3">
            <h6>Event Type:</h6>
            <span class="badge" style="background-color: ${event.backgroundColor};">
                ${getEventTypeName(eventType)}
            </span>
        </div>
        <div class="mb-3">
            <h6>Date:</h6>
            <p>${new Date(eventDate).toLocaleDateString()}</p>
        </div>
        <div class="mb-3">
            <h6>Details:</h6>
            <p>${eventTitle}</p>
        </div>
    `;
    
    if (event.url) {
        detailsHTML += `
            <div class="mb-3">
                <a href="${event.url}" class="btn btn-primary btn-sm">
                    <i class="ri-eye-line"></i> View Details
                </a>
            </div>
        `;
    }
    
    modalBody.innerHTML = detailsHTML;
    
    var modal = new bootstrap.Modal(document.getElementById('eventModal'));
    modal.show();
}

function getEventTypeName(type) {
    switch(type) {
        case 'breeding': return 'Breeding Event';
        case 'calving': return 'Expected Calving';
        case 'dry_off': return 'Dry-off Date';
        case 'pregnancy_check': return 'Pregnancy Check';
        default: return 'Event';
    }
}

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        window.location.href = '?month={{ prev_month }}&year={{ prev_year }}';
    } else if (e.key === 'ArrowRight') {
        window.location.href = '?month={{ next_month }}&year={{ next_year }}';
    }
});
</script>

<style>
.fc-event-title {
    font-size: 11px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.fc-daygrid-event {
    margin: 1px 0;
    border-radius: 3px;
}

.fc-h-event {
    border: none !important;
}

.fc-event-main {
    padding: 1px 3px;
}

.fc-day-today {
    background-color: rgba(13, 164, 135, 0.1) !important;
}

.fc-daygrid-day-number {
    font-weight: 600;
    color: #495057;
}

.fc-col-header-cell {
    background-color: #f8f9fa;
    font-weight: 600;
}

.fc-scrollgrid {
    border: 1px solid #dee2e6;
}

.fc-scrollgrid td, .fc-scrollgrid th {
    border-color: #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fc-event-title {
        font-size: 10px;
    }
    
    .fc-daygrid-event {
        margin: 0.5px 0;
    }
}
</style>
{% endblock %}