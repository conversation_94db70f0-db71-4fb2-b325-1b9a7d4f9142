{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}Breeding Records{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Breeding Records Content -->

        <div class="row mb-3">
            <div class="col-auto ms-auto">
                <a href="{% url 'dairy:breeding_create' %}" class="btn btn-primary btn-sm">
                    <i class="ri-add-line"></i> New Breeding
                </a>
            </div>
        </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Filter Records</h6>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-control">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="successful" {% if status_filter == 'successful' %}selected{% endif %}>Successful</option>
                                <option value="failed" {% if status_filter == 'failed' %}selected{% endif %}>Failed</option>
                                <option value="aborted" {% if status_filter == 'aborted' %}selected{% endif %}>Aborted</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Method</label>
                            <select name="method" class="form-control">
                                <option value="all" {% if method_filter == 'all' %}selected{% endif %}>All</option>
                                <option value="natural" {% if method_filter == 'natural' %}selected{% endif %}>Natural</option>
                                <option value="artificial" {% if method_filter == 'artificial' %}selected{% endif %}>Artificial</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Date From</label>
                            <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Date To</label>
                            <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Animal Tag</label>
                            <input type="text" name="animal" class="form-control" placeholder="Search tag" value="{{ animal_filter }}">
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="ri-search-line"></i> Apply Filters
                                </button>
                                <a href="{% url 'dairy:breeding_list' %}" class="btn btn-secondary btn-sm">
                                    <i class="ri-refresh-line"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Records</h6>
                    <div class="card-header-right">
                        <button class="btn btn-sm btn-outline-primary" onclick="exportTable()">
                            <i class="ri-download-line"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if breedings %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="breeding-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Animal</th>
                                    <th>Method</th>
                                    <th>Bull/Semen</th>
                                    <th>Lactation</th>
                                    <th>Attempt</th>
                                    <th>Status</th>
                                    <th>Expected Calving</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for breeding in breedings %}
                                <tr>
                                    <td>{{ breeding.date_of_insemination|date:"d M Y" }}</td>
                                    <td>
                                        <a href="{% url 'dairy:animal_detail' breeding.female_animal.pk %}">
                                            {{ breeding.female_animal.tag }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ breeding.get_breeding_method_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if breeding.breeding_method == 'natural' %}
                                            {% if breeding.bull %}
                                                <a href="{% url 'dairy:animal_detail' breeding.bull.pk %}">
                                                    {{ breeding.bull.tag }}
                                                </a>
                                            {% else %}
                                                -
                                            {% endif %}
                                        {% else %}
                                            {{ breeding.semen_tag|default:"-" }}
                                        {% endif %}
                                    </td>
                                    <td>{{ breeding.lactation_number }}</td>
                                    <td>{{ breeding.attempt }}</td>
                                    <td>
                                        <span class="badge {% if breeding.status == 'successful' %}bg-success{% elif breeding.status == 'failed' %}bg-danger{% elif breeding.status == 'aborted' %}bg-dark{% else %}bg-warning{% endif %}">
                                            {{ breeding.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if breeding.expected_calving_date %}
                                            {{ breeding.expected_calving_date|date:"d M Y" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{% url 'dairy:breeding_detail' breeding.pk %}" class="btn btn-primary" title="View Details">
                                                <i class="ri-eye-line"></i>
                                            </a>
                                            {% if breeding.status == 'pending' %}
                                            <a href="{% url 'dairy:pregnancy_check_create' breeding.pk %}" class="btn btn-warning" title="Pregnancy Check">
                                                <i class="ri-stethoscope-line"></i>
                                            </a>
                                            {% endif %}
                                            {% if breeding.status == 'successful' and not breeding.calving_record %}
                                            <a href="{% url 'dairy:calving_record_create' breeding.pk %}" class="btn btn-info" title="Record Calving">
                                                <i class="ri-baby-line"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if breedings.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if breedings.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ breedings.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if animal_filter %}&animal={{ animal_filter }}{% endif %}">
                                    Previous
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for num in breedings.paginator.page_range %}
                                {% if breedings.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > breedings.number|add:'-3' and num < breedings.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if animal_filter %}&animal={{ animal_filter }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if breedings.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ breedings.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if method_filter %}&method={{ method_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if animal_filter %}&animal={{ animal_filter }}{% endif %}">
                                    Next
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="ri-heart-line display-1 text-muted"></i>
                        <p class="text-muted mt-3">No breeding records found</p>
                        <div>
                            <a href="{% url 'dairy:breeding_create' %}" class="btn btn-primary btn-sm">
                                <i class="ri-add-line"></i> Create First Record
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script>
function exportTable() {
    // Simple CSV export
    var table = document.getElementById('breeding-table');
    var csv = [];
    var rows = table.querySelectorAll('tr');
    
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (var j = 0; j < cols.length - 1; j++) { // Skip actions column
            row.push(cols[j].innerText);
        }
        
        csv.push(row.join(','));
    }
    
    var csvContent = 'data:text/csv;charset=utf-8,' + csv.join('\n');
    var encodedUri = encodeURI(csvContent);
    var link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', 'breeding_records.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
{% endblock %}