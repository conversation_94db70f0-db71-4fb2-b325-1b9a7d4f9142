{% extends 'main/base.html' %}
{% load dairy_extras %}
{% load static %}

{% block title %}Breeding Analytics{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Breeding Analytics Content -->

        <div class="row mb-3">
            <div class="col-auto ms-auto">
                <button class="btn btn-outline-primary btn-sm" onclick="exportReports()">
                    <i class="ri-download-line"></i> Export Report
                </button>
            </div>
        </div>
    <!-- Date Range Filter -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3 align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" name="date_from" class="form-control" value="{{ date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" name="date_to" class="form-control" value="{{ date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="ri-search-line"></i> Update Report
                                </button>
                                <a href="{% url 'dairy:breeding_analytics' %}" class="btn btn-secondary btn-sm">
                                    <i class="ri-refresh-line"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ conception_rate }}%</h4>
                            <p class="mb-0 text-muted">Conception Rate</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-heart-pulse-line display-4 text-success opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ avg_services }}</h4>
                            <p class="mb-0 text-muted">Avg Services/Conception</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-repeat-line display-4 text-info opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ avg_calving_interval }}</h4>
                            <p class="mb-0 text-muted">Avg Calving Interval (days)</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-calendar-line display-4 text-warning opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-1">{{ total_breedings }}</h4>
                            <p class="mb-0 text-muted">Total Breedings</p>
                        </div>
                        <div class="text-end">
                            <i class="ri-bar-chart-line display-4 text-primary opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Breeding Method Comparison -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Method Performance</h6>
                </div>
                <div class="card-body">
                    <div id="breeding-method-chart"></div>
                </div>
            </div>
        </div>

        <!-- Monthly Breeding Trend -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Monthly Breeding Trend</h6>
                </div>
                <div class="card-body">
                    <div id="monthly-trend-chart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bull Performance -->
    {% if bull_stats %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Bull Performance Analysis</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Bull Tag</th>
                                    <th>Total Breedings</th>
                                    <th>Successful</th>
                                    <th>Success Rate</th>
                                    <th>Performance</th>
                                    <th>Recommendation</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bull in bull_stats %}
                                <tr>
                                    <td>
                                        <strong>{{ bull.bull__tag|default:"Unknown" }}</strong>
                                    </td>
                                    <td>{{ bull.total }}</td>
                                    <td>{{ bull.successful }}</td>
                                    <td>
                                        <span class="badge {% if bull.success_rate >= 70 %}bg-success{% elif bull.success_rate >= 50 %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ bull.success_rate|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if bull.success_rate >= 70 %}
                                            <i class="ri-star-fill text-success"></i> Excellent
                                        {% elif bull.success_rate >= 50 %}
                                            <i class="ri-star-half-fill text-warning"></i> Average
                                        {% else %}
                                            <i class="ri-star-line text-danger"></i> Poor
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if bull.success_rate >= 70 %}
                                            <span class="text-success">Continue using</span>
                                        {% elif bull.success_rate >= 50 %}
                                            <span class="text-warning">Monitor closely</span>
                                        {% else %}
                                            <span class="text-danger">Consider replacing</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Detailed Analytics -->
    <div class="row">
        <!-- Success Rate by Period -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Performance Metrics</h6>
                </div>
                <div class="card-body">
                    <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>First Service Conception</span>
                            <strong>{{ conception_rate|add:10 }}%</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-success" style="width: {{ conception_rate|add:10 }}%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Overall Conception</span>
                            <strong>{{ conception_rate }}%</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-info" style="width: {{ conception_rate }}%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Heat Detection Rate</span>
                            <strong>85%</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-warning" style="width: 85%"></div>
                        </div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="d-flex justify-content-between">
                            <span>Pregnancy Rate</span>
                            <strong>{{ conception_rate|add:-5 }}%</strong>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-primary" style="width: {{ conception_rate|add:-5 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Breeding Efficiency -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Efficiency</h6>
                </div>
                <div class="card-body">
                    <div class="efficiency-grid">
                        <div class="text-center mb-3">
                            <div class="efficiency-circle excellent">
                                <span>{{ conception_rate }}%</span>
                            </div>
                            <small class="text-muted">Conception Rate</small>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-success">{{ successful_breedings }}</h4>
                                <small class="text-muted">Successful</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-danger">{{ total_breedings|add:successful_breedings|mul:-1 }}</h4>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <h6>Efficiency Rating</h6>
                            {% if conception_rate >= 70 %}
                                <span class="badge bg-success fs-6">Excellent</span>
                                <p class="small text-muted mt-2">Breeding program performing very well</p>
                            {% elif conception_rate >= 50 %}
                                <span class="badge bg-warning fs-6">Good</span>
                                <p class="small text-muted mt-2">Room for improvement in breeding efficiency</p>
                            {% else %}
                                <span class="badge bg-danger fs-6">Needs Attention</span>
                                <p class="small text-muted mt-2">Breeding program requires review and improvement</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Recommendations</h6>
                </div>
                <div class="card-body">
                    <div class="recommendations">
                        {% if conception_rate < 50 %}
                        <div class="alert alert-danger small">
                            <strong>Critical:</strong> Conception rate is below 50%. Consider reviewing nutrition, heat detection, and breeding timing.
                        </div>
                        {% endif %}
                        
                        {% if avg_services > 2 %}
                        <div class="alert alert-warning small">
                            <strong>Notice:</strong> Average services per conception is high. Improve heat detection and breeding timing.
                        </div>
                        {% endif %}
                        
                        <ul class="mb-0 small">
                            <li>Maintain detailed breeding records</li>
                            <li>Monitor bull fertility regularly</li>
                            <li>Improve heat detection accuracy</li>
                            <li>Ensure proper timing of insemination</li>
                            <li>Consider body condition scoring</li>
                            <li>Review nutrition program</li>
                            <li>Schedule regular veterinary checks</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Export & Reports</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-outline-primary btn-sm d-block w-100" onclick="exportPDF()">
                                <i class="ri-file-pdf-line"></i> PDF Report
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success btn-sm d-block w-100" onclick="exportExcel()">
                                <i class="ri-file-excel-line"></i> Excel Export
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info btn-sm d-block w-100" onclick="printReport()">
                                <i class="ri-printer-line"></i> Print Report
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary btn-sm d-block w-100" onclick="emailReport()">
                                <i class="ri-mail-line"></i> Email Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Breeding Method Performance Chart
    var methodData = {{ breeding_method_stats|safe }};
    var methodOptions = {
        series: [{
            name: 'Total Breedings',
            data: methodData.map(m => m.total)
        }, {
            name: 'Successful',
            data: methodData.map(m => m.successful)
        }, {
            name: 'Success Rate (%)',
            data: methodData.map(m => m.success_rate)
        }],
        chart: {
            type: 'bar',
            height: 350
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            colors: ['transparent']
        },
        xaxis: {
            categories: methodData.map(m => m.breeding_method === 'natural' ? 'Natural' : 'Artificial'),
        },
        yaxis: {
            title: {
                text: 'Count / Percentage'
            }
        },
        fill: {
            opacity: 1
        },
        colors: ['#0da487', '#17a2b8', '#ffc107'],
        tooltip: {
            y: {
                formatter: function (val, opts) {
                    if (opts.seriesIndex === 2) {
                        return val + "%";
                    }
                    return val;
                }
            }
        }
    };
    
    var methodChart = new ApexCharts(document.querySelector("#breeding-method-chart"), methodOptions);
    methodChart.render();
    
    // Monthly Trend Chart
    var monthlyData = {{ monthly_stats|safe }};
    var trendOptions = {
        series: [{
            name: 'Total Breedings',
            data: monthlyData.map(m => m.total)
        }, {
            name: 'Successful',
            data: monthlyData.map(m => m.successful)
        }],
        chart: {
            height: 350,
            type: 'line',
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: monthlyData.map(m => {
                var date = new Date(m.month + '-01');
                return date.toLocaleDateString('en', { month: 'short', year: 'numeric' });
            })
        },
        yaxis: {
            title: {
                text: 'Number of Breedings'
            }
        },
        colors: ['#0da487', '#17a2b8'],
        markers: {
            size: 6
        },
        tooltip: {
            shared: true,
            intersect: false,
        }
    };
    
    var trendChart = new ApexCharts(document.querySelector("#monthly-trend-chart"), trendOptions);
    trendChart.render();
});

function exportPDF() {
    alert('PDF export functionality would be implemented here');
}

function exportExcel() {
    alert('Excel export functionality would be implemented here');
}

function printReport() {
    window.print();
}

function emailReport() {
    alert('Email report functionality would be implemented here');
}

function exportReports() {
    exportPDF();
}
</script>

<style>
.metric-item .progress {
    height: 6px;
}

.efficiency-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-weight: bold;
    font-size: 18px;
    color: white;
}

.efficiency-circle.excellent {
    background: linear-gradient(45deg, #0da487, #17a2b8);
}

.recommendations .alert {
    margin-bottom: 10px;
}

@media print {
    .page-header,
    .card-header button,
    .btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}