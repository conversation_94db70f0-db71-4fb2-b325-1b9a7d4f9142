{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
/* Custom styles for Select2 animal dropdown */
.select2-result-animal {
    padding: 8px 0;
}

.select2-result-animal .animal-tag {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.select2-result-animal .animal-category {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option {
    padding: 8px 12px;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted .animal-category {
    color: rgba(255, 255, 255, 0.8);
}

/* Improve search input styling */
.select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 8px 12px;
}

/* Make dropdown wider for better readability */
.select2-container--bootstrap-5 .select2-dropdown {
    min-width: 300px;
}

/* Select2 styling to match form-floating theme fields exactly */
.form-floating.theme-form-floating {
    position: relative !important;
    overflow: visible !important;
    width: 100% !important;
    max-width: 100% !important;
}

.form-floating.theme-form-floating .select2-container {
    width: 100% !important;
    max-width: 100% !important;
    position: relative !important;
    overflow: visible !important;
}

/* Ensure the Select2 container spans exactly match the form field */
.form-floating.theme-form-floating .select2-container .select2-selection {
    box-sizing: border-box !important;
    width: 100% !important;
    max-width: 100% !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single {
    height: calc(3.5rem + 2px) !important;
    padding: 1rem 40px 0.25rem 0.75rem !important;
    border: 1px solid var(--theme-color) !important;
    border-radius: 0.375rem !important;
    background-color: #fff !important;
    font-size: 1rem !important;
    line-height: 1.25 !important;
    box-shadow: none !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single:focus,
.form-floating.theme-form-floating .select2-container.select2-container--open .select2-selection--single {
    border-color: var(--theme-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 164, 135, 0.25) !important;
    outline: 0 !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 0 40px 0 0 !important;
    margin: 0 !important;
    line-height: 1.25 !important;
    color: #212529 !important;
    display: block !important;
    width: calc(100% - 40px) !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__placeholder {
    color: transparent !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__arrow {
    height: calc(3.5rem + 2px) !important;
    position: absolute !important;
    top: 0 !important;
    right: 12px !important;
    width: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1 !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__arrow b {
    border-color: var(--theme-color) transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 5px 5px 0 5px !important;
    height: 0 !important;
    left: 50% !important;
    margin-left: -5px !important;
    margin-top: -2px !important;
    position: absolute !important;
    top: 50% !important;
    width: 0 !important;
}

.form-floating.theme-form-floating .select2-container.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent var(--theme-color) transparent !important;
    border-width: 0 5px 5px 5px !important;
}

/* Form-floating label positioning - ensure it works with Select2 */
.form-floating.theme-form-floating > label {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    height: 100% !important;
    padding: 1rem 0.75rem !important;
    pointer-events: none !important;
    border: 1px solid transparent !important;
    transform-origin: 0 0 !important;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out !important;
    z-index: 2 !important;
}

/* When Select2 has content or is focused, move label up */
.form-floating.theme-form-floating .select2-container ~ label,
.form-floating.theme-form-floating .select2-container.select2-container--focus ~ label {
    opacity: 0.65 !important;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem) !important;
}

/* Column container positioning for dropdown containment */
.col-md-6 {
    position: relative !important;
    overflow: visible !important;
}

/* Ensure Select2 dropdown respects column boundaries */
.col-md-6 .select2-container--default .select2-dropdown {
    width: 100% !important;
    max-width: 100% !important;
    left: 0 !important;
    right: 0 !important;
}

/* Dropdown positioning and styling */
.select2-container--default .select2-dropdown {
    border: 1px solid var(--theme-color) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    z-index: 1060 !important;
    margin-top: 2px !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Ensure dropdown appears below the field and is contained */
.select2-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.select2-container--default .select2-search--dropdown {
    padding: 4px !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem !important;
    padding: 0.375rem 0.75rem !important;
    width: 100% !important;
}

.select2-container--default .select2-results__option {
    padding: 6px 12px !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--theme-color) !important;
    color: white !important;
}

/* Clear button styling */
.form-floating.theme-form-floating .select2-container .select2-selection__clear {
    color: var(--theme-color) !important;
    font-size: 1.25rem !important;
    position: absolute !important;
    right: 2rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    cursor: pointer !important;
}

/* Prevent dropdown from extending beyond container boundaries */
.select2-container .select2-dropdown {
    max-width: 100% !important;
    overflow: hidden !important;
}

.select2-container .select2-dropdown .select2-results {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

.select2-container .select2-dropdown .select2-results__option {
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}
</style>
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- {{ title }} Content -->

    <div class="row">
        <div class="col-xl-8 col-lg-10 col-md-12 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Information</h6>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Female Animal -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.female_animal|add_class:"form-control" }}
                                    <label for="{{ form.female_animal.auto_id }}">Female Animal <span class="text-danger">*</span></label>
                                    {% if form.female_animal.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.female_animal.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="ri-search-line"></i> Type to search animals by tag or category
                                </small>
                                <div id="select2-debug" class="small text-info mt-1" style="display: none;">
                                    Debug: Select2 status will appear here
                                </div>
                            </div>

                            <!-- Date of Insemination -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.date_of_insemination|add_class:"form-control" }}
                                    <label for="{{ form.date_of_insemination.auto_id }}">Breeding Date <span class="text-danger">*</span></label>
                                    {% if form.date_of_insemination.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.date_of_insemination.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Breeding Method -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.breeding_method|add_class:"form-control" }}
                                    <label for="{{ form.breeding_method.auto_id }}">Breeding Method <span class="text-danger">*</span></label>
                                    {% if form.breeding_method.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.breeding_method.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Breeding Bull (for natural) -->
                            <div class="col-md-6 mb-3" id="bull-section" style="display: none;">
                                <div class="form-floating theme-form-floating">
                                    {{ form.bull|add_class:"form-control" }}
                                    <label for="{{ form.bull.auto_id }}">Breeding Bull <span class="text-danger">*</span></label>
                                    {% if form.bull.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.bull.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="ri-search-line"></i> Type to search bulls by tag
                                </small>
                            </div>

                            <!-- Semen Tag (for artificial) -->
                            <div class="col-md-6 mb-3" id="semen-section" style="display: none;">
                                <div class="form-floating theme-form-floating">
                                    {{ form.semen_tag|add_class:"form-control"|attr:"placeholder:e.g., AI001, SEM-2024-001, BUL123" }}
                                    <label for="{{ form.semen_tag.auto_id }}">Semen ID/Code <span class="text-danger">*</span></label>
                                    {% if form.semen_tag.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.semen_tag.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="ri-information-line"></i> Enter semen identifier/batch number (e.g., AI001, SEM-2024-001, BUL123)
                                </small>
                            </div>

                            <!-- Notes -->
                            <div class="col-12 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.comment|add_class:"form-control"|attr:"rows:3" }}
                                    <label for="{{ form.comment.auto_id }}">Notes</label>
                                    {% if form.comment.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.comment.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Information Alert -->
                        <div class="alert alert-info mb-3">
                            <i class="ri-information-line"></i>
                            <strong>Note:</strong> Expected calving date will be calculated as 280 days from breeding date. 
                            Dry-off date will be set to 60 days before expected calving.
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'dairy:breeding_list' %}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i> Save Breeding
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Breeding Tips -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-2">
                                <i class="ri-heart-line"></i> Natural Breeding
                            </h6>
                            <ul class="mb-3">
                                <li>Select a healthy, proven bull from your farm</li>
                                <li>Ensure bull is in good breeding condition</li>
                                <li>Monitor breeding process and record time</li>
                                <li>Allow adequate rest period between services</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success mb-2">
                                <i class="ri-test-tube-line"></i> Artificial Insemination
                            </h6>
                            <ul class="mb-3">
                                <li>Use proper semen identification codes</li>
                                <li>Check semen quality and expiry date</li>
                                <li>Maintain cold chain during transport</li>
                                <li>Record exact semen batch/straw number</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-info mb-0">
                        <strong>General Tips:</strong> Optimal breeding time is 12-18 hours after heat detection. Schedule pregnancy check 30-45 days after breeding. Monitor for return to heat around 21 days if breeding failed.
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if jQuery is loaded
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Select2 requires jQuery.');
        return;
    }

    const methodSelect = document.getElementById('{{ form.breeding_method.auto_id }}');
    const bullSection = document.getElementById('bull-section');
    const semenSection = document.getElementById('semen-section');
    const bullField = document.getElementById('{{ form.bull.auto_id }}');
    const semenField = document.getElementById('{{ form.semen_tag.auto_id }}');

    function toggleBreedingFields() {
        if (!methodSelect) return;

        const method = methodSelect.value;

        if (method === 'natural') {
            if (bullSection) bullSection.style.display = 'block';
            if (semenSection) semenSection.style.display = 'none';
            if (window.bullSelect2) {
                window.bullSelect2.enable(true);
            }
            if (bullField) bullField.required = true;
            if (semenField) {
                semenField.required = false;
                semenField.value = '';
            }
        } else if (method === 'artificial') {
            if (bullSection) bullSection.style.display = 'none';
            if (semenSection) semenSection.style.display = 'block';
            if (window.bullSelect2) {
                window.bullSelect2.enable(false);
            }
            if (bullField) bullField.required = false;
            if (semenField) semenField.required = true;
            if (window.bullSelect2) {
                window.bullSelect2.val(null).trigger('change');
            }
        } else {
            if (bullSection) bullSection.style.display = 'none';
            if (semenSection) semenSection.style.display = 'none';
            if (window.bullSelect2) {
                window.bullSelect2.enable(false);
            }
            if (bullField) bullField.required = false;
            if (semenField) semenField.required = false;
        }
    }

    if (methodSelect) {
        methodSelect.addEventListener('change', toggleBreedingFields);
    }

    // Initialize on page load
    toggleBreedingFields();
    
    // Select2 initialization moved to extra_js block to avoid conflicts
    console.log('Select2 will be initialized after all scripts load...');

    // Bull Select2 initialization also moved to extra_js block

    // Handle breeding method change for bull selection
    if (methodSelect) {
        methodSelect.addEventListener('change', function() {
            toggleBreedingFields();
        });
    }

    // Initialize on page load
    toggleBreedingFields();

    // Initialize date picker
    try {
        const dateElement = document.querySelector("#{{ form.date_of_insemination.auto_id }}");
        if (dateElement && typeof flatpickr !== 'undefined') {
            flatpickr("#{{ form.date_of_insemination.auto_id }}", {
                dateFormat: "Y-m-d",
                maxDate: "today",
                defaultDate: "today"
            });
        }
    } catch (error) {
        console.error('Error initializing date picker:', error);
    }

    // Get animal from URL parameter if present
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const animalId = urlParams.get('animal');
        if (animalId && window.femaleAnimalSelect2) {
            window.femaleAnimalSelect2.val(animalId).trigger('change');
        }
    } catch (error) {
        console.error('Error setting animal from URL parameter:', error);
    }

    // Form validation
    try {
        (function() {
            'use strict';
            var forms = document.querySelectorAll('.needs-validation');

            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    } catch (error) {
        console.error('Error initializing form validation:', error);
    }
});
</script>

{% endblock %}

{% block extra_js %}
<!-- Load Select2 after all other scripts to avoid conflicts -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
// Wait for all scripts to load and DOM to be ready
$(document).ready(function() {
    // Add a small delay to ensure all scripts are loaded
    setTimeout(function() {
        console.log('Initializing Select2 for breeding form...');
        console.log('jQuery available:', typeof $ !== 'undefined');
        console.log('Select2 available:', typeof $.fn.select2 !== 'undefined');

        // Update debug div
        const debugDiv = $('#select2-debug');
        if (debugDiv.length > 0) {
            debugDiv.show();
            debugDiv.html(
                'jQuery: ' + (typeof $ !== 'undefined' ? '✓' : '✗') + ' | ' +
                'Select2: ' + (typeof $.fn.select2 !== 'undefined' ? '✓' : '✗') + ' | ' +
                'Element: ' + ($('#{{ form.female_animal.auto_id }}').length > 0 ? '✓' : '✗')
            );
        }

        if (typeof $ === 'undefined') {
            console.error('jQuery is not available');
            if (debugDiv.length > 0) debugDiv.append(' | <span class="text-danger">jQuery missing</span>');
            return;
        }

        if (typeof $.fn.select2 === 'undefined') {
            console.error('Select2 is not available');
            if (debugDiv.length > 0) debugDiv.append(' | <span class="text-danger">Select2 missing</span>');
            return;
        }

        // Initialize Female Animal Select2 (simplified version)
        try {
            const femaleAnimalElement = $('#{{ form.female_animal.auto_id }}');
            console.log('Female animal element found:', femaleAnimalElement.length > 0);

            if (femaleAnimalElement.length > 0) {
                const dropdownParent = femaleAnimalElement.closest('.col-md-6');
                console.log('Dropdown parent found:', dropdownParent.length > 0);

                window.femaleAnimalSelect2 = femaleAnimalElement.select2({
                    placeholder: 'Search and select female animal...',
                    allowClear: true,
                    width: '100%',
                    dropdownAutoWidth: false,
                    dropdownParent: dropdownParent
                });
                console.log('Female animal Select2 initialized successfully');
                const debugDiv = $('#select2-debug');
                if (debugDiv.length > 0) debugDiv.append(' | <span class="text-success">Female ✓</span>');
            }
        } catch (error) {
            console.error('Error initializing female animal Select2:', error);
            const debugDiv = $('#select2-debug');
            if (debugDiv.length > 0) debugDiv.append(' | <span class="text-danger">Female ✗: ' + error.message + '</span>');
        }

        // Initialize Bull Select2 (simplified version)
        try {
            const bullElement = $('#{{ form.bull.auto_id }}');
            console.log('Bull element found:', bullElement.length > 0);

            if (bullElement.length > 0) {
                const bullDropdownParent = bullElement.closest('.col-md-6');
                console.log('Bull dropdown parent found:', bullDropdownParent.length > 0);

                window.bullSelect2 = bullElement.select2({
                    placeholder: 'Search and select bull...',
                    allowClear: true,
                    width: '100%',
                    dropdownAutoWidth: false,
                    dropdownParent: bullDropdownParent
                });
                console.log('Bull Select2 initialized successfully');
                const debugDiv = $('#select2-debug');
                if (debugDiv.length > 0) debugDiv.append(' | <span class="text-success">Bull ✓</span>');
            }
        } catch (error) {
            console.error('Error initializing bull Select2:', error);
            const debugDiv = $('#select2-debug');
            if (debugDiv.length > 0) debugDiv.append(' | <span class="text-danger">Bull ✗: ' + error.message + '</span>');
        }

    }, 500); // Wait 500ms for all scripts to load
});
</script>
{% endblock %}