{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- {{ title }} Content -->

    <div class="row">
        <div class="col-xl-8 col-lg-10 col-md-12 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Information</h6>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Female Animal -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.female_animal|add_class:"form-control" }}
                                    <label for="{{ form.female_animal.auto_id }}">Female Animal <span class="text-danger">*</span></label>
                                    {% if form.female_animal.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.female_animal.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Date of Insemination -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.date_of_insemination|add_class:"form-control" }}
                                    <label for="{{ form.date_of_insemination.auto_id }}">Breeding Date <span class="text-danger">*</span></label>
                                    {% if form.date_of_insemination.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.date_of_insemination.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Breeding Method -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.breeding_method|add_class:"form-control" }}
                                    <label for="{{ form.breeding_method.auto_id }}">Breeding Method <span class="text-danger">*</span></label>
                                    {% if form.breeding_method.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.breeding_method.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Breeding Bull (for natural) -->
                            <div class="col-md-6 mb-3" id="bull-section" style="display: none;">
                                <div class="form-floating theme-form-floating">
                                    {{ form.bull|add_class:"form-control" }}
                                    <label for="{{ form.bull.auto_id }}">Breeding Bull <span class="text-danger">*</span></label>
                                    {% if form.bull.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.bull.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="ri-information-line"></i> Select a male breeding bull from your farm
                                </small>
                            </div>

                            <!-- Semen Tag (for artificial) -->
                            <div class="col-md-6 mb-3" id="semen-section" style="display: none;">
                                <div class="form-floating theme-form-floating">
                                    {{ form.semen_tag|add_class:"form-control"|attr:"placeholder:e.g., AI001, SEM-2024-001, BUL123" }}
                                    <label for="{{ form.semen_tag.auto_id }}">Semen ID/Code <span class="text-danger">*</span></label>
                                    {% if form.semen_tag.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.semen_tag.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="ri-information-line"></i> Enter semen identifier/batch number (e.g., AI001, SEM-2024-001, BUL123)
                                </small>
                            </div>

                            <!-- Notes -->
                            <div class="col-12 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.comment|add_class:"form-control"|attr:"rows:3" }}
                                    <label for="{{ form.comment.auto_id }}">Notes</label>
                                    {% if form.comment.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.comment.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Information Alert -->
                        <div class="alert alert-info mb-3">
                            <i class="ri-information-line"></i>
                            <strong>Note:</strong> Expected calving date will be calculated as 280 days from breeding date. 
                            Dry-off date will be set to 60 days before expected calving.
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'dairy:breeding_list' %}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i> Save Breeding
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Breeding Tips -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-2">
                                <i class="ri-heart-line"></i> Natural Breeding
                            </h6>
                            <ul class="mb-3">
                                <li>Select a healthy, proven bull from your farm</li>
                                <li>Ensure bull is in good breeding condition</li>
                                <li>Monitor breeding process and record time</li>
                                <li>Allow adequate rest period between services</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success mb-2">
                                <i class="ri-test-tube-line"></i> Artificial Insemination
                            </h6>
                            <ul class="mb-3">
                                <li>Use proper semen identification codes</li>
                                <li>Check semen quality and expiry date</li>
                                <li>Maintain cold chain during transport</li>
                                <li>Record exact semen batch/straw number</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-info mb-0">
                        <strong>General Tips:</strong> Optimal breeding time is 12-18 hours after heat detection. Schedule pregnancy check 30-45 days after breeding. Monitor for return to heat around 21 days if breeding failed.
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    const methodSelect = document.getElementById('{{ form.breeding_method.auto_id }}');
    const bullSection = document.getElementById('bull-section');
    const semenSection = document.getElementById('semen-section');
    const bullField = document.getElementById('{{ form.bull.auto_id }}');
    const semenField = document.getElementById('{{ form.semen_tag.auto_id }}');

    function toggleBreedingFields() {
        const method = methodSelect.value;
        
        if (method === 'natural') {
            bullSection.style.display = 'block';
            semenSection.style.display = 'none';
            if (window.bullSelect2) {
                window.bullSelect2.enable(true);
            }
            bullField.required = true;
            semenField.required = false;
            semenField.value = '';
        } else if (method === 'artificial') {
            bullSection.style.display = 'none';
            semenSection.style.display = 'block';
            if (window.bullSelect2) {
                window.bullSelect2.enable(false);
            }
            bullField.required = false;
            semenField.required = true;
            if (window.bullSelect2) {
                window.bullSelect2.val(null).trigger('change');
            }
        } else {
            bullSection.style.display = 'none';
            semenSection.style.display = 'none';
            if (window.bullSelect2) {
                window.bullSelect2.enable(false);
            }
            bullField.required = false;
            semenField.required = false;
        }
    }

    methodSelect.addEventListener('change', toggleBreedingFields);
    
    // Initialize on page load
    toggleBreedingFields();
    
    // Initialize Select2 on animal dropdowns
    window.femaleAnimalSelect2 = $('#{{ form.female_animal.auto_id }}').select2({
        theme: 'bootstrap-5',
        placeholder: 'Search and select female animal...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        templateResult: function(data) {
            if (!data.id) return data.text;
            
            // Extract animal info from the option text
            var text = data.text;
            var match = text.match(/^(.+?)\s+\((.+?)\)$/);
            if (match) {
                var tag = match[1];
                var category = match[2];
                return $('<span><strong>' + tag + '</strong> <small class="text-muted">(' + category + ')</small></span>');
            }
            return $('<span>' + text + '</span>');
        },
        templateSelection: function(data) {
            if (!data.id) return data.text;
            
            // Extract animal info for selection display
            var text = data.text;
            var match = text.match(/^(.+?)\s+\((.+?)\)$/);
            if (match) {
                var tag = match[1];
                return tag; // Show only the tag in selection
            }
            return text;
        }
    });

    window.bullSelect2 = $('#{{ form.bull.auto_id }}').select2({
        theme: 'bootstrap-5',
        placeholder: 'Search and select bull...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        templateResult: function(data) {
            if (!data.id) return data.text;
            
            // Extract animal info from the option text
            var text = data.text;
            var match = text.match(/^(.+?)\s+\((.+?)\)$/);
            if (match) {
                var tag = match[1];
                var category = match[2];
                return $('<span><strong>' + tag + '</strong> <small class="text-muted">(' + category + ')</small></span>');
            }
            return $('<span>' + text + '</span>');
        },
        templateSelection: function(data) {
            if (!data.id) return data.text;
            
            // Extract animal info for selection display
            var text = data.text;
            var match = text.match(/^(.+?)\s+\((.+?)\)$/);
            if (match) {
                var tag = match[1];
                return tag; // Show only the tag in selection
            }
            return text;
        }
    });

    // Handle breeding method change for bull selection
    methodSelect.addEventListener('change', function() {
        toggleBreedingFields();
    });

    // Initialize on page load
    toggleBreedingFields();

    // Initialize date picker
    flatpickr("#{{ form.date_of_insemination.auto_id }}", {
        dateFormat: "Y-m-d",
        maxDate: "today",
        defaultDate: "today"
    });

    // Get animal from URL parameter if present
    const urlParams = new URLSearchParams(window.location.search);
    const animalId = urlParams.get('animal');
    if (animalId) {
        window.femaleAnimalSelect2.val(animalId).trigger('change');
    }

    // Form validation
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();
});
</script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
{% endblock %}