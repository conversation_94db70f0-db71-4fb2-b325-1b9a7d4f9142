{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
/* Custom styles for Select2 animal dropdown */
.select2-result-animal {
    padding: 8px 0;
}

.select2-result-animal .animal-tag {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.select2-result-animal .animal-category {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option {
    padding: 8px 12px;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted .animal-category {
    color: rgba(255, 255, 255, 0.8);
}

/* Improve search input styling */
.select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 8px 12px;
}

/* Make dropdown wider for better readability */
.select2-container--bootstrap-5 .select2-dropdown {
    min-width: 300px;
}

/* Fix for form-floating conflicts */
.select2-container {
    z-index: 1050;
}

.select2-container .select2-selection--single {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem 0.25rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 0;
    line-height: 1.5;
}

.select2-container .select2-selection--single .select2-selection__arrow {
    height: calc(3.5rem + 2px);
    right: 10px;
}
</style>
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- {{ title }} Content -->

    <div class="row">
        <div class="col-xl-8 col-lg-10 col-md-12 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Information</h6>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Female Animal -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.female_animal.auto_id }}" class="form-label">Female Animal <span class="text-danger">*</span></label>
                                {{ form.female_animal|add_class:"form-control" }}
                                {% if form.female_animal.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.female_animal.errors.0 }}
                                </div>
                                {% endif %}
                                <small class="text-muted">
                                    <i class="ri-search-line"></i> Type to search animals by tag or category
                                </small>
                            </div>

                            <!-- Date of Insemination -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.date_of_insemination|add_class:"form-control" }}
                                    <label for="{{ form.date_of_insemination.auto_id }}">Breeding Date <span class="text-danger">*</span></label>
                                    {% if form.date_of_insemination.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.date_of_insemination.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Breeding Method -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.breeding_method|add_class:"form-control" }}
                                    <label for="{{ form.breeding_method.auto_id }}">Breeding Method <span class="text-danger">*</span></label>
                                    {% if form.breeding_method.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.breeding_method.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Breeding Bull (for natural) -->
                            <div class="col-md-6 mb-3" id="bull-section" style="display: none;">
                                <label for="{{ form.bull.auto_id }}" class="form-label">Breeding Bull <span class="text-danger">*</span></label>
                                {{ form.bull|add_class:"form-control" }}
                                {% if form.bull.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.bull.errors.0 }}
                                </div>
                                {% endif %}
                                <small class="text-muted">
                                    <i class="ri-search-line"></i> Type to search bulls by tag
                                </small>
                            </div>

                            <!-- Semen Tag (for artificial) -->
                            <div class="col-md-6 mb-3" id="semen-section" style="display: none;">
                                <div class="form-floating theme-form-floating">
                                    {{ form.semen_tag|add_class:"form-control"|attr:"placeholder:e.g., AI001, SEM-2024-001, BUL123" }}
                                    <label for="{{ form.semen_tag.auto_id }}">Semen ID/Code <span class="text-danger">*</span></label>
                                    {% if form.semen_tag.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.semen_tag.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    <i class="ri-information-line"></i> Enter semen identifier/batch number (e.g., AI001, SEM-2024-001, BUL123)
                                </small>
                            </div>

                            <!-- Notes -->
                            <div class="col-12 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.comment|add_class:"form-control"|attr:"rows:3" }}
                                    <label for="{{ form.comment.auto_id }}">Notes</label>
                                    {% if form.comment.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.comment.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Information Alert -->
                        <div class="alert alert-info mb-3">
                            <i class="ri-information-line"></i>
                            <strong>Note:</strong> Expected calving date will be calculated as 280 days from breeding date. 
                            Dry-off date will be set to 60 days before expected calving.
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% url 'dairy:breeding_list' %}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i> Save Breeding
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Breeding Tips -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-2">
                                <i class="ri-heart-line"></i> Natural Breeding
                            </h6>
                            <ul class="mb-3">
                                <li>Select a healthy, proven bull from your farm</li>
                                <li>Ensure bull is in good breeding condition</li>
                                <li>Monitor breeding process and record time</li>
                                <li>Allow adequate rest period between services</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success mb-2">
                                <i class="ri-test-tube-line"></i> Artificial Insemination
                            </h6>
                            <ul class="mb-3">
                                <li>Use proper semen identification codes</li>
                                <li>Check semen quality and expiry date</li>
                                <li>Maintain cold chain during transport</li>
                                <li>Record exact semen batch/straw number</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-info mb-0">
                        <strong>General Tips:</strong> Optimal breeding time is 12-18 hours after heat detection. Schedule pregnancy check 30-45 days after breeding. Monitor for return to heat around 21 days if breeding failed.
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if jQuery is loaded
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded. Select2 requires jQuery.');
        return;
    }

    const methodSelect = document.getElementById('{{ form.breeding_method.auto_id }}');
    const bullSection = document.getElementById('bull-section');
    const semenSection = document.getElementById('semen-section');
    const bullField = document.getElementById('{{ form.bull.auto_id }}');
    const semenField = document.getElementById('{{ form.semen_tag.auto_id }}');

    function toggleBreedingFields() {
        if (!methodSelect) return;

        const method = methodSelect.value;

        if (method === 'natural') {
            if (bullSection) bullSection.style.display = 'block';
            if (semenSection) semenSection.style.display = 'none';
            if (window.bullSelect2) {
                window.bullSelect2.enable(true);
            }
            if (bullField) bullField.required = true;
            if (semenField) {
                semenField.required = false;
                semenField.value = '';
            }
        } else if (method === 'artificial') {
            if (bullSection) bullSection.style.display = 'none';
            if (semenSection) semenSection.style.display = 'block';
            if (window.bullSelect2) {
                window.bullSelect2.enable(false);
            }
            if (bullField) bullField.required = false;
            if (semenField) semenField.required = true;
            if (window.bullSelect2) {
                window.bullSelect2.val(null).trigger('change');
            }
        } else {
            if (bullSection) bullSection.style.display = 'none';
            if (semenSection) semenSection.style.display = 'none';
            if (window.bullSelect2) {
                window.bullSelect2.enable(false);
            }
            if (bullField) bullField.required = false;
            if (semenField) semenField.required = false;
        }
    }

    if (methodSelect) {
        methodSelect.addEventListener('change', toggleBreedingFields);
    }

    // Initialize on page load
    toggleBreedingFields();
    
    // Initialize Select2 on animal dropdowns with enhanced search
    try {
        const femaleAnimalElement = $('#{{ form.female_animal.auto_id }}');
        if (femaleAnimalElement.length > 0) {
            window.femaleAnimalSelect2 = femaleAnimalElement.select2({
                theme: 'bootstrap-5',
                placeholder: 'Search and select female animal...',
                allowClear: true,
                width: '100%',
                dropdownAutoWidth: true,
                minimumInputLength: 0,
                escapeMarkup: function(markup) {
                    return markup;
                },
                templateResult: function(data) {
                    if (!data.id) return data.text;

                    // Extract animal info from the option text
                    var text = data.text;
                    var match = text.match(/^(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var tag = match[1];
                        var category = match[2];
                        return '<div class="select2-result-animal">' +
                               '<div class="animal-tag"><strong>' + tag + '</strong></div>' +
                               '<div class="animal-category text-muted">(' + category + ')</div>' +
                               '</div>';
                    }
                    return '<div>' + text + '</div>';
                },
                templateSelection: function(data) {
                    if (!data.id) return data.text;

                    // Extract animal info for selection display
                    var text = data.text;
                    var match = text.match(/^(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var tag = match[1];
                        var category = match[2];
                        return tag + ' (' + category + ')'; // Show tag and category in selection
                    }
                    return text;
                },
                matcher: function(params, data) {
                    // If there are no search terms, return all data
                    if ($.trim(params.term) === '') {
                        return data;
                    }

                    // Do not display the item if there is no 'text' property
                    if (typeof data.text === 'undefined') {
                        return null;
                    }

                    // Search in both tag and category
                    var text = data.text.toLowerCase();
                    var term = params.term.toLowerCase();

                    if (text.indexOf(term) > -1) {
                        return data;
                    }

                    // Return `null` if the term should not be displayed
                    return null;
                }
            });
            console.log('Female animal Select2 initialized successfully');
        } else {
            console.error('Female animal select element not found');
        }
    } catch (error) {
        console.error('Error initializing female animal Select2:', error);
    }

    try {
        const bullElement = $('#{{ form.bull.auto_id }}');
        if (bullElement.length > 0) {
            window.bullSelect2 = bullElement.select2({
                theme: 'bootstrap-5',
                placeholder: 'Search and select bull...',
                allowClear: true,
                width: '100%',
                dropdownAutoWidth: true,
                minimumInputLength: 0,
                escapeMarkup: function(markup) {
                    return markup;
                },
                templateResult: function(data) {
                    if (!data.id) return data.text;

                    // Extract animal info from the option text
                    var text = data.text;
                    var match = text.match(/^(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var tag = match[1];
                        var category = match[2];
                        return '<div class="select2-result-animal">' +
                               '<div class="animal-tag"><strong>' + tag + '</strong></div>' +
                               '<div class="animal-category text-muted">(' + category + ')</div>' +
                               '</div>';
                    }
                    return '<div>' + text + '</div>';
                },
                templateSelection: function(data) {
                    if (!data.id) return data.text;

                    // Extract animal info for selection display
                    var text = data.text;
                    var match = text.match(/^(.+?)\s+\((.+?)\)$/);
                    if (match) {
                        var tag = match[1];
                        var category = match[2];
                        return tag + ' (' + category + ')'; // Show tag and category in selection
                    }
                    return text;
                },
                matcher: function(params, data) {
                    // If there are no search terms, return all data
                    if ($.trim(params.term) === '') {
                        return data;
                    }

                    // Do not display the item if there is no 'text' property
                    if (typeof data.text === 'undefined') {
                        return null;
                    }

                    // Search in both tag and category
                    var text = data.text.toLowerCase();
                    var term = params.term.toLowerCase();

                    if (text.indexOf(term) > -1) {
                        return data;
                    }

                    // Return `null` if the term should not be displayed
                    return null;
                }
            });
            console.log('Bull Select2 initialized successfully');
        } else {
            console.error('Bull select element not found');
        }
    } catch (error) {
        console.error('Error initializing bull Select2:', error);
    }

    // Handle breeding method change for bull selection
    if (methodSelect) {
        methodSelect.addEventListener('change', function() {
            toggleBreedingFields();
        });
    }

    // Initialize on page load
    toggleBreedingFields();

    // Initialize date picker
    try {
        const dateElement = document.querySelector("#{{ form.date_of_insemination.auto_id }}");
        if (dateElement && typeof flatpickr !== 'undefined') {
            flatpickr("#{{ form.date_of_insemination.auto_id }}", {
                dateFormat: "Y-m-d",
                maxDate: "today",
                defaultDate: "today"
            });
        }
    } catch (error) {
        console.error('Error initializing date picker:', error);
    }

    // Get animal from URL parameter if present
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const animalId = urlParams.get('animal');
        if (animalId && window.femaleAnimalSelect2) {
            window.femaleAnimalSelect2.val(animalId).trigger('change');
        }
    } catch (error) {
        console.error('Error setting animal from URL parameter:', error);
    }

    // Form validation
    try {
        (function() {
            'use strict';
            var forms = document.querySelectorAll('.needs-validation');

            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    } catch (error) {
        console.error('Error initializing form validation:', error);
    }
});
</script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Fallback initialization for Select2 -->
<script>
// Fallback function to initialize Select2 if it fails initially
function initializeSelect2Fallback() {
    setTimeout(function() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            // Try to initialize female animal select2 if not already done
            if (!window.femaleAnimalSelect2) {
                const femaleAnimalElement = $('#{{ form.female_animal.auto_id }}');
                if (femaleAnimalElement.length > 0 && !femaleAnimalElement.hasClass('select2-hidden-accessible')) {
                    try {
                        window.femaleAnimalSelect2 = femaleAnimalElement.select2({
                            theme: 'bootstrap-5',
                            placeholder: 'Search and select female animal...',
                            allowClear: true,
                            width: '100%'
                        });
                        console.log('Female animal Select2 initialized via fallback');
                    } catch (error) {
                        console.error('Fallback Select2 initialization failed:', error);
                    }
                }
            }

            // Try to initialize bull select2 if not already done
            if (!window.bullSelect2) {
                const bullElement = $('#{{ form.bull.auto_id }}');
                if (bullElement.length > 0 && !bullElement.hasClass('select2-hidden-accessible')) {
                    try {
                        window.bullSelect2 = bullElement.select2({
                            theme: 'bootstrap-5',
                            placeholder: 'Search and select bull...',
                            allowClear: true,
                            width: '100%'
                        });
                        console.log('Bull Select2 initialized via fallback');
                    } catch (error) {
                        console.error('Fallback bull Select2 initialization failed:', error);
                    }
                }
            }
        } else {
            console.error('jQuery or Select2 not available for fallback initialization');
        }
    }, 1000); // Wait 1 second before trying fallback
}

// Call fallback initialization
initializeSelect2Fallback();
</script>
{% endblock %}