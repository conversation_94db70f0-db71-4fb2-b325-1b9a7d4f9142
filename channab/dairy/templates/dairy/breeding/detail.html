{% extends 'main/base.html' %}
{% load dairy_extras %}
{% load static %}

{% block title %}Breeding Details - {{ breeding.female_animal.tag }}{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Breeding Details Content -->

        <div class="row mb-3">
            <div class="col-auto ms-auto">
                <div class="d-flex gap-2">
                    {% if breeding.status == 'pending' %}
                    <a href="{% url 'dairy:pregnancy_check_create' breeding.pk %}" class="btn btn-warning btn-sm">
                        <i class="ri-stethoscope-line"></i> Pregnancy Check
                    </a>
                    {% endif %}
                    {% if breeding.status == 'successful' and not calving_record %}
                    <a href="{% url 'dairy:calving_record_create' breeding.pk %}" class="btn btn-info btn-sm">
                        <i class="ri-baby-line"></i> Record Calving
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    <!-- Basic Information -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Information</h6>
                    <span class="badge {% if breeding.status == 'successful' %}bg-success{% elif breeding.status == 'failed' %}bg-danger{% elif breeding.status == 'aborted' %}bg-dark{% else %}bg-warning{% endif %} float-end">
                        {{ breeding.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Female Animal:</strong></td>
                                    <td>
                                        <a href="{% url 'dairy:animal_detail' breeding.female_animal.pk %}">
                                            {{ breeding.female_animal.tag }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Breeding Date:</strong></td>
                                    <td>{{ breeding.date_of_insemination|date:"d M Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Method:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ breeding.get_breeding_method_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>{% if breeding.breeding_method == 'natural' %}Bull:{% else %}Semen Tag:{% endif %}</strong></td>
                                    <td>
                                        {% if breeding.breeding_method == 'natural' %}
                                            {% if breeding.bull %}
                                                <a href="{% url 'dairy:animal_detail' breeding.bull.pk %}">
                                                    {{ breeding.bull.tag }}
                                                </a>
                                            {% else %}
                                                Not specified
                                            {% endif %}
                                        {% else %}
                                            {{ breeding.semen_tag|default:"Not specified" }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Lactation Number:</strong></td>
                                    <td>{{ breeding.lactation_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Attempt Number:</strong></td>
                                    <td>{{ breeding.attempt }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Expected Calving:</strong></td>
                                    <td>
                                        {% if breeding.expected_calving_date %}
                                            {{ breeding.expected_calving_date|date:"d M Y" }}
                                            {% with days_left=breeding.expected_calving_date|days_until %}
                                            {% if days_left >= 0 %}
                                            <span class="badge bg-info ms-1">{{ days_left }} days</span>
                                            {% endif %}
                                            {% endwith %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Expected Dry-off:</strong></td>
                                    <td>
                                        {% if breeding.dry_off_date %}
                                            {{ breeding.dry_off_date|date:"d M Y" }}
                                            {% with days_left=breeding.dry_off_date|days_until %}
                                            {% if days_left >= 0 %}
                                            <span class="badge bg-warning ms-1">{{ days_left }} days</span>
                                            {% endif %}
                                            {% endwith %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge {% if breeding.status == 'successful' %}bg-success{% elif breeding.status == 'failed' %}bg-danger{% elif breeding.status == 'aborted' %}bg-dark{% else %}bg-warning{% endif %}">
                                            {{ breeding.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if breeding.comment %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Notes:</h6>
                            <p class="text-muted">{{ breeding.comment }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Status Summary -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Progress Summary</h6>
                </div>
                <div class="card-body">
                    <div class="progress-timeline">
                        <div class="timeline-item {% if breeding.date_of_insemination %}completed{% endif %}">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>Breeding</h6>
                                <small>{{ breeding.date_of_insemination|date:"d M Y" }}</small>
                            </div>
                        </div>
                        
                        <div class="timeline-item {% if pregnancy_checks %}completed{% endif %}">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>Pregnancy Check</h6>
                                <small>
                                    {% if pregnancy_checks %}
                                        {{ pregnancy_checks.0.check_date|date:"d M Y" }}
                                    {% else %}
                                        Pending
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        
                        <div class="timeline-item {% if calving_record %}completed{% endif %}">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>Calving</h6>
                                <small>
                                    {% if calving_record %}
                                        {{ calving_record.calving_date|date:"d M Y" }}
                                    {% elif breeding.expected_calving_date %}
                                        Expected: {{ breeding.expected_calving_date|date:"d M Y" }}
                                    {% else %}
                                        TBD
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-column gap-2">
                        <a href="{% url 'dairy:animal_detail' breeding.female_animal.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="ri-eye-line"></i> View Animal
                        </a>
                        {% if breeding.bull %}
                        <a href="{% url 'dairy:animal_detail' breeding.bull.pk %}" class="btn btn-outline-secondary btn-sm">
                            <i class="ri-eye-line"></i> View Bull
                        </a>
                        {% endif %}
                        {% if breeding.breeding_cycle %}
                        <button class="btn btn-outline-info btn-sm" onclick="showCycleDetails()">
                            <i class="ri-refresh-line"></i> View Cycle
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pregnancy Checks -->
    {% if pregnancy_checks %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Pregnancy Checks</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Check Date</th>
                                    <th>Result</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for check in pregnancy_checks %}
                                <tr>
                                    <td>{{ check.check_date|date:"d M Y" }}</td>
                                    <td>
                                        <span class="badge {% if check.status == 'pregnant' %}bg-success{% elif check.status == 'not_pregnant' %}bg-danger{% else %}bg-warning{% endif %}">
                                            {{ check.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ check.notes|default:"-" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Calving Record -->
    {% if calving_record %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Calving Record</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Calving Date:</strong></td>
                                    <td>{{ calving_record.calving_date|date:"d M Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Difficulty:</strong></td>
                                    <td>
                                        <span class="badge {% if calving_record.difficulty == 'normal' %}bg-success{% elif calving_record.difficulty == 'assisted' %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ calving_record.get_difficulty_display }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Calf:</strong></td>
                                    <td>
                                        {% if calving_record.calf %}
                                            <a href="{% url 'dairy:animal_detail' calving_record.calf.pk %}">
                                                {{ calving_record.calf.tag }}
                                            </a>
                                        {% else %}
                                            Not recorded
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            {% if calving_record.complications %}
                            <h6>Complications:</h6>
                            <p class="text-muted">{{ calving_record.complications }}</p>
                            {% endif %}
                            
                            {% if calving_record.notes %}
                            <h6>Notes:</h6>
                            <p class="text-muted">{{ calving_record.notes }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<style>
.progress-timeline {
    position: relative;
    padding-left: 30px;
}

.progress-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item.completed .timeline-marker {
    background: #0da487;
    border-color: #0da487;
}

.timeline-item.completed .timeline-content h6 {
    color: #0da487;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    width: 16px;
    height: 16px;
    border: 3px solid #dee2e6;
    border-radius: 50%;
    background: white;
}

.timeline-content {
    margin-left: 10px;
}

.timeline-content h6 {
    margin-bottom: 2px;
    font-size: 14px;
}

.timeline-content small {
    color: #6c757d;
}
</style>

<script>
function showCycleDetails() {
    // This could open a modal with breeding cycle details
    alert('Breeding cycle details functionality can be implemented here');
}
</script>
{% endblock %}