{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- {{ title }} Content -->

    <div class="row">
        <div class="col-xl-10 col-lg-12 mx-auto">
            <!-- Breeding Information Summary -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Pregnancy Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Mother:</strong></td>
                                    <td>{{ breeding.female_animal.tag }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Breeding Date:</strong></td>
                                    <td>{{ breeding.date_of_insemination|date:"d M Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Expected Calving:</strong></td>
                                    <td>{{ breeding.expected_calving_date|date:"d M Y" }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Father:</strong></td>
                                    <td>
                                        {% if breeding.bull %}
                                            {{ breeding.bull.tag }}
                                        {% else %}
                                            AI - {{ breeding.semen_tag }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Gestation:</strong></td>
                                    <td>
                                        {% now "Y-m-d" as today %}
                                        {% load dairy_extras %}
                                        {{ breeding.date_of_insemination|days_until|mul:-1 }} days
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Lactation #:</strong></td>
                                    <td>{{ breeding.lactation_number }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="ri-heart-pulse-line display-3 text-success"></i>
                                <p class="text-muted mb-0">Ready for Calving</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Calving Form -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Calving Details</h6>
                        </div>
                        <div class="card-body">
                            <form method="post" class="needs-validation" novalidate>
                                {% csrf_token %}
                                
                                <div class="row">
                                    <!-- Calving Date -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-floating theme-form-floating">
                                            {{ form.calving_date|add_class:"form-control" }}
                                            <label for="{{ form.calving_date.auto_id }}">Calving Date <span class="text-danger">*</span></label>
                                            {% if form.calving_date.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.calving_date.errors.0 }}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Difficulty -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-floating theme-form-floating">
                                            {{ form.difficulty|add_class:"form-control" }}
                                            <label for="{{ form.difficulty.auto_id }}">Calving Difficulty <span class="text-danger">*</span></label>
                                            {% if form.difficulty.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.difficulty.errors.0 }}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Calf Information -->
                                    <div class="col-12">
                                        <h6 class="border-bottom pb-2 mb-3">Calf Information</h6>
                                    </div>

                                    <!-- Calf Tag -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-floating theme-form-floating">
                                            {{ form.calf_tag|add_class:"form-control" }}
                                            <label for="{{ form.calf_tag.auto_id }}">Calf Tag <span class="text-danger">*</span></label>
                                            {% if form.calf_tag.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.calf_tag.errors.0 }}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Calf Sex -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-floating theme-form-floating">
                                            {{ form.calf_sex|add_class:"form-control" }}
                                            <label for="{{ form.calf_sex.auto_id }}">Calf Sex <span class="text-danger">*</span></label>
                                            {% if form.calf_sex.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.calf_sex.errors.0 }}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Complications -->
                                    <div class="col-12 mb-3">
                                        <div class="form-floating theme-form-floating">
                                            {{ form.complications|add_class:"form-control" }}
                                            <label for="{{ form.complications.auto_id }}">Complications (if any)</label>
                                            {% if form.complications.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.complications.errors.0 }}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Notes -->
                                    <div class="col-12 mb-3">
                                        <div class="form-floating theme-form-floating">
                                            {{ form.notes|add_class:"form-control"|attr:"rows:4" }}
                                            <label for="{{ form.notes.auto_id }}">Additional Notes</label>
                                            {% if form.notes.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.notes.errors.0 }}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Actions -->
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'dairy:breeding_detail' breeding.pk %}" class="btn btn-secondary">
                                        <i class="ri-arrow-left-line"></i> Back
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line"></i> Record Calving
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Calving Guide -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Calving Difficulty Guide</h6>
                        </div>
                        <div class="card-body">
                            <div class="difficulty-guide">
                                <div class="difficulty-item mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge bg-success me-2">Normal</span>
                                        <strong>Unassisted</strong>
                                    </div>
                                    <small class="text-muted">
                                        Natural delivery without any assistance
                                    </small>
                                </div>

                                <div class="difficulty-item mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge bg-warning me-2">Assisted</span>
                                        <strong>Manual Help</strong>
                                    </div>
                                    <small class="text-muted">
                                        Required pulling or repositioning
                                    </small>
                                </div>

                                <div class="difficulty-item mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge bg-danger me-2">Difficult</span>
                                        <strong>Mechanical Aid</strong>
                                    </div>
                                    <small class="text-muted">
                                        Required calf puller or significant intervention
                                    </small>
                                </div>

                                <div class="difficulty-item">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge bg-dark me-2">Veterinary</span>
                                        <strong>Professional Help</strong>
                                    </div>
                                    <small class="text-muted">
                                        Required veterinarian assistance or surgery
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Post-Calving Checklist -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Post-Calving Checklist</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check1">
                                <label class="form-check-label small" for="check1">
                                    Calf breathing normally
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check2">
                                <label class="form-check-label small" for="check2">
                                    Placenta expelled (within 12 hours)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check3">
                                <label class="form-check-label small" for="check3">
                                    Calf received colostrum
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check4">
                                <label class="form-check-label small" for="check4">
                                    Mother showing maternal behavior
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check5">
                                <label class="form-check-label small" for="check5">
                                    No signs of infection
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check6">
                                <label class="form-check-label small" for="check6">
                                    Navel dipped with iodine
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-column gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="generateCalfTag()">
                                    <i class="ri-magic-line"></i> Generate Calf Tag
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="calculateGestation()">
                                    <i class="ri-calculator-line"></i> Calculate Gestation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Alert -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="ri-information-line"></i>
                        <strong>Important:</strong> Recording calving will automatically:
                        <ul class="mb-0 mt-2">
                            <li>Create a new animal record for the calf</li>
                            <li>Update the mother's breeding cycle status</li>
                            <li>Create a new breeding cycle for future breedings</li>
                            <li>Link family relationships (mother/father/calf)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date picker
    flatpickr("#{{ form.calving_date.auto_id }}", {
        dateFormat: "Y-m-d",
        maxDate: "today",
        defaultDate: "today"
    });

    // Form validation
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();

    // Generate suggested calf tag
    generateCalfTag();
});

function generateCalfTag() {
    const motherTag = '{{ breeding.female_animal.tag }}';
    const calvingDate = new Date();
    const year = calvingDate.getFullYear().toString().slice(-2);
    const month = String(calvingDate.getMonth() + 1).padStart(2, '0');
    
    // Generate suggested tag: MotherTag-YYMM-C (C for calf)
    const suggestedTag = `${motherTag}-${year}${month}-C`;
    
    const calfTagField = document.getElementById('{{ form.calf_tag.auto_id }}');
    if (calfTagField && !calfTagField.value) {
        calfTagField.value = suggestedTag;
    }
}

function calculateGestation() {
    const breedingDate = new Date('{{ breeding.date_of_insemination|date:"Y-m-d" }}');
    const today = new Date();
    const diffTime = Math.abs(today - breedingDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    alert(`Gestation period: ${diffDays} days\nNormal range: 275-285 days`);
}

// Difficulty change handler to show relevant guidance
document.getElementById('{{ form.difficulty.auto_id }}').addEventListener('change', function() {
    const difficulty = this.value;
    const notesField = document.getElementById('{{ form.notes.auto_id }}');
    
    let placeholder = '';
    switch(difficulty) {
        case 'normal':
            placeholder = 'Record calf condition, birth weight if measured, any observations...';
            break;
        case 'assisted':
            placeholder = 'Describe assistance provided, reasons for intervention, calf and mother condition...';
            break;
        case 'difficult':
            placeholder = 'Detail mechanical aids used, duration of labor, any injuries or concerns...';
            break;
        case 'veterinary':
            placeholder = 'Record veterinary procedures, medications given, recovery notes...';
            break;
    }
    
    notesField.placeholder = placeholder;
});

// Auto-save form data locally
const form = document.querySelector('form');
form.addEventListener('input', function(e) {
    if (e.target.type !== 'submit') {
        localStorage.setItem('calvingFormData', JSON.stringify({
            calving_date: document.getElementById('{{ form.calving_date.auto_id }}').value,
            difficulty: document.getElementById('{{ form.difficulty.auto_id }}').value,
            calf_tag: document.getElementById('{{ form.calf_tag.auto_id }}').value,
            calf_sex: document.getElementById('{{ form.calf_sex.auto_id }}').value,
            complications: document.getElementById('{{ form.complications.auto_id }}').value,
            notes: document.getElementById('{{ form.notes.auto_id }}').value
        }));
    }
});

// Clear saved data on successful submission
form.addEventListener('submit', function() {
    localStorage.removeItem('calvingFormData');
});
</script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
.difficulty-guide .difficulty-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.difficulty-guide .difficulty-item:last-child {
    border-bottom: none;
}

.form-check {
    margin-bottom: 8px;
}

.form-check-label {
    margin-left: 5px;
}
</style>
{% endblock %}