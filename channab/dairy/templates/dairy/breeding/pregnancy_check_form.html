{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- {{ title }} Content -->

    <div class="row">
        <div class="col-xl-8 col-lg-10 col-md-12 mx-auto">
            <!-- Breeding Information Summary -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Breeding Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Animal:</strong></td>
                                    <td>{{ breeding.female_animal.tag }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Breeding Date:</strong></td>
                                    <td>{{ breeding.date_of_insemination|date:"d M Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Method:</strong></td>
                                    <td>{{ breeding.get_breeding_method_display }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Days Since Breeding:</strong></td>
                                    <td>
                                        {% now "Y-m-d" as today %}
                                        {% load dairy_extras %}
                                        {{ breeding.date_of_insemination|days_until|mul:-1 }} days
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Optimal Check Period:</strong></td>
                                    <td>30-45 days post-breeding</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Status:</strong></td>
                                    <td>
                                        <span class="badge bg-warning">{{ breeding.get_status_display }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pregnancy Check Form -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Pregnancy Check Details</h6>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Check Date -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.check_date|add_class:"form-control" }}
                                    <label for="{{ form.check_date.auto_id }}">Check Date <span class="text-danger">*</span></label>
                                    {% if form.check_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.check_date.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Result -->
                            <div class="col-md-6 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.status|add_class:"form-control" }}
                                    <label for="{{ form.status.auto_id }}">Result <span class="text-danger">*</span></label>
                                    {% if form.status.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.status.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="col-12 mb-3">
                                <div class="form-floating theme-form-floating">
                                    {{ form.notes|add_class:"form-control"|attr:"rows:4" }}
                                    <label for="{{ form.notes.auto_id }}">Notes & Observations</label>
                                    {% if form.notes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.notes.errors.0 }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Information Alert -->
                        <div class="alert alert-info mb-3">
                            <i class="ri-information-line"></i>
                            <strong>Pregnancy Check Guidelines:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Best accuracy: 30-45 days after breeding</li>
                                <li>Methods: Rectal palpation, ultrasound, blood test</li>
                                <li>Record any abnormal findings in notes</li>
                                <li>If uncertain, schedule follow-up check</li>
                            </ul>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'dairy:breeding_detail' breeding.pk %}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line"></i> Back to Breeding
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i> Save Pregnancy Check
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Previous Pregnancy Checks -->
            {% if breeding.pregnancy_checks.all %}
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Previous Checks</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Result</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for check in breeding.pregnancy_checks.all %}
                                <tr>
                                    <td>{{ check.check_date|date:"d M Y" }}</td>
                                    <td>
                                        <span class="badge {% if check.result == 'pregnant' %}bg-success{% elif check.result == 'not_pregnant' %}bg-danger{% else %}bg-warning{% endif %}">
                                            {{ check.get_result_display }}
                                        </span>
                                    </td>
                                    <td>{{ check.notes|default:"-" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Next Steps Guide -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Next Steps Based on Result</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <i class="ri-checkbox-circle-line display-4 text-success"></i>
                                <h6 class="mt-2">If Pregnant</h6>
                                <ul class="small text-start">
                                    <li>Monitor animal health</li>
                                    <li>Plan dry-off date</li>
                                    <li>Prepare for calving</li>
                                    <li>Adjust feeding program</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <i class="ri-close-circle-line display-4 text-danger"></i>
                                <h6 class="mt-2">If Not Pregnant</h6>
                                <ul class="small text-start">
                                    <li>Watch for return to heat</li>
                                    <li>Plan re-breeding</li>
                                    <li>Check animal health</li>
                                    <li>Review breeding protocol</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 border rounded">
                                <i class="ri-question-line display-4 text-warning"></i>
                                <h6 class="mt-2">If Uncertain</h6>
                                <ul class="small text-start">
                                    <li>Schedule re-check in 7-14 days</li>
                                    <li>Use different detection method</li>
                                    <li>Consult veterinarian</li>
                                    <li>Continue monitoring</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date picker
    flatpickr("#{{ form.check_date.auto_id }}", {
        dateFormat: "Y-m-d",
        maxDate: "today",
        defaultDate: "today"
    });

    // Form validation
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');
        
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    })();

    // Result change handler
    const resultSelect = document.getElementById('{{ form.status.auto_id }}');
    const notesField = document.getElementById('{{ form.notes.auto_id }}');
    
    resultSelect.addEventListener('change', function() {
        const result = this.value;
        let placeholder = '';
        
        switch(result) {
            case 'pregnant':
                placeholder = 'Record any abnormal findings, estimated due date confirmation, etc.';
                break;
            case 'not_pregnant':
                placeholder = 'Note possible reasons for failure, animal condition, next steps...';
                break;
            case 'uncertain':
                placeholder = 'Describe findings that make result uncertain, plan for re-check...';
                break;
        }
        
        notesField.placeholder = placeholder;
    });
});
</script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
{% endblock %}