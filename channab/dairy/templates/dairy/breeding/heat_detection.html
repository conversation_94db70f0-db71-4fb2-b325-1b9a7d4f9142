{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}Heat Detection{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- <PERSON> Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Heat Detection Content -->

    <div class="row">
        <!-- Heat Detection Form -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Record Heat Detection</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Animal Selection -->
                        <div class="mb-3">
                            <div class="form-floating theme-form-floating">
                                {{ form.animal|add_class:"form-control" }}
                                <label for="{{ form.animal.auto_id }}">Animal <span class="text-danger">*</span></label>
                                {% if form.animal.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.animal.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Detection Date -->
                        <div class="mb-3">
                            <div class="form-floating theme-form-floating">
                                {{ form.detection_date|add_class:"form-control" }}
                                <label for="{{ form.detection_date.auto_id }}">Detection Date <span class="text-danger">*</span></label>
                                {% if form.detection_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.detection_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Heat Signs -->
                        <div class="mb-3">
                            <label class="form-label">Heat Signs Observed <span class="text-danger">*</span></label>
                            <div class="heat-signs-grid">
                                {% for choice in form.heat_signs %}
                                <div class="form-check">
                                    {{ choice.tag }}
                                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                                        {{ choice.choice_label }}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% if form.heat_signs.errors %}
                            <div class="text-danger small">
                                {{ form.heat_signs.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <div class="form-floating theme-form-floating">
                                {{ form.notes|add_class:"form-control" }}
                                <label for="{{ form.notes.auto_id }}">Additional Notes</label>
                                {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i> Record Heat Detection
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Heat Detection Tips -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Heat Detection Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0 small">
                        <li>Observe animals 2-3 times daily</li>
                        <li>Best times: early morning and evening</li>
                        <li>Heat typically lasts 12-18 hours</li>
                        <li>Breed 12-18 hours after heat detection</li>
                        <li>Look for multiple signs for confirmation</li>
                        <li>Keep detailed records for pattern analysis</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Animals Potentially in Heat -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Animals Expected in Heat</h6>
                    <small class="text-muted">Based on 21-day cycle</small>
                </div>
                <div class="card-body">
                    {% if potential_heat %}
                    <div class="list-group">
                        {% for item in potential_heat %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">
                                    <a href="{% url 'dairy:animal_detail' item.animal.pk %}">
                                        {{ item.animal.tag }}
                                    </a>
                                </div>
                                <small class="text-muted">{{ item.last_event }}</small>
                                <br>
                                <small class="text-info">Expected: {{ item.expected_date|date:"d M Y" }}</small>
                            </div>
                            <div class="text-end">
                                <button class="btn btn-sm btn-outline-primary" onclick="selectAnimal('{{ item.animal.pk }}', '{{ item.animal.tag }}')">
                                    <i class="ri-add-line"></i> Record
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="ri-search-line display-4"></i>
                        <p class="mt-2">No animals expected in heat</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Heat Detections -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Recent Detections</h6>
                </div>
                <div class="card-body">
                    {% if recent_detections %}
                    <div class="timeline">
                        {% for detection in recent_detections %}
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between">
                                    <h6 class="mb-1">
                                        <a href="{% url 'dairy:animal_detail' detection.animal.pk %}">
                                            {{ detection.animal.tag }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">{{ detection.event_date|date:"d M" }}</small>
                                </div>
                                <p class="mb-1 small text-muted">{{ detection.description }}</p>
                                {% if detection.notes %}
                                <p class="mb-0 small">{{ detection.notes }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="ri-temp-hot-line display-4"></i>
                        <p class="mt-2">No recent heat detections</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Heat Detection Summary -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Heat Detection Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary">{{ recent_detections|length }}</h4>
                                <p class="mb-0 text-muted">This Month</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info">{{ potential_heat|length }}</h4>
                                <p class="mb-0 text-muted">Expected Soon</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success">21</h4>
                                <p class="mb-0 text-muted">Avg Cycle (days)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning">18</h4>
                                <p class="mb-0 text-muted">Heat Duration (hrs)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<style>
.heat-signs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.form-check {
    margin-bottom: 0.5rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
    max-height: 400px;
    overflow-y: auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    width: 12px;
    height: 12px;
    border: 2px solid #0da487;
    border-radius: 50%;
    background: white;
}

.timeline-content {
    margin-left: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #0da487;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

@media (max-width: 768px) {
    .heat-signs-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 on animal dropdown
    window.animalSelect2 = $('#{{ form.animal.auto_id }}').select2({
        theme: 'bootstrap-5',
        placeholder: 'Search and select animal...',
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        templateResult: function(data) {
            if (!data.id) return data.text;
            
            // Extract animal info from the option text
            var text = data.text;
            var match = text.match(/^(.+?)\s+\((.+?)\)$/);
            if (match) {
                var tag = match[1];
                var category = match[2];
                return $('<span><strong>' + tag + '</strong> <small class="text-muted">(' + category + ')</small></span>');
            }
            return $('<span>' + text + '</span>');
        },
        templateSelection: function(data) {
            if (!data.id) return data.text;
            
            // Extract animal info for selection display
            var text = data.text;
            var match = text.match(/^(.+?)\s+\((.+?)\)$/);
            if (match) {
                var tag = match[1];
                return tag; // Show only the tag in selection
            }
            return text;
        }
    });

    // Initialize date picker
    flatpickr("#{{ form.detection_date.auto_id }}", {
        dateFormat: "Y-m-d",
        maxDate: "today",
        defaultDate: "today"
    });
});

function selectAnimal(animalId, animalTag) {
    // Set the animal in the form using Select2
    if (window.animalSelect2) {
        window.animalSelect2.val(animalId).trigger('change');
        
        // Scroll to form
        document.querySelector('.card').scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
        
        // Focus on heat signs
        const firstCheckbox = document.querySelector('input[name="heat_signs"]');
        if (firstCheckbox) {
            firstCheckbox.focus();
        }
    }
}

// Auto-save functionality for frequent users
let formData = {};
const form = document.querySelector('form');

form.addEventListener('change', function(e) {
    if (e.target.type === 'checkbox') {
        // Save checkbox states
        const checkboxes = document.querySelectorAll('input[name="heat_signs"]:checked');
        formData.heat_signs = Array.from(checkboxes).map(cb => cb.value);
    } else {
        formData[e.target.name] = e.target.value;
    }
    
    // Save to localStorage
    localStorage.setItem('heatDetectionFormData', JSON.stringify(formData));
});

// Restore form data on page load
window.addEventListener('load', function() {
    const savedData = localStorage.getItem('heatDetectionFormData');
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            
            // Only restore if form is empty (new entry)
            if (window.animalSelect2 && !window.animalSelect2.val()) {
                // Don't auto-restore to avoid confusion
                // User can manually select if needed
            }
        } catch (e) {
            console.log('Could not restore form data');
        }
    }
});

// Clear saved data on successful submission
form.addEventListener('submit', function() {
    localStorage.removeItem('heatDetectionFormData');
});
</script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
{% endblock %}