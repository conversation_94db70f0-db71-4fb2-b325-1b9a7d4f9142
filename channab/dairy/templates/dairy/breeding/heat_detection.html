{% extends 'main/base.html' %}
{% load widget_tweaks %}
{% load dairy_extras %}
{% load static %}

{% block title %}Heat Detection{% endblock %}

{% block extra_css %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
/* Select2 styling to match form-floating theme fields exactly */
.form-floating.theme-form-floating {
    position: relative !important;
    overflow: visible !important;
    width: 100% !important;
    max-width: 100% !important;
}

.form-floating.theme-form-floating .select2-container {
    width: 100% !important;
    max-width: 100% !important;
    position: relative !important;
    overflow: visible !important;
}

/* Ensure the Select2 container spans exactly match the form field */
.form-floating.theme-form-floating .select2-container .select2-selection {
    box-sizing: border-box !important;
    width: 100% !important;
    max-width: 100% !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single {
    height: calc(3.5rem + 2px) !important;
    padding: 1rem 40px 0.25rem 0.75rem !important;
    border: 1px solid var(--theme-color) !important;
    border-radius: 0.375rem !important;
    background-color: #fff !important;
    font-size: 1rem !important;
    line-height: 1.25 !important;
    box-shadow: none !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single:focus,
.form-floating.theme-form-floating .select2-container.select2-container--open .select2-selection--single {
    border-color: var(--theme-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 164, 135, 0.25) !important;
    outline: 0 !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__rendered {
    padding: 0 40px 0 0 !important;
    margin: 0 !important;
    line-height: 1.25 !important;
    color: #212529 !important;
    display: block !important;
    width: calc(100% - 40px) !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__placeholder {
    color: transparent !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__arrow {
    height: calc(3.5rem + 2px) !important;
    position: absolute !important;
    top: 0 !important;
    right: 12px !important;
    width: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1 !important;
}

.form-floating.theme-form-floating .select2-container .select2-selection--single .select2-selection__arrow b {
    border-color: var(--theme-color) transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 5px 5px 0 5px !important;
    height: 0 !important;
    left: 50% !important;
    margin-left: -5px !important;
    margin-top: -2px !important;
    position: absolute !important;
    top: 50% !important;
    width: 0 !important;
}

.form-floating.theme-form-floating .select2-container.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent var(--theme-color) transparent !important;
    border-width: 0 5px 5px 5px !important;
}

/* Form-floating label positioning - ensure it works with Select2 */
.form-floating.theme-form-floating > label {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    height: 100% !important;
    padding: 1rem 0.75rem !important;
    pointer-events: none !important;
    border: 1px solid transparent !important;
    transform-origin: 0 0 !important;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out !important;
    z-index: 2 !important;
}

/* When Select2 has content or is focused, move label up */
.form-floating.theme-form-floating .select2-container ~ label,
.form-floating.theme-form-floating .select2-container.select2-container--focus ~ label {
    opacity: 0.65 !important;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem) !important;
}

/* Container positioning for dropdown containment */
.mb-3 {
    position: relative !important;
    overflow: visible !important;
}

/* Ensure Select2 dropdown respects container boundaries */
.mb-3 .select2-container--default .select2-dropdown {
    width: 100% !important;
    max-width: 100% !important;
    left: 0 !important;
    right: 0 !important;
}

/* Dropdown positioning and styling */
.select2-container--default .select2-dropdown {
    border: 1px solid var(--theme-color) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    z-index: 1060 !important;
    margin-top: 2px !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Ensure dropdown appears below the field and is contained */
.select2-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.select2-container--default .select2-search--dropdown {
    padding: 4px !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem !important;
    padding: 0.375rem 0.75rem !important;
    width: 100% !important;
}

.select2-container--default .select2-results__option {
    padding: 6px 12px !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--theme-color) !important;
    color: white !important;
}

/* Clear button styling */
.form-floating.theme-form-floating .select2-container .select2-selection__clear {
    color: var(--theme-color) !important;
    font-size: 1.25rem !important;
    position: absolute !important;
    right: 2rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    cursor: pointer !important;
}

/* Prevent dropdown from extending beyond container boundaries */
.select2-container .select2-dropdown {
    max-width: 100% !important;
    overflow: hidden !important;
}

.select2-container .select2-dropdown .select2-results {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

.select2-container .select2-dropdown .select2-results__option {
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}
</style>
{% endblock %}

{% block content %}
<!-- tap on top start -->
<div class="tap-top">
  <span class="lnr lnr-chevron-up"></span>
</div>
<!-- tap on tap end -->
<!-- page-wrapper Start-->
<div class="page-wrapper compact-wrapper" id="pageWrapper">
  <!-- Page Header Start-->
  {%include 'home/backend_header.html'%}
  <!-- Page Header Ends-->
  <!-- mobile fix menu start -->
  {%include 'home/mobile_menu.html'%}
  <!-- mobile fix menu end -->
  <!-- Page Body Start-->
  <div class="page-body-wrapper">
    <!-- Page Sidebar Start-->
    {%include 'home/backend_sidebar.html'%}
    <!-- Page Sidebar Ends-->
    <!-- Page Body Start -->
    <!-- Container-fluid starts-->
    <div class="page-body">
      <div class="container-fluid">
        <!-- Heat Detection Content -->

    <div class="row">
        <!-- Heat Detection Form -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Record Heat Detection</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Animal Selection -->
                        <div class="mb-3">
                            <div class="form-floating theme-form-floating">
                                {{ form.animal|add_class:"form-control" }}
                                <label for="{{ form.animal.auto_id }}">Animal <span class="text-danger">*</span></label>
                                {% if form.animal.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.animal.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                            <small class="text-muted">
                                <i class="ri-search-line"></i> Type to search animals by tag or category
                            </small>
                            <div id="select2-debug-heat" class="small text-info mt-1" style="display: none;">
                                Debug: Select2 status will appear here
                            </div>
                        </div>

                        <!-- Detection Date -->
                        <div class="mb-3">
                            <div class="form-floating theme-form-floating">
                                {{ form.detection_date|add_class:"form-control" }}
                                <label for="{{ form.detection_date.auto_id }}">Detection Date <span class="text-danger">*</span></label>
                                {% if form.detection_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.detection_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Heat Signs -->
                        <div class="mb-3">
                            <label class="form-label">Heat Signs Observed <span class="text-danger">*</span></label>
                            <div class="heat-signs-grid">
                                {% for choice in form.heat_signs %}
                                <div class="form-check">
                                    {{ choice.tag }}
                                    <label class="form-check-label" for="{{ choice.id_for_label }}">
                                        {{ choice.choice_label }}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            {% if form.heat_signs.errors %}
                            <div class="text-danger small">
                                {{ form.heat_signs.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <div class="form-floating theme-form-floating">
                                {{ form.notes|add_class:"form-control" }}
                                <label for="{{ form.notes.auto_id }}">Additional Notes</label>
                                {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="ri-save-line"></i> Record Heat Detection
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Heat Detection Tips -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Heat Detection Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0 small">
                        <li>Observe animals 2-3 times daily</li>
                        <li>Best times: early morning and evening</li>
                        <li>Heat typically lasts 12-18 hours</li>
                        <li>Breed 12-18 hours after heat detection</li>
                        <li>Look for multiple signs for confirmation</li>
                        <li>Keep detailed records for pattern analysis</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Animals Potentially in Heat -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Animals Expected in Heat</h6>
                    <small class="text-muted">Based on 21-day cycle</small>
                </div>
                <div class="card-body">
                    {% if potential_heat %}
                    <div class="list-group">
                        {% for item in potential_heat %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">
                                    <a href="{% url 'dairy:animal_detail' item.animal.pk %}">
                                        {{ item.animal.tag }}
                                    </a>
                                </div>
                                <small class="text-muted">{{ item.last_event }}</small>
                                <br>
                                <small class="text-info">Expected: {{ item.expected_date|date:"d M Y" }}</small>
                            </div>
                            <div class="text-end">
                                <button class="btn btn-sm btn-outline-primary" onclick="selectAnimal('{{ item.animal.pk }}', '{{ item.animal.tag }}')">
                                    <i class="ri-add-line"></i> Record
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="ri-search-line display-4"></i>
                        <p class="mt-2">No animals expected in heat</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Heat Detections -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Recent Detections</h6>
                </div>
                <div class="card-body">
                    {% if recent_detections %}
                    <div class="timeline">
                        {% for detection in recent_detections %}
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between">
                                    <h6 class="mb-1">
                                        <a href="{% url 'dairy:animal_detail' detection.animal.pk %}">
                                            {{ detection.animal.tag }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">{{ detection.event_date|date:"d M" }}</small>
                                </div>
                                <p class="mb-1 small text-muted">{{ detection.description }}</p>
                                {% if detection.notes %}
                                <p class="mb-0 small">{{ detection.notes }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-3">
                        <i class="ri-temp-hot-line display-4"></i>
                        <p class="mt-2">No recent heat detections</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Heat Detection Summary -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Heat Detection Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary">{{ recent_detections|length }}</h4>
                                <p class="mb-0 text-muted">This Month</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-info">{{ potential_heat|length }}</h4>
                                <p class="mb-0 text-muted">Expected Soon</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success">21</h4>
                                <p class="mb-0 text-muted">Avg Cycle (days)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning">18</h4>
                                <p class="mb-0 text-muted">Heat Duration (hrs)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
      </div>
    </div>
    <!-- Container-fluid ends-->
  </div>
  <!-- Page Body Ends -->
</div>
<!-- page-wrapper ends -->

<style>
.heat-signs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.form-check {
    margin-bottom: 0.5rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
    max-height: 400px;
    overflow-y: auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    width: 12px;
    height: 12px;
    border: 2px solid #0da487;
    border-radius: 50%;
    background: white;
}

.timeline-content {
    margin-left: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #0da487;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

@media (max-width: 768px) {
    .heat-signs-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- Load Select2 after all other scripts to avoid conflicts -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
console.log('Heat detection JavaScript loading...');

// Simple test to verify the page is working
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - testing basic functionality');

    // Test if we can find the animal select element
    const animalSelect = document.getElementById('{{ form.animal.auto_id }}');
    console.log('Animal select element found:', !!animalSelect);
    if (animalSelect) {
        console.log('Animal select HTML:', animalSelect.outerHTML.substring(0, 200));
    }

    // Test jQuery
    if (typeof $ !== 'undefined') {
        console.log('jQuery is available, version:', $.fn.jquery);
        const jqAnimalSelect = $('#{{ form.animal.auto_id }}');
        console.log('jQuery can find animal select:', jqAnimalSelect.length > 0);

        // Test Select2
        if (typeof $.fn.select2 !== 'undefined') {
            console.log('Select2 is available');

            // Try to initialize Select2 immediately
            try {
                jqAnimalSelect.select2({
                    placeholder: 'Search and select animal...',
                    allowClear: true,
                    width: '100%'
                });
                console.log('Select2 initialized successfully!');
                window.animalSelect2 = jqAnimalSelect;
            } catch (error) {
                console.error('Select2 initialization failed:', error);
            }
        } else {
            console.error('Select2 is not available');
        }
    } else {
        console.error('jQuery is not available');
    }
});

// Initialize date picker
try {
    if (typeof flatpickr !== 'undefined') {
        flatpickr("#{{ form.detection_date.auto_id }}", {
            dateFormat: "Y-m-d",
            maxDate: "today",
            defaultDate: "today"
        });
        console.log('Date picker initialized successfully');
    }
} catch (error) {
    console.error('Error initializing date picker:', error);
}

// Global function for selecting animals from the list
function selectAnimal(animalId, animalTag) {
    // Set the animal in the form using Select2
    if (window.animalSelect2) {
        try {
            window.animalSelect2.val(animalId).trigger('change');

            // Scroll to form
            const cardElement = document.querySelector('.card');
            if (cardElement) {
                cardElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }

            // Focus on heat signs
            const firstCheckbox = document.querySelector('input[name="heat_signs"]');
            if (firstCheckbox) {
                setTimeout(() => firstCheckbox.focus(), 500);
            }
        } catch (error) {
            console.error('Error selecting animal:', error);
        }
    } else {
        console.warn('Animal Select2 not initialized yet');
    }
}

// Auto-save functionality for frequent users
let formData = {};
const form = document.querySelector('form');

form.addEventListener('change', function(e) {
    if (e.target.type === 'checkbox') {
        // Save checkbox states
        const checkboxes = document.querySelectorAll('input[name="heat_signs"]:checked');
        formData.heat_signs = Array.from(checkboxes).map(cb => cb.value);
    } else {
        formData[e.target.name] = e.target.value;
    }
    
    // Save to localStorage
    localStorage.setItem('heatDetectionFormData', JSON.stringify(formData));
});

// Restore form data on page load
window.addEventListener('load', function() {
    const savedData = localStorage.getItem('heatDetectionFormData');
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            
            // Only restore if form is empty (new entry)
            if (window.animalSelect2 && !window.animalSelect2.val()) {
                // Don't auto-restore to avoid confusion
                // User can manually select if needed
            }
        } catch (e) {
            console.log('Could not restore form data');
        }
    }
});

// Clear saved data on successful submission
form.addEventListener('submit', function() {
    localStorage.removeItem('heatDetectionFormData');
});
</script>

{% endblock %}