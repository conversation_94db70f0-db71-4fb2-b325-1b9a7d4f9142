from rest_framework import serializers
from .models import Vaccine, FarmVaccine, VaccineSchedule, VaccinationRecord, VaccinationBatch

class VaccineSerializer(serializers.ModelSerializer):
    class Meta:
        model = Vaccine
        fields = ['id', 'name', 'code', 'manufacturer', 'description']

class VaccineScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = VaccineSchedule
        fields = ['animal_category', 'dosage', 'recommended_months', 'frequency']

class FarmVaccineSerializer(serializers.ModelSerializer):
    schedule = serializers.SerializerMethodField()
    site_vaccine_id = serializers.CharField(write_only=True, required=False)
    months = serializers.ListField(
        child=serializers.CharField(),
        required=True
    )
    
    class Meta:
        model = FarmVaccine
        fields = [
            'id', 'title', 'description', 'animal_type', 'dosage', 
            'months', 'is_recommended', 'is_required', 'schedule',
            'farm', 'created_at', 'updated_at', 'is_override',
            'site_vaccine_id', 'site_vaccine', 'site_schedule'
        ]
        read_only_fields = ['created_at', 'updated_at', 'farm', 'site_vaccine', 'site_schedule']
    
    def get_schedule(self, obj):
        return "/".join(obj.months) if obj.months else ""
    
    def validate_months(self, value):
        if not value:
            raise serializers.ValidationError("At least one month must be selected")
        valid_months = [choice[0] for choice in FarmVaccine.MONTHS_CHOICES]
        for month in value:
            if month not in valid_months:
                raise serializers.ValidationError(f"Invalid month: {month}")
        return value
    
    def create(self, validated_data):
        site_vaccine_id = validated_data.pop('site_vaccine_id', None)
        
        if site_vaccine_id:
            try:
                # Extract vaccine and schedule IDs from the composite key
                _, vaccine_id, schedule_id = site_vaccine_id.split('_')
                site_vaccine = Vaccine.objects.get(id=int(vaccine_id))
                site_schedule = VaccineSchedule.objects.get(id=int(schedule_id), vaccine=site_vaccine)
                
                # Check if override already exists
                existing_override = FarmVaccine.objects.filter(
                    farm=self.context['request'].user.farm,
                    site_vaccine=site_vaccine,
                    site_schedule=site_schedule,
                    is_override=True
                ).first()
                
                if existing_override:
                    raise serializers.ValidationError(
                        "An override for this site vaccine already exists"
                    )
                
                validated_data['site_vaccine'] = site_vaccine
                validated_data['site_schedule'] = site_schedule
                validated_data['is_override'] = True
                
            except (ValueError, IndexError, Vaccine.DoesNotExist, VaccineSchedule.DoesNotExist):
                raise serializers.ValidationError("Invalid site vaccine ID format")
        
        # Set the farm from the request user
        validated_data['farm'] = self.context['request'].user.farm
        return super().create(validated_data)

class VaccinationRecordSerializer(serializers.ModelSerializer):
    animal_tag = serializers.CharField(source='animal.tag', read_only=True)
    vaccine_name = serializers.CharField(source='vaccine.name', read_only=True)
    farm_vaccine_title = serializers.CharField(source='farm_vaccine.title', read_only=True)
    administered_by = serializers.CharField(read_only=True)  # Make this read-only

    class Meta:
        model = VaccinationRecord
        fields = [
            'id', 'animal', 'animal_tag', 'vaccine', 'vaccine_name',
            'farm_vaccine', 'farm_vaccine_title', 'date', 'dosage_given',
            'administered_by', 'notes', 'created_at', 'batch'
        ]
        read_only_fields = ['created_at', 'administered_by']

class VaccinationBatchSerializer(serializers.ModelSerializer):
    vaccination_records = VaccinationRecordSerializer(many=True, read_only=True)
    
    class Meta:
        model = VaccinationBatch
        fields = [
            'id', 'farm', 'vaccine', 'farm_vaccine', 'date',
            'scope', 'animal_type', 'notes', 'created_at',
            'vaccination_records'
        ]
        read_only_fields = ['created_at']

    def create(self, validated_data):
        # Create the batch record
        batch = VaccinationBatch.objects.create(**validated_data)
        
        # Get the animals data and user from the context
        animals_data = self.context.get('animals_data', [])
        user = self.context.get('request').user
        
        # Create individual vaccination records for each animal
        for animal_data in animals_data:
            VaccinationRecord.objects.create(
                batch=batch,
                animal_id=animal_data['animal_id'],
                vaccine=batch.vaccine,
                farm_vaccine=batch.farm_vaccine,
                date=batch.date,
                dosage_given=animal_data.get('dosage_given', ''),
                notes=animal_data.get('notes', ''),
                administered_by=f"{user.first_name} {user.last_name}".strip() or user.username
            )
        
        return batch 