from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    VaccineViewSet, VaccinationRecordViewSet,
    VaccinationBatchViewSet, FarmVaccineViewSet
)

router = DefaultRouter()
router.register(r'vaccines', VaccineViewSet, basename='vaccine')
router.register(r'farm-vaccines', FarmVaccineViewSet, basename='farm-vaccine')
router.register(r'vaccination-records', VaccinationRecordViewSet, basename='vaccination-record')
router.register(r'vaccination-batches', VaccinationBatchViewSet, basename='vaccination-batch')

urlpatterns = [
    path('', include(router.urls)),
] 