from django.contrib import admin
from .models import Vaccine, VaccineSchedule
from django import forms

class VaccineScheduleAdminForm(forms.ModelForm):
    animal_category = forms.MultipleChoiceField(
        choices=VaccineSchedule.CATEGORY_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple
    )
    recommended_months = forms.MultipleChoiceField(
        choices=VaccineSchedule.MONTHS_CHOICES,
        required=False,
        widget=forms.CheckboxSelectMultiple
    )

    class Meta:
        model = VaccineSchedule
        fields = '__all__'

@admin.register(Vaccine)
class VaccineAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'manufacturer']
    search_fields = ['name', 'code', 'manufacturer']
    ordering = ['name']

@admin.register(VaccineSchedule)
class VaccineScheduleAdmin(admin.ModelAdmin):
    form = VaccineScheduleAdminForm
    list_display = ['vaccine', 'get_categories_display', 'dosage', 'get_months_display', 'frequency']
    list_filter = ['frequency']
    search_fields = ['vaccine__name', 'dosage']
    ordering = ['vaccine']

    def get_categories_display(self, obj):
        if obj.animal_category:
            categories_dict = dict(VaccineSchedule.CATEGORY_CHOICES)
            return ", ".join(categories_dict.get(cat, cat) for cat in obj.animal_category)
        return "-"
    get_categories_display.short_description = "Animal Categories"

    def get_months_display(self, obj):
        if obj.recommended_months:
            months_dict = dict(VaccineSchedule.MONTHS_CHOICES)
            return ", ".join(months_dict.get(month, month) for month in obj.recommended_months)
        return "-"
    get_months_display.short_description = "Recommended Months"
