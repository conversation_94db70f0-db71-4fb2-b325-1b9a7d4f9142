# Generated by Django 3.2.16 on 2025-01-13 01:12

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Vaccine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200, unique=True)),
                ('manufacturer', models.CharField(blank=True, max_length=200, null=True)),
                ('default_recommended_doses', models.PositiveIntegerField(default=1, help_text='Default number of doses recommended (e.g., 1, 2, 3...).')),
                ('default_recommended_interval_days', models.PositiveIntegerField(default=0, help_text='Default interval in days between doses (if applicable).')),
                ('description', models.TextField(blank=True, help_text='Additional details or notes.', null=True)),
            ],
        ),
    ]
