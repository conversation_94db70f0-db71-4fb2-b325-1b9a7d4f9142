# Generated by Django 3.2.16 on 2025-01-31 06:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0013_salarytransaction_receipt_image'),
        ('dairy', '0042_animalevent'),
        ('health', '0006_farmvaccine'),
    ]

    operations = [
        migrations.CreateModel(
            name='VaccinationBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('scope', models.CharField(choices=[('all', 'All Farm Animals'), ('type', 'All Animals of Type'), ('selected', 'Selected Animals')], max_length=10)),
                ('animal_type', models.CharField(blank=True, choices=[('buffalo', 'Buffalo'), ('cow', 'Cow'), ('goat', 'Goat'), ('sheep', 'Sheep'), ('beef', 'Beef'), ('other', 'Other')], help_text="Required when scope is 'type'", max_length=50, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('farm', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.farm')),
                ('farm_vaccine', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='health.farmvaccine')),
                ('vaccine', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='health.vaccine')),
            ],
        ),
        migrations.CreateModel(
            name='VaccinationRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('dosage_given', models.CharField(max_length=50)),
                ('administered_by', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('animal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vaccinations', to='dairy.animal')),
                ('batch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='vaccination_records', to='health.vaccinationbatch')),
                ('farm_vaccine', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='health.farmvaccine')),
                ('vaccine', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='health.vaccine')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.AddIndex(
            model_name='vaccinationrecord',
            index=models.Index(fields=['animal', '-date'], name='health_vacc_animal__f46b16_idx'),
        ),
        migrations.AddIndex(
            model_name='vaccinationrecord',
            index=models.Index(fields=['vaccine', '-date'], name='health_vacc_vaccine_b0f6cd_idx'),
        ),
        migrations.AddIndex(
            model_name='vaccinationrecord',
            index=models.Index(fields=['farm_vaccine', '-date'], name='health_vacc_farm_va_487e03_idx'),
        ),
    ]
