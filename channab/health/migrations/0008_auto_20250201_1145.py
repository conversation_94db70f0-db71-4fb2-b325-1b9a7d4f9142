# Generated by Django 3.2.16 on 2025-02-01 11:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('health', '0007_auto_20250131_0629'),
    ]

    operations = [
        migrations.AddField(
            model_name='farmvaccine',
            name='is_override',
            field=models.BooleanField(default=False, help_text='Indicates if this is an override of a site-level vaccine'),
        ),
        migrations.AddField(
            model_name='farmvaccine',
            name='site_schedule',
            field=models.ForeignKey(blank=True, help_text='Reference to the site-level vaccine schedule if this is an override', null=True, on_delete=django.db.models.deletion.SET_NULL, to='health.vaccineschedule'),
        ),
        migrations.AddField(
            model_name='farmvaccine',
            name='site_vaccine',
            field=models.ForeignKey(blank=True, help_text='Reference to the site-level vaccine if this is an override', null=True, on_delete=django.db.models.deletion.SET_NULL, to='health.vaccine'),
        ),
        migrations.AddConstraint(
            model_name='farmvaccine',
            constraint=models.UniqueConstraint(condition=models.Q(('is_override', True)), fields=('farm', 'site_vaccine', 'site_schedule'), name='unique_farm_vaccine_override'),
        ),
    ]
