# Generated by Django 3.2.16 on 2025-01-23 14:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0013_salarytransaction_receipt_image'),
        ('health', '0005_alter_vaccineschedule_animal_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='FarmVaccine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('animal_type', models.CharField(choices=[('buffalo', 'Buffalo'), ('cow', 'Cow'), ('goat', 'Goat'), ('sheep', 'Sheep'), ('beef', 'Beef'), ('other', 'Other')], help_text='Type of animal this vaccine is for', max_length=50)),
                ('dosage', models.CharField(max_length=50)),
                ('months', models.J<PERSON><PERSON>ield(help_text='Array of months when this vaccine should be administered')),
                ('description', models.TextField(blank=True, help_text='Additional details about this vaccine', null=True)),
                ('is_recommended', models.BooleanField(default=False, help_text='Indicates if this vaccine is recommended')),
                ('is_required', models.BooleanField(default=False, help_text='Indicates if this vaccine is mandatory')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('farm', models.ForeignKey(help_text='Farm this vaccine record belongs to', on_delete=django.db.models.deletion.CASCADE, to='accounts.farm')),
            ],
            options={
                'verbose_name': 'Farm Vaccine',
                'verbose_name_plural': 'Farm Vaccines',
            },
        ),
    ]
