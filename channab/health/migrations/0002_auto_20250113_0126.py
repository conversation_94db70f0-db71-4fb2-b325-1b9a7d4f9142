# Generated by Django 3.2.16 on 2025-01-13 01:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('health', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='vaccine',
            name='default_recommended_doses',
        ),
        migrations.RemoveField(
            model_name='vaccine',
            name='default_recommended_interval_days',
        ),
        migrations.AddField(
            model_name='vaccine',
            name='code',
            field=models.CharField(default='DEFAULT_CODE', help_text="Short code or abbreviation, e.g. 'BQ'.", max_length=50, unique=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='vaccine',
            name='description',
            field=models.TextField(blank=True, help_text='Additional info about the vaccine.', null=True),
        ),
        migrations.AlterField(
            model_name='vaccine',
            name='manufacturer',
            field=models.CharField(blank=True, help_text='Name of the manufacturer or brand.', max_length=200, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='vaccine',
            name='name',
            field=models.Char<PERSON>ield(help_text="Global vaccine name, e.g. 'Black Quarter'.", max_length=200, unique=True),
        ),
        migrations.CreateModel(
            name='VaccineSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('animal_category', models.CharField(help_text="Type of animal, e.g. 'cow', 'goat', 'buffalo'.", max_length=100)),
                ('dosage', models.CharField(help_text="Dosage instructions, e.g. '5 ml s/c'.", max_length=100)),
                ('recommended_months', models.CharField(blank=True, help_text="Suggested months for vaccination, e.g. 'March/April, Sep/Nov'.", max_length=200, null=True)),
                ('frequency', models.CharField(blank=True, choices=[('yearly', 'Yearly'), ('quarterly', 'Quarterly'), ('monthly', 'Monthly'), ('weekly', 'Weekly'), ('daily', 'Daily')], help_text="Overall frequency, e.g. 'yearly', 'quarterly'.", max_length=50, null=True)),
                ('vaccine', models.ForeignKey(help_text='Reference to the global Vaccine.', on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='health.vaccine')),
            ],
            options={
                'verbose_name': 'Vaccine Schedule',
                'verbose_name_plural': 'Vaccine Schedules',
                'unique_together': {('vaccine', 'animal_category')},
            },
        ),
    ]
