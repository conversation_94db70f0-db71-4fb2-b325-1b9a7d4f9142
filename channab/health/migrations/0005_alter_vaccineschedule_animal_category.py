# Generated by Django 3.2.16 on 2025-01-13 01:42

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('health', '0004_alter_vaccineschedule_recommended_months'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='vaccineschedule',
            name='animal_category',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('buffalo', 'Buffalo'), ('cow', 'Cow'), ('goat', 'Goat'), ('sheep', 'Sheep'), ('beef', 'Beef'), ('other', 'Other')], max_length=20), blank=True, help_text='Select one or more animal categories.', null=True, size=None),
        ),
    ]
