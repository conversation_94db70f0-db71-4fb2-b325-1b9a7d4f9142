# Generated by Django 3.2.16 on 2025-01-13 01:37

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('health', '0003_alter_vaccineschedule_animal_category'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='vaccineschedule',
            name='recommended_months',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('jan', 'January'), ('feb', 'February'), ('mar', 'March'), ('apr', 'April'), ('may', 'May'), ('jun', 'June'), ('jul', 'July'), ('aug', 'August'), ('sep', 'September'), ('oct', 'October'), ('nov', 'November'), ('dec', 'December')], max_length=3), blank=True, help_text='Select one or more recommended months for vaccination.', null=True, size=12),
        ),
    ]
