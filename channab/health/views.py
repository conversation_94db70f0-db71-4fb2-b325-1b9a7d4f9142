from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .serializers import (
    FarmVaccineSerializer, VaccineSerializer, VaccineScheduleSerializer,
    VaccinationRecordSerializer, VaccinationBatchSerializer
)
from .models import (
    FarmVaccine, Vaccine, VaccineSchedule,
    VaccinationRecord, VaccinationBatch, Animal
)

# Create your views here.

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class VaccineViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    
    def get_queryset(self):
        # Get the user's farm from their profile
        user = self.request.user
        farm = user.farm if hasattr(user, 'farm') else None
        
        # Get farm-specific vaccines if user has a farm
        farm_vaccines = []
        if farm:
            farm_vaccines = FarmVaccine.objects.filter(farm=farm)
        
        # Get site-level vaccines with their schedules
        site_vaccines = Vaccine.objects.all().prefetch_related('schedules')
        
        return {
            'site_vaccines': site_vaccines,
            'farm_vaccines': farm_vaccines
        }
    
    def retrieve(self, request, pk=None):
        """
        Get details of a specific vaccine (both farm-level or site-level)
        """
        # Check if it's a farm vaccine or site vaccine based on the ID prefix
        if pk.startswith('farm_'):
            try:
                farm_id = int(pk.split('_')[1])
                farm_vaccine = get_object_or_404(FarmVaccine, id=farm_id, farm=request.user.farm)
                return Response({
                    'id': f"farm_{farm_vaccine.id}",
                    'title': farm_vaccine.title,
                    'description': farm_vaccine.description,
                    'animal_type': farm_vaccine.animal_type,
                    'dosage': farm_vaccine.dosage,
                    'months': farm_vaccine.months,
                    'is_recommended': farm_vaccine.is_recommended,
                    'is_required': farm_vaccine.is_required,
                    'status': 'Completed',
                    'schedule': '/'.join(farm_vaccine.months) if farm_vaccine.months else '',
                    'farm': farm_vaccine.farm_id,
                    'created_at': farm_vaccine.created_at,
                    'updated_at': farm_vaccine.updated_at,
                    'type': 'farm',
                    'manufacturer': None,
                    'code': None,
                    'is_override': farm_vaccine.is_override,
                    'site_vaccine_id': f"site_{farm_vaccine.site_vaccine.id}_{farm_vaccine.site_schedule.id}" if farm_vaccine.is_override else None
                })
            except (IndexError, ValueError):
                return Response({'error': 'Invalid farm vaccine ID format'}, status=status.HTTP_400_BAD_REQUEST)
        
        elif pk.startswith('site_'):
            try:
                # Extract vaccine and schedule IDs from the composite key
                _, vaccine_id, schedule_id = pk.split('_')
                vaccine = get_object_or_404(Vaccine, id=int(vaccine_id))
                schedule = get_object_or_404(VaccineSchedule, id=int(schedule_id), vaccine=vaccine)
                
                # Check if there's an override for this farm
                farm_override = None
                if hasattr(request.user, 'farm'):
                    farm_override = FarmVaccine.objects.filter(
                        farm=request.user.farm,
                        site_vaccine=vaccine,
                        site_schedule=schedule,
                        is_override=True
                    ).first()
                
                if farm_override:
                    # Return the farm override instead
                    return Response({
                        'id': f"farm_{farm_override.id}",
                        'title': farm_override.title,
                        'description': farm_override.description,
                        'animal_type': farm_override.animal_type,
                        'dosage': farm_override.dosage,
                        'months': farm_override.months,
                        'is_recommended': farm_override.is_recommended,
                        'is_required': farm_override.is_required,
                        'status': 'Override',
                        'schedule': '/'.join(farm_override.months) if farm_override.months else '',
                        'farm': farm_override.farm_id,
                        'created_at': farm_override.created_at,
                        'updated_at': farm_override.updated_at,
                        'type': 'farm',
                        'manufacturer': vaccine.manufacturer,
                        'code': vaccine.code,
                        'is_override': True,
                        'site_vaccine_id': f"site_{vaccine.id}_{schedule.id}"
                    })
                
                return Response({
                    'id': f"site_{vaccine.id}_{schedule.id}",
                    'title': vaccine.name,
                    'description': vaccine.description,
                    'animal_type': schedule.animal_category[0] if schedule.animal_category else None,
                    'dosage': schedule.dosage,
                    'months': schedule.recommended_months,
                    'is_recommended': True,
                    'is_required': True,
                    'status': 'Available',
                    'schedule': '/'.join(schedule.recommended_months) if schedule.recommended_months else '',
                    'farm': None,
                    'created_at': None,
                    'updated_at': None,
                    'type': 'site',
                    'manufacturer': vaccine.manufacturer,
                    'code': vaccine.code,
                    'frequency': schedule.get_frequency_display() if schedule.frequency else None,
                    'is_override': False,
                    'site_vaccine_id': None
                })
            except (IndexError, ValueError):
                return Response({'error': 'Invalid site vaccine ID format'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'error': 'Invalid vaccine ID format'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def all_vaccines(self, request):
        """
        Get all vaccines (both farm-specific and site-level)
        Uses the authenticated user's farm association
        """
        queryset = self.get_queryset()
        combined_data = []
        
        print("\n=== DEBUG: Vaccine Listing ===")
        print(f"Number of farm vaccines: {len(queryset['farm_vaccines'])}")
        print(f"Number of site vaccines: {queryset['site_vaccines'].count()}")
        
        # Get farm's animal types from actual animals
        farm = request.user.farm if hasattr(request.user, 'farm') else None
        farm_animal_types = []
        if farm:
            farm_animal_types = list(Animal.objects.filter(
                farm=farm,
                status='active'
            ).values_list('category', flat=True).distinct())
            print(f"Farm's actual animal types: {farm_animal_types}")
        
        # Add farm-level vaccines first
        for farm_vaccine in queryset['farm_vaccines']:
            print(f"\nProcessing farm vaccine: {farm_vaccine.title}")
            vaccine_data = {
                'id': f"farm_{farm_vaccine.id}",
                'title': farm_vaccine.title,
                'description': farm_vaccine.description,
                'animal_type': farm_vaccine.animal_type,
                'dosage': farm_vaccine.dosage,
                'months': farm_vaccine.months,
                'is_recommended': farm_vaccine.is_recommended,
                'is_required': farm_vaccine.is_required,
                'status': 'Override' if farm_vaccine.is_override else 'Farm',
                'schedule': '/'.join(farm_vaccine.months) if farm_vaccine.months else '',
                'farm': farm_vaccine.farm_id,
                'created_at': farm_vaccine.created_at,
                'updated_at': farm_vaccine.updated_at,
                'type': 'farm',
                'manufacturer': farm_vaccine.site_vaccine.manufacturer if farm_vaccine.site_vaccine else None,
                'code': farm_vaccine.site_vaccine.code if farm_vaccine.site_vaccine else None,
                'is_override': farm_vaccine.is_override,
                'site_vaccine_id': f"site_{farm_vaccine.site_vaccine.id}_{farm_vaccine.site_schedule.id}" if farm_vaccine.is_override else None
            }
            combined_data.append(vaccine_data)
            print(f"Added farm vaccine: {vaccine_data['title']} ({vaccine_data['status']})")
        
        # Get list of overridden site vaccines
        overridden_site_vaccines = set()
        for farm_vaccine in queryset['farm_vaccines']:
            if farm_vaccine.is_override and farm_vaccine.site_vaccine and farm_vaccine.site_schedule:
                key = (farm_vaccine.site_vaccine.id, farm_vaccine.site_schedule.id)
                overridden_site_vaccines.add(key)
                print(f"Found override for site vaccine: {farm_vaccine.site_vaccine.name} (Schedule: {farm_vaccine.site_schedule.id})")
        
        print(f"\nNumber of overridden site vaccines: {len(overridden_site_vaccines)}")
        
        # Then add site-level vaccines that haven't been overridden
        for vaccine in queryset['site_vaccines']:
            print(f"\nProcessing site vaccine: {vaccine.name}")
            schedules = vaccine.schedules.all()
            print(f"Number of schedules for {vaccine.name}: {len(schedules)}")
            
            for schedule in schedules:
                print(f"Processing schedule {schedule.id} for {vaccine.name}")
                # Skip if this site vaccine has been overridden
                if (vaccine.id, schedule.id) in overridden_site_vaccines:
                    print(f"Skipping overridden vaccine: {vaccine.name} (Schedule: {schedule.id})")
                    continue
                
                schedule_animal_type = schedule.animal_category[0] if schedule.animal_category else None
                print(f"Farm animal types: {farm_animal_types}")
                print(f"Schedule animal type: {schedule_animal_type}")
                
                # Only add site vaccines if they match the farm's animal types or if there are no farm animals
                if not farm_animal_types or schedule_animal_type in farm_animal_types:
                    vaccine_data = {
                        'id': f"site_{vaccine.id}_{schedule.id}",
                        'title': vaccine.name,
                        'description': vaccine.description,
                        'animal_type': schedule_animal_type,
                        'dosage': schedule.dosage,
                        'months': schedule.recommended_months,
                        'is_recommended': True,
                        'is_required': True,
                        'status': 'Available',
                        'schedule': '/'.join(schedule.recommended_months) if schedule.recommended_months else '',
                        'farm': None,
                        'created_at': None,
                        'updated_at': None,
                        'type': 'site',
                        'manufacturer': vaccine.manufacturer,
                        'code': vaccine.code,
                        'is_override': False,
                        'site_vaccine_id': None,
                        'frequency': schedule.get_frequency_display() if schedule.frequency else None
                    }
                    combined_data.append(vaccine_data)
                    print(f"Added site vaccine: {vaccine_data['title']}")
                else:
                    print(f"Skipping site vaccine {vaccine.name} - animal type {schedule_animal_type} not in farm types {farm_animal_types}")
        
        print(f"\nTotal vaccines in response: {len(combined_data)}")
        print("=== End Debug ===\n")
        
        # Manual pagination
        paginator = self.pagination_class()
        paginated_data = paginator.paginate_queryset(combined_data, request)
        
        return paginator.get_paginated_response(paginated_data)

class VaccinationRecordViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = VaccinationRecordSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user = self.request.user
        farm = user.farm if hasattr(user, 'farm') else None
        if not farm:
            return VaccinationRecord.objects.none()
        
        return VaccinationRecord.objects.filter(
            Q(animal__farm=farm) | Q(batch__farm=farm)
        ).select_related('animal', 'vaccine', 'farm_vaccine', 'batch')

    def perform_create(self, serializer):
        user = self.request.user
        administered_by = f"{user.first_name} {user.last_name}".strip() or user.username
        serializer.save(administered_by=administered_by)

    @action(detail=False, methods=['get'])
    def animal_vaccinations(self, request):
        """Get vaccination records for a specific animal"""
        animal_id = request.query_params.get('animal_id')
        if not animal_id:
            return Response(
                {'error': 'animal_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        queryset = self.get_queryset().filter(animal_id=animal_id).order_by('-date')
        
        # Group records by vaccine and date
        grouped_records = {}
        for record in queryset:
            # Get vaccine name and create key
            vaccine_name = record.vaccine.name if record.vaccine else record.farm_vaccine.title
            vaccine_id = f"site_{record.vaccine.id}" if record.vaccine else f"farm_{record.farm_vaccine.id}"
            date = record.date
            
            key = (vaccine_id, vaccine_name, date)
            
            if key not in grouped_records:
                grouped_records[key] = {
                    'vaccine_id': vaccine_id,
                    'vaccine_name': vaccine_name,
                    'date': date,
                    'dosage_given': record.dosage_given,
                    'administered_by': record.administered_by,
                    'notes': record.notes or '',
                    'animal_tag': record.animal.tag
                }
        
        # Convert to list and sort by date (newest first)
        result = list(grouped_records.values())
        result.sort(key=lambda x: x['date'], reverse=True)
        
        return Response(result)

    @action(detail=False, methods=['get'])
    def grouped_vaccinations(self, request):
        """Get vaccinations for a specific vaccine (farm or site) for farm animals"""
        # Check both vaccine and farm_vaccine parameters for backward compatibility
        vaccine_id = request.query_params.get('vaccine') or request.query_params.get('farm_vaccine')
        if not vaccine_id:
            return Response(
                {'error': 'vaccine parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            farm = request.user.farm
            if not farm:
                return Response(
                    {'error': 'User is not associated with any farm'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            base_queryset = self.get_queryset()

            if vaccine_id.startswith('farm_'):
                # For farm vaccines
                _, fv_id = vaccine_id.split('_')
                farm_vaccine = get_object_or_404(FarmVaccine, id=int(fv_id), farm=farm)
                queryset = base_queryset.filter(
                    farm_vaccine=farm_vaccine,
                    animal__farm=farm
                ).select_related('animal', 'farm_vaccine')
                vaccine_name = farm_vaccine.title

            elif vaccine_id.startswith('site_'):
                # For site vaccines
                parts = vaccine_id.split('_')
                if len(parts) == 3:
                    _, vaccine_id, schedule_id = parts
                else:
                    _, vaccine_id = parts
                vaccine = get_object_or_404(Vaccine, id=int(vaccine_id))
                queryset = base_queryset.filter(
                    vaccine=vaccine,
                    animal__farm=farm
                ).select_related('animal', 'vaccine')
                vaccine_name = vaccine.name

            else:
                return Response(
                    {'error': 'Invalid vaccine ID format. Must start with "farm_" or "site_"'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Group records by date and dosage
            grouped_records = {}
            for record in queryset.order_by('-date'):
                date = record.date
                dosage = record.dosage_given
                
                key = (date, dosage)
                
                if key not in grouped_records:
                    grouped_records[key] = {
                        'vaccine_id': vaccine_id,  # Use the original vaccine_id from request
                        'vaccine_name': vaccine_name,
                        'date': date,
                        'dosage_given': dosage,
                        'administered_by': record.administered_by or '',
                        'notes': record.notes or '',
                        'animal_tags': []
                    }
                
                # Add animal tag to the list if not already present
                if record.animal.tag not in grouped_records[key]['animal_tags']:
                    grouped_records[key]['animal_tags'].append(record.animal.tag)
            
            # Convert to list and sort by date (newest first)
            result = list(grouped_records.values())
            result.sort(key=lambda x: x['date'], reverse=True)
            
            return Response(result)

        except (ValueError, IndexError) as e:
            return Response(
                {'error': f'Invalid vaccine ID format: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

class VaccinationBatchViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = VaccinationBatchSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user = self.request.user
        farm = user.farm if hasattr(user, 'farm') else None
        if not farm:
            return VaccinationBatch.objects.none()
        
        return VaccinationBatch.objects.filter(farm=farm).prefetch_related(
            'vaccination_records',
            'vaccination_records__animal',
            'vaccination_records__vaccine',
            'vaccination_records__farm_vaccine'
        )

    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        user = request.user
        farm = user.farm if hasattr(user, 'farm') else None
        
        if not farm:
            return Response(
                {'error': 'User is not associated with any farm'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Set the farm automatically
        data['farm'] = farm.id
        
        # Handle composite vaccine ID (site_vaccine_id_schedule_id or farm_vaccine_id)
        vaccine_id = data.get('vaccine')
        if isinstance(vaccine_id, str):
            if vaccine_id.startswith('site_'):
                try:
                    _, vaccine_id, schedule_id = vaccine_id.split('_')
                    vaccine = get_object_or_404(Vaccine, id=int(vaccine_id))
                    data['vaccine'] = vaccine.id
                    data.pop('farm_vaccine', None)  # Ensure farm_vaccine is not set
                except (ValueError, IndexError):
                    return Response(
                        {'error': 'Invalid site vaccine ID format'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            elif vaccine_id.startswith('farm_'):
                try:
                    _, farm_vaccine_id = vaccine_id.split('_')
                    farm_vaccine = get_object_or_404(FarmVaccine, id=int(farm_vaccine_id), farm=farm)
                    data['farm_vaccine'] = farm_vaccine.id
                    data.pop('vaccine', None)  # Ensure vaccine is not set
                except (ValueError, IndexError):
                    return Response(
                        {'error': 'Invalid farm vaccine ID format'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
        
        # Add animals_data to serializer context
        animals_data = data.pop('animals', [])
        serializer = self.get_serializer(
            data=data,
            context={'animals_data': animals_data, 'request': request}
        )
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )

class FarmVaccineViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = FarmVaccineSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user = self.request.user
        farm = user.farm if hasattr(user, 'farm') else None
        if not farm:
            return FarmVaccine.objects.none()
        return FarmVaccine.objects.filter(farm=farm)

    def perform_create(self, serializer):
        serializer.save(farm=self.request.user.farm)

    @action(detail=False, methods=['get'])
    def animal_types(self, request):
        """Get list of available animal types"""
        return Response([
            {'value': choice[0], 'label': choice[1]} 
            for choice in FarmVaccine.CATEGORY_CHOICES
        ])

    @action(detail=False, methods=['get'])
    def months(self, request):
        """Get list of available months"""
        return Response([
            {'value': choice[0], 'label': choice[1]} 
            for choice in FarmVaccine.MONTHS_CHOICES
        ])

    @action(detail=True, methods=['post'])
    def override_site_vaccine(self, request, pk=None):
        """Create an override for a site-level vaccine"""
        try:
            # Get the site vaccine and schedule
            _, vaccine_id, schedule_id = pk.split('_')
            site_vaccine = get_object_or_404(Vaccine, id=int(vaccine_id))
            site_schedule = get_object_or_404(VaccineSchedule, id=int(schedule_id), vaccine=site_vaccine)
            
            # Check if override already exists
            existing_override = FarmVaccine.objects.filter(
                farm=request.user.farm,
                site_vaccine=site_vaccine,
                site_schedule=site_schedule,
                is_override=True
            ).first()
            
            if existing_override:
                return Response(
                    {'error': 'An override for this site vaccine already exists'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Create the override
            data = request.data.copy()
            data.update({
                'site_vaccine_id': pk,
                'is_override': True
            })
            
            serializer = self.get_serializer(data=data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except (ValueError, IndexError):
            return Response(
                {'error': 'Invalid site vaccine ID format'},
                status=status.HTTP_400_BAD_REQUEST
            )
