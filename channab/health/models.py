from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from dairy.models import Animal
from django.core.exceptions import ValidationError


class Vaccine(models.Model):
    """
    A globally defined Vaccine entity, managed by system-level (site) admins.
    Stores universal attributes such as name, manufacturer, general description, etc.
    """
    name = models.CharField(
        max_length=200,
        unique=True,
        help_text="Global vaccine name, e.g. 'Black Quarter'."
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        help_text="Short code or abbreviation, e.g. 'BQ'."
    )
    manufacturer = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text="Name of the manufacturer or brand."
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Additional info about the vaccine."
    )

    def __str__(self):
        return f"{self.name} ({self.code})"


class VaccineSchedule(models.Model):
    """
    Defines category-specific schedules for a given Vaccine.
    Different animals (cow, goat, buffalo, etc.) may have different dosages,
    recommended months, or frequencies for the same vaccine.
    """
    FREQUENCY_CHOICES = [
        ("yearly", "Yearly"),
        ("quarterly", "Quarterly"),  # Every three months
        ("monthly", "Monthly"),
        ("weekly", "Weekly"),
        ("daily", "Daily"),
    ]

    CATEGORY_CHOICES = [     
        ('buffalo', 'Buffalo'),
        ('cow', 'Cow'),
        ('goat', 'Goat'),
        ('sheep', 'Sheep'),
        ('beef', 'Beef'),
        ('other', 'Other')
    ]
    MONTHS_CHOICES = [
        ('jan', 'January'),
        ('feb', 'February'),
        ('mar', 'March'),
        ('apr', 'April'),
        ('may', 'May'),
        ('jun', 'June'),
        ('jul', 'July'),
        ('aug', 'August'),
        ('sep', 'September'),
        ('oct', 'October'),
        ('nov', 'November'),
        ('dec', 'December'),
    ]

    vaccine = models.ForeignKey(
        Vaccine,
        on_delete=models.CASCADE,
        related_name='schedules',
        help_text="Reference to the global Vaccine."
    )
    animal_category = ArrayField(
        models.CharField(max_length=20, choices=CATEGORY_CHOICES),
        blank=True,
        null=True,
        help_text="Select one or more animal categories."
    )
    dosage = models.CharField(
        max_length=100,
        help_text="Dosage instructions, e.g. '5 ml s/c'."
    )
    recommended_months = ArrayField(
        models.CharField(max_length=3, choices=MONTHS_CHOICES),
        blank=True,
        null=True,
        help_text="Select one or more recommended months for vaccination.",
        size=12
    )
    frequency = models.CharField(
        max_length=50,
        choices=FREQUENCY_CHOICES,
        blank=True,
        null=True,
        help_text="Overall frequency, e.g. 'yearly', 'quarterly'."
    )

    class Meta:
        unique_together = ('vaccine', 'animal_category')
        verbose_name = "Vaccine Schedule"
        verbose_name_plural = "Vaccine Schedules"

    def __str__(self):
        return f"{self.vaccine.name} for {self.animal_category} ({self.get_frequency_display()})"


class FarmVaccine(models.Model):
    """
    Farm-specific vaccine records that can be managed by farm owners/managers
    through the mobile app. Can be either a custom farm vaccine or an override
    of a site-level vaccine.
    """
    MONTHS_CHOICES = [
        ('jan', 'January'),
        ('feb', 'February'),
        ('mar', 'March'),
        ('apr', 'April'),
        ('may', 'May'),
        ('jun', 'June'),
        ('jul', 'July'),
        ('aug', 'August'),
        ('sep', 'September'),
        ('oct', 'October'),
        ('nov', 'November'),
        ('dec', 'December'),
    ]
    
    title = models.CharField(max_length=255)
    description = models.TextField(
        blank=True, 
        null=True,
        help_text="Additional details about this vaccine"
    )
    animal_type = models.CharField(
        max_length=50,
        choices=VaccineSchedule.CATEGORY_CHOICES,
        help_text="Type of animal this vaccine is for"
    )
    dosage = models.CharField(max_length=50)
    months = models.JSONField(
        help_text="Array of months when this vaccine should be administered"
    )
    is_recommended = models.BooleanField(
        default=False,
        help_text="Indicates if this vaccine is recommended"
    )
    is_required = models.BooleanField(
        default=False,
        help_text="Indicates if this vaccine is mandatory"
    )
    farm = models.ForeignKey(
        'accounts.Farm', 
        on_delete=models.CASCADE,
        help_text="Farm this vaccine record belongs to"
    )
    site_vaccine = models.ForeignKey(
        'Vaccine',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Reference to the site-level vaccine if this is an override"
    )
    site_schedule = models.ForeignKey(
        'VaccineSchedule',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Reference to the site-level vaccine schedule if this is an override"
    )
    is_override = models.BooleanField(
        default=False,
        help_text="Indicates if this is an override of a site-level vaccine"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Farm Vaccine"
        verbose_name_plural = "Farm Vaccines"
        constraints = [
            models.UniqueConstraint(
                fields=['farm', 'site_vaccine', 'site_schedule'],
                condition=models.Q(is_override=True),
                name='unique_farm_vaccine_override'
            )
        ]

    def clean(self):
        super().clean()
        if not isinstance(self.months, list):
            raise ValidationError({'months': 'Months must be a list'})
        
        valid_months = [choice[0] for choice in self.MONTHS_CHOICES]
        for month in self.months:
            if month not in valid_months:
                raise ValidationError({'months': f'Invalid month: {month}'})

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        if self.is_override:
            return f"{self.title} (Override) - {self.animal_type}"
        return f"{self.title} - {self.animal_type}"


class VaccinationBatch(models.Model):
    """
    Represents a batch vaccination event for multiple animals
    """
    farm = models.ForeignKey('accounts.Farm', on_delete=models.CASCADE)
    vaccine = models.ForeignKey(
        'Vaccine', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True
    )
    farm_vaccine = models.ForeignKey(
        'FarmVaccine',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    date = models.DateField()
    SCOPE_CHOICES = [
        ('all', 'All Farm Animals'),
        ('type', 'All Animals of Type'),
        ('selected', 'Selected Animals')
    ]
    scope = models.CharField(max_length=10, choices=SCOPE_CHOICES)
    animal_type = models.CharField(
        max_length=50,
        choices=VaccineSchedule.CATEGORY_CHOICES,
        null=True,
        blank=True,
        help_text="Required when scope is 'type'"
    )
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def clean(self):
        if not (self.vaccine or self.farm_vaccine):
            raise ValidationError('Either vaccine or farm_vaccine must be specified')
        if self.vaccine and self.farm_vaccine:
            raise ValidationError('Cannot specify both vaccine and farm_vaccine')
        if self.scope == 'type' and not self.animal_type:
            raise ValidationError('Animal type is required when scope is "type"')

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
        
        # Create individual vaccination records based on scope
        if self.scope == 'all':
            animals = Animal.objects.filter(farm=self.farm, status='active')
        elif self.scope == 'type':
            animals = Animal.objects.filter(
                farm=self.farm,
                status='active',
                category=self.animal_type
            )
        else:  # 'selected' scope is handled separately when adding individual animals
            return
        
        # Create vaccination records for each animal
        for animal in animals:
            VaccinationRecord.objects.create(
                batch=self,
                animal=animal,
                vaccine=self.vaccine,
                farm_vaccine=self.farm_vaccine,
                date=self.date
            )

    def __str__(self):
        vaccine_name = self.vaccine.name if self.vaccine else self.farm_vaccine.title
        return f"Batch vaccination of {vaccine_name} on {self.date}"


class VaccinationRecord(models.Model):
    """
    Records individual animal vaccination events
    """
    batch = models.ForeignKey(
        VaccinationBatch,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='vaccination_records'
    )
    animal = models.ForeignKey(
        'dairy.Animal',
        on_delete=models.CASCADE,
        related_name='vaccinations'
    )
    vaccine = models.ForeignKey(
        'Vaccine',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    farm_vaccine = models.ForeignKey(
        'FarmVaccine',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    date = models.DateField()
    dosage_given = models.CharField(max_length=50)
    administered_by = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def clean(self):
        if not (self.vaccine or self.farm_vaccine):
            raise ValidationError('Either vaccine or farm_vaccine must be specified')
        if self.vaccine and self.farm_vaccine:
            raise ValidationError('Cannot specify both vaccine and farm_vaccine')

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-date']
        indexes = [
            models.Index(fields=['animal', '-date']),
            models.Index(fields=['vaccine', '-date']),
            models.Index(fields=['farm_vaccine', '-date'])
        ]

    def __str__(self):
        vaccine_name = self.vaccine.name if self.vaccine else self.farm_vaccine.title
        return f"{self.animal.tag} - {vaccine_name} on {self.date}"


