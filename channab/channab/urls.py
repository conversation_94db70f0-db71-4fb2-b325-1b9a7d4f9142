"""channab URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic.base import RedirectView
from django.contrib.sitemaps.views import sitemap
from django.views.generic import TemplateView
from home.sitemap import HomeSitemap

sitemaps = {
    'home': HomeSitemap,
}

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls', namespace='accounts')),
    path('dairy/', include('dairy.urls', namespace='dairy')),
    path('crops/', include('crops.urls', namespace='crops')),
    path('erp/', include('farm_finances.urls', namespace='farm_finances')),  
    path('feeds/', include('feeds.urls', namespace='feeds')),
    path('api/health/', include('health.urls')),  
    path('', include('home.urls', namespace='home')),
    
    # Serve robots.txt and sitemap.xml
    path('robots.txt', TemplateView.as_view(template_name="robots.txt", content_type="text/plain")),
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),
    
    # Canonical URL redirects
    path('index.html', RedirectView.as_view(url='/', permanent=True)),
    path('index.php', RedirectView.as_view(url='/', permanent=True)),
    path('home/', RedirectView.as_view(url='/', permanent=True)),
    path('home.html', RedirectView.as_view(url='/', permanent=True)),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT) + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

if settings.DEBUG:
    pass

# Handle 404 errors
handler404 = 'home.views.custom_404'
