ERROR 2025-01-11 10:48:04,622 views_api 713388 *************** Unexpected error during signup: 'email'
Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views_api.py", line 570, in post
    if CustomUser.objects.filter(email=data['email']).exists():
KeyError: 'email'
ERROR 2025-01-11 10:49:43,441 views_api 713389 *************** Unexpected error during signup: 'email'
Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views_api.py", line 570, in post
    logger.warning("Signup failed - Mobile number is required")
KeyError: 'email'
ERROR 2025-02-11 03:39:14,241 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:14,242 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:21,988 views 1211593 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:21,991 views 1211593 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:32,248 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:32,251 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:40:02,713 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:40:02,714 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:51:58,785 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:51:58,786 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:01,349 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:01,350 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:28,391 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:28,392 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:31,488 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:31,489 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:38,622 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:38,623 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:40,463 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:40,463 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 04:06:40,462 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:06:40,463 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:06:49,342 views 1211593 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:06:49,342 views 1211593 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:07:16,458 views 1211593 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:07:16,458 views 1211593 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-13 14:22:25,219 views 1223329 *************** Error checking user existence: could not extend file "base/16384/16839": No space left on device
HINT:  Check free disk space.
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 581, in get_or_create
    return self.get(**kwargs), False
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 435, in get
    raise self.model.DoesNotExist(
rest_framework.authtoken.models.Token.DoesNotExist: Token matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.DiskFull: could not extend file "base/16384/16839": No space left on device
HINT:  Check free disk space.


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 150, in post
    token, created = Token.objects.get_or_create(user=user)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 588, in get_or_create
    return self.create(**params), True
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/rest_framework/authtoken/models.py", line 33, in save
    return super().save(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return 