WARNING 2025-01-11 10:29:53,252 views 710787 *************** Login failed - User does not exist: **************
ERROR 2025-01-11 10:48:04,622 views_api 713388 *************** Unexpected error during signup: 'email'
Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views_api.py", line 570, in post
    if CustomUser.objects.filter(email=data['email']).exists():
KeyError: 'email'
ERROR 2025-01-11 10:49:43,441 views_api 713389 *************** Unexpected error during signup: 'email'
Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views_api.py", line 570, in post
    logger.warning("Signup failed - Mobile number is required")
KeyError: 'email'
WARNING 2025-01-12 10:11:00,413 views 715221 *************** Login failed - User does not exist: +************
WARNING 2025-01-12 10:11:14,271 views 715241 *************** Login failed - User does not exist: +************
WARNING 2025-01-12 14:43:35,129 views 715242 *************** Login failed - User does not exist: +************
WARNING 2025-01-12 17:15:19,602 views 810655 *************** Login failed - User does not exist: +************
WARNING 2025-01-12 17:17:58,011 views 810655 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 02:32:31,034 views 857478 *************** Login failed for mobile: *********** - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-13 02:33:38,424 views 857478 *************** Login failed for mobile: 3448402812 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-13 06:12:26,111 views 857476 *************** Login failed - User does not exist: +923158288248
WARNING 2025-01-13 06:44:12,982 views 857476 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:44:17,285 views 857476 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:45:18,821 views 857475 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:45:36,455 views 857476 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:47:01,573 views 857475 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:47:23,232 views 857475 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:47:30,592 views 857475 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:48:38,342 views 857478 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:49:02,034 views 857478 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:49:24,429 views 857478 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:56:49,316 views 857475 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 06:57:03,620 views 857476 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-01-13 07:24:52,630 views 900929 132267714367488 Login failed for mobile: 03007992558 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-14 14:47:22,789 views 900929 132267714367488 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-01-14 14:47:57,193 views 900931 132267714367488 Login failed for mobile: 03046307734 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-14 14:48:32,341 views 900931 132267714367488 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-01-14 18:04:03,070 views 900929 132267714367488 Login failed - User does not exist: +92************
WARNING 2025-01-14 18:04:34,817 views 900929 132267714367488 Login failed - User does not exist: +92************
WARNING 2025-01-14 18:05:21,829 views 900929 132267714367488 Login failed - User does not exist: +966590965893
WARNING 2025-01-14 18:05:46,801 views 900930 132267714367488 Login failed - User does not exist: +92590965893
WARNING 2025-01-15 01:41:49,659 views 900930 132267714367488 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-15 02:48:37,863 views 900931 132267714367488 Login failed - Invalid credentials for mobile: +923105351256
WARNING 2025-01-15 06:23:20,197 views 900929 132267714367488 Login failed - User does not exist: +923043997454
WARNING 2025-01-15 06:24:51,780 views 900930 132267714367488 Login failed - User does not exist: +923043997454
WARNING 2025-01-15 08:21:46,681 views 900929 132267714367488 Login failed - User does not exist: +923067692519
WARNING 2025-01-15 08:21:53,802 views 900929 132267714367488 Login failed - User does not exist: +923067692519
WARNING 2025-01-15 10:14:01,185 views 900931 132267714367488 Login failed - User does not exist: +9232690865
WARNING 2025-01-15 10:14:03,795 views 900929 132267714367488 Login failed - User does not exist: +9232690865
WARNING 2025-01-15 10:14:08,322 views 900931 132267714367488 Login failed - User does not exist: +9232690865
WARNING 2025-01-16 08:30:16,045 views 900929 132267714367488 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-16 16:29:01,468 views 900929 132267714367488 Login failed - User does not exist: +923193750041
WARNING 2025-01-16 16:29:08,692 views 900930 132267714367488 Login failed - User does not exist: +923193750041
WARNING 2025-01-17 06:13:00,070 views 900931 132267714367488 Login failed for mobile: LD dairy farm  - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 06:13:20,708 views 900930 132267714367488 Login failed for mobile: 9353144770 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 07:33:16,978 views 900930 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:33:27,904 views 900929 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:33:40,633 views 900930 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:34:46,901 views 900931 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:34:55,714 views 900931 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:35:09,767 views 900929 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:35:16,237 views 900930 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:35:29,246 views 900931 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:35:38,356 views 900930 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:36:08,485 views 900931 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:36:14,563 views 900929 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:36:20,390 views 900929 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:37:50,361 views 900930 132267714367488 Login failed - User does not exist: +923454158719
WARNING 2025-01-17 07:38:41,968 views 900930 132267714367488 Login failed for mobile: Sharma Dairy Farm - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 13:12:34,052 views 900930 132267714367488 Login failed for mobile: Alyanvirk - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 13:12:47,550 views 900930 132267714367488 Login failed for mobile: Alyanvirk - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 13:13:01,904 views 900931 132267714367488 Login failed for mobile: Alyanvirk - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 13:13:19,586 views 900930 132267714367488 Login failed for mobile: Alyanvirk - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-17 13:13:30,381 views 900930 132267714367488 Login failed for mobile: Alyanvirk - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-01-19 16:39:54,986 views 900930 132267714367488 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-19 16:44:51,419 views 900929 132267714367488 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-20 05:57:23,450 views 900931 132267714367488 Login failed - Invalid credentials for mobile: +923325566601
WARNING 2025-01-20 05:57:29,407 views 900929 132267714367488 Login failed - Invalid credentials for mobile: +923325566601
WARNING 2025-01-20 16:44:12,880 views 900929 132267714367488 Login failed - User does not exist: +9234074256732
WARNING 2025-01-20 16:44:19,813 views 900929 132267714367488 Login failed - User does not exist: +9234074256732
WARNING 2025-01-20 16:44:25,801 views 900931 132267714367488 Login failed - User does not exist: +9234074256732
WARNING 2025-01-24 02:55:20,442 views 900930 132267714367488 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-24 05:38:00,222 views 900931 132267714367488 Login failed - User does not exist: +92347425673
WARNING 2025-01-24 15:26:20,249 views 900929 132267714367488 Login failed - User does not exist: +928077541407
WARNING 2025-01-26 18:06:32,175 views 900930 132267714367488 Login failed - User does not exist: +926006416628
WARNING 2025-01-27 12:08:58,049 views 1067902 137243529887744 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-27 12:10:14,336 views 1067901 137243529887744 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-01-29 01:46:55,283 views 1067902 137243529887744 Login failed - User does not exist: +92347425673
WARNING 2025-01-29 15:06:56,865 views 1067902 137243529887744 Login failed - User does not exist: +92347425673
WARNING 2025-01-30 14:32:01,359 views 1067901 137243529887744 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-02-01 05:02:36,297 views 1067902 137243529887744 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-02-03 14:16:14,813 views 1067902 137243529887744 Login failed - User does not exist: +923407125673
WARNING 2025-02-05 03:44:20,500 views 1067901 137243529887744 Login failed - User does not exist: +923007792957
WARNING 2025-02-05 03:44:38,062 views 1067902 137243529887744 Login failed - User does not exist: +923007792957
WARNING 2025-02-05 03:44:59,095 views 1067901 137243529887744 Login failed - User does not exist: +923007792957
WARNING 2025-02-05 03:45:11,180 views 1067903 137243529887744 Login failed - User does not exist: +923007792957
WARNING 2025-02-05 15:07:46,931 views 1067903 137243529887744 Login failed - User does not exist: +923016509484
WARNING 2025-02-06 02:12:26,991 views 1067902 137243529887744 Login failed - User does not exist: +923007792957
WARNING 2025-02-08 02:49:31,968 views 1174906 128434054508544 Login failed - User does not exist: +929879772350
WARNING 2025-02-08 02:49:37,999 views 1174906 128434054508544 Login failed - User does not exist: +929879772350
WARNING 2025-02-08 04:56:30,780 views 1174907 128434054508544 Login failed - User does not exist: +926006039151
WARNING 2025-02-08 05:37:42,974 views 1174908 128434054508544 Login failed - User does not exist: +929617510860
WARNING 2025-02-08 13:39:38,741 views 1174908 128434054508544 Login failed - User does not exist: +923131668112
WARNING 2025-02-08 13:39:45,606 views 1174907 128434054508544 Login failed - User does not exist: +923131668112
WARNING 2025-02-08 13:42:45,991 views 1174907 128434054508544 Login failed - User does not exist: +923131668112
WARNING 2025-02-08 13:56:19,915 views 1174908 128434054508544 Login failed - User does not exist: +929646035797
WARNING 2025-02-08 13:56:21,746 views 1174906 128434054508544 Login failed - User does not exist: +929646035797
WARNING 2025-02-08 16:02:54,510 views 1174906 128434054508544 Login failed - User does not exist: +926006345800
WARNING 2025-02-08 16:03:00,246 views 1174908 128434054508544 Login failed - User does not exist: +926006345800
WARNING 2025-02-08 16:23:14,107 views 1174906 128434054508544 Login failed - User does not exist: +929149499489
WARNING 2025-02-08 16:23:18,598 views 1174907 128434054508544 Login failed - User does not exist: +929149499489
WARNING 2025-02-08 16:26:55,899 views 1174908 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-08 16:27:03,214 views 1174907 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-08 16:27:17,834 views 1174907 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-08 16:27:37,085 views 1174908 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-08 16:28:07,117 views 1174906 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-09 03:09:15,037 views 1174908 128434054508544 Login failed - User does not exist: +928050977447
WARNING 2025-02-09 03:09:41,140 views 1174908 128434054508544 Login failed - User does not exist: +928050977447
WARNING 2025-02-09 03:22:11,926 views 1174908 128434054508544 Login failed - User does not exist: +928050977447
WARNING 2025-02-09 03:22:36,786 views 1174908 128434054508544 Login failed - User does not exist: +928050977447
WARNING 2025-02-09 03:24:25,701 views 1174906 128434054508544 Login failed - User does not exist: +929149628569
WARNING 2025-02-09 05:00:12,504 views 1174907 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-09 05:00:38,535 views 1174906 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-09 05:01:25,983 views 1174907 128434054508544 Login failed - User does not exist: +929714970677
WARNING 2025-02-09 06:06:09,537 views 1174906 128434054508544 Login failed - User does not exist: +929079622606
WARNING 2025-02-09 07:22:25,237 views 1174906 128434054508544 Login failed for mobile: +923037225755 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-09 07:22:32,128 views 1174907 128434054508544 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-02-09 08:04:10,694 views 1174906 128434054508544 Login failed - User does not exist: +3037225755
WARNING 2025-02-09 08:52:46,419 views 1174908 128434054508544 Login failed - User does not exist: +929855389222
WARNING 2025-02-09 09:07:18,876 views 1174907 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:21,460 views 1174906 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:22,848 views 1174907 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:26,364 views 1174906 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:31,869 views 1174906 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:36,436 views 1174906 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:38,291 views 1174906 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:07:50,104 views 1174907 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:36:19,637 views 1174907 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:36:22,998 views 1174908 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:36:25,312 views 1174908 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:36:26,935 views 1174908 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:36:29,079 views 1174908 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 09:36:30,697 views 1174907 128434054508544 Login failed - User does not exist: +927016726036
WARNING 2025-02-09 10:25:19,657 views 1174908 128434054508544 Login failed - User does not exist: +928872112543
WARNING 2025-02-09 10:25:23,380 views 1174906 128434054508544 Login failed - User does not exist: +928872112543
WARNING 2025-02-09 10:25:31,869 views 1174906 128434054508544 Login failed - User does not exist: +928872112543
WARNING 2025-02-09 10:25:41,016 views 1174908 128434054508544 Login failed - User does not exist: +928872112543
WARNING 2025-02-09 10:26:41,857 views 1174908 128434054508544 Login failed - User does not exist: +928872112543
WARNING 2025-02-09 10:52:37,654 views 1174908 128434054508544 Login failed - User does not exist: +927988508103
WARNING 2025-02-09 13:36:54,771 views 1174908 128434054508544 Login failed - User does not exist: +927526903747
WARNING 2025-02-09 13:37:05,322 views 1174908 128434054508544 Login failed - User does not exist: +927526903747
WARNING 2025-02-09 13:37:31,292 views 1174908 128434054508544 Login failed - User does not exist: +927526903747
WARNING 2025-02-09 13:37:39,245 views 1174906 128434054508544 Login failed - User does not exist: +927526903747
WARNING 2025-02-09 13:48:47,118 views 1174908 128434054508544 Login failed - User does not exist: +927698999928
WARNING 2025-02-09 13:48:55,043 views 1174908 128434054508544 Login failed - User does not exist: +927698999928
WARNING 2025-02-09 13:49:18,801 views 1174908 128434054508544 Login failed - User does not exist: +927698999928
WARNING 2025-02-09 13:49:28,142 views 1174907 128434054508544 Login failed - User does not exist: +927698999928
WARNING 2025-02-09 14:06:23,051 views 1174908 128434054508544 Login failed - User does not exist: +927889515580
WARNING 2025-02-09 14:06:29,570 views 1174907 128434054508544 Login failed - User does not exist: +927889515580
WARNING 2025-02-09 14:06:34,504 views 1174907 128434054508544 Login failed - User does not exist: +927889515580
WARNING 2025-02-09 14:12:26,979 views 1174908 128434054508544 Login failed - Invalid credentials for mobile: +923037225755
WARNING 2025-02-09 16:10:24,049 views 1174907 128434054508544 Login failed - User does not exist: +929106968933
WARNING 2025-02-09 16:10:27,608 views 1174906 128434054508544 Login failed - User does not exist: +929106968933
WARNING 2025-02-09 16:10:29,772 views 1174906 128434054508544 Login failed - User does not exist: +929106968933
WARNING 2025-02-09 16:55:55,897 views 1174907 128434054508544 Login failed - User does not exist: +926280762879
WARNING 2025-02-09 16:56:00,121 views 1174908 128434054508544 Login failed - User does not exist: +926280762879
WARNING 2025-02-09 16:56:28,023 views 1174908 128434054508544 Login failed - User does not exist: +926280762879
WARNING 2025-02-09 16:56:51,084 views 1174907 128434054508544 Login failed - User does not exist: +926280762879
WARNING 2025-02-09 16:57:19,154 views 1174906 128434054508544 Login failed - User does not exist: +926280762879
WARNING 2025-02-10 00:33:11,582 views 1174906 128434054508544 Login failed - User does not exist: +929463560515
WARNING 2025-02-10 00:33:35,854 views 1174908 128434054508544 Login failed - User does not exist: +929463560515
WARNING 2025-02-10 00:33:46,236 views 1174907 128434054508544 Login failed - User does not exist: +929463560515
WARNING 2025-02-10 01:36:50,945 views 1174906 128434054508544 Login failed - User does not exist: +929420103081
WARNING 2025-02-10 01:36:52,909 views 1174906 128434054508544 Login failed - User does not exist: +929420103081
WARNING 2025-02-10 01:36:56,375 views 1174906 128434054508544 Login failed - User does not exist: +929420103081
WARNING 2025-02-10 01:37:34,244 views 1174906 128434054508544 Login failed - User does not exist: +929420103081
WARNING 2025-02-10 02:02:12,767 views 1174908 128434054508544 Login failed - User does not exist: +929906514898
WARNING 2025-02-10 04:22:14,704 views 1174908 128434054508544 Login failed - User does not exist: +929049232980
WARNING 2025-02-10 04:28:08,480 views 1174907 128434054508544 Login failed - User does not exist: +929113493507
WARNING 2025-02-10 04:28:18,930 views 1174906 128434054508544 Login failed - User does not exist: +929113493507
WARNING 2025-02-10 04:29:29,796 views 1174906 128434054508544 Login failed - User does not exist: +929113493507
WARNING 2025-02-10 04:51:18,297 views 1174906 128434054508544 Login failed for mobile: +923037225755 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-10 04:51:20,235 views 1174908 128434054508544 Login failed for mobile: +923037225755 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-10 04:51:26,541 views 1174907 128434054508544 Login failed for mobile: +923037225755 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-10 04:52:11,237 views 1174908 128434054508544 Login failed - User does not exist: +923037225755
WARNING 2025-02-10 04:52:17,476 views 1174907 128434054508544 Login failed - User does not exist: +923037225755
WARNING 2025-02-10 05:38:56,362 views 1174906 128434054508544 Login failed - User does not exist: +929464568882
WARNING 2025-02-10 09:26:14,789 views 1199400 139200280829952 Login failed - User does not exist: +926280405737
WARNING 2025-02-10 09:28:19,572 views 1199402 139200280829952 Login failed for mobile: 9814793912 - <ul class="errorlist"><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-02-10 09:46:37,213 views 1199400 139200280829952 Login failed - User does not exist: +929855745772
WARNING 2025-02-10 10:33:46,095 views 1199400 139200280829952 Login failed - User does not exist: +92966592350186
WARNING 2025-02-10 10:34:07,049 views 1199402 139200280829952 Login failed - User does not exist: +92966592350186
WARNING 2025-02-10 12:05:40,915 views 1199402 139200280829952 Login failed - User does not exist: +926005740127
WARNING 2025-02-10 12:05:41,894 views 1199402 139200280829952 Login failed - User does not exist: +927863803356
WARNING 2025-02-10 12:05:48,486 views 1199402 139200280829952 Login failed - User does not exist: +927863803356
WARNING 2025-02-10 12:05:55,854 views 1199402 139200280829952 Login failed - User does not exist: +927863803356
WARNING 2025-02-10 12:06:05,756 views 1199400 139200280829952 Login failed - User does not exist: +927863803356
WARNING 2025-02-10 12:06:08,740 views 1199400 139200280829952 Login failed - User does not exist: +926005740127
WARNING 2025-02-10 12:06:13,435 views 1199401 139200280829952 Login failed - User does not exist: +927863803356
WARNING 2025-02-10 12:06:57,487 views 1199401 139200280829952 Login failed - User does not exist: +927863803356
WARNING 2025-02-10 13:52:48,668 views 1199400 139200280829952 Login failed - User does not exist: +928094048094
WARNING 2025-02-10 13:52:51,794 views 1199402 139200280829952 Login failed - User does not exist: +928094048094
WARNING 2025-02-10 13:58:57,906 views 1199400 139200280829952 Login failed - User does not exist: +927005964193
WARNING 2025-02-10 14:04:41,635 views 1199401 139200280829952 Login failed - User does not exist: +927006848802
WARNING 2025-02-10 14:12:03,353 views 1199401 139200280829952 Login failed - User does not exist: +929915032890
WARNING 2025-02-10 14:12:14,916 views 1199402 139200280829952 Login failed - User does not exist: +929915032890
WARNING 2025-02-10 14:12:18,922 views 1199401 139200280829952 Login failed - User does not exist: +929915032890
WARNING 2025-02-10 14:12:26,152 views 1199400 139200280829952 Login failed - User does not exist: +929915032890
WARNING 2025-02-10 14:12:37,231 views 1199402 139200280829952 Login failed - User does not exist: +929915032890
WARNING 2025-02-10 15:01:50,509 views 1199401 139200280829952 Login failed - User does not exist: +923037225752
WARNING 2025-02-10 15:01:58,454 views 1199401 139200280829952 Login failed - User does not exist: +923037225752
WARNING 2025-02-10 17:07:47,005 views 1199400 139200280829952 Login failed - User does not exist: +929906400300
WARNING 2025-02-11 00:36:28,388 views 1211593 *************** Login failed - User does not exist: +929915921464
WARNING 2025-02-11 00:36:44,419 views 1211595 *************** Login failed - User does not exist: +929915921464
WARNING 2025-02-11 01:17:44,163 views 1211594 *************** Login failed - User does not exist: +**************
WARNING 2025-02-11 01:18:00,475 views 1211594 *************** Login failed - User does not exist: +**************
WARNING 2025-02-11 01:18:04,723 views 1211593 *************** Login failed - User does not exist: +**************
WARNING 2025-02-11 01:18:08,556 views 1211593 *************** Login failed - User does not exist: +**************
WARNING 2025-02-11 01:18:11,956 views 1211595 *************** Login failed - User does not exist: +**************
ERROR 2025-02-11 03:39:14,241 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:14,242 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (16, 2025-02-11 03:39:14.240575+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:21,988 views 1211593 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:21,991 views 1211593 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (17, 2025-02-11 03:39:21.987749+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:32,248 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:39:32,251 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (18, 2025-02-11 03:39:32.247585+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:40:02,713 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:40:02,714 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (19, 2025-02-11 03:40:02.712256+00, null, Dart/3.5 (dart:io), 242, null).

ERROR 2025-02-11 03:51:58,785 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:51:58,786 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (20, 2025-02-11 03:51:58.784035+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:01,349 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:01,350 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (21, 2025-02-11 03:52:01.348095+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:28,391 views 1211595 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:28,392 views 1211595 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (22, 2025-02-11 03:52:28.389923+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:31,488 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:31,489 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (23, 2025-02-11 03:52:31.487549+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:38,622 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:38,623 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (24, 2025-02-11 03:52:38.621751+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:40,463 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).

ERROR 2025-02-11 03:52:40,463 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (25, 2025-02-11 03:52:40.462145+00, null, Dart/3.5 (dart:io), 243, null).

WARNING 2025-02-11 04:06:31,402 views 1211594 *************** Login failed - Invalid credentials for mobile: +************
ERROR 2025-02-11 04:06:40,462 views 1211594 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:06:40,463 views 1211594 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (26, 2025-02-11 04:06:40.461744+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:06:49,342 views 1211593 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:06:49,342 views 1211593 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (27, 2025-02-11 04:06:49.340783+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:07:16,458 views 1211593 *************** Error checking user existence: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).

ERROR 2025-02-11 04:07:16,458 views 1211593 *************** Unexpected error during login: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.NotNullViolation: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 153, in post
    # Track login
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.IntegrityError: null value in column "login_type" of relation "accounts_userlogin" violates not-null constraint
DETAIL:  Failing row contains (28, 2025-02-11 04:07:16.456899+00, null, Dart/3.5 (dart:io), 244, null).

WARNING 2025-02-11 05:09:17,756 views 1223306 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 05:09:20,146 views 1223303 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 05:09:24,218 views 1223306 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 05:09:26,068 views 1223303 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:10:19,815 views 1223303 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:10:37,138 views 1223306 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:21:32,717 views 1223306 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:24:51,929 views 1223303 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:24:58,757 views 1223329 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:25:50,122 views 1223329 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 11:25:53,788 views 1223306 *************** Login failed - User does not exist: +************
WARNING 2025-02-11 12:33:36,979 views 1223329 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-02-11 14:04:40,912 views 1223303 *************** Login failed for mobile: +923037225755 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-11 16:12:52,029 views 1223329 *************** Login failed - User does not exist: +929877369650
WARNING 2025-02-11 19:22:07,268 views 1223329 *************** Login failed - User does not exist: +929780103278
WARNING 2025-02-11 23:01:22,230 views 1223329 *************** Login failed - User does not exist: +929646508660
WARNING 2025-02-11 23:01:37,579 views 1223306 *************** Login failed - User does not exist: +929646508660
WARNING 2025-02-12 00:52:54,661 views 1223329 *************** Login failed - User does not exist: +927015863496
WARNING 2025-02-12 02:50:25,668 views 1223306 *************** Login failed - User does not exist: +928699150451
WARNING 2025-02-12 03:09:14,828 views 1223303 *************** Login failed - User does not exist: +928007041443
WARNING 2025-02-12 03:09:23,069 views 1223306 *************** Login failed - User does not exist: +928007041443
WARNING 2025-02-12 03:09:32,236 views 1223329 *************** Login failed - User does not exist: +928007041443
WARNING 2025-02-12 08:31:55,332 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:31:57,631 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:01,813 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:06,210 views 1223306 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:10,144 views 1223306 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:12,579 views 1223306 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:13,597 views 1223306 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:18,987 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:22,755 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:30,265 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:31,363 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:35,247 views 1223303 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:39,515 views 1223306 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 08:32:41,229 views 1223306 *************** Login failed - User does not exist: +927016726036
WARNING 2025-02-12 10:10:01,862 views 1223329 *************** Login failed - User does not exist: +929956740009
WARNING 2025-02-12 14:13:14,345 views 1223329 *************** Login failed - User does not exist: +929915032890
WARNING 2025-02-12 14:13:26,501 views 1223306 *************** Login failed - User does not exist: +929915032890
WARNING 2025-02-12 14:13:40,095 views 1223306 *************** Login failed - User does not exist: +929915032890
WARNING 2025-02-12 14:13:43,820 views 1223306 *************** Login failed - User does not exist: +929915032890
WARNING 2025-02-13 03:28:44,164 views 1223329 *************** Login failed - User does not exist: +926005868203
WARNING 2025-02-13 03:28:47,093 views 1223303 *************** Login failed - User does not exist: +926005868203
WARNING 2025-02-13 03:29:06,066 views 1223303 *************** Login failed - User does not exist: +926005868203
WARNING 2025-02-13 05:46:21,792 views 1223303 *************** Login failed - User does not exist: +926376526668
WARNING 2025-02-13 06:15:21,737 views 1223329 *************** Login failed - User does not exist: +929596472201
WARNING 2025-02-13 14:19:04,807 views 1223306 *************** Login failed - User does not exist: +929872336496
WARNING 2025-02-13 14:19:10,477 views 1223329 *************** Login failed - User does not exist: +929872336496
WARNING 2025-02-13 14:19:25,873 views 1223329 *************** Login failed - User does not exist: +929872336496
WARNING 2025-02-13 14:20:06,834 views 1223329 *************** Login failed - User does not exist: +929872336496
WARNING 2025-02-13 14:20:34,853 views 1223306 *************** Login failed - User does not exist: +929872336496
WARNING 2025-02-13 14:20:40,399 views 1223303 *************** Login failed - User does not exist: +929872336496
ERROR 2025-02-13 14:22:25,219 views 1223329 *************** Error checking user existence: could not extend file "base/16384/16839": No space left on device
HINT:  Check free disk space.
Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 581, in get_or_create
    return self.get(**kwargs), False
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 435, in get
    raise self.model.DoesNotExist(
rest_framework.authtoken.models.Token.DoesNotExist: Token matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
psycopg2.errors.DiskFull: could not extend file "base/16384/16839": No space left on device
HINT:  Check free disk space.


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/channab/channab4/channab/accounts/views.py", line 150, in post
    token, created = Token.objects.get_or_create(user=user)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 588, in get_or_create
    return self.create(**params), True
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 453, in create
    obj.save(force_insert=True, using=self.db)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/rest_framework/authtoken/models.py", line 33, in save
    return super().save(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 739, in save
    self.save_base(using=using, force_insert=force_insert,
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 776, in save_base
    updated = self._save_table(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 881, in _save_table
    results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/base.py", line 919, in _do_insert
    return manager._insert(
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/query.py", line 1270, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/models/sql/compiler.py", line 1416, in execute_sql
    cursor.execute(sql,WARNING 2025-02-13 14:31:41,760 views 1223306 *************** Login failed - User does not exist: +92701101981
WARNING 2025-02-13 14:31:44,416 views 1223306 *************** Login failed - User does not exist: +92701101981
WARNING 2025-02-13 14:31:46,074 views 1223306 *************** Login failed - User does not exist: +92701101981
 params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 98, in execute
    return super().execute(sql, params)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 79, in _execute
    with self.db.wrap_database_errors:
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home/<USER>/channab/channabenv/lib/python3.10/site-packages/django/db/backends/utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
django.db.utils.OperationalError: could not extend file "base/16384/16839": No space left on device
HINT:  Check free disk space.

WARNING 2025-02-14 01:38:29,024 views 30004 128994364375040 Login failed - User does not exist: +929686673394
WARNING 2025-02-14 01:38:47,347 views 30005 128994364375040 Login failed - User does not exist: +929686673394
WARNING 2025-02-14 01:38:57,186 views 30006 128994364375040 Login failed - User does not exist: +929686673394
WARNING 2025-02-14 08:19:57,161 views 30004 128994364375040 Login failed - User does not exist: +929510733233
WARNING 2025-02-14 08:19:59,365 views 30004 128994364375040 Login failed - User does not exist: +929510733233
WARNING 2025-02-14 08:20:02,664 views 30004 128994364375040 Login failed - User does not exist: +929510733233
WARNING 2025-02-14 08:20:06,673 views 30004 128994364375040 Login failed - User does not exist: +929510733233
WARNING 2025-02-14 08:20:43,269 views 30004 128994364375040 Login failed - User does not exist: +929510733233
WARNING 2025-02-14 12:24:04,692 views 30004 128994364375040 Login failed - User does not exist: +927051209596
WARNING 2025-02-14 12:24:26,035 views 30006 128994364375040 Login failed - User does not exist: +927051209596
WARNING 2025-02-14 13:26:12,895 views 49342 140293951291392 Login failed - User does not exist: +928872687292
WARNING 2025-02-15 10:32:12,036 views 49342 140293951291392 Login failed - User does not exist: +929665909648
WARNING 2025-02-15 10:32:25,091 views 49342 140293951291392 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 10:34:39,659 views 49342 140293951291392 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 10:35:27,302 views 56311 125819512948288 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 10:53:45,421 views 56531 136503934060096 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 10:59:03,738 views 56531 136503934060096 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 10:59:21,693 views 56531 136503934060096 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 11:01:17,015 views 56531 136503934060096 Login failed - User does not exist: +92590964890
WARNING 2025-02-15 11:02:31,350 views 56531 136503934060096 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-02-15 11:07:50,098 views 49341 140293951291392 Login failed - User does not exist: +92590964893
WARNING 2025-02-15 11:08:00,788 views 49345 140293951291392 Login failed - User does not exist: +92590964893
WARNING 2025-02-15 11:08:17,813 views 49345 140293951291392 Login failed - User does not exist: +92590964893
WARNING 2025-02-15 11:08:59,659 views 49345 140293951291392 Login failed - User does not exist: +92590964893
WARNING 2025-02-15 11:21:00,590 views 56531 136503934060096 Login failed - User does not exist: +966590964895
WARNING 2025-02-15 17:45:37,933 views 77319 127166430765056 Login failed for mobile: +923478181583 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-17 07:30:20,493 views 5601 130500453502976 Login failed for mobile: 03407425673 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-02-17 10:48:35,088 views 5602 130500453502976 Login failed - User does not exist: +929730767299
WARNING 2025-02-17 10:48:47,823 views 5603 130500453502976 Login failed - User does not exist: +929730767299
WARNING 2025-02-17 10:49:06,026 views 5602 130500453502976 Login failed - User does not exist: +929730767299
WARNING 2025-02-17 10:49:09,895 views 5602 130500453502976 Login failed - User does not exist: +929730767299
WARNING 2025-02-17 10:49:11,301 views 5602 130500453502976 Login failed - User does not exist: +929730767299
WARNING 2025-02-18 11:14:24,392 views 5603 130500453502976 Login failed - User does not exist: +928872112543
WARNING 2025-02-18 11:14:30,645 views 5603 130500453502976 Login failed - User does not exist: +928872112543
WARNING 2025-02-19 16:34:40,111 views 5602 130500453502976 Login failed - User does not exist: +9276520699
WARNING 2025-02-19 16:34:49,753 views 5602 130500453502976 Login failed - User does not exist: +9276520699
WARNING 2025-02-21 02:35:14,214 views 45962 136585054396416 Login failed - User does not exist: +928888046467
WARNING 2025-02-21 02:35:22,297 views 45963 136585054396416 Login failed - User does not exist: +928888046467
WARNING 2025-02-21 02:35:32,053 views 45962 136585054396416 Login failed - User does not exist: +928888046467
WARNING 2025-02-22 18:07:21,618 views 59279 124020302794752 Login failed - User does not exist: +927841081057
WARNING 2025-02-22 18:07:26,411 views 59279 124020302794752 Login failed - User does not exist: +927841081057
WARNING 2025-02-22 18:07:31,155 views 59279 124020302794752 Login failed - User does not exist: +927841081057
WARNING 2025-02-24 14:47:31,813 views 59273 124020302794752 Login failed - User does not exist: +923075569981
WARNING 2025-02-24 14:47:38,220 views 59279 124020302794752 Login failed - User does not exist: +923075569981
WARNING 2025-02-26 07:26:16,600 views 59279 124020302794752 Login failed - User does not exist: +************
WARNING 2025-02-26 07:26:19,132 views 59279 124020302794752 Login failed - User does not exist: +************
WARNING 2025-03-01 04:14:54,241 views 111587 133733821161472 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-01 04:14:55,315 views 111586 133733821161472 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-02 02:22:31,196 views 177704 135695989653504 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-03-02 04:15:37,541 views 177703 135695989653504 Login failed for mobile: 9860167967 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-03 15:17:57,012 views 177704 135695989653504 Login failed - User does not exist: +929503415344
WARNING 2025-03-03 15:18:11,567 views 177703 135695989653504 Login failed - User does not exist: +929503415344
WARNING 2025-03-03 18:10:50,914 views 177704 135695989653504 Login failed - User does not exist: +923476777444
WARNING 2025-03-03 18:35:47,122 views 177707 135695989653504 Login failed - Invalid credentials for mobile: +923476777444
WARNING 2025-03-04 03:51:09,342 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:51:16,994 views 177707 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:51:29,765 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:51:47,735 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:53:18,396 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:54:25,601 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:54:32,101 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-04 03:54:38,585 views 177704 135695989653504 Login failed - User does not exist: +923408077838
WARNING 2025-03-06 00:06:58,512 views 177707 135695989653504 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-07 04:20:11,990 views 177704 135695989653504 Login failed - User does not exist: +927417451481
WARNING 2025-03-07 10:26:07,475 views 177704 135695989653504 Login failed - User does not exist: +923176223859
WARNING 2025-03-08 04:53:13,960 views 177707 135695989653504 Login failed for mobile: 03264285726 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-08 04:53:29,575 views 177707 135695989653504 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-09 07:10:44,876 views 265205 138543586926592 Login failed - User does not exist: +923477702297
WARNING 2025-03-09 07:11:04,584 views 265205 138543586926592 Login failed - User does not exist: +923477702297
WARNING 2025-03-09 07:11:10,821 views 265202 138543586926592 Login failed - User does not exist: +923477702297
WARNING 2025-03-09 18:23:34,149 views 328221 124069043242560 Login failed - User does not exist: +************
WARNING 2025-03-09 18:23:42,622 views 328221 124069154391616 Login failed - User does not exist: +************
WARNING 2025-03-09 18:23:51,952 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:23:59,996 views 328221 124069020173888 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:09,649 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:13,783 views 328221 124069009688128 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:18,420 views 328221 124069154391616 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:28,083 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:31,063 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:36,405 views 328221 124068919510592 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:45,109 views 328221 124068909024832 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:48,512 views 328221 124068898539072 Login failed - User does not exist: +************
WARNING 2025-03-09 18:24:59,493 views 328221 124069154391616 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:02,998 views 328221 124068909024832 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:05,390 views 328221 124068898539072 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:08,457 views 328221 124069154391616 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:17,031 views 328221 124069020173888 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:19,904 views 328221 124068909024832 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:21,692 views 328221 124069020173888 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:22,231 views 328221 124069009688128 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:37,366 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:44,628 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:53,435 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:25:59,838 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:02,033 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:05,008 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:07,799 views 328221 124068898539072 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:13,646 views 328221 124069043242560 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:16,324 views 328221 124068898539072 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:22,247 views 328221 124068909024832 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:24,894 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:30,823 views 328221 124069053728320 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:33,195 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:41,422 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:49,613 views 328221 124069009688128 Login failed - User does not exist: +************
WARNING 2025-03-09 18:26:58,116 views 328221 124068999202368 Login failed - User does not exist: +************
WARNING 2025-03-09 18:27:07,604 views 328221 124068898539072 Login failed - User does not exist: +************
WARNING 2025-03-09 18:27:15,878 views 328221 124069009688128 Login failed - User does not exist: +************
WARNING 2025-03-09 19:08:14,822 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:08:31,778 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:08:47,395 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:08:54,264 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:02,153 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:05,188 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:08,360 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:17,315 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:23,504 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:25,820 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:34,441 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:41,255 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:42,706 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:50,990 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:58,298 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:09:59,438 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:07,884 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:15,197 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:16,570 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:24,928 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:32,148 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:33,077 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:41,331 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:49,467 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:54,701 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:10:57,940 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:04,015 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:06,522 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:12,542 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:15,119 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:21,329 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:29,643 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:38,165 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:11:46,503 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:12:29,791 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:12:47,191 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:13:04,872 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:13:23,034 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:13:39,931 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:02,197 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:11,290 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:19,697 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:25,121 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:33,424 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:41,677 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:50,266 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:14:57,954 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:15:03,786 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:15:12,732 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:15:21,076 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:15:26,440 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-09 19:15:34,921 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:36:58,071 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:37:15,583 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:37:32,734 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:37:50,642 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:38:08,486 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:38:25,824 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:38:48,406 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:38:53,888 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:38:59,847 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:05,704 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:14,598 views 265202 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:22,540 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:30,200 views 265203 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:38,928 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:47,550 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-10 12:39:56,276 views 265205 138543586926592 Login failed - User does not exist: +************
WARNING 2025-03-11 08:57:53,152 views 265205 138543586926592 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-11 08:57:56,347 views 265202 138543586926592 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-11 08:58:25,202 views 265203 138543586926592 Login failed for mobile: 09945073759 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 16:29:49,791 views 265202 138543586926592 Login failed - User does not exist: +923408077838
WARNING 2025-03-11 20:08:49,464 views 265205 138543586926592 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-11 20:12:58,474 views 265203 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:04,155 views 265202 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:05,568 views 265202 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:06,021 views 265202 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:06,362 views 265205 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:06,583 views 265203 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:06,826 views 265205 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:07,072 views 265203 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:07,413 views 265205 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:09,129 views 265202 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-11 20:14:09,577 views 265202 138543586926592 Login failed for mobile: 08129437463 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-12 11:47:36,425 views 265202 138543586926592 Login failed - User does not exist: +920342641126
WARNING 2025-03-12 11:47:53,053 views 265203 138543586926592 Login failed - User does not exist: +920342641126
WARNING 2025-03-12 11:48:01,505 views 265202 138543586926592 Login failed - User does not exist: +920342641126
WARNING 2025-03-13 11:16:10,958 views 265202 138543586926592 Login failed - Invalid credentials for mobile: +923067692519
WARNING 2025-03-13 13:15:21,397 views 265205 138543586926592 Login failed - User does not exist: +923067695819
WARNING 2025-03-13 13:15:24,059 views 265202 138543586926592 Login failed - User does not exist: +923067695819
WARNING 2025-03-13 13:15:29,810 views 265203 138543586926592 Login failed - User does not exist: +923067695819
WARNING 2025-03-13 17:17:32,123 views 265205 138543586926592 Login failed - User does not exist: +920342043466
WARNING 2025-03-13 17:17:36,965 views 265203 138543586926592 Login failed - User does not exist: +920342043466
WARNING 2025-03-14 10:51:27,041 views 403255 127609891041280 Login failed - User does not exist: +920303722575
WARNING 2025-03-15 20:00:38,903 views 403253 127609891041280 Login failed - User does not exist: +8800195932988
WARNING 2025-03-15 20:00:46,482 views 403254 127609891041280 Login failed - User does not exist: +8800195932988
WARNING 2025-03-15 20:00:53,542 views 403255 127609891041280 Login failed - User does not exist: +8800195932988
WARNING 2025-03-15 20:01:00,648 views 403255 127609891041280 Login failed - User does not exist: +8800195932988
WARNING 2025-03-15 20:01:14,580 views 403255 127609891041280 Login failed - User does not exist: +8800195932988
WARNING 2025-03-15 20:01:44,751 views 403253 127609891041280 Login failed - User does not exist: +8800195932988
WARNING 2025-03-15 20:03:22,668 views 403255 127609891041280 Login failed - User does not exist: +920195932988
WARNING 2025-03-15 20:03:29,190 views 403254 127609891041280 Login failed - User does not exist: +920195932988
WARNING 2025-03-15 20:04:03,098 views 403254 127609891041280 Login failed - User does not exist: +920195932988
WARNING 2025-03-15 20:06:58,970 views 403254 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-15 20:07:02,344 views 403254 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-15 20:07:04,380 views 403253 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-15 21:07:40,881 views 403253 127609891041280 Login failed for mobile: 09264953825 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-16 02:25:04,382 views 403253 127609891041280 Login failed for mobile: 09671905913 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-16 02:25:25,003 views 403253 127609891041280 Login failed for mobile: 09671905913 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-16 02:25:29,373 views 403255 127609891041280 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-16 02:25:31,453 views 403255 127609891041280 Login failed for mobile:  - <ul class="errorlist"><li>username<ul class="errorlist"><li>This field is required.</li></ul></li><li>password<ul class="errorlist"><li>This field is required.</li></ul></li></ul>
WARNING 2025-03-16 02:25:52,761 views 403255 127609891041280 Login failed for mobile: 09671905913 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-16 06:23:37,557 views 403253 127609891041280 Login failed - User does not exist: +920337225755
WARNING 2025-03-16 06:23:44,281 views 403254 127609891041280 Login failed - User does not exist: +920337225755
WARNING 2025-03-16 06:37:06,490 views 403254 127609891041280 Login failed - User does not exist: +920337225755
WARNING 2025-03-16 06:37:10,382 views 403253 127609891041280 Login failed - User does not exist: +920337225755
WARNING 2025-03-17 09:10:39,305 views 403255 127609891041280 Login failed - User does not exist: +920310499070
WARNING 2025-03-17 09:10:46,129 views 403253 127609891041280 Login failed - User does not exist: +920310499070
WARNING 2025-03-17 10:33:24,672 views 403254 127609891041280 Login failed - User does not exist: +92912272439
WARNING 2025-03-17 15:06:51,125 views 403253 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-17 15:06:59,486 views 403255 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-18 06:52:43,825 views 403254 127609891041280 Login failed - User does not exist: +920306599352
WARNING 2025-03-18 06:52:56,255 views 403255 127609891041280 Login failed - User does not exist: +920306599352
WARNING 2025-03-18 06:53:14,417 views 403254 127609891041280 Login failed - User does not exist: +920306599352
WARNING 2025-03-18 06:53:32,509 views 403255 127609891041280 Login failed - User does not exist: +920306599352
WARNING 2025-03-18 06:53:37,903 views 403254 127609891041280 Login failed - User does not exist: +920306599352
WARNING 2025-03-18 06:58:39,312 views 403255 127609891041280 Login failed - User does not exist: +920306599352
WARNING 2025-03-18 07:00:05,052 views 403253 127609891041280 Login failed - User does not exist: +923065993529
WARNING 2025-03-19 22:44:31,125 views 403253 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-19 22:44:38,452 views 403255 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-19 22:44:41,586 views 403255 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-19 22:44:45,078 views 403255 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-19 22:44:47,580 views 403255 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-19 22:44:58,265 views 403254 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-20 15:09:26,750 views 403253 127609891041280 Login failed - User does not exist: +920337225755
WARNING 2025-03-20 15:09:32,160 views 403255 127609891041280 Login failed - User does not exist: +920337225755
WARNING 2025-03-20 16:07:25,864 views 403255 127609891041280 Login failed for mobile: 03484124508 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-21 04:41:03,626 views 403254 127609891041280 Login failed - User does not exist: +923016996926
WARNING 2025-03-21 04:42:24,702 views 403254 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-21 05:03:46,555 views 403255 127609891041280 Login failed - User does not exist: +923434884841
WARNING 2025-03-21 05:18:28,412 views 403255 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-21 05:18:31,070 views 403253 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-21 05:18:44,439 views 403254 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-21 05:18:48,224 views 403255 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-21 05:18:52,386 views 403255 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-21 05:19:39,670 views 403254 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-21 05:19:45,027 views 403254 127609891041280 Login failed - User does not exist: +929915032890
WARNING 2025-03-22 08:11:38,329 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:11:55,135 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:12:11,852 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:12:28,691 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:12:50,060 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:12:58,642 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:13:07,142 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:13:15,788 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:13:23,937 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:13:32,025 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:14:10,244 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 08:14:19,116 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-22 15:41:57,644 views 403254 127609891041280 Login failed - User does not exist: +929413225873
WARNING 2025-03-22 15:42:02,044 views 403255 127609891041280 Login failed - User does not exist: +929413225873
WARNING 2025-03-22 15:42:11,964 views 403253 127609891041280 Login failed - User does not exist: +929413225873
WARNING 2025-03-22 15:43:27,136 views 403254 127609891041280 Login failed - User does not exist: +929413225873
WARNING 2025-03-22 19:26:39,673 views 403254 127609891041280 Login failed - User does not exist: +923032224810
WARNING 2025-03-22 19:26:44,226 views 403255 127609891041280 Login failed - User does not exist: +923032224810
WARNING 2025-03-22 19:26:46,711 views 403255 127609891041280 Login failed - User does not exist: +923032224810
WARNING 2025-03-22 19:26:49,720 views 403253 127609891041280 Login failed - User does not exist: +923032224810
WARNING 2025-03-22 19:26:54,727 views 403253 127609891041280 Login failed - User does not exist: +923032224810
WARNING 2025-03-22 19:26:55,865 views 403254 127609891041280 Login failed - User does not exist: +923032224810
WARNING 2025-03-22 19:27:06,453 views 403255 127609891041280 Login failed - User does not exist: +923126004818
WARNING 2025-03-22 19:27:10,143 views 403255 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-22 19:27:16,016 views 403254 127609891041280 Login failed - User does not exist: +923126004810
WARNING 2025-03-22 19:28:14,871 views_api 403253 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-22 19:28:19,529 views_api 403255 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-22 19:28:34,703 views_api 403253 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-22 19:28:38,737 views_api 403253 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-22 19:29:03,425 views 403255 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-23 19:44:31,224 views 403253 127609891041280 Login failed - User does not exist: +923084101300
WARNING 2025-03-23 19:44:40,730 views 403255 127609891041280 Login failed - User does not exist: +923084101300
WARNING 2025-03-23 19:44:48,375 views 403254 127609891041280 Login failed - User does not exist: +923084101300
WARNING 2025-03-23 19:48:18,977 views 403253 127609891041280 Login failed - User does not exist: +967781358888
WARNING 2025-03-23 19:48:25,128 views 403254 127609891041280 Login failed - User does not exist: +967781358888
WARNING 2025-03-23 21:09:30,523 views 403255 127609891041280 Login failed - User does not exist: +359087775720
WARNING 2025-03-23 21:09:38,662 views 403253 127609891041280 Login failed - User does not exist: +359087775720
WARNING 2025-03-24 05:53:18,145 views 403253 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-24 18:42:51,885 views 403253 127609891041280 Login failed for mobile: 03219502501 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-24 18:51:40,116 views 403255 127609891041280 Login failed for mobile: 03219502501 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-03-27 03:03:37,752 views 403255 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:28:02,466 views 403253 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:28:09,156 views 403253 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:28:31,439 views 403254 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:28:39,446 views 403253 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:30:14,724 views 403255 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:30:18,739 views 403254 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:31:24,138 views 403253 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:31:30,459 views 403255 127609891041280 Login failed - User does not exist: +923254195231
WARNING 2025-03-27 05:32:18,075 views 403255 127609891041280 Login failed - User does not exist: +920325868925
WARNING 2025-03-28 05:36:23,114 views 403254 127609891041280 Login failed - User does not exist: +919021021947
WARNING 2025-03-28 06:47:18,968 views_api 403255 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-29 03:36:22,498 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-29 03:36:38,593 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-29 03:37:44,250 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-29 03:37:59,007 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-29 03:38:03,188 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:38:08,486 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:38:14,032 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:38:14,071 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-03-29 03:38:25,039 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:39:03,437 views 403253 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:39:22,067 views 403255 127609891041280 Login failed - User does not exist: +376+96659
WARNING 2025-03-29 03:39:34,296 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:39:43,015 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:40:07,656 views 403254 127609891041280 Login failed - User does not exist: +376
WARNING 2025-03-29 03:40:08,709 views 403254 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:40:11,600 views 403255 127609891041280 Login failed - User does not exist: +**************
WARNING 2025-03-29 03:40:20,122 views 403255 127609891041280 Login failed - User does not exist: +376+96659
WARNING 2025-03-29 03:40:32,527 views 403255 127609891041280 Login failed - User does not exist: +376+96659
WARNING 2025-03-29 03:40:34,125 views 403254 127609891041280 Login failed - User does not exist: +376+96659
WARNING 2025-03-29 03:40:43,868 views 403253 127609891041280 Login failed - User does not exist: +376
WARNING 2025-03-29 03:40:56,598 views 403254 127609891041280 Login failed - User does not exist: +376
WARNING 2025-03-29 03:41:02,702 views 403255 127609891041280 Login failed - User does not exist: +376
WARNING 2025-03-29 14:30:11,883 views 403255 127609891041280 Login failed - Invalid credentials for mobile: +923407425673
WARNING 2025-03-31 02:03:54,165 views_api 403254 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-31 02:25:13,337 views 403253 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-03-31 02:25:53,772 views_api 403253 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-03-31 02:30:07,343 views 403255 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-01 09:52:32,061 views 403253 127609891041280 Login failed for mobile: 03407425673 - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-04-02 00:16:17,012 views_api 403254 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-04-02 05:47:13,565 views 403254 127609891041280 Login failed - User does not exist: +923136044002
WARNING 2025-04-03 14:48:44,339 views 403253 127609891041280 Login failed - User does not exist: +923206731279
WARNING 2025-04-03 14:50:50,741 views 403254 127609891041280 Login failed - Invalid credentials for mobile: +923206731279
WARNING 2025-04-03 14:50:53,477 views 403254 127609891041280 Login failed - Invalid credentials for mobile: +923206731279
WARNING 2025-04-04 11:06:14,030 views 403253 127609891041280 Login failed - User does not exist: +923028984873
WARNING 2025-04-04 11:06:19,033 views 403255 127609891041280 Login failed - User does not exist: +923028984873
WARNING 2025-04-04 11:06:25,869 views 403254 127609891041280 Login failed - User does not exist: +923028984873
WARNING 2025-04-04 11:06:41,852 views 403254 127609891041280 Login failed - User does not exist: +923028984873
WARNING 2025-04-04 19:07:11,644 views 403255 127609891041280 Login failed - User does not exist: +920311935626
WARNING 2025-04-04 19:08:48,390 views 403253 127609891041280 Login failed - Invalid credentials for mobile: +920311935626
WARNING 2025-04-06 15:01:23,180 views 403254 127609891041280 Login failed - User does not exist: +923348323727
WARNING 2025-04-06 15:01:48,770 views 403255 127609891041280 Login failed - User does not exist: +923119356269
WARNING 2025-04-07 06:27:20,399 views 403253 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-07 06:27:43,999 views 403255 127609891041280 Login failed - User does not exist: +38602136956
WARNING 2025-04-07 06:27:53,094 views 403253 127609891041280 Login failed - User does not exist: +38602136956
WARNING 2025-04-07 06:27:58,533 views 403253 127609891041280 Login failed - User does not exist: +38602136956
WARNING 2025-04-07 06:28:12,637 views 403254 127609891041280 Login failed - User does not exist: +38602136956
WARNING 2025-04-07 06:28:17,870 views 403254 127609891041280 Login failed - User does not exist: +38602136956
WARNING 2025-04-07 06:28:18,967 views 403253 127609891041280 Login failed - User does not exist: +38602136956
WARNING 2025-04-08 12:34:59,886 views 403253 127609891041280 Login failed - User does not exist: +8800151878576
WARNING 2025-04-08 12:35:03,654 views 403253 127609891041280 Login failed - User does not exist: +8800151878576
WARNING 2025-04-08 12:35:17,198 views 403254 127609891041280 Login failed - User does not exist: +8800151878576
WARNING 2025-04-08 12:38:41,537 views 403254 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-08 12:38:44,566 views 403254 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-11 15:54:31,075 views 403253 127609891041280 Login failed - User does not exist: +918094048094
WARNING 2025-04-14 09:10:42,325 views_api 403253 127609891041280 Signup failed - Invalid phone number format: 
WARNING 2025-04-14 09:11:19,723 views 403253 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-14 09:11:25,846 views 403255 127609891041280 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-15 11:02:51,598 views 403254 127609891041280 Login failed - User does not exist: +923000353161
WARNING 2025-04-15 17:09:08,504 views 403253 127609891041280 Login failed - User does not exist: +920327138217
WARNING 2025-04-15 17:09:10,333 views 403255 127609891041280 Login failed - User does not exist: +920327138217
WARNING 2025-04-15 17:09:25,995 views 403255 127609891041280 Login failed - User does not exist: +920327138217
WARNING 2025-04-15 17:09:28,106 views 403254 127609891041280 Login failed - User does not exist: +920327138217
WARNING 2025-04-15 18:39:36,546 views 403255 127609891041280 Login failed - User does not exist: +923271382172
WARNING 2025-04-15 18:39:42,266 views 403253 127609891041280 Login failed - User does not exist: +923271382172
WARNING 2025-04-15 23:46:12,827 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:46:30,980 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:46:49,392 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:47:08,495 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:47:26,748 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:47:45,157 views 403254 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:48:04,183 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:48:23,407 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:48:41,885 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-15 23:49:00,134 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:29:22,053 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:29:39,961 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:29:57,362 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:30:15,311 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:30:33,254 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:30:50,195 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:31:08,383 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:31:26,377 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:31:43,761 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:32:06,701 views 403255 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-18 01:32:16,679 views 403253 127609891041280 Login failed - User does not exist: +************
WARNING 2025-04-19 00:58:23,921 views 8358 140496294905408 Login failed for mobile: +************ - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-04-19 00:58:34,053 views 8358 140496294905408 Login failed for mobile: +************ - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-04-19 00:59:07,606 views 8358 140496294905408 Login failed for mobile: +************ - <ul class="errorlist"><li>__all__<ul class="errorlist nonfield"><li>Please enter a correct mobile and password. Note that both fields may be case-sensitive.</li></ul></li></ul>
WARNING 2025-04-19 01:01:26,961 views 650 130839727353856 Login failed - User does not exist: +923497829270
WARNING 2025-04-19 01:02:28,108 views 16055 127446575744576 Login failed for mobile: +************ - Reason: Please enter a correct mobile and password. Note that both fields may be case-sensitive.
WARNING 2025-04-19 01:03:36,402 views 16444 132990755669568 Login failed for mobile: +************ - Reason: Please enter a correct mobile and password. Note that both fields may be case-sensitive.
WARNING 2025-04-19 01:03:36,402 views 16444 132990755669568 Login failed for mobile: +************ - Full Form Errors: {"__all__": [{"message": "Please enter a correct mobile and password. Note that both fields may be case-sensitive.", "code": "invalid_login"}]}
WARNING 2025-04-19 01:07:02,323 views 16444 132************ Login failed for mobile: +************ - Reason: Please enter a correct mobile and password. Note that both fields may be case-sensitive.
WARNING 2025-04-19 01:07:02,324 views 16444 132************ Login failed for mobile: +************ - Full Form Errors: {"__all__": [{"message": "Please enter a correct mobile and password. Note that both fields may be case-sensitive.", "code": "invalid_login"}]}
WARNING 2025-04-19 15:00:54,116 views 36435 *************** Login failed - User does not exist: +************
WARNING 2025-04-19 15:01:08,318 views 36435 *************** Login failed - User does not exist: +************
WARNING 2025-04-21 17:51:16,572 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-21 17:51:22,977 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-21 17:53:45,372 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-21 17:54:33,005 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-21 18:02:29,906 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-21 18:04:13,523 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-21 18:04:46,026 views 162942 *************** Login failed - Account expired: +************
WARNING 2025-04-23 16:10:55,599 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:26:00,311 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:26:29,496 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:27:00,297 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:27:29,833 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:27:57,478 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:28:08,595 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-26 09:28:36,356 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:13:10,191 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:13:14,774 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:13:22,670 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:13:28,237 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:13:35,309 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:13:42,957 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:14:27,914 views 131199 *************** Login failed - User does not exist: +376+96659
WARNING 2025-04-29 02:14:50,512 views 131199 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 02:14:55,564 views 131199 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 02:14:59,391 views 131199 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 02:15:36,763 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 02:15:43,308 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 02:15:43,694 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:15:46,888 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:15:54,362 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:17:10,144 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:17:12,825 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:17:19,932 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 02:17:27,258 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 04:06:56,712 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 04:07:26,527 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 04:07:56,757 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 04:08:26,746 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 04:08:54,752 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 04:09:22,804 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:19:15,092 views 131197 *************** Login failed - User does not exist: +2348037181210
WARNING 2025-04-29 11:22:56,231 views 131199 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-04-29 11:38:40,978 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:38:54,470 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:39:09,141 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:39:16,026 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:39:28,443 views 131198 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:39:41,003 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:40:17,290 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:40:23,109 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:40:25,724 views 131199 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:40:32,924 views 131197 *************** Login failed - User does not exist: +376+96659
WARNING 2025-04-29 11:41:03,198 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:09,534 views 131199 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:15,510 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:19,165 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:29,051 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:35,416 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:38,334 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:49,626 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:41:50,591 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:41:53,740 views 131197 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:41:55,918 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:42:01,229 views 131199 *************** Login failed - User does not exist: +************
WARNING 2025-04-29 11:42:02,579 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:42:06,210 views 131199 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:42:23,177 views 131198 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 11:42:37,957 views 131197 *************** Login failed - User does not exist: +**************
WARNING 2025-04-29 17:03:10,163 views 131198 *************** Login failed - User does not exist: +923278769746
WARNING 2025-04-29 17:04:44,380 views 131198 *************** Login failed - Invalid credentials for mobile: +923278769746
WARNING 2025-04-29 17:04:51,341 views 131198 *************** Login failed - Invalid credentials for mobile: +923278769746
WARNING 2025-04-29 17:04:54,275 views 131198 *************** Login failed - Invalid credentials for mobile: +923278769746
WARNING 2025-05-01 07:55:26,644 views 131199 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-01 07:55:34,718 views 131198 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-01 10:32:58,369 views_api 131199 *************** Signup failed - Invalid phone number format: 
WARNING 2025-05-01 10:34:11,250 views 131199 *************** Login failed - Invalid credentials for mobile: +923007979362
WARNING 2025-05-01 10:34:22,310 views 131199 *************** Login failed - Invalid credentials for mobile: +923007979362
WARNING 2025-05-01 10:34:43,640 views 131198 *************** Login failed - Invalid credentials for mobile: +923007979362
WARNING 2025-05-01 10:35:20,853 views 131198 *************** Login failed - Invalid credentials for mobile: +923007979362
WARNING 2025-05-01 10:35:28,392 views 131198 *************** Login failed - Invalid credentials for mobile: +923007979362
WARNING 2025-05-02 10:19:24,132 views 131199 *************** Login failed - Invalid credentials for mobile: +923026943982
WARNING 2025-05-02 10:19:41,879 views 131199 *************** Login failed - Invalid credentials for mobile: +923026943982
WARNING 2025-05-02 10:24:27,152 views 131198 *************** Login failed - Invalid credentials for mobile: +923026943982
WARNING 2025-05-02 10:24:33,308 views 131198 *************** Login failed - Invalid credentials for mobile: +923026943982
WARNING 2025-05-02 14:05:21,474 views 264325 129043942612992 Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-02 14:05:31,645 views 264325 129043942612992 Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-03 09:46:40,161 views 5884 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-05 15:27:40,452 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-05 15:29:18,685 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-05 15:29:48,925 views 5884 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-05 15:30:15,736 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-05 15:30:37,488 views 5884 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-05 15:31:29,349 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-06 05:47:37,206 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-06 05:47:43,019 views 5882 *************** Login failed - User does not exist: +************
WARNING 2025-05-06 05:47:55,641 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-06 05:48:07,822 views 5883 *************** Login failed - User does not exist: +************
WARNING 2025-05-06 05:48:16,529 views 5883 *************** Login failed - User does not exist: +************
WARNING 2025-05-06 05:48:31,365 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-06 15:28:11,124 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-06 15:28:30,066 views 5883 *************** Login failed - Account expired: +************
WARNING 2025-05-06 15:28:37,780 views 5882 *************** Login failed - Account expired: +************
WARNING 2025-05-07 03:43:56,775 views 5882 *************** Login failed - Account expired: +************
WARNING 2025-05-08 12:32:59,033 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-08 12:33:15,730 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-08 12:33:37,319 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-08 12:33:55,608 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-08 12:34:33,441 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-09 01:15:31,724 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-09 01:15:53,485 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-09 01:16:13,421 views 5882 *************** Login failed - Account expired: +************
WARNING 2025-05-09 01:18:45,051 views 5884 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-09 01:19:08,077 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-11 09:35:09,010 views 5882 *************** Login failed - User does not exist: +************
WARNING 2025-05-11 09:35:15,656 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-11 09:35:42,989 views 5882 *************** Login failed - User does not exist: +************
WARNING 2025-05-11 09:35:55,502 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-13 16:34:16,100 views 5883 *************** Login failed - User does not exist: +************
WARNING 2025-05-13 16:34:31,788 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-13 16:34:43,574 views 5883 *************** Login failed - User does not exist: +************
WARNING 2025-05-13 16:36:23,042 views_api 5882 *************** Signup failed - Invalid phone number format: 
WARNING 2025-05-13 16:37:10,662 views 5883 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-13 16:37:16,806 views 5882 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-13 16:37:38,146 views 5883 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-13 16:38:03,087 views 5884 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-13 16:49:11,921 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-13 16:49:15,480 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-13 16:53:09,541 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-13 17:16:34,110 views 5882 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-13 17:18:15,762 views 5884 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-14 09:37:47,127 views 5883 *************** Login failed - User does not exist: +************
WARNING 2025-05-14 09:38:00,541 views 5882 *************** Login failed - User does not exist: +************
WARNING 2025-05-14 09:38:32,281 views 5882 *************** Login failed - User does not exist: +************
WARNING 2025-05-14 09:38:55,508 views 5884 *************** Login failed - User does not exist: +************
WARNING 2025-05-14 09:39:22,660 views 5883 *************** Login failed - User does not exist: +************
WARNING 2025-05-14 09:39:51,534 views 5882 *************** Login failed - User does not exist: +************
WARNING 2025-05-15 09:31:57,452 views 5884 *************** Login failed - Invalid credentials for mobile: +923176334360
WARNING 2025-05-18 07:39:10,901 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-18 07:40:14,113 views 5883 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-05-19 17:23:34,006 views 5882 *************** Login failed - User does not exist: +8801324081298
WARNING 2025-05-19 17:23:54,914 views 5883 *************** Login failed - User does not exist: +8801324081298
WARNING 2025-05-21 13:24:27,491 views 5882 *************** Login failed - User does not exist: +918076429809
WARNING 2025-05-21 13:24:39,947 views 5883 *************** Login failed - User does not exist: +918076429809
WARNING 2025-05-21 13:24:50,438 views 5882 *************** Login failed - User does not exist: +918076429809
WARNING 2025-05-21 13:24:58,785 views 5883 *************** Login failed - User does not exist: +918076429809
WARNING 2025-05-23 11:01:09,228 views 240969 123138736525312 Login failed - User does not exist: +923349744525
WARNING 2025-05-23 11:01:17,267 views 240965 123138736525312 Login failed - User does not exist: +923349744525
WARNING 2025-05-25 11:08:50,876 views 318113 *************** Login failed - User does not exist: +************
WARNING 2025-05-25 11:09:03,743 views 318116 *************** Login failed - User does not exist: +************
WARNING 2025-05-25 11:09:16,839 views 318113 *************** Login failed - User does not exist: +************
WARNING 2025-05-25 11:10:24,679 views 318113 *************** Login failed - User does not exist: +244590964894
WARNING 2025-05-25 11:10:29,707 views 318116 *************** Login failed - User does not exist: +244590964894
WARNING 2025-05-25 11:10:33,256 views 318113 *************** Login failed - User does not exist: +244590964894
WARNING 2025-05-25 11:10:42,441 views 318113 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:10:48,740 views 318114 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:10:54,315 views 318113 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:10:57,541 views 318114 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:11:46,507 views 318116 *************** Login failed - User does not exist: +1684+966590
WARNING 2025-05-25 11:12:24,836 views 318116 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:12:30,927 views 318114 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:12:34,091 views 318113 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:12:42,820 views 318114 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:12:48,226 views 318114 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 11:12:51,347 views 318116 *************** Login failed - User does not exist: +213590964894
WARNING 2025-05-25 19:02:40,233 views 318116 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-25 19:02:58,470 views 318116 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-25 19:03:06,707 views 318114 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-25 19:03:35,021 views 318113 *************** Login failed - User does not exist: +*************
WARNING 2025-05-25 19:04:59,488 views 318113 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-25 19:05:05,648 views 318116 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-05-31 02:10:52,661 views 609157 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:11:25,992 views 609152 *************** Login failed - User does not exist: +************
WARNING 2025-05-31 02:11:43,882 views 609153 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:12:22,594 views 609153 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:13:34,249 views 609152 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:13:58,716 views 609153 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:26:41,310 views 609157 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:27:35,460 views 609153 *************** Login failed - User does not exist: +************
WARNING 2025-05-31 02:28:20,357 views 609153 *************** Login failed - Account expired: +************
WARNING 2025-05-31 02:31:21,007 views 609157 *************** Login failed - Account expired: +************
WARNING 2025-05-31 14:59:03,054 views 609157 *************** Login failed - Account expired: +************
WARNING 2025-05-31 14:59:45,514 views 609152 *************** Login failed - Account expired: +************
WARNING 2025-05-31 21:18:31,707 views 609153 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:41:20,733 views 609153 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:41:33,256 views 609157 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:41:45,939 views 609153 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:41:56,674 views 609157 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:42:10,353 views 609153 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:42:23,821 views 609157 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:42:38,731 views 609152 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:42:41,376 views 609152 *************** Login failed - User does not exist: +376+96659
WARNING 2025-06-02 13:43:44,548 views 609153 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:43:50,376 views 609152 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:43:52,938 views 609157 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:44:00,666 views 609157 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:44:03,959 views 609157 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:44:11,444 views 609157 *************** Login failed - User does not exist: +************
WARNING 2025-06-02 13:44:29,107 views 609152 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:44:35,775 views 609153 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:44:40,856 views 609153 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:44:43,688 views 609153 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:44:56,114 views 609153 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:45:02,635 views 609153 *************** Login failed - User does not exist: +**************
WARNING 2025-06-02 13:45:37,452 views 609152 *************** Login failed - User does not exist: +54
WARNING 2025-06-02 13:45:49,480 views 609157 *************** Login failed - User does not exist: +54
WARNING 2025-06-03 12:15:41,655 views 764915 *************** Login failed - Account expired: +************
WARNING 2025-06-07 05:44:42,171 views 608 *************** Login failed - User does not exist: +************
WARNING 2025-06-12 17:09:56,639 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-12 20:06:28,000 views 602 *************** Login failed - Invalid credentials for mobile: +************
WARNING 2025-06-13 03:52:36,964 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 03:52:42,898 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 03:52:56,083 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:16,986 views 602 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:22,657 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:26,696 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:30,398 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:36,616 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:43,189 views 602 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:50,557 views 601 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:54,713 views 602 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:35:59,139 views 602 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:36:02,340 views 599 *************** Login failed - User does not exist: +************
WARNING 2025-06-13 04:39:42,797 views 602 *************** Login failed - User does not exist: +923000068658
WARNING 2025-06-13 04:39:54,460 views 599 *************** Login failed - User does not exist: +923000068658
WARNING 2025-06-13 04:40:06,042 views 601 *************** Login failed - User does not exist: +923000068658
WARNING 2025-06-13 04:40:12,515 views 602 *************** Login failed - User does not exist: +923000068658
WARNING 2025-06-13 04:40:26,575 views 601 *************** Login failed - User does not exist: +923000275975
WARNING 2025-06-13 04:40:38,991 views 602 *************** Login failed - User does not exist: +923000068658
WARNING 2025-06-13 04:41:49,053 views 601 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 06:21:11,251 views 601 *************** Login failed - User does not exist: +923342034343
WARNING 2025-06-13 07:24:35,284 views 599 *************** Login failed - User does not exist: +920325516816
WARNING 2025-06-13 07:24:41,740 views 602 *************** Login failed - User does not exist: +920325516816
WARNING 2025-06-13 07:24:57,589 views 602 *************** Login failed - User does not exist: +920325516816
WARNING 2025-06-13 07:25:14,625 views 602 *************** Login failed - User does not exist: +920325516816
WARNING 2025-06-13 08:17:18,782 views 602 *************** Login failed - User does not exist: +923149769981
WARNING 2025-06-13 08:18:44,829 views 602 *************** Login failed - User does not exist: +923166341507
WARNING 2025-06-13 09:19:37,338 views 601 *************** Login failed - User does not exist: +923407812912
WARNING 2025-06-13 09:19:47,916 views 599 *************** Login failed - User does not exist: +923407812912
WARNING 2025-06-13 09:21:32,777 views 601 *************** Login failed - User does not exist: +923407812912
WARNING 2025-06-13 09:22:00,585 views 601 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 09:22:09,288 views 601 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 09:22:46,766 views 602 *************** Login failed - User does not exist: +923407812912
WARNING 2025-06-13 10:29:25,707 views 599 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 10:35:12,463 views 599 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 10:35:24,598 views 599 *************** Login failed - User does not exist: +923487750894
WARNING 2025-06-13 10:35:44,048 views 599 *************** Login failed - User does not exist: +923487750894
WARNING 2025-06-13 10:35:59,320 views 599 *************** Login failed - User does not exist: +923487750894
WARNING 2025-06-13 10:37:28,650 views 602 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 12:38:55,282 views 601 *************** Login failed - Invalid credentials for mobile: +923019111203
WARNING 2025-06-13 12:40:33,659 views 601 *************** Login failed - Invalid credentials for mobile: +923019111203
WARNING 2025-06-13 12:40:45,282 views 601 *************** Login failed - Invalid credentials for mobile: +923019111203
WARNING 2025-06-13 12:50:44,980 views 601 *************** Login failed - User does not exist: +923494784128
WARNING 2025-06-13 12:51:09,251 views 599 *************** Login failed - User does not exist: +923139344192
WARNING 2025-06-13 12:51:15,286 views 601 *************** Login failed - User does not exist: +923139344192
WARNING 2025-06-13 12:51:17,503 views 601 *************** Login failed - User does not exist: +923139344192
WARNING 2025-06-13 12:51:19,654 views 601 *************** Login failed - User does not exist: +923139344192
WARNING 2025-06-13 16:26:13,816 views 601 *************** Login failed - User does not exist: +923049245700
WARNING 2025-06-13 16:26:24,911 views 601 *************** Login failed - User does not exist: +923049245700
WARNING 2025-06-13 16:26:30,943 views 601 *************** Login failed - User does not exist: +923049245700
WARNING 2025-06-13 16:26:32,200 views 599 *************** Login failed - User does not exist: +923049245700
WARNING 2025-06-13 16:26:44,346 views 599 *************** Login failed - User does not exist: +923049245700
WARNING 2025-06-13 16:37:43,063 views 599 *************** Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-13 17:09:30,142 views 94323 128587891404800 Login failed - User does not exist: +923017962013
WARNING 2025-06-13 17:09:39,189 views 94326 128587891404800 Login failed - User does not exist: +923017962013
WARNING 2025-06-13 19:47:31,406 views 94323 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:49:20,081 views 94323 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:49:26,536 views 94323 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:49:53,911 views 94323 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:50:13,449 views 94322 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:50:20,217 views 94322 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:51:02,141 views 94326 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-13 19:51:21,643 views 94326 128587891404800 Login failed - User does not exist: +923327819131
WARNING 2025-06-14 00:08:38,241 views 94322 128587891404800 Login failed - User does not exist: +920301490222
WARNING 2025-06-14 00:08:47,754 views 94323 128587891404800 Login failed - User does not exist: +920301490222
WARNING 2025-06-14 00:09:56,662 views 94322 128587891404800 Login failed - User does not exist: +923016058474
WARNING 2025-06-14 00:11:20,862 views 94326 128587891404800 Login failed - Invalid credentials for mobile: +923016058474
WARNING 2025-06-14 00:16:21,273 views 94326 128587891404800 Login failed - Invalid credentials for mobile: +923016058474
WARNING 2025-06-14 01:43:35,455 views 94323 128587891404800 Login failed - User does not exist: +923436108001
WARNING 2025-06-14 01:45:04,935 views 94326 128587891404800 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-14 01:45:20,795 views 94322 128587891404800 Login failed - User does not exist: +923436108001
WARNING 2025-06-14 01:45:28,230 views 94322 128587891404800 Login failed - User does not exist: +923436108001
WARNING 2025-06-14 01:47:24,777 views 94326 128587891404800 Login failed - Invalid credentials for mobile: +923436108001
WARNING 2025-06-14 02:12:18,164 views 94326 128587891404800 Login failed - User does not exist: +923006243334
WARNING 2025-06-14 02:22:21,318 views 94322 128587891404800 Login failed - User does not exist: +923018144478
WARNING 2025-06-14 04:12:16,112 views 94323 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:12:32,125 views 94323 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:12:50,934 views 94323 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:13:09,833 views 94323 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:13:29,846 views 94326 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:13:46,873 views 94326 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:14:04,068 views 94322 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:14:25,493 views 94322 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:14:44,030 views 94323 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 04:15:00,422 views 94322 128587891404800 Login failed - User does not exist: +************
WARNING 2025-06-14 05:04:13,359 views 94326 128587891404800 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-14 05:04:21,437 views 94326 128587891404800 Login failed - User does not exist: +923490420140
WARNING 2025-06-14 05:04:27,153 views 94326 128587891404800 Login failed - User does not exist: +923490420140
WARNING 2025-06-14 05:04:36,504 views 94323 128587891404800 Login failed - User does not exist: +923490420140
WARNING 2025-06-14 05:04:38,182 views 94323 128587891404800 Login failed - User does not exist: +923490420140
WARNING 2025-06-14 05:04:39,382 views 94323 128587891404800 Login failed - User does not exist: +923490420140
WARNING 2025-06-14 05:35:31,191 views 94323 128587891404800 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-14 05:35:59,007 views 94326 128587891404800 Login failed - User does not exist: +923087789558
WARNING 2025-06-14 05:45:20,109 views 94322 128587891404800 Login failed - User does not exist: +923057209772
WARNING 2025-06-14 05:45:29,403 views 94322 128587891404800 Login failed - User does not exist: +923057209772
WARNING 2025-06-14 05:56:26,713 views 94323 128587891404800 Login failed - User does not exist: +923159071373
WARNING 2025-06-14 06:51:57,400 views 598 136743401828352 Login failed - User does not exist: +923457787138
WARNING 2025-06-14 09:03:03,696 views 598 136743401828352 Login failed - User does not exist: +923023323056
WARNING 2025-06-14 09:03:11,304 views 600 136743401828352 Login failed - User does not exist: +923023323056
WARNING 2025-06-14 09:03:20,800 views 598 136743401828352 Login failed - User does not exist: +923023323056
WARNING 2025-06-14 09:24:23,912 views 601 136743401828352 Login failed - User does not exist: +923431976893
WARNING 2025-06-14 09:44:09,438 views 598 136743401828352 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-14 09:59:43,991 views 601 136743401828352 Login failed - User does not exist: +920300784495
WARNING 2025-06-14 10:32:11,382 views 600 136743401828352 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-14 11:00:22,486 views 598 136743401828352 Login attempt failed - Missing credentials for mobile: 
WARNING 2025-06-14 11:46:46,224 views 600 136743401828352 Login attempt failed - Missing credentials for mobile: 
