from datetime import datetime, timedelta

def get_date_range(time_filter, start_date=None, end_date=None):
    if start_date:
        start_date = datetime.fromisoformat(start_date)
    if end_date:
        end_date = datetime.fromisoformat(end_date)

    today = datetime.today()
    if not start_date or not end_date:
        if time_filter == 'last_7_days':
            start_date = today - timedelta(days=7)
            end_date = today
        elif time_filter == 'one_month':
            start_date = today - timedelta(days=30)
            end_date = today
        elif time_filter == 'three_months':
            start_date = today - timedelta(days=90)
            end_date = today
        elif time_filter == 'one_year':
            start_date = today - timedelta(days=365)
            end_date = today
        elif time_filter == 'This Year':
            start_date = datetime(today.year, 1, 1)
            end_date = today
        elif time_filter == 'Last Year':
            start_date = datetime(today.year - 1, 1, 1)
            end_date = datetime(today.year - 1, 12, 31)
        elif time_filter == 'This Month':
            start_date = datetime(today.year, today.month, 1)
            end_date = today
        elif time_filter == 'Last Month':
            first_day_of_current_month = datetime(today.year, today.month, 1)
            last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
            start_date = datetime(last_day_of_last_month.year, last_day_of_last_month.month, 1)
            end_date = last_day_of_last_month
        elif time_filter == 'Same Month Last Year':
            start_date = datetime(today.year - 1, today.month, 1)
            end_date = (datetime(today.year - 1, today.month + 1, 1) - timedelta(days=1) if today.month < 12 else datetime(today.year - 1, 12, 31))
    
    return start_date, end_date
