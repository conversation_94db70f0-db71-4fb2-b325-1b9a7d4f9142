from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views, views_api

app_name = 'farm_finances'

router = DefaultRouter()
router.register(r'api_income_list', views.IncomeViewSet)

urlpatterns = [
    # Your other URL patterns
    path('', include(router.urls)),
    path('status/', views.status_dashboard, name='status_dashboard'),
    path('income-categories/', views.income_categories, name='income_categories'),
    path('expense-categories/', views.expense_categories, name='expense_categories'),
    path('update-income-category/<int:income_category_id>/', views.update_income_category, name='update_income_category'),
    path('income-categories/<int:pk>/delete/', views.delete_income_category, name='delete_income_category'),

    path('create_income/', views.create_income, name='create_income'),
    path('income_list/', views.income_list, name='income_list'),
    path('api/incomes/categories/', views_api.list_income_categories, name='list_income_categories'),
    path('api/incomes/categories/create/', views_api.create_income_category, name='create_income_category_api'),
    path('api/income/', views_api.income_list_api, name='income_list_api'),

    path('update_income/<int:income_id>/', views.update_income, name='update_income'),
    path('incomes/<int:pk>/delete/', views.delete_income, name='delete_income'),

    path('create_expense/', views.create_expense, name='create_expense'),
    path('expense_list/', views.expense_list, name='expense_list'),
    path('api/expenses/', views_api.expense_list_api, name='expense_list_api'),
    path('api/expenses/create/', views_api.create_expense_api, name='create_expense_api'),
    path('api/expenses/categories/', views_api.list_expense_categories, name='list_expense_categories'),
    path('api/expenses/categories/create/', views_api.create_expense_category, name='create_expense_category_api'),
    path('api/expenses/entries/', views_api.list_expense_entries, name='list_expense_entries'),
    path('api/expenses/<int:pk>/', views_api.expense_detail, name='expense_detail_api'),
    # Your other URL patterns
    path('', include(router.urls)),
    path('income-categories/', views.income_categories, name='income_categories'),
    path('expense-categories/', views.expense_categories, name='expense_categories'),
    path('update-income-category/<int:income_category_id>/', views.update_income_category, name='update_income_category'),
    path('income-categories/<int:pk>/delete/', views.delete_income_category, name='delete_income_category'),

    path('create_income/', views.create_income, name='create_income'),
    path('income_list/', views.income_list, name='income_list'),
    path('api/incomes/categories/', views_api.list_income_categories, name='list_income_categories'),
    path('api/incomes/categories/create/', views_api.create_income_category, name='create_income_category_api'),
    path('api/income/', views_api.income_list_api, name='income_list_api'),

    path('update_income/<int:income_id>/', views.update_income, name='update_income'),
    path('incomes/<int:pk>/delete/', views.delete_income, name='delete_income'),


    path('create_expense/', views.create_expense, name='create_expense'),
    path('expense_list/', views.expense_list, name='expense_list'),
    path('api/expenses/', views_api.expense_list_api, name='expense_list_api'),
    path('api/expenses/create/', views_api.create_expense_api, name='create_expense_api'),
    path('api/expenses/categories/', views_api.list_expense_categories, name='list_expense_categories'),
    path('api/expenses/categories/create/', views_api.create_expense_category, name='create_expense_category_api'),
    path('api/expenses/entries/', views_api.list_expense_entries, name='list_expense_entries'),
    path('api/expenses/<int:pk>/', views_api.expense_detail, name='expense_detail_api'),
    path('api/expenses/<int:pk>/update/', views_api.update_expense_api, name='update_expense_api'),
    path('api/expenses/<int:pk>/delete/', views_api.delete_expense_api, name='delete_expense_api'),
    path('update_expense/<int:expense_id>/', views.update_expense, name='update_expense'),
    path('expenses/<int:pk>/delete/', views.delete_expense, name='delete_expense'),


    path('create-income-category/', views.create_income_category, name='create_income_category'),

    
    path('create-expense-category/', views.create_expense_category, name='create_expense_category'),
    path('update_expense_category/<int:expense_category_id>/', views.update_expense_category, name='update_expense_category'),
    path('expense-categories/<int:pk>/delete/', views.delete_expense_category, name='delete_expense_category'),

    # Add update and delete URL patterns for income and expense categories
    path('export_income/', views.export_income, name='export_income'),
    path('export_income_pdf/', views.export_income_pdf, name='export_income_pdf'),
    path('export_expense/', views.export_expense, name='export_expense'),
    path('export_expense_pdf/', views.export_expense_pdf, name='export_expense_pdf'),
    path('import_income/', views.import_income, name='import_income'),
    path('import_expense/', views.import_expense, name='import_expense'),


    path('api/income-expense-summary/', views_api.income_expense_summary, name='income_expense_summary'),
    path('api/current-month-expense-summary/', views_api.current_month_expense_summary, name='current_month_expense_summary'),
    path('api/current-month-income-summary/', views_api.current_month_income_summary, name='current_month_income_summary'),

    # Income API endpoints
    path('api/incomes/entries/', views_api.list_income_entries, name='list_income_entries'),
    path('api/incomes/create/', views_api.create_income_api, name='create_income_api'),
    path('api/incomes/<int:pk>/', views_api.income_detail, name='income_detail_api'),
    path('api/incomes/<int:pk>/update/', views_api.update_income_api, name='update_income_api'),
    path('api/incomes/<int:pk>/delete/', views_api.delete_income_api, name='delete_income_api'),
]
