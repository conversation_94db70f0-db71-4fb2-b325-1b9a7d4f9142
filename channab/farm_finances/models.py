from django.db import models
from django.contrib.auth.models import User
from accounts.models import Farm
from datetime import date, datetime
from django.conf import settings
from django.utils import timezone

from accounts.models import SalaryTransaction
from dairy.models import MilkPayment

class IncomeCategory(models.Model):
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name

class ExpenseCategory(models.Model):
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name

class Income(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)  
    date = models.DateField(default=date.today)
    description = models.TextField(blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    category = models.ForeignKey(IncomeCategory, on_delete=models.CASCADE)
    milk_payment = models.ForeignKey(MilkPayment, on_delete=models.SET_NULL, null=True, blank=True)
    image = models.ImageField(upload_to='invoices/', blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)
    is_edited = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if self.pk:  
            self.is_edited = True
            self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.description} - {self.amount} - {self.category}"


class Expense(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)  
    date = models.DateField(default=date.today)
    description = models.TextField(blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    category = models.ForeignKey(ExpenseCategory, on_delete=models.CASCADE)
    image = models.ImageField(upload_to='invoices/', blank=True, null=True)
    salary_transaction = models.ForeignKey(SalaryTransaction, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)
    is_edited = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if self.pk:  
            self.is_edited = True
            self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.description} - {self.amount} - {self.category}"
