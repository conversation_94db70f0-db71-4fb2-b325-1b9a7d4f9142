# views.py
from farm_finances.utils import get_date_range
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils.dateparse import parse_datetime
from django.db.models import F, Q, Sum
from django.db.models.functions import TruncMonth, Coalesce
from django.db.models import DecimalField
from datetime import datetime, timedelta
from .models import Expense, ExpenseCategory, Income, IncomeCategory
from .serializers import ExpenseCategorySerializer, ExpenseSerializer, IncomeCategorySerializer, IncomeSerializer
import pandas as pd
from django.utils.timezone import now
from django.http import HttpResponse


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def income_list_api(request):
    farm = request.user.farm
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')

    if sort_order == 'asc':
        incomes = Income.objects.filter(farm=farm).order_by(sort_by)
    else:
        incomes = Income.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

    time_filter = request.GET.get('time_filter')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')


    start_date, end_date = get_date_range(time_filter, start_date, end_date)


    if start_date and end_date:
        incomes = incomes.filter(date__range=(start_date, end_date))

    if request.method == 'GET':
        if request.GET.get('export') == 'excel':
            data = list(incomes.values('date', 'amount', 'category__name', 'description'))
            if data:
                df = pd.DataFrame(data)
                df.columns = ['Date', 'Amount', 'Category', 'Description']
                response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = 'attachment; filename="incomes.xlsx"'
                with pd.ExcelWriter(response, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Incomes')
                return response
            else:
                return Response({"detail": "No income data available to export."}, status=status.HTTP_204_NO_CONTENT)

        serializer = IncomeSerializer(incomes, many=True)
        response_data = serializer.data
        return Response(response_data, status=status.HTTP_200_OK)

    if request.method == 'POST':
        excel_file = request.FILES['file']
        df = pd.read_excel(excel_file)
        df.columns = [x.strip().lower() for x in df.columns]

        if 'date' not in df.columns or 'amount' not in df.columns or 'description' not in df.columns:
            return Response({"detail": "The Excel file must contain columns named 'Date', 'Amount', and 'Description'."}, status=status.HTTP_400_BAD_REQUEST)

        default_category, _ = IncomeCategory.objects.get_or_create(farm=farm, name="Other")

        for index, row in df.iterrows():
            category_name = str(row.get('category_name', '')).strip() if pd.notna(row.get('category_name', '')) else None

            if category_name:
                category, created = IncomeCategory.objects.get_or_create(farm=farm, name=category_name)
            else:
                category = default_category

            Income.objects.create(
                farm=farm,
                user=request.user,
                date=row['date'],
                amount=row['amount'],
                category=category,
                description=row['description'] if pd.notna(row['description']) else ""
            )
        return Response({"detail": "Incomes imported successfully."}, status=status.HTTP_201_CREATED)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def expense_list_api(request):
    farm = request.user.farm
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')

    if sort_order == 'asc':
        expenses = Expense.objects.filter(farm=farm).order_by(sort_by)
    else:
        expenses = Expense.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

    time_filter = request.GET.get('time_filter')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')


    start_date, end_date = get_date_range(time_filter, start_date, end_date)


    if start_date and end_date:
        expenses = expenses.filter(date__range=(start_date, end_date))

    if request.method == 'GET':
        if request.GET.get('export') == 'excel':
            data = list(expenses.values('date', 'amount', 'category__name', 'description'))
            if data:
                df = pd.DataFrame(data)
                df.columns = ['Date', 'Amount', 'Category', 'Description']
                response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = 'attachment; filename="expenses.xlsx"'
                with pd.ExcelWriter(response, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Expenses')
                return response
            else:
                return Response({"detail": "No expenses data available to export."}, status=status.HTTP_204_NO_CONTENT)

        serializer = ExpenseSerializer(expenses, many=True)
        response_data = serializer.data
        return Response(response_data, status=status.HTTP_200_OK)

    if request.method == 'POST':
        excel_file = request.FILES['file']
        df = pd.read_excel(excel_file)
        df.columns = [x.strip().lower() for x in df.columns]

        if 'date' not in df.columns or 'amount' not in df.columns or 'description' not in df.columns:
            return Response({"detail": "The Excel file must contain columns named 'Date', 'Amount', and 'Description'."}, status=status.HTTP_400_BAD_REQUEST)

        default_category, _ = ExpenseCategory.objects.get_or_create(farm=farm, name="Other")

        for index, row in df.iterrows():
            category_name = str(row.get('category_name', '')).strip() if pd.notna(row.get('category_name', '')) else None

            if category_name:
                category, created = ExpenseCategory.objects.get_or_create(farm=farm, name=category_name)
            else:
                category = default_category

            Expense.objects.create(
                farm=farm,
                user=request.user,
                date=row['date'],
                amount=row['amount'],
                category=category,
                description=row['description'] if pd.notna(row['description']) else ""
            )
        return Response({"detail": "Expenses imported successfully."}, status=status.HTTP_201_CREATED)



@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_expense_categories(request):
    farm = request.user.farm
    time_filter = request.GET.get('time_filter', 'this_month')  # Default to this month
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Calculate date range based on time_filter
    today = now().date()
    if time_filter == 'last_7_days':
        start_date = today - timedelta(days=7)
        end_date = today
    elif time_filter == 'this_month':
        start_date = today.replace(day=1)
        end_date = today
    elif time_filter == 'last_month':
        last_month = today.replace(day=1) - timedelta(days=1)
        start_date = last_month.replace(day=1)
        end_date = last_month
    elif time_filter == 'this_year':
        start_date = today.replace(month=1, day=1)
        end_date = today
    elif time_filter == 'custom_range':
        if not start_date or not end_date:
            return Response(
                {"error": "Both start_date and end_date are required for custom range"},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Get categories with their total expenses for the period
    categories = ExpenseCategory.objects.filter(farm=farm).annotate(
        total_expenses=Coalesce(
            Sum(
                'expense__amount',
                filter=Q(expense__date__gte=start_date, expense__date__lte=end_date)
            ),
            0,
            output_field=DecimalField()
        )
    ).values('name', 'total_expenses')

    # Add period information to response
    response_data = {
        'period': {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'time_filter': time_filter
        },
        'categories': list(categories)
    }

    return Response(response_data)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def create_expense_api(request):
    """
    GET: Get available categories for creating a new expense
    POST: Create a new expense with optional image upload
    """
    try:
        if request.method == 'GET':
            
            # Get all categories for the farm
            categories = ExpenseCategory.objects.filter(farm=request.user.farm)
            categories_data = [{'id': cat.id, 'name': cat.name} for cat in categories]
            
            # Prepare initial data with categories
            response_data = {
                'available_categories': categories_data,
                # You can add any default values here
                'date': datetime.now().strftime('%Y-%m-%d'),
                'amount': '',
                'category': '',
                'description': ''
            }
            
            
            return Response(response_data)

        # Handle POST request - create the expense
        
        serializer = ExpenseSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            
            # Save the new expense
            expense = serializer.save()
            
            
            response_data = serializer.data
            if expense.image:
                response_data['image_url'] = request.build_absolute_uri(expense.image.url)
            
            # Add available categories to response
            categories = ExpenseCategory.objects.filter(farm=request.user.farm)
            response_data['available_categories'] = [
                {'id': cat.id, 'name': cat.name} for cat in categories
            ]
            
            
            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred while creating the expense"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def expense_detail(request, pk):
    """
    Retrieve a specific expense entry with image URL if present
    """
    try:
        expense = Expense.objects.select_related('category').get(pk=pk, farm=request.user.farm)
        
        serializer = ExpenseSerializer(expense)
        data = serializer.data
        
        # Add image URL if image exists
        if expense.image:
            data['image_url'] = request.build_absolute_uri(expense.image.url)
            
        # Add edit information
        data['is_edited'] = expense.is_edited
        data['created_at'] = expense.created_at
        data['updated_at'] = expense.updated_at
        
        return Response(data)
    except Expense.DoesNotExist:
        return Response(status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def update_expense_api(request, pk):
    """
    GET: Get expense details with available categories for update
    PUT: Update a specific expense entry with optional image update
    """
    try:
        expense = Expense.objects.get(pk=pk, farm=request.user.farm)
        
        # Handle GET request - return current expense data and available categories
        if request.method == 'GET':
            
            # Get all categories for the farm
            categories = ExpenseCategory.objects.filter(farm=request.user.farm)
            categories_data = [{'id': cat.id, 'name': cat.name} for cat in categories]
            
            # Serialize current expense
            expense_serializer = ExpenseSerializer(expense)
            response_data = expense_serializer.data
            
            # Add image URL if exists
            if expense.image:
                response_data['image_url'] = request.build_absolute_uri(expense.image.url)
            
            # Add available categories to response
            response_data['available_categories'] = categories_data
            
            
            return Response(response_data)

        # Handle PUT request - update the expense
        

        serializer = ExpenseSerializer(expense, data=request.data, context={'request': request})
        
        if serializer.is_valid():
            
            # Save the updated expense
            updated_expense = serializer.save()
            
            
            response_data = serializer.data
            if updated_expense.image:
                response_data['image_url'] = request.build_absolute_uri(updated_expense.image.url)
            
            # Add available categories to response
            categories = ExpenseCategory.objects.filter(farm=request.user.farm)
            response_data['available_categories'] = [
                {'id': cat.id, 'name': cat.name} for cat in categories
            ]
            
            
            return Response(response_data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
    except Expense.DoesNotExist:
        return Response(
            {"error": f"Expense with ID {pk} not found"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred while updating the expense"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_income_categories(request):
    """
    List income categories with total income for the given period
    """
    farm = request.user.farm
    time_filter = request.GET.get('time_filter', 'this_month')  # Default to this month
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Calculate date range based on time_filter
    today = now().date()
    if time_filter == 'last_7_days':
        start_date = today - timedelta(days=7)
        end_date = today
    elif time_filter == 'this_month':
        start_date = today.replace(day=1)
        end_date = today
    elif time_filter == 'last_month':
        last_month = today.replace(day=1) - timedelta(days=1)
        start_date = last_month.replace(day=1)
        end_date = last_month
    elif time_filter == 'this_year':
        start_date = today.replace(month=1, day=1)
        end_date = today
    elif time_filter == 'custom_range':
        if not start_date or not end_date:
            return Response(
                {"error": "Both start_date and end_date are required for custom range"},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Get categories with their total income for the period
    categories = IncomeCategory.objects.filter(farm=farm).annotate(
        total_income=Coalesce(
            Sum(
                'income__amount',
                filter=Q(income__date__gte=start_date, income__date__lte=end_date)
            ),
            0,
            output_field=DecimalField()
        )
    ).values('name', 'total_income')

    # Add period information to response
    response_data = {
        'period': {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'time_filter': time_filter
        },
        'categories': list(categories)
    }

    return Response(response_data)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_income_category(request):
    """
    Create a new income category for the logged-in user's farm
    """
    farm = request.user.farm
    name = request.data.get('name')

    if not name:
        return Response(
            {"error": "Category name is required"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Check if category already exists for this farm
    if IncomeCategory.objects.filter(farm=farm, name__iexact=name).exists():
        return Response(
            {"error": f"Category '{name}' already exists for this farm"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Create new category
    try:
        category = IncomeCategory.objects.create(
            farm=farm,
            name=name
        )
        serializer = IncomeCategorySerializer(category)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_expense_category(request):
    """
    Create a new expense category for the logged-in user's farm
    """
    farm = request.user.farm
    name = request.data.get('name')

    if not name:
        return Response(
            {"error": "Category name is required"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Check if category already exists for this farm
    if ExpenseCategory.objects.filter(farm=farm, name__iexact=name).exists():
        return Response(
            {"error": f"Category '{name}' already exists for this farm"},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Create new category
    try:
        category = ExpenseCategory.objects.create(
            farm=farm,
            name=name
        )
        serializer = ExpenseCategorySerializer(category)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def current_month_expense_summary(request):
    farm = request.user.farm
    today = now().date()
    start_date = today.replace(day=1)  # First day of the current month

    # Query expenses for the current month
    expenses = Expense.objects.filter(farm=farm, date__range=(start_date, today))

    # Aggregate expenses by category
    category_expenses = expenses.values('category__name').annotate(total_amount=Sum('amount')).order_by('category__name')

    # Calculate the total amount for all expenses
    total_expenses = expenses.aggregate(total=Sum('amount'))['total'] or 0

    # Prepare the response data
    response_data = {
        'category_expenses': category_expenses,
        'total_expenses': total_expenses
    }

    return Response(response_data, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def current_month_income_summary(request):
    farm = request.user.farm
    today = now().date()
    start_date = today.replace(day=1)  # First day of the current month

    # Query incomes for the current month
    incomes = Income.objects.filter(farm=farm, date__range=(start_date, today))

    # Aggregate incomes by category
    category_incomes = incomes.values('category__name').annotate(total_amount=Sum('amount')).order_by('category__name')

    # Calculate the total amount for all incomes
    total_incomes = incomes.aggregate(total=Sum('amount'))['total'] or 0

    # Prepare the response data
    response_data = {
        'category_incomes': category_incomes,
        'total_incomes': total_incomes
    }

    return Response(response_data, status=status.HTTP_200_OK)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_income_api(request):
    serializer = IncomeSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

from datetime import datetime, timedelta
from django.utils.dateparse import parse_datetime
import pandas as pd
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.http import HttpResponse
from django.db.models import F

from .models import Income, IncomeCategory
from .serializers import IncomeSerializer


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def income_expense_summary(request):
    farm = request.user.farm

    # Calculate the date range for the last 6 months
    end_date = now().date().replace(day=1) + timedelta(days=32)
    start_date = end_date - timedelta(days=180)

    income_summary = Income.objects.filter(farm=farm, date__range=(start_date, end_date)).annotate(month=TruncMonth('date')).values('month').annotate(total_income=Sum('amount')).order_by('month')
    expense_summary = Expense.objects.filter(farm=farm, date__range=(start_date, end_date)).annotate(month=TruncMonth('date')).values('month').annotate(total_expense=Sum('amount')).order_by('month')

    # Create a dictionary with months as keys and initialize with zero values
    summary = {month.strftime("%b %Y"): {'month': month.strftime("%b %Y"), 'total_income': 0, 'total_expense': 0} for month in pd.date_range(start=start_date, end=end_date, freq='MS')}

    # Fill the dictionary with actual data
    for income in income_summary:
        month_str = income['month'].strftime("%b %Y")
        if month_str in summary:
            summary[month_str]['total_income'] = income['total_income']
        else:
            summary[month_str] = {'month': month_str, 'total_income': income['total_income'], 'total_expense': 0}
    
    for expense in expense_summary:
        month_str = expense['month'].strftime("%b %Y")
        if month_str in summary:
            summary[month_str]['total_expense'] = expense['total_expense']
        else:
            summary[month_str] = {'month': month_str, 'total_income': 0, 'total_expense': expense['total_expense']}

    # Convert the dictionary to a list
    summary_list = list(summary.values())

    return Response(summary_list, status=200)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_expense_entries(request):
    """
    List expense entries with category and time period filtering
    """
    farm = request.user.farm
    category_id = request.GET.get('category_id')
    time_filter = request.GET.get('time_filter', 'this_month')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')

    # Base query with edit information
    expenses = Expense.objects.filter(farm=farm).annotate(
        last_modified=F('updated_at'),
        has_been_edited=F('is_edited')
    )

    # Apply category filter if provided
    if category_id:
        expenses = expenses.filter(category_id=category_id)

    # Calculate date range based on time_filter
    today = now().date()
    if time_filter == 'last_7_days':
        start_date = today - timedelta(days=7)
        end_date = today
    elif time_filter == 'this_month':
        start_date = today.replace(day=1)
        end_date = today
    elif time_filter == 'last_month':
        last_month = today.replace(day=1) - timedelta(days=1)
        start_date = last_month.replace(day=1)
        end_date = last_month
    elif time_filter == 'this_year':
        start_date = today.replace(month=1, day=1)
        end_date = today
    elif time_filter == 'custom_range':
        if not start_date or not end_date:
            return Response(
                {"error": "Both start_date and end_date are required for custom range"},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Apply date filter
    expenses = expenses.filter(date__gte=start_date, date__lte=end_date)

    # Apply sorting
    if sort_order == 'asc':
        expenses = expenses.order_by(sort_by)
    else:
        expenses = expenses.order_by(F(sort_by).desc(nulls_last=True))

    # Get category totals
    category_totals = expenses.values('category__name').annotate(
        total_amount=Sum('amount')
    ).order_by('-total_amount')

    # Get expense entries
    serializer = ExpenseSerializer(expenses, many=True)

    # Prepare response
    response_data = {
        'period': {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'time_filter': time_filter
        },
        'summary': {
            'total_expenses': sum(cat['total_amount'] for cat in category_totals),
            'category_totals': list(category_totals)
        },
        'expenses': serializer.data
    }

    return Response(response_data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_income_entries(request):
    """
    List income entries with category and time period filtering
    """
    farm = request.user.farm
    category_id = request.GET.get('category_id')
    time_filter = request.GET.get('time_filter', 'this_month')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')

    # Base query with edit information
    incomes = Income.objects.filter(farm=farm).annotate(
        last_modified=F('updated_at'),
        has_been_edited=F('is_edited')
    )

    # Apply category filter if provided
    if category_id:
        incomes = incomes.filter(category_id=category_id)

    # Calculate date range based on time_filter
    today = now().date()
    if time_filter == 'last_7_days':
        start_date = today - timedelta(days=7)
        end_date = today
    elif time_filter == 'this_month':
        start_date = today.replace(day=1)
        end_date = today
    elif time_filter == 'last_month':
        last_month = today.replace(day=1) - timedelta(days=1)
        start_date = last_month.replace(day=1)
        end_date = last_month
    elif time_filter == 'this_year':
        start_date = today.replace(month=1, day=1)
        end_date = today
    elif time_filter == 'custom_range':
        if not start_date or not end_date:
            return Response(
                {"error": "Both start_date and end_date are required for custom range"},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )

    # Apply date filter
    incomes = incomes.filter(date__gte=start_date, date__lte=end_date)

    # Apply sorting
    if sort_order == 'asc':
        incomes = incomes.order_by(sort_by)
    else:
        incomes = incomes.order_by(F(sort_by).desc(nulls_last=True))

    # Get category totals
    category_totals = incomes.values('category__name').annotate(
        total_amount=Sum('amount')
    ).order_by('-total_amount')

    # Get income entries
    serializer = IncomeSerializer(incomes, many=True)

    # Prepare response
    response_data = {
        'period': {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'time_filter': time_filter
        },
        'summary': {
            'total_incomes': sum(cat['total_amount'] for cat in category_totals),
            'category_totals': list(category_totals)
        },
        'incomes': serializer.data
    }

    return Response(response_data)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_expense_api(request, pk):
    """
    Delete a specific expense entry
    """
    try:
        expense = Expense.objects.get(pk=pk, farm=request.user.farm)
        expense.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
    except Expense.DoesNotExist:
        return Response(status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def income_detail(request, pk):
    """
    Retrieve a specific income entry
    """
    try:
        income = Income.objects.select_related('category').get(pk=pk, farm=request.user.farm)
        serializer = IncomeSerializer(income)
        data = serializer.data

        # Add image URL if image exists
        if income.image:
            data['image_url'] = request.build_absolute_uri(income.image.url)
            
        # Add edit information
        data['is_edited'] = income.is_edited
        data['created_at'] = income.created_at
        data['updated_at'] = income.updated_at

        return Response(data)
    except Income.DoesNotExist:
        return Response(status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def update_income_api(request, pk):
    """
    GET: Get income details with available categories for update
    PUT: Update a specific income entry
    """
    try:
        income = Income.objects.get(pk=pk, farm=request.user.farm)
    except Income.DoesNotExist:
        return Response(status=status.HTTP_404_NOT_FOUND)

    if request.method == 'GET':
        serializer = IncomeSerializer(income)
        categories = IncomeCategory.objects.filter(farm=request.user.farm)
        category_serializer = IncomeCategorySerializer(categories, many=True)
        return Response({
            'income': serializer.data,
            'categories': category_serializer.data
        })

    elif request.method == 'PUT':
        serializer = IncomeSerializer(income, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_income_api(request, pk):
    """
    Delete a specific income entry
    """
    try:
        income = Income.objects.get(pk=pk, farm=request.user.farm)
    except Income.DoesNotExist:
        return Response(status=status.HTTP_404_NOT_FOUND)
    
    income.delete()
    return Response(status=status.HTTP_204_NO_CONTENT)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def create_income_api(request):
    """
    GET: Get available categories for creating a new income
    POST: Create a new income entry
    """
    try:
        if request.method == 'GET':
            
            # Get all categories for the farm
            categories = IncomeCategory.objects.filter(farm=request.user.farm)
            categories_data = [{'id': cat.id, 'name': cat.name} for cat in categories]
            
            # Prepare initial data with categories
            response_data = {
                'available_categories': categories_data,
                # You can add any default values here
                'date': datetime.now().strftime('%Y-%m-%d'),
                'amount': '',
                'category': '',
                'description': ''
            }
            
            
            return Response(response_data)

        # Handle POST request - create the income
        
        serializer = IncomeSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            
            # Save the new income
            income = serializer.save()
            
            
            response_data = serializer.data
            
            # Add available categories to response
            categories = IncomeCategory.objects.filter(farm=request.user.farm)
            response_data['available_categories'] = [
                {'id': cat.id, 'name': cat.name} for cat in categories
            ]
            
            
            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response(
            {"error": "An unexpected error occurred while creating the income"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
