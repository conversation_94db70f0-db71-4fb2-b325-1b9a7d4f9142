from django.shortcuts import render
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from .models import  IncomeCategory, ExpenseCategory
from accounts.models import Farm
from .forms import IncomeCategoryForm, ExpenseCategoryForm
from datetime import date, datetime, timedelta
from django.db.models import F
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from .serializers import IncomeSerializer
import pandas as pd
from django.http import HttpResponse
from django.contrib import messages
from django.urls import reverse
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from io import BytesIO


# Other imports and views

from .models import Income, Expense
from django.db.models import Sum, Q

@login_required
def status_dashboard(request):
    farm = request.user.farm
    from calendar import monthrange
    # --- Filter logic ---
    time_filter = request.GET.get('time_filter', 'this_month')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    today = datetime.today().date()
    # Calculate date range based on filter
    if time_filter == 'last_month':
        first = today.replace(day=1)
        last_month_end = first - timedelta(days=1)
        start = last_month_end.replace(day=1)
        end = last_month_end
    elif time_filter == 'last_3_months':
        first = today.replace(day=1)
        start = (first - timedelta(days=90)).replace(day=1)
        end = today
    elif time_filter == 'this_year':
        start = today.replace(month=1, day=1)
        end = today
    elif time_filter == 'custom' and start_date and end_date:
        try:
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
        except Exception:
            start = today.replace(day=1)
            end = today
    else:  # Default: this month
        start = today.replace(day=1)
        end = today

    # --- Opening Balance Calculation ---
    # Find the closing balance before the start of the selected range
    prev_income = Income.objects.filter(farm=farm, date__lt=start).aggregate(total=Sum('amount'))['total'] or 0
    prev_expense = Expense.objects.filter(farm=farm, date__lt=start).aggregate(total=Sum('amount'))['total'] or 0
    opening_balance = prev_income - prev_expense

    # Filtered queries for the selected period
    income_qs = Income.objects.filter(farm=farm, date__gte=start, date__lte=end)
    expense_qs = Expense.objects.filter(farm=farm, date__gte=start, date__lte=end)

    total_income = income_qs.aggregate(total=Sum('amount'))['total'] or 0
    total_expense = expense_qs.aggregate(total=Sum('amount'))['total'] or 0
    net_change = total_income - total_expense
    closing_balance = opening_balance + net_change
    current_balance = closing_balance

    # Monthly data for the bar chart (last 6 months, not filtered)
    months = []
    income_per_month = []
    expense_per_month = []
    chart_today = datetime.today()
    for i in range(5, -1, -1):
        month = (chart_today.replace(day=1) - timedelta(days=30*i)).replace(day=1)
        month_str = month.strftime('%b')
        months.append(month_str)
        income = Income.objects.filter(farm=farm, date__year=month.year, date__month=month.month).aggregate(total=Sum('amount'))['total'] or 0
        expense = Expense.objects.filter(farm=farm, date__year=month.year, date__month=month.month).aggregate(total=Sum('amount'))['total'] or 0
        income_per_month.append(float(income))
        expense_per_month.append(float(expense))
    months = months[::-1]
    income_per_month = income_per_month[::-1]
    expense_per_month = expense_per_month[::-1]

    # --- Sorting and Pagination for Latest Transactions ---
    from django.core.paginator import Paginator
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    # Allowed sort fields
    allowed_sorts = {'date', 'amount', 'category', 'type'}
    if sort_by not in allowed_sorts:
        sort_by = 'date'
    reverse = sort_order == 'desc'

    # Gather all filtered transactions (income & expense)
    latest_incomes = [
        {
            'type': 'income',
            'amount': t.amount,
            'description': t.description,
            'date': t.date,
            'created_at': t.created_at,
            'category': t.category.name if hasattr(t, 'category') and t.category else '',
        } for t in income_qs
    ]
    latest_expenses = [
        {
            'type': 'expense',
            'amount': t.amount,
            'description': t.description,
            'date': t.date,
            'created_at': t.created_at,
            'category': t.category.name if hasattr(t, 'category') and t.category else '',
        } for t in expense_qs
    ]
    latest_transactions = latest_incomes + latest_expenses
    # Sorting
    latest_transactions = sorted(
        latest_transactions,
        key=lambda x: (
            x[sort_by] if sort_by != 'category' else (x['category'] or ''),
            x['created_at']
        ),
        reverse=reverse
    )
    # Pagination
    page_number = request.GET.get('page', 1)
    paginator = Paginator(latest_transactions, 10)
    page_obj = paginator.get_page(page_number)

    # --- Percent Change Calculation ---
    def previous_period(start, end, filter_type):
        if filter_type == 'this_month' or filter_type == 'last_month':
            prev_end = start - timedelta(days=1)
            prev_start = prev_end.replace(day=1)
        elif filter_type == 'last_3_months':
            prev_end = start - timedelta(days=1)
            prev_start = (prev_end - timedelta(days=89)).replace(day=1)
        elif filter_type == 'this_year':
            prev_end = start - timedelta(days=1)
            prev_start = prev_end.replace(month=1, day=1)
        elif filter_type == 'custom' and start and end:
            delta = (end - start).days + 1
            prev_end = start - timedelta(days=1)
            prev_start = prev_end - timedelta(days=delta-1)
        else:
            prev_end = prev_start = None
        return prev_start, prev_end

    prev_start, prev_end = previous_period(start, end, time_filter)
    if prev_start and prev_end:
        prev_income = Income.objects.filter(farm=farm, date__gte=prev_start, date__lte=prev_end).aggregate(total=Sum('amount'))['total'] or 0
        prev_expense = Expense.objects.filter(farm=farm, date__gte=prev_start, date__lte=prev_end).aggregate(total=Sum('amount'))['total'] or 0
    else:
        prev_income = prev_expense = 0

    def pct_change(current, prev):
        if prev == 0:
            return 0 if current == 0 else 100
        return round(((current - prev) / prev) * 100, 2)

    income_change_pct = pct_change(total_income, prev_income)
    expense_change_pct = pct_change(total_expense, prev_expense)

    context = {
        'current_balance': current_balance,
        'opening_balance': opening_balance,
        'total_income': total_income,
        'total_expense': total_expense,
        'net_change': net_change,
        'closing_balance': closing_balance,
        'income_change_pct': income_change_pct,
        'expense_change_pct': expense_change_pct,
        'months': months,
        'income_per_month': income_per_month,
        'expense_per_month': expense_per_month,
        'latest_transactions': page_obj.object_list,
        'page_obj': page_obj,
        'sort_by': sort_by,
        'sort_order': sort_order,
        'time_filter': time_filter,
        'start_date': start.strftime('%Y-%m-%d') if start else '',
        'end_date': end.strftime('%Y-%m-%d') if end else '',
    }
    return render(request, 'farm_finances/status_dashboard.html', context)


@login_required
def income_categories(request):
    farm = request.user.farm
    income_categories = IncomeCategory.objects.filter(farm=farm)

    return render(request, 'farm_finances/income_categories.html', {'farm': farm, 'income_categories': income_categories})

@login_required
def create_income_category(request):
    farm = request.user.farm
    if request.method == 'POST':
        form = IncomeCategoryForm(request.POST)
        if form.is_valid():
            income_category = form.save(commit=False)
            income_category.farm = farm
            income_category.save()
            return redirect('farm_finances:income_categories')
    else:
        form = IncomeCategoryForm()

    return render(request, 'farm_finances/create_income_category.html', {'form': form, 'farm': farm})

@login_required
def create_expense_category(request):
    farm = request.user.farm
    if request.method == 'POST':
        form = ExpenseCategoryForm(request.POST)
        if form.is_valid():
            expense_category = form.save(commit=False)
            expense_category.farm = farm
            expense_category.save()
            return redirect('farm_finances:expense_categories')
    else:
        form = ExpenseCategoryForm()

    return render(request, 'farm_finances/create_expense_category.html', {'form': form, 'farm': farm})

@login_required
def update_income_category(request, income_category_id):
    farm = request.user.farm
    income_category = get_object_or_404(IncomeCategory, pk=income_category_id, farm=farm)
    if request.method == 'POST':
        form = IncomeCategoryForm(request.POST, instance=income_category)
        if form.is_valid():
            form.save()
            return redirect('farm_finances:income_categories')
    else:
        form = IncomeCategoryForm(instance=income_category)
    return render(request, 'farm_finances/update_income_category.html', {'form': form, 'farm': farm, 'income_category': income_category})


@login_required
def update_expense_category(request, expense_category_id):
    farm = request.user.farm
    expense_category = get_object_or_404(ExpenseCategory, pk=expense_category_id, farm=farm)
    if request.method == 'POST':
        form = ExpenseCategoryForm(request.POST, instance=expense_category)
        if form.is_valid():
            form.save()
            return redirect('farm_finances:expense_categories')
    else:
        form = ExpenseCategoryForm(instance=expense_category)
    return render(request, 'farm_finances/update_expense_category.html', {'form': form, 'farm': farm, 'expense_category': expense_category})

@login_required
def delete_income_category(request, pk):
    income_category = get_object_or_404(IncomeCategory, pk=pk, farm=request.user.farm)
    
    if income_category.name == 'Milk Sale':
        # Prevent deleting Milk Sale category
        return redirect('farm_finances:income_categories')
    
    income_category.delete()
    return redirect('farm_finances:income_categories')



@login_required
def delete_expense_category(request, pk):
    expense_category = get_object_or_404(ExpenseCategory, pk=pk, farm=request.user.farm)
    if expense_category.name == 'Salary':
        return redirect('farm_finances:expense_categories')
    expense_category.delete()
    return redirect('farm_finances:expense_categories')






@login_required
def expense_categories(request):
    farm = request.user.farm
    expense_categories = ExpenseCategory.objects.filter(farm=farm)

    return render(request, 'farm_finances/expense_categories.html', {'farm': farm, 'expense_categories': expense_categories})


from django.http import HttpResponse, HttpResponseRedirect, JsonResponse



from .models import Expense, ExpenseCategory
from .forms import ExpenseForm, IncomeForm

@login_required
def create_income(request):
    farm = request.user.farm
    if request.method == "POST":
        form = IncomeForm(request.POST, request.FILES, farm=farm)
        if form.is_valid():
            income = form.save(commit=False)
            income.user = request.user
            income.farm = farm
            income.save()
            return redirect('farm_finances:income_list')
    else:
        form = IncomeForm(farm=farm)
        today_str = date.today().strftime('%Y-%m-%d')
    return render(request, 'farm_finances/create_income.html', {'form': form, 'today': today_str})


@login_required
def update_income(request, income_id):
    income = get_object_or_404(Income, id=income_id, user=request.user)
    if request.method == "POST":
        form = IncomeForm(request.POST, request.FILES, instance=income, farm=request.user.farm)
        if form.is_valid():
            form.save()
            return redirect('farm_finances:income_list')
    else:
        form = IncomeForm(instance=income, farm=request.user.farm)
    today_str = date.today().strftime('%Y-%m-%d')
    return render(request, 'farm_finances/update_income.html', {'form': form, 'income': income, 'today': today_str})




@login_required
def delete_income(request, pk):
    income = get_object_or_404(Income, pk=pk, farm=request.user.farm)
    income.delete()
    return redirect('farm_finances:income_list')


from django.shortcuts import render
from .models import Income



from datetime import timedelta, datetime

@login_required
def income_list(request):
    farm = request.user.farm
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    if sort_order == 'asc':
        incomes = Income.objects.filter(farm=farm).order_by(sort_by)
    else:
        incomes = Income.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

    from calendar import monthrange
    time_filter = request.GET.get('time_filter')
    # Only use GET params for custom filter
    get_start_date = request.GET.get('start_date')
    get_end_date = request.GET.get('end_date')

    today = datetime.today()
    if not time_filter or time_filter == 'this_month':
        start_date = today.replace(day=1).date()
        last_day = monthrange(today.year, today.month)[1]
        end_date = today.replace(day=last_day).date()
        time_filter = 'this_month'
    elif time_filter == 'last_month':
        first_of_this_month = today.replace(day=1)
        last_month_end = first_of_this_month - timedelta(days=1)
        start_date = last_month_end.replace(day=1).date()
        end_date = last_month_end.date()
    elif time_filter == 'last_3_months':
        start_date = (today - timedelta(days=90)).date()
        end_date = today.date()
    elif time_filter == 'this_year':
        start_date = today.replace(month=1, day=1).date()
        end_date = today.replace(month=12, day=31).date()
    elif time_filter == 'custom' and get_start_date and get_end_date:
        try:
            start_date = datetime.strptime(get_start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(get_end_date, '%Y-%m-%d').date()
        except Exception:
            start_date = end_date = None
    else:
        start_date = end_date = None

    if start_date and end_date:
        incomes = incomes.filter(date__range=(start_date, end_date))

    # Category filter
    category_filter = request.GET.get('category')
    filtered_incomes = incomes
    if category_filter:
        filtered_incomes = incomes.filter(category__name=category_filter)

    # Calculate total income and category totals for summary cards
    from django.db.models import Sum
    total_income = incomes.aggregate(total=Sum('amount'))['total'] or 0
    category_totals = (
        incomes.values('category__name')
        .annotate(total=Sum('amount'))
        .order_by('-total')
    )

    return render(
        request,
        'farm_finances/income_list.html',
        {
            'incomes': incomes,  # Use the main incomes queryset (filtered as needed)
            'farm': farm,
            'sort_by': sort_by,
            'sort_order': sort_order,
            'time_filter': time_filter,
            'start_date': start_date,
            'end_date': end_date,
            'category_filter': category_filter,
            'total_income': total_income,
            'category_totals': category_totals,
        }
    )


@login_required
def create_expense(request):
    farm = request.user.farm
    if request.method == "POST":
        form = ExpenseForm(request.POST, request.FILES, farm=farm)
        if form.is_valid():
            expense = form.save(commit=False)
            expense.user = request.user
            expense.farm = farm
            expense.save()
            return redirect('farm_finances:expense_list')
    else:
        form = ExpenseForm(farm=farm)
    today_str = date.today().strftime('%Y-%m-%d')
    return render(request, 'farm_finances/create_expense.html', {'form': form, 'today': today_str})



from django.shortcuts import render
from .models import Expense

@login_required
def update_expense(request, expense_id):
    expense = get_object_or_404(Expense, id=expense_id, user=request.user)
    farm = expense.farm  # get the farm from the expense instance
    if request.method == "POST":
        form = ExpenseForm(request.POST, request.FILES, instance=expense, farm=farm)
        if form.is_valid():
            form.save()
            return redirect('farm_finances:expense_list')
    else:
        form = ExpenseForm(instance=expense, farm=farm)
    today_str = date.today().strftime('%Y-%m-%d')
    return render(request, 'farm_finances/update_expense.html', {'form': form, 'expense': expense, 'today': today_str})

@login_required
def delete_expense(request, pk):
    expense = get_object_or_404(Expense, pk=pk, farm=request.user.farm)
    expense.delete()
    return redirect('farm_finances:expense_list')


from datetime import timedelta, datetime
from django.utils import timezone


@login_required
def expense_list(request):
    from calendar import monthrange
    farm = request.user.farm
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')

    if sort_order == 'asc':
        expenses = Expense.objects.filter(farm=farm).order_by(sort_by)
    else:
        expenses = Expense.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

    time_filter = request.GET.get('time_filter')
    get_start_date = request.GET.get('start_date')
    get_end_date = request.GET.get('end_date')

    today = datetime.today()
    if not time_filter or time_filter == 'this_month':
        start_date = today.replace(day=1).date()
        last_day = monthrange(today.year, today.month)[1]
        end_date = today.replace(day=last_day).date()
        time_filter = 'this_month'
    elif time_filter == 'last_month':
        first_of_this_month = today.replace(day=1)
        last_month_end = first_of_this_month - timedelta(days=1)
        start_date = last_month_end.replace(day=1).date()
        end_date = last_month_end.date()
    elif time_filter == 'last_3_months':
        start_date = (today - timedelta(days=90)).date()
        end_date = today.date()
    elif time_filter == 'this_year':
        start_date = today.replace(month=1, day=1).date()
        end_date = today.replace(month=12, day=31).date()
    elif time_filter == 'custom' and get_start_date and get_end_date:
        try:
            start_date = datetime.strptime(get_start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(get_end_date, '%Y-%m-%d').date()
        except Exception:
            start_date = end_date = None
    else:
        start_date = end_date = None

    if start_date and end_date:
        expenses = expenses.filter(date__range=(start_date, end_date))

    # Category filter
    category_filter = request.GET.get('category')
    filtered_expenses = expenses
    if category_filter:
        filtered_expenses = expenses.filter(category__name=category_filter)

    if request.GET.get('export') == 'excel':
        data = list(expenses.values('date', 'amount', 'category__name', 'description'))
        if data:
            df = pd.DataFrame(data)
            df.columns = ['Date', 'Amount', 'Category', 'Description']
            response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = 'attachment; filename="expenses.xlsx"'
            with pd.ExcelWriter(response, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Expenses')
            return response
        else:
            pass  # No data available to export
            messages.warning(request, "No expenses data available to export.")
            return HttpResponseRedirect(reverse('farm_finances:expense_list'))

    if request.method == 'POST':
        form = UploadFileForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['file']
            df = pd.read_excel(excel_file)
            df.columns = [x.strip().lower() for x in df.columns]

            if 'date' not in df.columns or 'amount' not in df.columns or 'description' not in df.columns:
                messages.error(request, "The Excel file must contain columns named 'Date', 'Amount', and 'Description'.")
                return HttpResponseRedirect(reverse('farm_finances:expense_list'))

            # Ensure the default category exists
            default_category, _ = ExpenseCategory.objects.get_or_create(farm=farm, name="Other")

            for index, row in df.iterrows():
                # Safely convert category name to string and strip it, defaulting to None if it's NaN
                category_name = str(row.get('category_name', '')).strip() if pd.notna(row.get('category_name', '')) else None

                if category_name:
                    category, created = ExpenseCategory.objects.get_or_create(farm=farm, name=category_name)
                else:
                    category = default_category

                # Create the Expense record
                Expense.objects.create(
                    farm=farm,
                    user=request.user,
                    date=row['date'],
                    amount=row['amount'],
                    category=category,
                    description=row['description'] if pd.notna(row['description']) else ""
                )
            messages.success(request, 'Expenses imported successfully.')
            return redirect('farm_finances:expense_list')
    else:
        form = UploadFileForm()

    # Calculate total for all categories (date filter only)
    total_expenses = expenses.aggregate(total=Sum('amount'))['total'] or 0
    # Category totals for summary cards (date filter only)
    category_totals = (
        expenses.values('category__name')
        .annotate(total=Sum('amount'))
        .order_by('-total')
    )

    # Pass filtered_expenses for table display
    return render(request, 'farm_finances/expense_list.html', {
        'form': form,
        'expenses': filtered_expenses,
        'farm': farm,
        'sort_by': sort_by,
        'sort_order': sort_order,
        'total_expenses': total_expenses,
        'category_totals': category_totals,
        'time_filter': time_filter,
    })


import openpyxl
from .models import Income, Expense
from django.db import models


from django.db.models.fields.files import ImageFieldFile


def export_to_excel(model):
    # create a new excel workbook
    wb = openpyxl.Workbook()
    ws = wb.active

    # get the model's fields and append them as the header row
    fields = model._meta.fields
    header_row = [field.name for field in fields if not isinstance(field, models.ImageField)]
    ws.append(header_row)

    # get the model's objects and append each object as a row
    for obj in model.objects.all():
        row = []
        for field in fields:
            if isinstance(field, models.ImageField):
                continue
            value = getattr(obj, field.name)
            if isinstance(value, models.Model):
                value = str(value)
            elif isinstance(value, ImageFieldFile):
                if value:
                    value = str(value.path)
                else:
                    value = None
            row.append(value)
        ws.append(row)

    return wb




from django.core.exceptions import ObjectDoesNotExist

def import_from_excel(model, file, user):  # add the user parameter
    # open the workbook
    wb = openpyxl.load_workbook(file)
    ws = wb.active

    # get the header row and model fields
    header_row = [cell.value for cell in ws[1]]
    fields = {field.name: field for field in model._meta.fields}

    # iterate over the rows in the worksheet
    for row in ws.iter_rows(min_row=2, values_only=True):
        obj_dict = {}
        for key, value in zip(header_row, row):
            field = fields.get(key)
            if field is None:
                continue

            # if the field is a ForeignKey and not the 'user' field, look up the related object
            if isinstance(field, models.ForeignKey) and field.name != 'user':
                # check which field to use for lookup based on field name
                lookup_field = 'name' if field.name in ['farm', 'category'] else 'id'  # revise this as needed
                try:
                    value = field.related_model.objects.get(**{lookup_field: value})
                except ObjectDoesNotExist:
                    value = None
            obj_dict[key] = value

        # assign the logged-in user to the 'user' field
        obj_dict['user'] = user

        # create the object
        model.objects.create(**obj_dict)




@login_required
def export_income(request):
    wb = export_to_excel(Income)
    # Save the workbook to a HttpResponse
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=Incomes.xlsx'
    wb.save(response)
    return response

@login_required
def export_expense(request):
    wb = export_to_excel(Expense)
    # Save the workbook to a HttpResponse
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=Expenses.xlsx'
    wb.save(response)
    return response


@login_required
def import_income(request):
    if request.method == 'POST':
        form = UploadFileForm(request.POST, request.FILES)
        if form.is_valid():
            import_from_excel(Income, request.FILES['file'], request.user)  # pass the user here
            return HttpResponseRedirect('/success/url/')
    else:
        form = UploadFileForm()
    return render(request, 'farm_finances/upload.html', {'form': form})

@login_required
def import_expense(request):
    if request.method == 'POST':
        form = UploadFileForm(request.POST, request.FILES)
        if form.is_valid():
            import_from_excel(Expense, request.FILES['file'], request.user)
            return HttpResponseRedirect('/success/url/')
    else:
        form = UploadFileForm()
    return render(request, 'farm_finances/upload.html', {'form': form})

from django import forms

class UploadFileForm(forms.Form):
    file = forms.FileField()

# Mobile API

from django.db.models import Sum, F
from django.utils import timezone
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import viewsets
from .serializers import IncomeSerializer
from .models import Income, IncomeCategory, Farm

class IncomeViewSet(viewsets.ModelViewSet):
    queryset = Income.objects.all()
    serializer_class = IncomeSerializer

    @action(detail=False, methods=['get'])
    def income_list(self, request):
        user = request.user
        farm = user.farm
        sort_by = request.GET.get('sort_by', 'date')
        sort_order = request.GET.get('sort_order', 'desc')
        if sort_order == 'asc':
            incomes = Income.objects.filter(farm=farm).order_by(sort_by)
        else:
            incomes = Income.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

        time_filter = request.GET.get('time_filter')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')

        if time_filter == 'last_7_days':
            start_date = timezone.now() - timezone.timedelta(days=7)
            end_date = timezone.now()
        elif time_filter == 'one_month':
            start_date = timezone.now() - timezone.timedelta(days=30)
            end_date = timezone.now()
        elif time_filter == 'three_months':
            start_date = timezone.now() - timezone.timedelta(days=90)
            end_date = timezone.now()
        elif time_filter == 'one_year':
            start_date = timezone.now() - timezone.timedelta(days=365)
            end_date = timezone.now()

        if start_date and end_date:
            incomes = incomes.filter(date__range=(start_date, end_date))

        income_categories = IncomeCategory.objects.filter(farm=farm)

        summary = []
        for category in income_categories:
            total_amount = incomes.filter(category=category).aggregate(total_amount=Sum('amount'))['total_amount'] or 0
            summary.append({
                "category": category.name,
                "total_amount": total_amount,
            })

        serializer = IncomeSerializer(incomes, many=True)
        return Response({
            'income_list': serializer.data,
            'summary': summary
        })

@login_required
def export_income_pdf(request):
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, landscape
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from io import BytesIO
    from django.utils import timezone
    from django.db.models import Sum, F
    
    # Get the farm's income data and date range
    farm = request.user.farm
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    time_filter = request.GET.get('time_filter')  # Remove default 'all' to match income_list view
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # Apply sorting and filtering - exactly like income_list view
    if sort_order == 'asc':
        incomes = Income.objects.filter(farm=farm).order_by(sort_by)
    else:
        incomes = Income.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

    # Apply time filter - exactly like income_list view
    if time_filter == 'last_7_days':
        start_date = timezone.now() - timezone.timedelta(days=7)
        end_date = timezone.now()
    elif time_filter == 'one_month':
        start_date = timezone.now() - timezone.timedelta(days=30)
        end_date = timezone.now()
    elif time_filter == 'three_months':
        start_date = timezone.now() - timezone.timedelta(days=90)
        end_date = timezone.now()
    elif time_filter == 'one_year':
        start_date = timezone.now() - timezone.timedelta(days=365)
        end_date = timezone.now()
    elif time_filter == 'custom':
        try:
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            start_date = None
            end_date = None

    if start_date and end_date:
        incomes = incomes.filter(date__range=(start_date, end_date))

    # Get category summary
    income_categories = IncomeCategory.objects.filter(farm=farm)
    summary = []
    for category in income_categories:
        total_amount = incomes.filter(category=category).aggregate(total_amount=Sum('amount'))['total_amount'] or 0
        if total_amount > 0:  # Only include categories with income
            summary.append({
                "category": category.name,
                "total_amount": total_amount,
            })
    
    # Create the HttpResponse object with PDF headers
    buffer = BytesIO()
    doc = SimpleDocTemplate(
        buffer,
        pagesize=landscape(letter),
        rightMargin=50,
        leftMargin=50,
        topMargin=50,
        bottomMargin=50
    )
    
    # Container for the 'Flowable' objects
    elements = []
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    subtitle_style = ParagraphStyle(
        'CustomSubtitle',
        parent=styles['Normal'],
        fontSize=12,
        textColor=colors.grey,
        spaceAfter=20,
        alignment=1  # Center alignment
    )
    
    # Add title
    elements.append(Paragraph(f"{farm.name} - Income Report", title_style))
    
    # Add date range subtitle
    if time_filter == 'all':
        date_range_text = "All Time"
    elif time_filter == 'last_7_days':
        date_range_text = "Last 7 Days"
    elif time_filter == 'one_month':
        date_range_text = "Last Month"
    elif time_filter == 'three_months':
        date_range_text = "Last 3 Months"
    elif time_filter == 'one_year':
        date_range_text = "Last Year"
    elif time_filter == 'custom' and start_date and end_date:
        date_range_text = f"Period: {start_date.strftime('%B %d, %Y')} - {end_date.strftime('%B %d, %Y')}"
    else:
        date_range_text = "All Time"
    
    elements.append(Paragraph(date_range_text, subtitle_style))
    
    # Calculate totals
    total_income = sum(income.amount for income in incomes)
    
    # Add summary information
    summary_style = ParagraphStyle(
        'Summary',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=20
    )
    elements.append(Paragraph(f"Total Income: ${total_income:,.2f}", summary_style))
    
    # Add category-wise summary
    if summary:
        elements.append(Spacer(1, 10))
        elements.append(Paragraph("Category-wise Summary:", summary_style))
        for cat_summary in summary:
            elements.append(Paragraph(
                f"{cat_summary['category']}: ${cat_summary['total_amount']:,.2f}",
                ParagraphStyle(
                    'CategorySummary',
                    parent=styles['Normal'],
                    fontSize=10,
                    leftIndent=20,
                    spaceAfter=5
                )
            ))
    
    elements.append(Spacer(1, 20))
    
    # Create the table data
    data = [['Date', 'Amount ($)', 'Category', 'Description']]  # Header row
    for income in incomes:
        data.append([
            income.date.strftime('%Y-%m-%d'),
            f"{income.amount:,.2f}",
            str(income.category),
            income.description or ''
        ])
    
    # Add total row
    data.append(['Total', f"{total_income:,.2f}", '', ''])
    
    # Create the table with custom widths
    table = Table(data, colWidths=[1.5*inch, 1.5*inch, 2*inch, 4*inch])
    table.setStyle(TableStyle([
        # Header style
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#333333')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        
        # Data rows style
        ('BACKGROUND', (0, 1), (-1, -2), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        
        # Total row style
        ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f5f5f5')),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('ALIGN', (0, -1), (-1, -1), 'CENTER'),
        
        # Alternating row colors
        *[('BACKGROUND', (0, i), (-1, i), colors.HexColor('#f9f9f9')) 
          for i in range(2, len(data)-1, 2)],
    ]))
    
    elements.append(table)
    
    # Add footer with timestamp
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=8,
        textColor=colors.grey,
        alignment=1  # Center alignment
    )
    elements.append(Spacer(1, 30))
    elements.append(Paragraph(
        f"Generated on {timezone.now().strftime('%B %d, %Y at %I:%M %p')}",
        footer_style
    ))
    
    # Build PDF document
    doc.build(elements)
    
    # Get the value of the BytesIO buffer and return it
    pdf = buffer.getvalue()
    buffer.close()
    
    # Generate filename with date range
    if time_filter == 'all':
        filename = "income_report_all_time.pdf"
    elif time_filter == 'custom' and start_date and end_date:
        filename = f"income_report_{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}.pdf"
    else:
        filename = f"income_report_{time_filter}.pdf"
    
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    response.write(pdf)
    
    return response

@login_required
def export_expense_pdf(request):
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, landscape
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from io import BytesIO
    from django.utils import timezone
    from django.db.models import Sum, F
    
    # Get the farm's expense data and date range
    farm = request.user.farm
    sort_by = request.GET.get('sort_by', 'date')
    sort_order = request.GET.get('sort_order', 'desc')
    time_filter = request.GET.get('time_filter')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    # Apply sorting and filtering
    if sort_order == 'asc':
        expenses = Expense.objects.filter(farm=farm).order_by(sort_by)
    else:
        expenses = Expense.objects.filter(farm=farm).order_by(F(sort_by).desc(nulls_last=True))

    # Apply time filter
    if time_filter == 'last_7_days':
        start_date = timezone.now() - timezone.timedelta(days=7)
        end_date = timezone.now()
    elif time_filter == 'one_month':
        start_date = timezone.now() - timezone.timedelta(days=30)
        end_date = timezone.now()
    elif time_filter == 'three_months':
        start_date = timezone.now() - timezone.timedelta(days=90)
        end_date = timezone.now()
    elif time_filter == 'one_year':
        start_date = timezone.now() - timezone.timedelta(days=365)
        end_date = timezone.now()
    elif time_filter == 'custom':
        try:
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            start_date = None
            end_date = None

    if start_date and end_date:
        expenses = expenses.filter(date__range=(start_date, end_date))

    # Get category summary
    expense_categories = ExpenseCategory.objects.filter(farm=farm)
    summary = []
    for category in expense_categories:
        total_amount = expenses.filter(category=category).aggregate(total_amount=Sum('amount'))['total_amount'] or 0
        if total_amount > 0:  # Only include categories with expenses
            summary.append({
                "category": category.name,
                "total_amount": total_amount,
            })
    
    # Create the HttpResponse object with PDF headers
    buffer = BytesIO()
    doc = SimpleDocTemplate(
        buffer,
        pagesize=landscape(letter),
        rightMargin=50,
        leftMargin=50,
        topMargin=50,
        bottomMargin=50
    )
    
    # Container for the 'Flowable' objects
    elements = []
    
    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    subtitle_style = ParagraphStyle(
        'CustomSubtitle',
        parent=styles['Normal'],
        fontSize=12,
        textColor=colors.grey,
        spaceAfter=20,
        alignment=1  # Center alignment
    )
    
    # Add title
    elements.append(Paragraph(f"{farm.name} - Expense Report", title_style))
    
    # Add date range subtitle
    if time_filter == 'all':
        date_range_text = "All Time"
    elif time_filter == 'last_7_days':
        date_range_text = "Last 7 Days"
    elif time_filter == 'one_month':
        date_range_text = "Last Month"
    elif time_filter == 'three_months':
        date_range_text = "Last 3 Months"
    elif time_filter == 'one_year':
        date_range_text = "Last Year"
    elif time_filter == 'custom' and start_date and end_date:
        date_range_text = f"Period: {start_date.strftime('%B %d, %Y')} - {end_date.strftime('%B %d, %Y')}"
    else:
        date_range_text = "All Time"
    
    elements.append(Paragraph(date_range_text, subtitle_style))
    
    # Calculate totals
    total_expense = sum(expense.amount for expense in expenses)
    
    # Add summary information
    summary_style = ParagraphStyle(
        'Summary',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=20
    )
    elements.append(Paragraph(f"Total Expense: ${total_expense:,.2f}", summary_style))
    
    # Add category-wise summary
    if summary:
        elements.append(Spacer(1, 10))
        elements.append(Paragraph("Category-wise Summary:", summary_style))
        for cat_summary in summary:
            elements.append(Paragraph(
                f"{cat_summary['category']}: ${cat_summary['total_amount']:,.2f}",
                ParagraphStyle(
                    'CategorySummary',
                    parent=styles['Normal'],
                    fontSize=10,
                    leftIndent=20,
                    spaceAfter=5
                )
            ))
    
    elements.append(Spacer(1, 20))
    
    # Create the table data
    data = [['Date', 'Amount ($)', 'Category', 'Description']]  # Header row
    for expense in expenses:
        data.append([
            expense.date.strftime('%Y-%m-%d'),
            f"{expense.amount:,.2f}",
            str(expense.category),
            expense.description or ''
        ])
    
    # Add total row
    data.append(['Total', f"{total_expense:,.2f}", '', ''])
    
    # Create the table with custom widths
    table = Table(data, colWidths=[1.5*inch, 1.5*inch, 2*inch, 4*inch])
    table.setStyle(TableStyle([
        # Header style
        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#333333')),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        
        # Data rows style
        ('BACKGROUND', (0, 1), (-1, -2), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        
        # Total row style
        ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f5f5f5')),
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
        ('ALIGN', (0, -1), (-1, -1), 'CENTER'),
        
        # Alternating row colors
        *[('BACKGROUND', (0, i), (-1, i), colors.HexColor('#f9f9f9')) 
          for i in range(2, len(data)-1, 2)],
    ]))
    
    elements.append(table)
    
    # Add footer with timestamp
    footer_style = ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=8,
        textColor=colors.grey,
        alignment=1  # Center alignment
    )
    elements.append(Spacer(1, 30))
    elements.append(Paragraph(
        f"Generated on {timezone.now().strftime('%B %d, %Y at %I:%M %p')}",
        footer_style
    ))
    
    # Build PDF document
    doc.build(elements)
    
    # Get the value of the BytesIO buffer and return it
    pdf = buffer.getvalue()
    buffer.close()
    
    # Generate filename with date range
    if time_filter == 'all':
        filename = "expense_report_all_time.pdf"
    elif time_filter == 'custom' and start_date and end_date:
        filename = f"expense_report_{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}.pdf"
    else:
        filename = f"expense_report_{time_filter}.pdf"
    
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    response.write(pdf)
    
    return response
