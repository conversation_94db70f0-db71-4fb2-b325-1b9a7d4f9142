from .models import Expense, ExpenseCategory, Income, IncomeCategory
from rest_framework import serializers, viewsets

class IncomeSerializer(serializers.ModelSerializer):
    category = serializers.StringRelatedField()

    class Meta:
        model = Income
        fields = ['user', 'farm', 'date', 'description', 'amount', 'category', 'image']


class ExpenseCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ExpenseCategory
        fields = ['name']


class ExpenseSerializer(serializers.ModelSerializer):
    category = serializers.CharField()
    image = serializers.ImageField(required=False, allow_null=True)

    class Meta:
        model = Expense
        fields = ['id', 'date', 'amount', 'category', 'description', 'image', 'created_at', 'updated_at', 'is_edited']

    def create(self, validated_data):
        category_name = validated_data.pop('category')
        request = self.context.get('request')
        farm = request.user.farm

        # Get or create the category
        category, _ = ExpenseCategory.objects.get_or_create(
            farm=farm,
            name=category_name
        )

        # Create the expense with the user and farm
        expense = Expense.objects.create(
            user=request.user,
            farm=farm,
            category=category,
            **validated_data
        )
        return expense

    def update(self, instance, validated_data):
        category_name = validated_data.pop('category', None)
        if category_name:
            category, _ = ExpenseCategory.objects.get_or_create(
                farm=instance.farm,
                name=category_name
            )
            instance.category = category
        
        # Update other fields
        instance.date = validated_data.get('date', instance.date)
        instance.amount = validated_data.get('amount', instance.amount)
        instance.description = validated_data.get('description', instance.description)
        
        # Handle image update
        if 'image' in validated_data:
            # If there's an existing image and a new one is being uploaded, delete the old one
            if instance.image and validated_data['image']:
                instance.image.delete(save=False)
            instance.image = validated_data['image']
        
        instance.save()
        return instance


class IncomeCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeCategory
        fields = ['name']

class IncomeSerializer(serializers.ModelSerializer):
    category = serializers.CharField()
    image = serializers.ImageField(required=False, allow_null=True)

    class Meta:
        model = Income
        fields = ['id', 'date', 'amount', 'category', 'description', 'milk_payment', 'image', 'created_at', 'updated_at', 'is_edited']

    def create(self, validated_data):
        category_name = validated_data.pop('category')
        request = self.context.get('request')
        farm = request.user.farm

        # Get or create the category
        category, _ = IncomeCategory.objects.get_or_create(
            farm=farm,
            name=category_name
        )

        # Create the income with the user and farm
        income = Income.objects.create(
            user=request.user,
            farm=farm,
            category=category,
            **validated_data
        )
        return income

    def update(self, instance, validated_data):
        category_name = validated_data.pop('category', None)
        if category_name:
            category, _ = IncomeCategory.objects.get_or_create(
                farm=instance.farm,
                name=category_name
            )
            instance.category = category

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance